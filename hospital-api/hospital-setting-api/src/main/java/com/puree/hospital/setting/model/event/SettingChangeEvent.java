package com.puree.hospital.setting.model.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 业务配置变更事件
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/12 12:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettingChangeEvent {
    /**
     * key
     */
    private String key;
    /**
     * 模版名称
     */
    private String templateName;
    /**
     * 范围 （平台、医院）
     */
    private Classify classify;
    /**
     * 操作 (新增、修改、删除)
     */
    private Operate operate;
    /**
     * 操作时间
     */

    private String time;
    /**
     * 医院 ID
     */
    private Long hospitalId;

    /**
     * 类型 (模板、配置项)
     */
    private Type type;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 请求正文
     */
    private String requestBody;

    /**
     * 环境
     */
    private String environment;

    /**
     * 模版/配置项 ID
     */
    private Long id;

    @Getter
    public enum Classify {
        PLATFORM("运营平台"), HOSPITAL("医院");

        private final String value;

        Classify(String value) {
            this.value = value;
        }
    }
    @Getter
    public enum Operate {
        ADD("新增"), UPDATE("修改"), DELETE("删除");
        private final String value;

        Operate(String value) {
            this.value = value;
        }

    }
    @Getter
    public enum Type {
        TEMPLATE("模板"), ITEMS("配置项");
        private final String value;

        Type(String value) {
            this.value = value;
        }
    }

}
