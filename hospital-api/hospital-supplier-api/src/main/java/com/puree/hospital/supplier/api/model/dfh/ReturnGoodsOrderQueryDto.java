package com.puree.hospital.supplier.api.model.dfh;

import lombok.Data;

import java.util.Date;

/**
 * 退货订单查询
 */
@Data
public class ReturnGoodsOrderQueryDto {
    /**页码*/
    private Integer pageNo;
    /**每页大小*/
    private Integer pageSize;
    /**单据编号*/
    private String code;
    /**创建时间开始段*/
    private Date startCreate;
    /**创建时间结束段*/
    private Date endCreate;
    /**入库时间开始段*/
    private Date inBeginTime;
    /**入库时间结束段*/
    private Date inEndTime;
    /**店铺代码*/
    private String shopCode;
    /**分销商名称*/
    private String drpTenantName;
    /**平台单号*/
    private String platformCode;
    /**退货类型代码*/
    private String returntype;
    /**快递单号*/
    private String expressNo;
    /**会员名称*/
    private String vipName;
    /**同意状态
     * 0:未同意
     * 1:同意
     */
    private Integer agree;
    /**入库状态
     * 0:未入库
     * 1:已入库
     */
    private Integer receive;
    /**作废状态
     * 0:未作废
     * 1:已作废
     */
    private Integer cancel;
    /**是否三无包裹
     * 0:不是
     * 1:是
     */
    private Integer noParcel;
    /**退货人姓名*/
    private String receiverName;
    /**退货人手机*/
    private String receiverPhone;
    /**退入仓库代码*/
    private String warehouseinCode;
}
