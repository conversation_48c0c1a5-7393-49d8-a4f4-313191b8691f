package com.puree.hospital.supplier.api.factory;


import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteXXService;
import com.puree.hospital.supplier.api.model.xx.XXCountPrice;
import com.puree.hospital.supplier.api.model.xx.XXOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2022/08/16/12:03
 * @Description:
 */
@Component
@Slf4j
public class RemoteXXFallbackFactory implements FallbackFactory<RemoteXXService>{

    @Override
    public RemoteXXService create(Throwable cause) {
        log.error("香雪supplier服务调用失败:{}", cause.getMessage());
        return  new RemoteXXService(){
            @Override
            public R<JSONObject> insertOrder(XXOrder xiangXueOrder) {
                return R.fail("远程调用-录入订单失败");
            }
            @Override
            public R<JSONObject> getCountPrice(XXCountPrice xiangXueCountPrice) {
                return R.fail("远程调用-计算药品价格失败");
            }
            @Override
            public R<JSONObject> getUpdateStatus(String pspnum) {
                return R.fail("远程调用-获取订单状态失败");
            }
            @Override
            public R<JSONObject> listDrug() {
                return R.fail("远程调用-获取药品信息失败");
            }
            @Override
            public R<JSONObject> getOrderlogistics(String pspnum) {
                return R.fail("远程调用-获取物流信息失败");
            }
        };
    }
}
