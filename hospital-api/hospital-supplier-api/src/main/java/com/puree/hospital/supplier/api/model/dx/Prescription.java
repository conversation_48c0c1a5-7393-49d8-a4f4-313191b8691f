package com.puree.hospital.supplier.api.model.dx;

import lombok.Data;

/**
 * 处方信息
 */
@Data
public class Prescription {
    /**
     * 医院名称
     */
    private String hospital_name;
    /**
     * 处方号
     */
    private String pspnum;
    /**
     * 患者姓名
     */
    private String name;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 年龄
     */
    private String age;
    /**
     * 贴数
     */
    private Integer dose;
    /**
     * 每天服药次数
     */
    private Integer takenum;
    /**
     * 取药时间 yyyy-MM-dd HH:mm:ss
     */
    private String getdrugtime;

    /**
     * 煎药方案
     * 传入编号
     * 1： 微压（密闭）解表（15min）
     * 2： 微压（密闭）汤药（30min）
     * 3：微压(密闭)(40min)
     * 4： 常压解表（10min， 10min）
     * 5： 常压汤药（20min， 15min）
     * 6：常压补药（25min， 20min）
     * 20：先煎解表（10min， 10min，
     * 10min）
     * 21：先煎汤药（10min， 20min，
     * 15min）
     */
    private Integer decscheme;
    /**
     * 一煎时间
     */
    private String oncetime;
    /**
     * 二煎时间
     */
    private String twicetime;
    /**
     * 包装量
     */
    private Integer packagenum;
    private String dotime;
    /**
     * 操作人员
     */
    private String doperson;
    private Integer dtbtype;
    private String soakwater;
    private Integer soaktime;
    private String ordertime;
    private String decmothed;
    private String takeway;
    private Integer drug_count;
    private String isDaijian;
    private String ptype;

    //不是必填项但是需要
    private String delnum;
    private String phone;
    private String address;
    private String department;
    private String inpatientarea;
    private String Ward;
    private String Sickbed;
    private String diagresult;
    private String getdrugnum;
    private String dtbcompany;
    private String dtbaddress;
    private String dtbphone;
    private String labelnum;
    private String remark;
    private String doctor;
    private String footnote;
    private String curstate;
    private String takemethod;
    private String remarkA;
    private String remarkB;
    private String payment;
    private String Yizhu;
    private String money;
    private String outpatientNumber;
    private String outpatientIndex;
    private String particular;
    private String token;
}
