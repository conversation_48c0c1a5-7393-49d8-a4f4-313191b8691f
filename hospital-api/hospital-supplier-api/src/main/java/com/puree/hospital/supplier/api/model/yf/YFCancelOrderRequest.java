package com.puree.hospital.supplier.api.model.yf;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName YFCancelOrderRequest
 * <AUTHOR>
 * @Description 取消订单请求
 * @Date 2023/12/28 16:22
 * @Version 1.0
 */
@Data
public class YFCancelOrderRequest implements Serializable {

    /**
     * 加密开关 为 true 时代表加密字段已存放
     */
    @JsonIgnore
    private Boolean isEncrypt;
    /**
     * 其它字段 AES 加密存放，防篡改
     */
    private String encryptedContent;
    /**
     * 第三方订单ID
     */
    private String thirdPartyId;
    /**
     * 机构ID，这里为配送企业里面的id也就是我们数据库的id
     */
    private String hostpitalId;

    /**
     * @Param
     * @Return java.lang.String
     * @Description 通过加密开关来取值
     * <AUTHOR>
     * @Date 2023/12/27 14:41
     **/
    public String getEncryptedContent() {
        if (Boolean.FALSE.equals(isEncrypt) || isEncrypt == null) {
            return YFCerpConstant.AES_PLACEHOLDER;
        }
        return encryptedContent;
    }
}
