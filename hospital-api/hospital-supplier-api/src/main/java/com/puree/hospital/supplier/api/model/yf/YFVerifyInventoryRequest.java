package com.puree.hospital.supplier.api.model.yf;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName YFVerifyInventory
 * <AUTHOR>
 * @Description 验证库存请求实体
 * @Date 2023/12/26 15:59
 * @Version 1.0
 */
@Data
public class YFVerifyInventoryRequest implements Serializable {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 机构id 这里为配送企业里面的id也就是我们数据库的id
     */
    private String hostpitalId;
    /**
     * 付数
     */
    private String drugNum;
    /**
     * 药方内容（id 药品id、drug_num 药品数量、type 药品类型）必传字段
     */
    private String fContent;
    /**
     * //药方内容（id 药品id、drug_num 药品数量、type 药品类型）必传字
     */
    private String prescriptionContent;
    /**
     * 密钥
     */
    private String secretKey;
    /**
     * 颗粒可以不传药片类型：颗粒：grain；防疫方：prevention
     */
    private String drugType;
}
