package com.puree.hospital.supplier.api.model.zx;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单药品列表
 */
@Data
public class Medicines {
    /**
     * 药品编码（医院的药品编码）
     */
    private String hospitalMedicineCode;
    /**
     * 药品名称（医院的药品名称）
     */
    private String medicineName;
    /**
     * 购买数量(单剂),单位克g
     */
    private BigDecimal purchaseNbr;
    /**
     * 特殊煎煮法 如先煎、后下、另煎、包煎、冲服、焗、服、另炖、烊化
     */
    private String specialDecoctionWay;
    /**
     * 默认为g
     */
    private String dosageUnit="g";
    /**
     * 药品零售价
     */
    private BigDecimal medPrice;
    /**
     * 备注（忽略）
     */
    private String medRemark;
}
