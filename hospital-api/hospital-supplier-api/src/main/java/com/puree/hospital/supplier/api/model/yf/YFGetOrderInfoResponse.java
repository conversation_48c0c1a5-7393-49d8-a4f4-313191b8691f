package com.puree.hospital.supplier.api.model.yf;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName YFGetOrderInfoResponse
 * <AUTHOR>
 * @Description 获取订单详情 响应
 * @Date 2023/12/28 16:41
 * @Version 1.0
 */
@Data
public class YFGetOrderInfoResponse implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单状态。
     * 0待审方、1审方不通过、2待制备、3制备中、4待发货[已调配]、5已发货、6已收货、7创建第三方订失败、8订单取消、
     * 9已退贷、10制备失败、11机构余额不足、66待复核、77互联网医院待支付、88待抄方
     */
    private Integer orderStatus;
    /**
     * 快递单号
     */
    private String expressNo;
    /**
     * 快递名称
     */
    private String expressName;
    /**
     * 第三方订单id
     */
    private String thirdPartyId;
}
