package com.puree.hospital.supplier.api;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteDFHFallbackFactory;
import com.puree.hospital.supplier.api.model.dfh.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;

/**
 * im接口
 */
@FeignClient(contextId = "RemoteDFHService", value = ServiceNameConstants.SUPPLIER_SERVICE, fallbackFactory = RemoteDFHFallbackFactory.class)
public interface RemoteDFHService {

    /**
     * 添加订单
     * @param dfhOrder
     * @return
     */
    @PostMapping("/dfh/addOrder")
    HashMap<String, Object> addOrder(@RequestBody DFHOrder dfhOrder);

    /**
     * 退货订单新增
     * @param dhfOrder
     * @return
     */
    @PostMapping("/dfh/addTradeReturn")
    HashMap<String, Object> addTradeReturn(@RequestBody DFHOrder dhfOrder);

    /**
     * 新库存查询
     * @param stockQueryDto
     * @return
     */
    @PostMapping("/dfh/getNewStock")
    R<JSONObject> getNewStock(@RequestBody StockQueryDto stockQueryDto);

    /**
     * 订单查询
     * @param orderQueryDto
     * @return
     */
    @PostMapping("/dfh/getOrderInfo")
    R<JSONObject> getOrderInfo(@RequestBody OrderQueryDto orderQueryDto);

    /**
     * 退货订单查询
     * @param returnGoodsOrderQueryDto
     * @return
     */
    @PostMapping("/dfh/getTradeReturns")
    R<JSONObject> getTradeReturns(@RequestBody ReturnGoodsOrderQueryDto returnGoodsOrderQueryDto);

    /**
     * 发货订单查询
     * @param deliverOrderQueryDto
     * @return
     */
    @PostMapping("/dfh/getDeliverys")
    R<JSONObject> getDeliverys(@RequestBody DeliverOrderQueryDto deliverOrderQueryDto);

    @PostMapping("/dfh/refundUpdate")
    R<JSONObject> refundUpdate(@RequestBody DFHOrder dhfOrder);
}
