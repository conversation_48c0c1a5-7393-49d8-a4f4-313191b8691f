package com.puree.hospital.supplier.api.model.xx;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2022/08/15/14:38
 * @Description:
 */
@Data
public class XXCountPrice {

    /**
     *密钥 加密方式： Secret（） + deptId（） + time（当前13位时间戳） 拼接后进行md5加密 得到 sign
     * 必填
     */
    private String sign;
    /**
     *订单管控平台管理员分配
     * 必填
     */
    private Integer deptId;
    /**
     *位时间戳
     * 必填
     */
    private String time;
    /**
     *煎药方式 0自煎  1代煎  2丸剂   4散剂  5膏方   31 素丸  33 水蜜丸
     * 必填
     */
    private String makeff;
    /**
     * 剂数
     * 必填
     */
    private Integer dose;
    /**
     * 问诊费
     * 默认传 0
     */
    private Integer consultcost;
      /**
     * 物流费
     * 默认传 0
     */
    private BigDecimal logisticscost;
    /**
     * 药品列表
     * 默认 null
     * 传值：200,150,100,50
     */
    private List<XXDrug> drugs;

}
