package com.puree.hospital.supplier.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteSystemService;
import com.puree.hospital.supplier.api.model.system.SysProvince;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName RemoteSystemFallbackFactory
 * <AUTHOR>
 * @Description system 远程调用降级处理
 * @Date 2023/12/29 18:40
 * @Version 1.0
 */
@Component
public class RemoteSystemFallbackFactory implements FallbackFactory<RemoteSystemService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteSystemFallbackFactory.class);

    @Override
    public RemoteSystemService create(Throwable cause) {
        log.error("一方 supplier 服务调用失败:{}", cause.getMessage());
        return new RemoteSystemService() {
            @Override
            public R<List<SysProvince>> getSysProvince() {
                return R.fail("远程调用-获取地址信息失败！");
            }
        };
    }
}
