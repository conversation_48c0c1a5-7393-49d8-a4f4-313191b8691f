package com.puree.hospital.supplier.api.model.gy.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 处方明细类
 * <AUTHOR>
 * @date 2022/8/8
 */
@Data
public class RecipeItemDTO {
    private String recipedtlno;
    private String drugcode;
    private String drugname;
    private String prodarea;
    private String factoryname;
    private String drugspec;
    private String freqname;
    private String sustaineddays;
    private String classtypeno;
    private String classtypename;
    private BigDecimal quantity;
    private String drugunit;
    private String usagename;
    private String dosage;
    private String dosageunit;
    private BigDecimal unitprice;
    private String measurement;
    private String measurementunit;
    private String doctor_note;
}
