package com.puree.hospital.supplier.api.model.gy.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 处方信息类
 * <AUTHOR>
 * @date 2022/8/8
 */
@Data
public class RecipeDTO {
    private String recipeno;
    private String doctorAnswerNo;
    private String hospitalid;
    private String caseno;
    private String hiscardno;
    private String patientname;
    private String idnumber;
    private String mobile;
    private String outhospno;
    private String empsex;
    private String age;
    private String birthdate;
    private String visitdate;
    private String patienttypename;
    private String medicarecategname;
    private String signalsourcetypename;
    private String registdeptcode;
    private String registdeptname;
    private String recipestatus;
    private String hospital;
    private String registdrcode;
    private String registdrname;     //octorName
    private String recipebegindate;
    private String recipeenddate;
    private String contactname;
    private String contactaddr;
    private String contactphone;
    private String country;//国家
    private String province;//省
    private String city;//市
    private String disrict;//区
    private String address;//地址
    private String paydate;
    private String paystatus;
    private String storeno;//POS门店编码
    private String diagcode;
    private String diagname;
    private String patientno;

    private List<RecipeItemDTO> detaillist;

    //2019年9月24日  新增
    private String recipe_source_flag = "2";    //处方来源类型 1：住院, 2：门诊
    private String leave_hospital_date;   //出院时间
    private String hospitalization_department;//患者住院科室名称
    private String hospitalization_no;//住院号
    private String hospitalization_bedno;//住院床号

    //2020年3月3日 新增
    private String platformtype;//平台类型，HIS/kingdee
    private Integer deliverytype = 1;//配送类型 1 快递配送 2 到店自提
    private Integer paymethod;//付款方式
    private String payno;//支付流水号
    private BigDecimal payamount;//付款金额
    private String deliveryfee;//配送费
    private String drugfeeamount;//药品费合计
    private Integer paytype = 1;//付款类型 1 到店支付 2 在线支付 3 货到付款
    private String companyid;//医药公司标识
    private String storeid;//药店标识
    private Integer needinvoice = 0;//是否开具发票 0 否，1是
    private String invtaxpayno;//纳税人识别号
    private String invtitle;//发票抬头
    private String invtitletype;//抬头类型：1-企业，2-个人
    // 系统自用
    private String appId;
    private String appKey;
    private String privateKey;
    private String publicKey;
    private String baseUrl;
}
