package com.puree.hospital.supplier.api.model.hr;

import lombok.Data;

/**
 * 订单明细
 *
 * <AUTHOR>
 * @date 2023/7/6 9:28
 */
@Data
public class HrOrderDetails {
    /**
     * 订单明细id
     */
    private String DetailId;
    /**
     * 产品id
     */
    private String ProductId;
    /**
     * 产品名称
     */
    private String MedicineName;
    /**
     * 产品通用名
     */
    private String CommonName;
    /**
     * 规格
     */
    private String Specs;
    /**
     * 厂家
     */
    private String Producer;
    /**
     * 数量
     */
    private int Quantity;
    /**
     * 单价
     */
    private Double Price;
    /**
     * 费用序列号
     */
    private String FeeSerialNo;
    /**
     *费用类型
     */
    private String FeeType;
    /**
     * 单位
     */
    private String Unit;
    /**
     * 金额
     */
    private Double Amount;
    /**
     * 备注
     */
    private String Description;
    /**
     * 供货门店
     */
    private String SupplyStoreId;
}
