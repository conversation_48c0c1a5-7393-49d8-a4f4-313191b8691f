package com.puree.hospital.supplier.api.model.yf;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName YFVerifyInventory
 * <AUTHOR>
 * @Description 验证库存响应
 * @Date 2023/12/26 16:02
 * @Version 1.0
 */
@Data
public class YFVerifyInventoryResponse implements Serializable {
    /**
     * 处方内容
     */
    private List<YFPrescriptionContent> prescriptionContent;
    /**
     * 颗粒量内容
     */
    private String granuleSting;
    /**
     * 是否开启
     */
    private boolean isOpen;
    /**
     * 有药内容
     */
    private String haveContentString;
    /**
     * 缺药内容
     */
    private String contentString;
    /**
     * 无颗粒计数
     */
    private String noGranuleCount;
    /**
     * 机构价格
     */
    private String jgPrice;
    /**
     * 调剂总价
     */
    private String adjustPrice;
    /**
     * 调剂类型
     */
    private String adjustType;
    /**
     * 当前价格
     */
    private String currentPrice;
    /**
     * 颗粒总量
     */
    private String granuleCount;
    /**
     * 状态(1、缺药，2、有)
     */
    private String stock;
    /**
     * 零售价
     */
    private String retailPrice;

}
