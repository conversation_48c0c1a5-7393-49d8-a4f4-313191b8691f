package com.puree.hospital.supplier.api.model.yf;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName YFAddOrderRequest
 * <AUTHOR>
 * @Description 添加订单请求
 * @Date 2023/12/27 11:39
 * @Version 1.0
 */
@Data
public class YFAddOrderRequest implements Serializable {

    /**
     * 加密开关 为 true 时代表加密字段已存放
     */
    @JsonIgnore
    private Boolean isEncrypt;
    /**
     * 其它字段 AES 加密存放，防篡改
     */
    private String encryptedContent;
    /**
     * 机构ID，这里为配送企业里面的id也就是我们数据库的id
     */
    private String hostpitalId;
    /**
     * 剂量
     */
    private String drugNum;
    /**
     * 0待审方、1审方不通过、2待制备、3制备中、
     * 4待发货[已调配]、5 已发货、6已收货、
     * 7创建第三方订失败、8订单取消、9已退贷、
     * 10制备失败、11机构余额不足、66待复核、77互联网医院待支付、88待抄方
     */
    private String orderStatus;
    /**
     * 处方网址
     */
    private String prescriptionUrl;
    /**
     * 处方内容
     */
    private String prescriptionContent;
    /**
     * 是否浓煎
     */
    private boolean isHeavy;
    /**
     * 是否外用
     */
    private boolean isOutside;
    /**
     * 是否小儿量
     */
    private boolean isChild;
    /**
     * 是否妊娠
     */
    private boolean isGestation;
    /**
     * 医院备注，煎煮要求
     */
    private String dtrAdvice;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 用户手机号
     */
    private String userPhone;
    /**
     * 省份代码
     */
    private String province;
    /**
     * 省份名称
     */
    private String city;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 医生名称
     */
    private String doctorName;
    /**
     * 科室名字
     */
    private String departmentName;
    /**
     * 患者年龄
     */
    private String age;
    /**
     * 患者性别（1=男；2=女）
     */
    private String sex;
    /**
     * 诊断内容
     */
    private String clinicalDiagnosis;
    /**
     * 配送类型(receipt_mode 配送类型; express 快递到家; patientarea 送至病房; drugstore 送至药房)
     */
    private String receiptMode;
    /**
     * 药物类型：grain=颗粒；
     */
    private String drugType;
    /**
     * 第三方订单id
     */
    private String thirdPartyId;
    /**
     * 收件人名字
     */
    private String borrowerName;
    /**
     * 收件人电话
     */
    private String borrowerPhone;
    /**
     * 收件人地址编码
     */
    private String borrowerProvince;
    /**
     * 收件人地址
     */
    private String borrowerCity;
    /**
     * 收件人详细地址
     */
    private String borrowerAddress;
    /**
     * 单位数量 默认几小
     */
    private String unitNum;
    /**
     * 默认为药房发件人，如机构需指定发件人信息，可以联系后台人员设置获取
     */
    private String issuingId;
    /**
     * 药方名称
     */
    private String prescriptionName;
    /**
     * 业务类型；
     * granule_makeup颗粒代配(机配)、granule_pack颗粒代配(小袋装)、tcm_decoction 饮片代煎、
     * tcm_makeup饮片代配、plaster_makeup膏方代制、pill 蜜丸、
     * capsule 胶囊、powder 打粉、stock_in医院进货、medicinal_liquor 药酒
     */
    private String businessType;
    /**
     * 服药频次
     */
    private String frequency;
    /**
     * 机构类型（固定：3）
     */
    private Integer hostpitalType;


    /**
     * @Param
     * @Return java.lang.String
     * @Description 通过加密开关来取值
     * <AUTHOR>
     * @Date 2023/12/27 14:41
     **/
    public String getEncryptedContent() {
        if (Boolean.FALSE.equals(isEncrypt) || isEncrypt == null) {
            return YFCerpConstant.AES_PLACEHOLDER;
        }
        return encryptedContent;
    }
}
