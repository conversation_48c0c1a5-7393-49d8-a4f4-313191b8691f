package com.puree.hospital.supplier.api.model.dfh;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 东方红订单
 */
@Data
public class DFHOrder {
    /**店铺代码*/
    private String shopCode;
    /**会员代码*/
    private String vipCode;
    /**拍单时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dealDatetime;
    /**订单类型
     *Sales-销售订单
     * Return-换货订单
     * Charge-费用订单
     * Delivery-补发货订单
     * Invoice-补发票订单
     */
    private String orderTypeCode;
    /**仓库代码*/
    private String warehouseCode;
    /**物流公司*/
    private String expressCode;
    /**商品明细*/
    private List<Item> details;
    /**收货人*/
    private String receiverName;
    /**固定电话*/
    private String receiverPhone;
    /**手机号码*/
    private String receiverMobile;
    /**省名称*/
    private String receiverProvince;
    /**市名称*/
    private String receiverCity;
    /**区名称*/
    private String receiverDistrict;
    /**收货地址*/
    private String receiverAddress;
    /**退货原因代码*/
    private String typeCode;
    /**退入商品明细*/
    private List<Item> returnDetail;
    /**退款单号*/
    private String tradeCode;
    /**平台单号*/
    private String platformCode;
    /**销售订单平台单号*/
    private String tradePlatformCode;
    /**
     * 退款状态
     */
    private Integer refundState;
    /**
     * 子订单号
     */
    private String oid;
}
