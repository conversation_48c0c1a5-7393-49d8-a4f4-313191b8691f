package com.puree.hospital.supplier.api.model.yf;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName YFPrescriptionContent
 * <AUTHOR>
 * @Description 处方内容
 * @Date 2023/12/26 18:56
 * @Version 1.0
 */
@Data
public class YFPrescriptionContent implements Serializable {

    /**
     *
     */
    private String id;
    /**
     * 处方类型（cheng、shu、yin）
     */
    private String type;
    /**
     * 药品编号
     */
    private String drugNum;
    /**
     * 标签
     */
    private String lable;
    /**
     * 颗粒量
     */
    private String granuleNum;
    /**
     * 缩方量
     */
    private String proportionNum;
    /**
     * 颗粒编号
     */
    private String noGranuleNum;
    /**
     * 单个药材总价
     */
    private String price;
    /**
     * 处方名称
     */
    private String name;
    /**
     * 药材编码
     */
    private String code;
}
