package com.puree.hospital.supplier.api.factory;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteHRService;
import com.puree.hospital.supplier.api.model.hr.HrDrug;
import com.puree.hospital.supplier.api.model.hr.HrDrugInfoDTO;
import com.puree.hospital.supplier.api.model.hr.HrPrescrOrder;
import com.puree.hospital.supplier.api.model.hr.HrRefundOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RemoteHRFallbackFactory implements FallbackFactory<RemoteHRService> {

    @Override
    public RemoteHRService create(Throwable cause) {
        log.error("华润配送企业服务调用失败：", cause);
        return new RemoteHRService(){
            @Override
            public R<JSONObject> uploadRecipe(HrPrescrOrder prescrOrder) {
                return R.fail("上传处方失败：" + cause.getMessage());
            }

            @Override
            public R<List<HrDrug>> getDrugInfo(HrDrugInfoDTO drugInfoDTO) {
                return R.fail("查询药品详情失败：" + cause.getMessage());
            }

            @Override
            public R<String> refund(HrRefundOrder refundOrder) {
                return R.fail("退货/退款申请失败：" + cause.getMessage());
            }
        };
    }
}
