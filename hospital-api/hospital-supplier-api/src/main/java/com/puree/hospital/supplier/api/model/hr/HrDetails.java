package com.puree.hospital.supplier.api.model.hr;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 处方药品明细
 *
 * <AUTHOR>
 * @date 2023/7/5 18:04
 */
@Data
public class HrDetails {
    /**
     * 产品id
     */
    private String ProductId;
    /**
     * 产品单价
     */
    private Double Price;
    /**
     * 产品数量
     */
    private int Quantity;
    /**
     * 产品单位
     */
    private String Unit;
    /**
     * 备注
     */
    private String Description;
    /**
     * 剂量
     */
    private Double DrugDoseQuantity;
    /**
     * 剂量单位
     */
    private String DrugDoseUnit;
    /**
     * 用法用量
     */
    private String Frequency;
    /**
     * 用药频次
     */
    private BigDecimal FrequencyQuantity;
    /**
     * 用药频次描述
     */
    private String FrequencyDescription;
    /**
     * 用药途径
     */
    private String Usage;
    /**
     * 用药途径描述
     */
    private String UsageDescription;
    /**
     * 每次量
     */
    private Double PerDosageQuantity;
    /**
     * 次量单位
     */
    private String PerDosageUnit;
    /**
     * 每次用量描述
     */
    private String PerDosageDescription;
    /**
     * 用药天数
     */
    private int Days;
    /**
     * 药品类型
     */
    private String Style;
    /**
     * 通用名
     */
    private String CommonName;
    /**
     * 规格
     */
    private String Specs;
    /**
     * 名称
     */
    private String MedicineName;
    /**
     * 厂家
     */
    private String Producer;

}
