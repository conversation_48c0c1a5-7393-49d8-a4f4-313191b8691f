package com.puree.hospital.supplier.api.model.xx;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2022/08/15/12:06
 * @Description:
 */
@Data
public class XXDrug {

    /**
     *药品id
     * 必填
     */
    private String id;
    /**
     *药品编号
     * 必填
     */
    private String code;
    /**
     *药品名称
     * 必填
     */
    private String name;
    /**
     *药品重量
     * 必填
     * 例如：10
     */
    private BigDecimal weight;
    /**
     *特殊制法
     * 必填
     */
    private String specialmake;
    /**
     *药品单位
     * 默认g
     */
    private String unit;
    /**
     *药品价格
     * 必填
     */
    private BigDecimal ygprice;
}
