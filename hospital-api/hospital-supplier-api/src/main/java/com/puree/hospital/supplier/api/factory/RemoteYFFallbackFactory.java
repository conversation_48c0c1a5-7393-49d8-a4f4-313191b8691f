package com.puree.hospital.supplier.api.factory;


import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteYFService;
import com.puree.hospital.supplier.api.model.yf.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * @ClassName RemoteYFFallbackFactory
 * <AUTHOR>
 * @Description 一方远程调用降级处理
 * @Date 2023/12/29 18:40
 * @Version 1.0
 */
@Component
public class RemoteYFFallbackFactory implements FallbackFactory<RemoteYFService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteYFFallbackFactory.class);

    @Override
    public RemoteYFService create(Throwable cause) {
        log.error("一方 supplier 服务调用失败:{}", cause.getMessage());
        return new RemoteYFService() {
            @Override
            public R<YFVerifyInventoryResponse> verifyInventory(YFVerifyInventoryRequest request) {
                return R.fail("远程调用-校验库存失败！");
            }

            @Override
            public R<YFAddOrderResponse> addOrder(YFAddOrderRequest request) {
                return R.fail("远程调用-创建订单失败！");
            }

            @Override
            public R<Boolean> cancelOrder(YFCancelOrderRequest request) {
                return R.fail("远程调用-取消订单失败！");
            }

            @Override
            public R<YFGetOrderInfoResponse> getOrderInfo(YFGetOrderInfoRequest request) {
                return R.fail("远程调用-获取订单信息失败！");
            }
        };
    }
}
