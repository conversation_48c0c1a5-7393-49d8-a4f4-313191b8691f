package com.puree.hospital.supplier.api.factory;


import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteZXService;
import com.puree.hospital.supplier.api.model.zx.dto.CannelOrderDTO;
import com.puree.hospital.supplier.api.model.zx.dto.GetOrderDTO;
import com.puree.hospital.supplier.api.model.zx.dto.NewCreateDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 至信 配送企业远程调用
 *
 * @Author: wjx
 * @Date: 2023/05/16
 */
@Component
public class RemoteZXFallbackFactory implements FallbackFactory<RemoteZXService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteZXFallbackFactory.class);
    @Override
    public RemoteZXService create(Throwable cause) {
        log.error("至信supplier服务调用失败:{}", cause.getMessage());
        return  new RemoteZXService(){
            @Override
            public R<String> createOrder(NewCreateDTO newCreateDTO) {
                return R.fail("远程调用-创建订单失败！");
            }
            @Override
            public R<String> queryOrder(GetOrderDTO getOrderDTO) {
                return R.fail("远程调用-查询订单失败！");
            }
            @Override
            public R<String> cannelOrder(CannelOrderDTO cannelOrderDTO) {
                return R.fail("远程调用-取消订单失败！");
            }
        };
    }
}
