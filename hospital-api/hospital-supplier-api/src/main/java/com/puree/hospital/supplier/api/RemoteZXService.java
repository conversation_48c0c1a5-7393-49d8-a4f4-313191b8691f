package com.puree.hospital.supplier.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteZXFallbackFactory;
import com.puree.hospital.supplier.api.model.zx.dto.CannelOrderDTO;
import com.puree.hospital.supplier.api.model.zx.dto.GetOrderDTO;
import com.puree.hospital.supplier.api.model.zx.dto.NewCreateDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@FeignClient(contextId = "RemoteZXService",
        value = ServiceNameConstants.SUPPLIER_SERVICE,
        fallbackFactory = RemoteZXFallbackFactory.class)
public interface RemoteZXService {

    /**
     * 创建订单
     *
     * @param newCreateDTO 创建订单请求参数
     * @return 响应结果
     */
    @PostMapping("/zhixin/createOrder")
    R<String> createOrder(@RequestBody NewCreateDTO newCreateDTO);

    /**
     * 查询订单
     *
     * @param getOrderDTO 查询订单请求参数
     * @return 响应结果
     */
    @PostMapping("/zhixin/queryOrder")
    R<String> queryOrder(@RequestBody GetOrderDTO getOrderDTO);

    /**
     * 取消订单
     *
     * @param cannelOrderDTO 取消订单请求参数
     * @return 响应结果
     */
    @PostMapping("/zhixin/cannelOrder")
    R<String> cannelOrder(@RequestBody CannelOrderDTO cannelOrderDTO);

}
