package com.puree.hospital.supplier.api.model.hr;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 处方信息
 *
 * <AUTHOR>
 * @date 2023/7/5 17:48
 */
@Data
public class HrPrescrs {
    /**
     * 患者id
     */
    private String PatientId;
    /**
     * 处方日期
     */
    private String PrescrDate;
    /**
     * 处方失效日期
     */
    private Date ExpiryDate;
    /**
     * 处方文件url
     */
    private String ImageUri;
    /**
     * 处方号
     */
    private String PrescrNo;
    /**
     * 门诊号
     */
    private String OutpatientNo;
    /**
     * 购药人手机号
     */
    private String BuyerMobile;
    /**
     * 购药人id
     */
    private String BuyerId;
    /**
     * 诊断结果
     */
    private String DiagnosisResult;
    /**
     * 就诊日期
     */
    private Date DiagnosisDate;
    /**
     * 就诊类别
     */
    private String DiagnosisType;
    /**
     * 医嘱
     */
    private String MedicalOrder;
    /**
     * 审方药师
     */
    private String ReviewerName;
    /**
     * 备注
     */
    private String Description;
    /**
     * 医院名称
     */
    private String HospitalName;
    /**
     * 科室名称
     */
    private String DepartmentName;
    /**
     * 开方医生
     */
    private String DoctorName;
    /**
     * 医院id
     */
    private String HospitalId;
    /**
     * 医生id
     */
    private String DoctorId;
    /**
     * 处方金额
     */
    private Double PrescrAmount;
    /**
     * 处方药品明细
     */
    private List<HrDetails>  Details;
}
