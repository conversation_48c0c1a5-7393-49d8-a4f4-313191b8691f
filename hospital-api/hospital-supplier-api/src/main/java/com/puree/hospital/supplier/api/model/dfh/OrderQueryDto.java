package com.puree.hospital.supplier.api.model.dfh;

import lombok.Data;

import java.util.Date;

@Data
public class OrderQueryDto {
    /**页码*/
    private Integer pageNo;
    /**每页大小*/
    private Integer pageSize;
    /**开始时间*/
    private Date startDate;
    /**结束时间*/
    private Date endDate;
    /**时间类型
     * 0:创建时间
     * 1:拍单时间
     * 2:付款时间
     * 3:修改时间
     */
    private Integer dateType;
    /**订单类型
     * 0:全部
     * 1:未审核
     * 2:已审核
     */
    private Integer orderState;
    /**仓库代码*/
    private String warehouseCode;
    /**店铺代码*/
    private String shopCode;
    /**会员名称*/
    private String vipName;
    /**平台单号*/
    private String platformCode;
    /**收件手机*/
    private String receiverMobile;
    /**单据编号*/
    private String code;
    /**是否附带返回已取消与已删除的订单数据*/
    private boolean hasCancelData;
}
