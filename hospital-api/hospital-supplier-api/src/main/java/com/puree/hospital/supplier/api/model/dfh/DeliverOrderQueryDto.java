package com.puree.hospital.supplier.api.model.dfh;

import lombok.Data;

import java.util.Date;

@Data
public class DeliverOrderQueryDto {
    /**页码*/
    private Integer pageNo;
    /**每页大小*/
    private Integer pageSize;
    /**开始时间*/
    private Date startDate;
    /**结束时间*/
    private Date endDate;
    /**发货开始时间*/
    private Date startDeliveryDate;
    /**发货结束时间*/
    private Date endDeliveryDate;

    /**仓库代码*/
    private String warehouseCode;
    /**店铺代码*/
    private String shopCode;
    /**会员名称*/
    private String vipName;
    /**平台单号*/
    private String platformCode;
    private Integer delivery;
    private String outerCode;
}
