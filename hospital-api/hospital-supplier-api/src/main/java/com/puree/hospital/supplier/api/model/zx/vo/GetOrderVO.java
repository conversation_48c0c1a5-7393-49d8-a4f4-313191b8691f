package com.puree.hospital.supplier.api.model.zx.vo;

import com.puree.hospital.supplier.api.model.zx.Res;
import lombok.Data;

import java.util.List;

/**
 * 查询反参实体
 */
@Data
public class GetOrderVO {
    /**
     * 状态码：
     * 下单成功返回 200
     * 下单失败返回400
     */
    private String code;
    /**
     * 成功返回“下单成功”
     * 失败返回“报错信息”
     */
    private String msg;
    /**
     * 订单信息列表
     */
    private List<Res> resList;
}
