package com.puree.hospital.supplier.api.model.zx;

import lombok.Data;

/**
 * 订单信息列表
 */
@Data
public class Res {
    /**
     * 代收货款金额
     */
    private String agencyFund;
    /**
     * 处方剂型
     */
    private String agentTypeZx;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 运费
     */
    private String carriage;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 地址后缀(街道地址)
     */
    private String suffix_addr;
    /**
     * 发货人
     */
    private String consignor;
    /**
     * 发货地址
     */
    private String consignorAddr;
    /**
     * 发货人联系电话
     */
    private String consignorPhone;
    /**
     * 创建日期
     */
    private String createDate;
    /**
     * 开方人/开方医师
     */
    private String createPre;
    /**
     * 月结账号
     */
    private String cusMonthlyId;
    /**
     * 顺丰快递月结方式
     */
    private String cusMonthlyType;
    /**
     * 病人的临床诊断
     */
    private String diagnosis;
    /**
     * 订单总价格
     */
    private String costPrice;
    /**
     * 保价金额
     */
    private String insureValue;
    /**
     * 接口订单号
     */
    private String interfaceOrderCode;
    /**
     * 是否代收款 0-否 1-是
     */
    private String isCollection;
    /**
     * 是否是合并单 0-不是合并单， 1-合并单
     */
    private String isCombine;
    /**
     * 是否派单：0-未派单 1-已派单
     */
    private String isDispatch;
    /**
     * 是否已经打印订单 -0-否 1-是
     */
    private String isPrint;
    /**
     * 是否是特殊订单 0-否 1-是
     */
    private String isSpecial;
    /**
     * 是否是加急的 0-否 1-是
     */
    private String isUrgent;
    /**
     * 快递号条形码链接
     */
    private String logisticBarcodePath;
    /**
     * 快递号
     */
    private String logisticCode;
    /**
     * 快递公司名
     */
    private String logisticCompany;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 订单号条形码链接
     */
    private String orderCodeBarcodePath;
    /**
     * 客户编码
     */
    private String orderCusCode;
    /**
     * 订单剂量
     */
    private String orderDosis;
    /**
     * 订单备注
     */
    private String orderRemark;
    /**
     * 包裹数量
     */
    private String parcelQuantity;
    /**
     * 病人编码
     */
    private String patientCode;
    /**
     * 调剂师
     */
    private String pharmacist;
    /**
     * 收件人
     */
    private String receiver;
    /**
     * 状态 -1取消订单 0-等待确认 1-初审通过 2-初审不通过 3-人工复审不通过 4-已确认,可派单(人工审核通过) 5-已派单 6-抓药完成 7-出库，发货  8-确认收货 9-申请退货中 10-退货完成 11-调剂完成 12-退货审核中 13-退货审核不通过 14-退货审核完成 15-退货完成
     */
    private String status;
    /**
     * 订单提交状态 0-未提交 1-已经提交
     */
    private String submitStatus;
    /**
     * 订单金额
     */
    private String totalPrice;
    /**
     * 服用方法
     */
    private String usingType;

}
