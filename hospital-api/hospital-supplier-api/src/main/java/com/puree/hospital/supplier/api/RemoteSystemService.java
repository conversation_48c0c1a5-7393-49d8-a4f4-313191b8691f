package com.puree.hospital.supplier.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteSystemFallbackFactory;
import com.puree.hospital.supplier.api.model.system.SysProvince;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * im接口
 */
@FeignClient(contextId = "RemoteSystemService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteSystemFallbackFactory.class)
public interface RemoteSystemService {

    /**
     * @Param
     * @Return com.puree.hospital.common.api.domain.R<java.util.List < com.puree.hospital.supplier.api.model.system.SysProvince>>
     * @Description 获取地址
     * <AUTHOR>
     * @Date 2023/12/29 18:40
     **/
    @GetMapping("/district/list")
    R<List<SysProvince>> getSysProvince();
}
