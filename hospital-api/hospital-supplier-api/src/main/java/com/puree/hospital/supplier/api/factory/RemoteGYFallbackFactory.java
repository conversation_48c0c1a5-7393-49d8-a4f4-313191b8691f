package com.puree.hospital.supplier.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteGYService;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/11 10:02
 */
@Component
public class RemoteGYFallbackFactory implements FallbackFactory<RemoteGYService> {
    @Override
    public RemoteGYService create(Throwable cause) {
        return dto -> R.fail("广药上传处方失败:"+cause.getMessage());
    }
}
