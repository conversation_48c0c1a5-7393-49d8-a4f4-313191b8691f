package com.puree.hospital.supplier.api.model.yf;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName YFAddOrderResponse
 * <AUTHOR>
 * @Description 添加订单响应
 * @Date 2023/12/28 11:04
 * @Version 1.0
 */
@Data
public class YFAddOrderResponse implements Serializable {

    private String id;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 患者编号
     */
    private String patientId;
    /**
     * 服务价格
     */
    private String servicePrice;
    /**
     * 发行 ID
     */
    private String issuingId;
    /**
     * 处方 ID
     */
    private String cfId;
    /**
     * 第三方 ID
     */
    private String thirdPartyId;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 处方地址
     */
    private String prescriptionUrl;
    /**
     * 诊断 ID
     */
    private String diagnosisNewsId;
    /**
     * 处方名称
     */
    private String prescriptionName;
    /**
     * 订单内容
     */
    private String orderContent;
    /**
     * 处方内容
     */
    private String prescriptionContent;
    /**
     *
     */
    private String lookContent;
    /**
     * 区域 ID
     */
    private String areaId;
    /**
     * 机构 ID
     */
    private String hostpitalId;
    /**
     * 医生名
     */
    private String doctorName;
    /**
     * 原因
     */
    private String reason;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改时间
     */
    private Long updateTime;
    /**
     * 部门编号
     */
    private String departmentId;
    /**
     * 部门名称
     */
    private String departmentName;
    /**
     * 医生 ID
     */
    private String doctorId;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;
    /**
     * 药品编号
     */
    private Integer drugNum;
    /**
     * 医院类型
     */
    private Integer hostpitalType;
    /**
     * 合作伙伴编号
     */
    private String partnerNo;
    /**
     * 用户手机号
     */
    private String userPhone;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 收件人名字
     */
    private String borrowerName;
    /**
     * 收件人电话
     */
    private String borrowerPhone;
    /**
     * 收件人地址编码
     */
    private String borrowerProvince;
    /**
     * 收件人地址
     */
    private String borrowerCity;
    /**
     * 收件人详细地址
     */
    private String borrowerAddress;
    /**
     * 订单价格
     */
    private String orderPrice;
    /**
     * 运费价格
     */
    private String freightPrice;
    /**
     *
     */
    private String expressId;
    /**
     *
     */
    private String expressName;
    /**
     *
     */
    private String expressCode;
    /**
     *
     */
    private String expressNo;
    /**
     *
     */
    private String cancelReason;
    /**
     * 取消时间
     */
    private Long cancelTime;
    /**
     * 检查 ID
     */
    private String examineId;
    /**
     * 发货 ID
     */
    private String fhId;
    /**
     * 制剂编号
     */
    private String preparationId;
    /**
     * 操作员 ID
     */
    private String operatorId;
    /**
     * 更新ID
     */
    private String updateId;
    /**
     * 交付 ID
     */
    private String deliverId;
    /**
     * 基础价格
     */
    private String basicsPrice;
    /**
     * 当前价格
     */
    private Double currentPrice;
    /**
     * 企业价格？不确定
     */
    private String qyPrice;
    /**
     * 机构价格
     */
    private String jgPrice;
    /**
     * 调整价格
     */
    private Integer adjustPrice;
    /**
     * 调整类型
     */
    private Integer adjustType;
    /**
     * 单位
     */
    private String unit;
    /**
     *
     */
    private Integer unitNum;
    /**
     *
     */
    private String frequency;
    /**
     *
     */
    private boolean isHeavy;
    /**
     *
     */
    private boolean isOutside;
    /**
     *
     */
    private boolean isChild;
    /**
     *
     */
    private boolean isGestation;
    /**
     *
     */
    private String noteFlag;
    /**
     *
     */
    private String dtrAdvice;
    /**
     *
     */
    private String insuranceNum;
    /**
     *
     */
    private String receiptMode;
    /**
     *
     */
    private String sourceMode;
    /**
     *
     */
    private String drugType;
    /**
     *
     */
    private String prescriptionContentOfThirdParty;
    /**
     *
     */
    private String businessTypeOfThirdParty;
    /**
     *
     */
    private String archivesPrescriptionId;
    /**
     *
     */
    private String doctorAdvice;
    /**
     *
     */
    private String wechatOrderId;
    /**
     *
     */
    private Integer isYf;
    /**
     *
     */
    private String consultationPrice;
    /**
     *
     */
    private String isSq;
    /**
     *
     */
    private String sqBaseImg;
    /**
     *
     */
    private String transactionId;
    /**
     *
     */
    private String weChatPayOrderNo;
    /**
     *
     */
    private String payerConfigType;
    /**
     *
     */
    private String alipayOutTradeNo;
    /**
     *
     */
    private String isTjf;
    /**
     *
     */
    private String xsXx;
    /**
     *
     */
    private String retailPrice;
}
