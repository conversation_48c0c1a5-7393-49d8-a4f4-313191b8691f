package com.puree.hospital.supplier.api.model.hr;

import lombok.Data;

/**
 * 华润药品
 *
 * <AUTHOR>
 * @date 2023/7/11 17:23
 */
@Data
public class HrDrug {
    private Integer Id;
    /**
     * 药店ID
     */
    private String StoreId;
    /**
     * 药店名称
     */
    private String StoreName;
    /**
     * 医院ID
     */
    private String HospitalId;
    /**
     * 产品ID
     */
    private String ProductId;
    /**
     * 产品名称
     */
    private String MedicineName;
    /**
     * 产品通用名
     */
    private String CommonName;
    /**
     * 查询码
     */
    private String QueryCode;
    /**
     * 规格
     */
    private String Specs;
    /**
     * 厂家
     */
    private String Producer;
    /**
     * 批准文号
     */
    private String CurrentLicenceNo;
    /**
     * 药品类型
     */
    private Integer DrugType;
    /**
     * 供货价
     */
    private Double Price;
    /**
     * 是否易碎
     */
    private Boolean IsFragile;
    /**
     * 是否冷链
     */
    private Boolean IsColdChain;
    /**
     * 单位
     */
    private String Unit;
    /**
     * 包装规格单位
     */
    private String PackageUnit;
    /**
     * 包装规格数量
     */
    private String PackageQuantity;
    /**
     * 剂量单位
     */
    private String DoseUnit;
    /**
     * 剂量
     */
    private String Dose;
    /**
     * 存储条件
     */
    private String StoreCondition;

}
