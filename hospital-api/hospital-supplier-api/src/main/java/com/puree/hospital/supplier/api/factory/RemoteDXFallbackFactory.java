package com.puree.hospital.supplier.api.factory;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteDXService;
import com.puree.hospital.supplier.api.model.dx.PrescriptionDrugInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class RemoteDXFallbackFactory  implements FallbackFactory<RemoteDXService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteDXFallbackFactory.class);

    @Override
    public RemoteDXService create(Throwable cause) {
        log.error("大翔supplier服务调用失败:{}", cause.getMessage());
        return new RemoteDXService(){

            @Override
            public R<Boolean> addPrescription(PrescriptionDrugInfo prescriptionDrugInfo) {
                return R.fail("添加处方失败");
            }

            @Override
            public R<String> getPrescriptionStatus(Long hospitalId,Long enterpriseId,String pspnum) {
                return R.fail("查看处方状态失败");
            }

            @Override
            public R<JSONObject> presRefund(Long hospitalId,Long enterpriseId,String pspnum) {
                return R.fail("处方退方失败");
            }

            @Override
            public R<JSONObject> queryStock(Long hospitalId,Long enterpriseId,String[] drugnumList) {
                return R.fail("处方库存查询失败");
            }

            @Override
            public R<JSONObject> queryLogisticsNo(Long hospitalId,Long enterpriseId,String pspnum) {
                return R.fail("处方物流查询失败");
            }
        };
    }
}
