package com.puree.hospital.supplier.api.factory;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.RemoteDFHService;
import com.puree.hospital.supplier.api.model.dfh.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class RemoteDFHFallbackFactory  implements FallbackFactory<RemoteDFHService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteDFHFallbackFactory.class);

    @Override
    public RemoteDFHService create(Throwable cause) {
        log.error("supplier服务调用失败:{}", cause.getMessage());
        return new RemoteDFHService(){

            @Override
            public HashMap<String, Object> addOrder(DFHOrder dfhOrder) {
                return null;
            }

            @Override
            public HashMap<String, Object> addTradeReturn(DFHOrder dhfOrder) {
                return null;
            }

            @Override
            public R<JSONObject> getNewStock(StockQueryDto stockQueryDto) {
                return R.fail("新库存查询失败");
            }

            @Override
            public R<JSONObject> getOrderInfo(OrderQueryDto orderQueryDto) {
                return R.fail("发货订单查询失败");
            }

            @Override
            public R<JSONObject> getTradeReturns(ReturnGoodsOrderQueryDto returnGoodsOrderQueryDto) {
                return R.fail("退货订单查询失败");
            }

            /**
             * 发货订单查询
             *
             * @param deliverOrderQueryDto
             * @return
             */
            @Override
            public R<JSONObject> getDeliverys(DeliverOrderQueryDto deliverOrderQueryDto) {
                return R.fail("发货订单查询失败");
            }

            @Override
            public R<JSONObject> refundUpdate(DFHOrder dhfOrder) {
                return R.fail("修改订单状态失败");
            }
        };
    }
}
