package com.puree.hospital.supplier.api.model.zx.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.supplier.api.model.zx.Medicines;
import com.puree.hospital.supplier.api.model.zx.ZxBaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 下单入参实体
 */
@Data
public class NewCreateDTO extends ZxBaseModel {
    /**
     * 当前时间
     */
    private String currentTime;
    /**
     * 处方号
     */
    private String hospitalOrderCode;
    /**
     * 协定方名可忽略(药房方名如十全大补汤，冬瓜水)
     */
    private String noteName;
    /**
     * 来源可忽略（门诊、长嘱、临嘱、等等）
     */
    private String noteSrc;
    /**
     * 处方剂数
     */
    private Integer orderDosis;
    /**
     * 几煎（1单煎 2双煎 3浓煎）
     */
    private String friedNum;
    /**
     * 处方明细计数
     */
    private Integer orderItemCnt;
    /**
     * 处方结算价
     */
    private BigDecimal sumPrice;
    /**
     * 医院的病人id
     */
    private String patientId;
    /**
     * 病人年龄
     */
    private String patientAge;
    /**
     * 病人性别（0-女 1-男）
     */
    private String patientGender;
    /**
     * 病人姓名
     */
    private String patientName;
    /**
     * 下单医生姓名
     */
    private String doctorName;
    /**
     * 下单医生联系方式
     */
    private String doctorPhone;
    /**
     * 病人住址,收货地址
     */
    private String receiptAddress;
    /**
     * 收件人（不允许空格和符号）
     */
    private String receiver;
    /**
     * 收货人联系方式
     */
    private String contactPhone;
    /**
     * 备注
     */
    private String orderRemark;
    /**
     * 煎服法（医嘱备注 必须要填写内服/外用/外洗）
     */
    private String usingType;
    /**
     * 0-不代收货款，1-代收货款，默认值0
     */
    private String isCollection;
    /**
     * 代收货款金额
     */
    private String agencyFund;
    /**
     * 1 顺丰  3院内  18京东 2邮政
     */
    private String logisticCompany;
    /**
     * 用法 1-“自煎”/“2-膏方”/”3-代煎“，默认值1
     */
    private String agentFlag;
    /**
     * 膏方类型（1罐 2袋）
     */
    private String agentType;
    /**
     * 是否隐藏 0-隐藏(无用参数)（忽略）
     */
    private String identify;
    /**
     * 订单类型 1-至信系统订单 2-外接订单
     */
    private String orderType;
    /**
     * 处方开具时间"2018-01-05 16:54:00"
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderMakeTime;
    /**
     * 处方支付时间"2018-01-05 16:54:00"
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderPayTime;
    /**
     * 处方发送时间（忽略）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderSendTime;
    /**
     * 医生二维码链接（忽略）
     */
    private String doctorCodeUrl;
    /**
     * 医生处方确认时间（忽略）
     */
    private String doctorconfTime;
    /**
     * 医生处方确认签名链接
     */
    private String doctorconfUrl;
    /**
     * 0 门诊  1住院
     */
    private String deskName="0";
    /**
     * 院区/分院
     */
    private String compound;
    /**
     * 病区
     */
    private String roomNo;
    /**
     * 科室
     */
    private String desk;
    /**
     * 门诊号/住院号
     */
    private String patientNumber;
    /**
     * 病床号
     */
    private String theBed;
    /**
     * 每包剂量（代煎）默认：200ml
     */
    private String packDose="200ml";
    /**
     * 每剂包数（代煎）默认：2
     */
    private String packNum="2";
    /**
     * 用法和医生说明拼接
     */
    private String prescriptionUsage;
    /**
     * 服法：每日一剂，每次一包
     */
    private String medicationInstruction;
    /**
     * 孕妇标识：0 否  1是 默认0
     */
    private String isPregnant;
    /**
     * 收件人地址-省(如:广东省)
     */
    private String province;
    /**
     * 收件人地址-市(如:广州市)
     */
    private String city;
    /**
     * 收件人地址-区(如:天河区)
     */
    private String district;
    /**
     * 收件人地址-街道地址
     */
    private String suffix_addr;
    /**
     * 病人的临床诊断
     */
    private String diagnosis;
    /**
     * 是否加急 0-否 1-是（忽略）
     */
    private String isUrgent;
    /**
     * 订单药品列表
     */
    private List<Medicines> medicines;
}
