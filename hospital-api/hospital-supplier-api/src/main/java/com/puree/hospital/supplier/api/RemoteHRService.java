package com.puree.hospital.supplier.api;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteHRFallbackFactory;
import com.puree.hospital.supplier.api.model.hr.HrDrug;
import com.puree.hospital.supplier.api.model.hr.HrDrugInfoDTO;
import com.puree.hospital.supplier.api.model.hr.HrPrescrOrder;
import com.puree.hospital.supplier.api.model.hr.HrRefundOrder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 华润接口
 */
@FeignClient(contextId = "RemoteHRService", value = ServiceNameConstants.SUPPLIER_SERVICE, fallbackFactory = RemoteHRFallbackFactory.class)
public interface RemoteHRService {

    /**
     * 上传处方
     *
     * @param prescrOrder 处方订单信息
     * @return 上传结果
     */
    @PostMapping("/crc/uploadRecipe")
    R<JSONObject> uploadRecipe(@RequestBody HrPrescrOrder prescrOrder);

    /**
     * 查询药品详情
     *
     * @param drugInfoDTO 入参
     * @return 药品详情
     */
    @GetMapping("/crc/getDrugInfo")
    R<List<HrDrug>> getDrugInfo(@SpringQueryMap HrDrugInfoDTO drugInfoDTO);

    /**
     * 华润退货/退款申请
     * @param refundOrder
     * @return
     */
    @PostMapping("/crc/refund")
    R<String> refund(@RequestBody HrRefundOrder refundOrder);
}
