package com.puree.hospital.supplier.api;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteXXFallbackFactory;
import com.puree.hospital.supplier.api.model.xx.XXCountPrice;
import com.puree.hospital.supplier.api.model.xx.XXOrder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/8/11 9:58
 */
@FeignClient(contextId = "RemoteXXService",
        value = ServiceNameConstants.SUPPLIER_SERVICE,
        fallbackFactory = RemoteXXFallbackFactory.class)
public interface RemoteXXService {

    /**
     * 订单新增
     *
     * @param xiangXueOrder
     * @return
     */
    @PostMapping("/xiangxue/insertOrder")
     R<JSONObject> insertOrder(@RequestBody XXOrder xiangXueOrder);

    /**
     * 药品价格计算
     *
     * @param xiangXueCountPrice
     * @return
     */
    @PostMapping("/xiangxue/countPrice")
    R<JSONObject>  getCountPrice(@RequestBody XXCountPrice xiangXueCountPrice);

    /**
     * 获取药品订单状态
     *
     * @param pspnum
     * @return
     */
    @GetMapping(value = "/xiangxue/getUpdateStatus/{pspnum}")
    R<JSONObject> getUpdateStatus(@PathVariable("pspnum") String pspnum);


    /**
     * 获取药品数据
     *
     * @param
     * @return
     */
    @GetMapping(value = "/xiangxue/drugList")
     R<JSONObject>  listDrug();

    /**
     * 根据订单号获取物流信息
     *
     * @param
     * @return
     */
    @GetMapping(value = "/xiangxue/getOrderlogistics/{pspnum}")
    R<JSONObject> getOrderlogistics(@PathVariable("pspnum") String pspnum);
}
