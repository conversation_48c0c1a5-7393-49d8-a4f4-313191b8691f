package com.puree.hospital.supplier.api.model.hr;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 处方订单
 *
 * <AUTHOR>
 * @date 2023/7/6 10:00
 */
@Data
public class HrPrescrOrder {
    /**
     * 订单号
     */
    private String OrderId;
    /**
     * 订单业务日期
     */
    private String OrderDate;
    /**
     * 药店id
     */
    private String StoreId;
    /**
     * 订单描述
     */
    private String Description;
    /**
     * 是否开票
     */
    private boolean IsNeedInvoice = false;
    /**
     * 取药方式 0-配送到家 1-到店取药 2-配送到院
     */
    private int PickMode = 0;
    /**
     * 结算方式
     * 0-未指定 1-在线支付 2-到店支付 3-平台代收
     */
    private int SettleMode = 3;
    /**
     * 是否已经支付：0-未支付 1-已支付
     */
    private int PayFlag = 1;
    /**
     * 是否已经审方 0-false 1-true
     */
    private int ReviewedFlag;
    /**
     * 审方人
     */
    private String Reviewer;
    /**
     * 审方日期
     */
    private Date ReviewDate;
    /**
     * 订单金额
     */
    private Double Amount;
    /**
     * 期望取药时间
     */
    private Date ExpectPickDate;
    /**
     * 配送地址
     */
    private HrReceiveAddress ReceiveAddress;
    /**
     * 购药人
     */
    private HrBuyer Buyer;
    /**
     * 支付信息
     */
    private List<HrPayment> Payment;
    /**
     * 患者信息
     */
    private List<HrPatients> Patients;
    /**
     * 处方信息
     */
    private List<HrPrescrs> Prescrs;
    /**
     * 订单明细
     */
    private List<HrOrderDetails> Details;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码
     */
    private String password;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
