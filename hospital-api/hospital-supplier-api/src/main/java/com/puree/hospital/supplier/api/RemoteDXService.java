package com.puree.hospital.supplier.api;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteDXFallbackFactory;
import com.puree.hospital.supplier.api.model.dx.PrescriptionDrugInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(contextId = "RemoteDXService", value = ServiceNameConstants.SUPPLIER_SERVICE, fallbackFactory = RemoteDXFallbackFactory.class)
public interface RemoteDXService {


    /**
     * 处方传入
     * @param prescriptionDrugInfo
     * @return
     */
    @PostMapping("/dx/addPrescription")
    R<Boolean> addPrescription(@RequestBody PrescriptionDrugInfo prescriptionDrugInfo);


    /**
     * 查询处方状态
     * @param pspnum
     * @return
     */
    @GetMapping("/dx/getPrescriptionStatus")
    R<String> getPrescriptionStatus(@RequestParam("hospitalId") Long hospitalId,@RequestParam("enterpriseId") Long enterpriseId,@RequestParam("pspnum") String pspnum);

    /**
     * 取消处方单申请
     * @param pspnum
     * @return
     */
    @GetMapping("/dx/presRefund")
    R<JSONObject> presRefund(@RequestParam("hospitalId") Long hospitalId,@RequestParam("enterpriseId") Long enterpriseId,@RequestParam("pspnum") String pspnum);

    /**
     * 查询处方药品库存库存
     * @param drugnumList
     * @param hospitalId
     * @return
     */
    @GetMapping("/dx/queryStock")
    R<JSONObject> queryStock(@RequestParam("hospitalId") Long hospitalId,@RequestParam("enterpriseId") Long enterpriseId,@RequestParam("drugnumList") String [] drugnumList);


    /**
     * 处方查询物流
     * @param pspnum
     * @return
     */
    @GetMapping("/dx/queryLogisticsNo")
    R<JSONObject> queryLogisticsNo(@RequestParam("hospitalId") Long hospitalId,@RequestParam("enterpriseId") Long enterpriseId,@RequestParam("pspnum") String  pspnum);
}
