package com.puree.hospital.supplier.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteYFFallbackFactory;
import com.puree.hospital.supplier.api.model.yf.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ClassName YFCerpController
 * <AUTHOR>
 * @Description 一方对接远程调用
 * @Date 2023/12/29 11:32
 * @Version 1.0
 */
@FeignClient(contextId = "RemoteYFService",
        value = ServiceNameConstants.SUPPLIER_SERVICE,
        fallbackFactory = RemoteYFFallbackFactory.class)
public interface RemoteYFService {

    /**
     * @Param
     * @Return com.puree.hospital.common.core.web.domain.AjaxResult
     * @Description 库存查询
     * <AUTHOR>
     * @Date 2023/12/27 10:48
     **/
    @PostMapping("/yifang/verify-inventory")
    R<YFVerifyInventoryResponse> verifyInventory(@RequestBody YFVerifyInventoryRequest request);

    /**
     * @Param request
     * @Return java.lang.Object
     * @Description 添加订单
     * <AUTHOR>
     * @Date 2023/12/28 18:54
     **/
    @PostMapping("/yifang/add-order")
    R<YFAddOrderResponse> addOrder(@RequestBody YFAddOrderRequest request);

    /**
     * @Param request
     * @Return java.lang.Object
     * @Description 取消订单
     * <AUTHOR>
     * @Date 2023/12/28 18:54
     **/
    @PostMapping("/yifang/cancel-order")
    R<Boolean> cancelOrder(@RequestBody YFCancelOrderRequest request);

    /**
     * @Param request
     * @Return java.lang.Object
     * @Description 获取订单信息
     * <AUTHOR>
     * @Date 2023/12/28 18:54
     **/
    @PostMapping("/yifang/get-order-info")
    R<YFGetOrderInfoResponse> getOrderInfo(@RequestBody YFGetOrderInfoRequest request);

}
