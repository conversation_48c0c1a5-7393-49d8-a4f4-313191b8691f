package com.puree.hospital.supplier.api.model.xx;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2022/08/12/17:28
 * @Description:
 */
@Data
public class XXOrder {
    /**
     * 委托单号
     * 必填
     */
    private String delnum;
    /**
     * 订单类型
     * 默认传1
     */
    private String orderType;
    /**
     * 问诊费
     * 默认传 0
     */
    private Integer consultcost;
    /**
     * 患者名称
     * 必填
     */
    private String name;
    /**
     * 患者年龄
     * 必填
     */
    private Integer age;
    /**
     * 患者性别 1 男   2 女
     * 必填
     */
    private Integer gender;
    /**
     * 患者手机号
     * 必填
     */
    private String phone;
    /**
     * 患者地址
     * 必填
     */
    private String address;
    /**
     * 收件人
     * 必填
     */
    private String shrname;
    /**
     * 收件人手机号
     * 必填
     */
    private String shrtel;
    /**
     * 物流费
     * 默认传 0
     */
    private BigDecimal logisticscost;
    /**
     * 诊断信息
     * 必填
     */
    private String diagresult;
    /**
     * 剂数
     * 必填
     */
    private Integer dose;
    /**
     * 次数
     * 必填
     */
    private Integer takenum;
    /**
     * 医嘱
     * 默认传 1-150字
     */
    private String yizhu;
    /**
     * 默认传 ：0 无操作  1：先煎 2：后下 3：加糖加蜜
     * 默认传 0
     */
    private Integer decmothed;
    /**
     *煎药方式 0自煎  1代煎  2丸剂   4散剂  5膏方   31 素丸  33 水蜜丸
     * 必填
     */
    private String drugmakeff;
    /**
     *服用方法 1：水煎餐前  2：水煎餐后  3：餐前 4： 空
     * 必填
     */
    private Integer takeway;
    /**
     *服用方式（内服，外用，熏洗，清洗，
     * 灌肠， 传汉字）
     * 必填
     */
    private String takemethod;
    /**
     *医生姓名
     * 必填
     */
    private String doctorName;
    /**
     *医生电话
     * 必填
     */
    private String doctorTel;
    /**
     *医生电话
     * 必填
     */
    private String hospitalName;
    /**
     *医馆订单价格
     * 默认0  系统会自动计算
     */
    private BigDecimal ygOrderCost;
    /**
     *密钥 加密方式： Secret（） + deptId（） + time（当前13位时间戳） 拼接后进行md5加密 得到 sign
     * 必填
     */
    private String sign;
    /**
     *订单管控平台管理员分配
     * 必填
     */
    private Integer deptId;
    /**
     *位时间戳
     * 必填
     */
    private String time;
    /**
     *备注
     * 默认 null
     */
    private String remark;
    /**
     *包装量
     * 传值：250,200,150,100,50
     */
    private Integer packagenum ;
    /**
     *膏方类型
     * 默认 null
     * 传值：袋装，玻璃罐装
     */
    private Integer gflx ;
    /**
     *膏方规格
     * 默认 null
     * 传值：200,150,100,50
     */
    private Integer plasterspec ;
    /**
     * 药品列表
     * 默认 null
     * 传值：200,150,100,50
     */
    private List<XXDrug> drugList;
}
