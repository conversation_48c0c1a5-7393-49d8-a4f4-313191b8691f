package com.puree.hospital.supplier.api.model.hr;

import lombok.Data;

import java.util.List;

/**
 * 查询药品目录入参
 *
 * <AUTHOR>
 * @date 2023/7/11 10:03
 */
@Data
public class HrDrugInfoDTO {
    /**
     * 门店id
     */
    private String StoreId;
    /**
     * 医院id
     */
    private String HospitalId;
    /**
     * 产品id
     */
    private List<String> ProductId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码
     */
    private String password;

    /**
     * 医院id（bus_hospital表中的id）,区别于上面的HospitalId
     */
    private Long busHospitalId;
}
