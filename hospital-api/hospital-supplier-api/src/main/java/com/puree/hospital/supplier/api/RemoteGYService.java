package com.puree.hospital.supplier.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.supplier.api.factory.RemoteGYFallbackFactory;
import com.puree.hospital.supplier.api.model.gy.dto.RecipeDTO;
import com.puree.hospital.supplier.api.model.gy.vo.GYResponseVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/8/11 9:58
 */
@FeignClient(contextId = "RemoteGYService",
        value = ServiceNameConstants.SUPPLIER_SERVICE,
        fallbackFactory = RemoteGYFallbackFactory.class)
public interface RemoteGYService {

    /**
     * 上传处方
     *
     * @param dto 处方dto
     * @return R
     */
    @PostMapping("/gy/uploadRecipe")
    R<GYResponseVo> uploadRecipe(@RequestBody RecipeDTO dto);
}
