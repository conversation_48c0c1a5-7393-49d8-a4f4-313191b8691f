package com.puree.hospital.supplier.api.model.hr;

import lombok.Data;

/**
 * 处方药品明细
 *
 * <AUTHOR>
 * @date 2023/7/5 18:04
 */
@Data
public class HrRefundDetails {
    /**
     * 产品id
     */
    private String ProductId;
    /**
     * 名称
     */
    private String MedicineName;
    /**
     * 通用名
     */
    private String CommonName;
    /**
     * 规格
     */
    private String Specs;
    /**
     * 厂家
     */
    private String Producer;
    /**
     * 原销售数量
     */
    private Integer OriQuantity;
    /**
     * 退货/退款数量
     */
    private Integer RefundQuantity;
    /**
     * 产品单价
     */
    private Double Price;
    /**
     * 产品单位
     */
    private String Unit;
    /**
     * 原销售金额
     */
    private Double OriAmount;
    /**
     * 退款金额
     */
    private Double RefundAmount;
    /**
     * 备注
     */
    private String Description;

}
