package com.puree.hospital.supplier.api.model.hr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/10 16:21
 */
@Data
public class HrRefundOrder {
    /**
     * 源订单号/订单ID，必须唯一
     */
    @JsonProperty
    private String OrderId;
    /**
     * 退货/退款申请单号
     */
    @JsonProperty
    private String RefundNo;
    /**
     * 退货/退款申请时间
     */
    @JsonProperty
    private String RefundDate;
    /**
     * 操作人
     */
    @JsonProperty
    private String Operator;
    /**
     * 退货/退款原因
     */
    @JsonProperty
    private String RefundReason;
    /**
     * 是否退货
     */
    @JsonProperty
    private Boolean IsRetunGoods;
    /**
     * 是否退款
     */
    @JsonProperty
    private Boolean IsRefund;
    /**
     * 退款金额
     */
    @JsonProperty
    private Double RefundAmount;
    /**
     * 退款运费
     */
    @JsonProperty
    private Double DeliveryFee;
    /**
     * 备注
     */
    @JsonProperty
    private String Description;
    /**
     * 退款明细
     */
    @JsonProperty
    private List<HrRefundDetails> Details;
    /**
     * 用户名
     */
    private String userName = "ApiUser";
    /**
     * 密码
     */
    private String password = "abc@123";

}
