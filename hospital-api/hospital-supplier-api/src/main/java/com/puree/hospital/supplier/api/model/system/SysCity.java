package com.puree.hospital.supplier.api.model.system;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
@Data
@Accessors(chain = true)
public class SysCity implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Integer code;
    private String name;
    private Integer provinceCode;
    private List<SysArea> children;
}
