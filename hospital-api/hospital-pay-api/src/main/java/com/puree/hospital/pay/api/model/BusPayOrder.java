package com.puree.hospital.pay.api.model;

import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BusPayOrder extends Entity {
    /**
     * 订单金额  - 单位：元 （建议保留两位小数）
     */
    private BigDecimal amount;
    /** 是否删除（0否 1是） */
    private String delFlag;
    /** 医院ID */
    private Long hospitalId;
    /** 商户类型 */
    @Deprecated
    private String mchType;
    /** openid */
    private String openid;
    /** 订单编号 */
    private String orderNo;
    /** 第三方支付订单编号 */
    @Deprecated
    private String orderOtherNo;
    /**
     * 支付流水号
     */
    private String outTradeNo;
    /**
     * 交易成功返回的三方流水号
     */
    private String transactionId;

    /**
     * 订单类型
     * 0-问诊订单 1-药品订单 2-服务包订单 3-购物车提交订单
     */
    private String orderType;
    /** 机构code */
    private String partnersCode;
    /** 支付方式 1微信支付 2通联支付 */
    private String payWay;
    /** 退款时间 */
    private Date refundTime;
    /** 0待支付，1已支付，2已取消，3已退款，4退款中 */
    private String status;
    /** 商户号id */
    private String mchId;
    /**
     * 支付对应的appid
     */
    private String appid;
    /**
     * 支付说明
     */
    private String payRemark;
    /**
     * 回调通知信息
     */
    private String notifyInfo;
    /**
     * 支付成功时间
     */
    private Date paySuccessTime;

    /**
     * 对应的患者id（原来的familyId）
     */
    private Long patientId;

    /**
     * 用户id（原来的patientId）
     */
    private Long userId;

}