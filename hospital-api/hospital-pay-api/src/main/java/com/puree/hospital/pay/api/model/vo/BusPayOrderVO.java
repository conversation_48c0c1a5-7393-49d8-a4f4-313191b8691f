package com.puree.hospital.pay.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单支付记录VO
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/30 13:49
 */
@Data
public class BusPayOrderVO implements Serializable {

    private static final long serialVersionUID = 4478675390956115551L;

    /**
     * id
     */
    private Long id;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 商户类型
     */
    private String mchType;
    /**
     * openid
     */
    private String openid;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 第三方支付订单编号
     */
    private String orderOtherNo;

    /**
     * 支付流水号
     */
    private String outTradeNo;

    /**
     * 交易成功返回的三方流水号
     */
    private String transactionId;

    /**
     * 订单类型
     * 0-问诊订单 1-药品订单 2-服务包订单 3-购物车提交订单
     */
    private String orderType;

    /**
     * 机构code
     */
    private String partnersCode;
    /**
     * 支付方式 1微信支付 2通联支付 3-微信医保支付
     */
    private String payWay;
    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /**
     * 0待支付，1已支付，2已取消，3已退款，4退款中
     */
    private String status;

    /**
     * 商户号id
     */
    private String mchId;

    /**
     * 支付对应的appid
     */
    private String appid;

    /**
     * 支付说明
     */
    private String payRemark;

    /**
     * 回调通知信息
     */
    private String notifyInfo;

    /**
     * 支付成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paySuccessTime;

    /**
     * 用户id，对应原来的patientId
     */
    private Long userId;

    /**
     * 患者id，对应原来的familyId
     */
    private Long patientId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 退款记录列表
     */
    private List<BusOrderRefundRecordVO> orderRefundList;

}
