package com.puree.hospital.pay.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单支付记录查询dto
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/30 15:21
 */
@Data
public class BusPayOrderQueryDTO implements Serializable {

    private static final long serialVersionUID = -7752917711186866418L;

    /**
     * id
     */
    private Long id;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 商户类型
     */
    private String mchType;
    /**
     * openid
     */
    private String openid;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 第三方支付订单编号
     */
    private String orderOtherNo;

    /**
     * 支付流水号
     */
    private String outTradeNo;

    /**
     * 交易成功返回的三方流水号
     */
    private String transactionId;

    /**
     * 订单类型
     * 0-问诊订单 1-药品订单 2-服务包订单 3-购物车提交订单
     */
    private String orderType;

    /**
     * 机构code
     */
    private String partnersCode;
    /**
     * 支付方式 1微信支付 2通联支付 3-微信医保支付
     */
    private String payWay;
    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 0待支付，1已支付，2已取消，3已退款，4退款中
     */
    private String status;

    /**
     * 商户号id
     */
    private String mchId;

    /**
     * 支付对应的appid
     */
    private String appid;

    /**
     * 支付说明
     */
    private String payRemark;

    /**
     * 回调通知信息
     */
    private String notifyInfo;

    /**
     * 支付成功时间
     */
    private String paySuccessTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 开始时间
     */
    private Date paySuccessStartTime;

    /**
     * 结束时间
     */
    private Date paySuccessEndTime;

    /**
     * 结算方式：1-自费，2-医保
     */
    private Integer settlementType;

    /**
     * 患者姓名
     */
    private String familyName;

    /**
     * 就诊人id集合
     */
    private List<Long> patientIdList;

    /**
     * 开始时间
     */
    private Date refundSuccessStartTime;

    /**
     * 结束时间
     */
    private Date refundSuccessEndTime;
}
