package com.puree.hospital.pay.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.pay.api.factory.RemoteRefundRecordFallbackFactory;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 订单退款记录-rpc调用
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/31 9:28
 */
@FeignClient(contextId = "RemoteRefundRecordService", value = ServiceNameConstants.PAY, fallbackFactory = RemoteRefundRecordFallbackFactory.class)
public interface RemoteRefundRecordService {

    /**
     * 保存退款记录
     *
     * @param refundRecordDTO 保存退款记录参数
     * @return 保存结果
     */
    @PostMapping("/feign/refund-record/save")
    R<Boolean> saveRecord(@RequestBody BusOrderRefundRecordDTO refundRecordDTO);

    /**
     * 更新退款记录
     *
     * @param refundRecordDTO 更新退款记录参数
     * @return 更新结果
     */
    @PutMapping("/feign/refund-record/update")
    R<Boolean> updateRecord(@RequestBody BusOrderRefundRecordDTO refundRecordDTO);

    /**
     * 根据查询参数查询退款记录
     *
     * @param query 查询参数
     * @return 退款记录
     */
    @PostMapping("/feign/refund-record/query")
    R<List<BusOrderRefundRecordVO>> query(@RequestBody BusOrderRefundRecordDTO query);

}
