package com.puree.hospital.pay.api.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p>
 * 支付明细VO
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/30 14:22
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class BusPayOrderDetailVO extends BusPayOrderVO {

    private static final long serialVersionUID = -9046304852389051885L;

    /**
     * 就诊人姓名
     */
    private String familyName;

    /**
     * 就诊人性别
     */
    private String familySex;

    /**
     * 就诊人电话
     */
    private String familyPhoneNumber;

    /**
     * 就诊人身份证号
     */
    private String familyIdCard;

    /**
     * 医保：现金支付
     */
    private BigDecimal ownPayAmt;

    /**
     * 医保：医保基金支付
     */
    private BigDecimal fundPay;

    /**
     * 医保：个人账户支出
     */
    private BigDecimal psnAcctPay;

    /**
     * 其他现金支付金额（运费 + 诊查费）
     */
    private BigDecimal otherCashAmount;

    /**
     * 医保类型：11-普通门诊，14-门特门慢
     */
    private String miMedType;
}
