package com.puree.hospital.pay.api.model.dto;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 退款请求入参
 * <AUTHOR>
 */
@SuppressWarnings("SpellCheckingInspection")
@Data
public class BusRefundPayDTO {

    /**
     * 退款金额 必传
     * */
    private Double refundAmount;

    /**
     * 订单金额 必传
     *
     */
    private Double orderAmount;

    /**
     *  订单号 可空 （订单号、平台订单号二选一）
     *
     */
    private String orderNo;

    /**
     *  订单号 可空 （订单号、平台订单号二选一）
     *
     */
    private String outTradeNo;

    /**
     *  三方交易流水号
     */
    private String transactionId;

    /**
     *  商户类型 必传
     *
     */
    @Deprecated
    private String mchType;

    /**
     *  医院ID 必传
     *
     */
    @NotNull(message = "医院ID不能为空")
    private  Long hospitalId;

    /**
     *  支付类型 必传
     */
    @NotEmpty(message = "支付方式不能为空")
    private String payWay;

    /**
     *  oldreqsn和oldtrxid必填其一
     *  建议:商户如果同时拥有oldtrxid和oldreqsn,优先使用oldtrxid
     **/
    private String oldtrxid;

    /**
     * 订单类型 必传 0-问诊订单 1-药品订单 2-服务包订单 3-购物车订单
     */
    private String orderType;

    private Long orderId;

    /**
     * 金额
     */
    private long trxamt;
    /**
     * 退款标识
     *  1代表没有售后信息退款/0代表后台同意退款
     */
    private String automaticRefund;
    /**
     * 商户订单
     */
    private String reqsn;

    private String oldreqsn;
    /**
     * 商户id
     */
    private  String cusid ;

    private String appid;
    private String publicKey;

    /**
     * 接收微信参数
     */
    private JSONObject params;
    /**
     * 微信配置信息
     */
    @Deprecated
    private JSONObject busWechatPayConfig;
    /**
     * 支付配置id
     */
    @NotNull(message = "支付配置不能为空")
    private Long payConfigId;
    /**
     * 售后单ID
     */
    private Long orderAfterSaleId;
    /**
     * 售后id
     */
    private Long afterId;

    /** 应用程序类型
     * @see com.puree.hospital.common.api.enums.ClientTypeEnum
     * */
    private String appType = ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType();
}
