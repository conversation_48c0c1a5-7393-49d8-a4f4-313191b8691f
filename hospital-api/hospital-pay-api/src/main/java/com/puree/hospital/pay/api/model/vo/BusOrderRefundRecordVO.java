package com.puree.hospital.pay.api.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单退款记录vo
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/29 20:50
 */
@Data
public class BusOrderRefundRecordVO implements Serializable {

    private static final long serialVersionUID = 8972271280939433190L;
    /**
     * id
     */
    private Long id;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 退款发起人
     */
    private String promoter;

    /**
     * 支付流水号
     */
    private String outTradeNo;

    /**
     * 退款流水号
     */
    private String outRefundNo;

    /**
     * 退款类型：FULL-全额退款，2.PARTIAL-部分退款
     */
    private String refundType;

    /**
     * 订单支付金额
     */
    private BigDecimal payAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款状态: PENDING_REFUND-待退款, REFUNDING-退款中, REFUND_SUCCESS退款成功, REFUND_FAILED-退款失败
     */
    private String refundStatus;

    /**
     * 退款成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundSuccessTime;

    /**
     * 退款说明
     */
    private String refundRemark;

    /**
     * 退款id：支付渠道方相应的退款id
     */
    private String refundId;

    /**
     * 退款回调快照
     */
    private String notifySnapshot;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
