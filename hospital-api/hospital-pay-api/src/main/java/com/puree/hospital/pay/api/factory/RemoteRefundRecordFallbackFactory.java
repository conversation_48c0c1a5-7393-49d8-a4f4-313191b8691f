package com.puree.hospital.pay.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.pay.api.RemoteRefundRecordService;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;

/**
 * <p>
 * 订单退款记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/31 9:39
 */
public class RemoteRefundRecordFallbackFactory implements FallbackFactory<RemoteRefundRecordService> {

    @Override
    public RemoteRefundRecordService create(Throwable cause) {
        return new RemoteRefundRecordService() {
            @Override
            public R<Boolean> saveRecord(BusOrderRefundRecordDTO refundRecordDTO) {
                return R.fail("远程调用保存订单退款记录失败");
            }

            @Override
            public R<Boolean> updateRecord(BusOrderRefundRecordDTO refundRecordDTO) {
                return R.fail("远程调用更新订单退款记录失败");
            }

            @Override
            public R<List<BusOrderRefundRecordVO>> query(BusOrderRefundRecordDTO query) {
                return R.fail("远程调用查询订单退款记录失败");
            }
        };
    }
}
