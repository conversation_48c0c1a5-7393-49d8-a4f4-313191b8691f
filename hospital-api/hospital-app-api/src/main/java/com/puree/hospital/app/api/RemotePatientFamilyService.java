package com.puree.hospital.app.api;

import com.puree.hospital.app.api.factory.RemotePatientFamilyFallbackFactory;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName RemotePatientFamilyService
 * <AUTHOR>
 * @Description 就诊人远程调用
 * @Date 2024/4/29 15:29
 * @Version 1.0
 */
@FeignClient(contextId = "RemotePatientFamilyService", value = ServiceNameConstants.APP_SERVICE, fallbackFactory = RemotePatientFamilyFallbackFactory.class)
public interface RemotePatientFamilyService {

    /**
     * 查询就诊人信息
     *
     * @param idNumber 身份证号
     * @
     * @return
     */
    @GetMapping("/interrogator/info-idNumber")
    R<BusPatientFamilyVo> queryPatientInfo(@RequestParam("idNumber") String idNumber, @RequestParam("hospitalId") Long hospitalId);

    /**
     * 通过身份证号查询就诊人列表
     *
     * @param idNumber 身份证号
     * @param hospitalId 医院id
     * @return 就诊人列表
     */
    @GetMapping("/interrogator/list-idNumber")
    R<List<BusPatientFamilyVo>> queryPatientList(@RequestParam("idNumber") String idNumber,
                                                 @RequestParam(value = "hospitalId", required = false) Long hospitalId);

    /**
     * 查询就诊人信息
     *
     * @param userId     账户id
     * @param hospitalId 医院id
     * @return
     */
    @GetMapping("/interrogator/info-userid")
    R<Long> queryInfoByUserId(@RequestParam("userId") Long userId, @RequestParam("hospitalId") Long hospitalId);

    /**
     * 根据手机号查询就诊人信息
     *
     * @param cellPhoneNumber 手机号
     * @param hospitalId      医院id
     * @return 就诊人列表
     */
    @GetMapping("/interrogator/list-phoneNumber")
    R<List<BusPatientFamilyVo>> listByPhoneNumber(@RequestParam("cellPhoneNumber") String cellPhoneNumber,
                                                  @RequestParam("hospitalId") Long hospitalId);
}
