package com.puree.hospital.app.api;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.factory.RemoteWxPayFallbackFactory;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(contextId = "RemoteWxPayService", value = ServiceNameConstants.APP_SERVICE, fallbackFactory = RemoteWxPayFallbackFactory.class)
public interface RemoteWxPayService {
    /**
     * 问诊/药品退款
     *
     * @param outTradeNo
     * @param orderType
     * @param refundAmount
     * @param mchType
     * @param automaticRefund
     * @return
     */
    @PostMapping("/wxPay/refund")
    R refundOrder(@RequestParam("outTradeNo") String outTradeNo, @RequestParam("orderType") String orderType,
                  @RequestParam("refundAmount") Double refundAmount, @RequestParam("mchType") String mchType,
                  @RequestParam("automaticRefund") String automaticRefund, @RequestParam("pCode") String pCode,
                  @RequestParam("orderAfterSaleId") Long orderAfterSaleId);

    /**
     * 服务包退款
     *
     * @param orderId
     * @param orderType
     * @param refundAmount
     * @param mchType
     * @return
     */
    @PostMapping("/wxPay/service/pack/refund")
    R<JSONObject> servicePackRefund(@RequestParam("orderId") Long orderId, @RequestParam("orderType") String orderType,
                                    @RequestParam("refundAmount") Double refundAmount, @RequestParam("mchType") String mchType);

    /**
     * 查询订单支付信息
     *
     * @param hospitalId
     * @param mchType
     * @param orderNo
     * @return
     */
    @GetMapping("/wxPay/query")
    R<String> queryOrder(@RequestParam("hospitalId") Long hospitalId, @RequestParam("mchType") String mchType,
                         @RequestParam("orderNo") String orderNo);

    /**
     * 远程调用修改药品订单状态
     *
     * @param orderNo
     * @return
     */
    @GetMapping("/wxPay/feign/update/status")
    R updateOrderStatus(@RequestParam("orderNo") String orderNo);

    /**
     * 推送药品配送企业订单
     *
     * @param orderId 子订单ID
     * @return
     */
    @PostMapping("/wxPay/feign/push/drugOrder")
    R pushDrugOrderToEnterprise(@RequestParam("orderId") Long orderId);

    /**
     * 推送商品配送企业订单
     *
     * @param orderId 子订单ID
     * @return
     */
    @PostMapping("/wxPay/feign/push/shopOrder")
    R pushShopOrderToEnterprise(@RequestParam("orderId") Long orderId);

    /**
     * 医保退款成功流转售后订单状态
     *
     * @param orderAfterSaleId 售后订单ID
     * @param orderNo          总订单编号
     * @return
     */
    @GetMapping("/wxPay/feign/update/orderStatus")
    R updateOrderStatus(@RequestParam("orderAfterSaleId") Long orderAfterSaleId,
                        @RequestParam("orderNo") String orderNo);

    /**
     * 医保退款成功批量流转售后订单状态--未发货的情况下
     *
     * @param orderNo          总订单编号
     * @return
     */
    @GetMapping("/wxPay/feign/update-batch/after-sales-order-status")
    R<Boolean> updateBatchAfterSalesOrderStatus(@RequestParam("orderNo") String orderNo);

    /**
     * 服务包激活
     *
     * @param autoActivate 是否自动激活
     * @param orderNo      订单编号
     * @return 激活结果
     */
    @PostMapping("/wxPay/active-service-pack")
    R<Boolean> payUpdateServicePackOrderStatus(@RequestParam("orderNo") String orderNo, @RequestParam("autoActivate") String autoActivate);

    /**
     * 查询微信支付详情
     *
     * @param hospitalId 医院ID
     * @param mchType    支付类型
     * @param orderNo    订单编号
     * @return 支付详情
     */
    @GetMapping("/wxPay/query-wx-pay-detail")
    R<JSONObject> queryWxPayDetail(@RequestParam("hospitalId") Long hospitalId,
                                   @RequestParam(value = "mchType") String mchType,
                                   @RequestParam(value = "appType") String appType,
                                   @RequestParam("orderNo") String orderNo);
}
