package com.puree.hospital.app.api;

import com.puree.hospital.app.api.factory.RemotePrescriptionFallbackFactory;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.app.api.model.BusPrescription;
import com.puree.hospital.app.api.model.BusTcmSyndromeIdDTO;
import com.puree.hospital.app.api.model.MMDiagnosisVo;
import com.puree.hospital.app.api.model.TCMDiagnosisVo;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 12:07
 */
@FeignClient(contextId = "RemotePrescriptionService", value = ServiceNameConstants.APP_SERVICE, fallbackFactory = RemotePrescriptionFallbackFactory.class)
public interface RemotePrescriptionService {
    /**
     * 生成处方pdf文件
     * @param busPrescription
     * @return
     */
    @PostMapping("/prescription/feign/pdf")
    R<String> generatePdf(@RequestBody BusPrescription busPrescription);

    /**
     * 查询医保的处方详情
     *
     * @param rxNoList 处方号
     * @return 处方详情
     */
    @PostMapping("/prescription/feign/getMiRx")
    R<List<BusPrescription>> getMiRx(@RequestBody List<String> rxNoList);
    /**
     * 查询处方详细信息-单表
     * @param id
     * @return
     */
    @PostMapping("/prescription/feign/getIdCardPrescriptionDesc")
    R<BusPatientFamilyVo> getIdCardPrescriptionDesc(@RequestParam("id") Long id);

    /**
     * 根据西医诊断id列表，获取西医诊断列表
     *
     * @param ids 西医诊断id列表
     * @return
     */
    @PostMapping("/prescription/mm/diagnosis-ids")
    R<List<MMDiagnosisVo>> getMMDiagnosisByIds(@RequestBody List<Long> ids);
    /**
     * 根据中医诊断id列表，获取中医诊断列表
     *
     * @param dtos 中医诊断id列表
     * @return
     */
    @PostMapping("/prescription/tcm/diagnosis-ids")
    R<List<TCMDiagnosisVo>> getTCMDiagnosisByIds(@RequestBody List<BusTcmSyndromeIdDTO> dtos);

    /**
     * 查询医保的处方详情
     *
     * @param rxIdList 处方id列表
     * @return 处方详情
     */
    @PostMapping("/prescription/feign/rx-by-ids")
    R<List<BusPrescription>> getRxByIds(@RequestBody List<Long> rxIdList);
}
