package com.puree.hospital.app.api.factory;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.RemoteWxPayService;
import com.puree.hospital.common.api.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public class RemoteWxPayFallbackFactory implements FallbackFactory<RemoteWxPayService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteWxPayFallbackFactory.class);

    @Override
    public RemoteWxPayService create(Throwable cause) {
        log.error("app服务调用失败:{}", cause.getMessage());
        return new RemoteWxPayService() {

            @Override
            public R<?> refundOrder(String outTradeNo, String orderType, Double refundAmount, String mchType,
                                         String automaticRefund,String pCode, Long orderAfterSaleId) {
                return R.fail("调用微信退款失败");
            }

            @Override
            public R<JSONObject> servicePackRefund(Long orderId, String orderType, Double refundAmount, String mchType) {
                return R.fail("调用微信退款失败");
            }

            @Override
            public R<String> queryOrder(Long hospitalId, String mchType, String orderNo) {
                return R.fail("调用微信查询订单失败");
            }

            @Override
            public R<?> updateOrderStatus(String orderNo) {
                return R.fail("微信医保回调修改订单状态失败");
            }

            @Override
            public R<?> pushDrugOrderToEnterprise(Long orderId) {
                return R.fail("调用app服务推送药品配送企业订单失败");
            }

            @Override
            public R<?> pushShopOrderToEnterprise(Long orderId) {
                return R.fail("调用app服务推送商品配送企业订单失败");
            }

            @Override
            public R<?> updateOrderStatus(Long orderAfterSaleId, String orderNo) {
                return R.fail("流转订单状态失败:" + cause.getMessage());
            }
            @Override
            public R<Boolean> updateBatchAfterSalesOrderStatus(String orderNo) {
                return R.fail("流转售后订单状态失败:" + cause.getMessage());
            }

            @Override
            public R<Boolean> payUpdateServicePackOrderStatus(String orderNo, String autoActivate) {
                return R.fail("激活服务包失败：" + cause.getMessage());
            }

            @Override
            public R<JSONObject> queryWxPayDetail(Long hospitalId, String mchType,String appType, String orderNo) {
                return R.fail("查询微信支付详情失败：" + cause.getMessage());
            }
        };
    }
}
