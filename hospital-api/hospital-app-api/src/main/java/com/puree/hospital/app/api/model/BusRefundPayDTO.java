package com.puree.hospital.app.api.model;

import lombok.Data;

/**
 * 退款请求入参
 */
@Data
public class BusRefundPayDTO {


    /**
     * 退款金额 必传
     * */
    private Double refundAmount;

    /**
     * 订单金额 必传
     *
     * */
    private Double orderAmount;

    /**
     *  订单号 可空 （订单号、平台订单号二选一）
     *
     * */
    private  String orderNo;

    /**
     *  订单号 可空 （订单号、平台订单号二选一）
     *
     * */
    private String orderOtherNo;

    /**
     *  商户类型 必传
     *
     * */
    private String mchType;

    /**
     *  医院ID 必传
     *
     * */
    private  Long hospitalId;

    /**
     *  支付类型 必传
     *
     * */
    private String payWay;
    /**
     *  oldreqsn和oldtrxid必填其一
     *
     * 建议:商户如果同时拥有oldtrxid和oldreqsn,优先使用oldtrxid
     *
     * */
    private String oldtrxid;
    /**
     * 订单类型 必传 0-问诊订单 1-药品订单 2-服务包订单 3-购物车订单
     */
    private String orderType;
    private Long orderId;
    /**
     * 售后单ID
     */
    private Long orderAfterSaleId;
}
