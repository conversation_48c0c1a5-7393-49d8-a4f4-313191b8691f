package com.puree.hospital.app.api.factory;

import com.puree.hospital.app.api.RemotePrescriptionService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.app.api.model.BusPrescription;
import com.puree.hospital.app.api.model.BusTcmSyndromeIdDTO;
import com.puree.hospital.app.api.model.MMDiagnosisVo;
import com.puree.hospital.app.api.model.TCMDiagnosisVo;
import com.puree.hospital.common.api.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 12:07
 */
@Slf4j
@Component
public class RemotePrescriptionFallbackFactory implements FallbackFactory<RemotePrescriptionService> {

    @Override
    public RemotePrescriptionService create(Throwable cause) {
        log.error("app处方服务调用失败:{}", cause.getMessage());
        return new RemotePrescriptionService() {
            /**
             * 生成处方pdf文件
             *
             * @param busPrescription 处方信息
             * @return 生成的pdf文件path
             */
            @Override
            public R<String> generatePdf(BusPrescription busPrescription) {
                return R.fail("生成处方pdf文件失败");
            }

            @Override
            public R<BusPatientFamilyVo> getIdCardPrescriptionDesc(Long id) {
                return R.fail("查询处方详情失败");
            }

            @Override
            public R<List<MMDiagnosisVo>> getMMDiagnosisByIds(List<Long> ids) {
                return R.fail("查询西医诊断列表失败");
            }

            @Override
            public R<List<TCMDiagnosisVo>> getTCMDiagnosisByIds(List<BusTcmSyndromeIdDTO> dtos) {
                return R.fail("查询中医诊断列表失败");
            }

            @Override
            public R<List<BusPrescription>> getRxByIds(List<Long> rxIdList) {
                return R.fail("根据处方id查询处方列表失败");
            }

            @Override
            public R<List<BusPrescription>> getMiRx(@RequestBody List<String> rxNoList) {
                return R.fail("根据处方号查询处方列表失败");
            }
        };
    }
}
