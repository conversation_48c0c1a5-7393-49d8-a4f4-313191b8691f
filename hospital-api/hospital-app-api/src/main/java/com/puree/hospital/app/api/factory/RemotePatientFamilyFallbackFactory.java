package com.puree.hospital.app.api.factory;

import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName RemotePatientFamilyFallbackFactory
 * <AUTHOR>
 * @Description 失败处理器
 * @Date 2024/4/29 14:48
 * @Version 1.0
 */
@Slf4j
@Component
public class RemotePatientFamilyFallbackFactory implements FallbackFactory<RemotePatientFamilyService> {
    @Override
    public RemotePatientFamilyService create(Throwable cause) {
        log.error("app服务调用失败:{}", cause.getMessage());
        return new RemotePatientFamilyService() {

            @Override
            public R<BusPatientFamilyVo> queryPatientInfo(String idNumber, Long hospitalId) {
                return R.fail("远程调用失败");
            }

            @Override
            public R<List<BusPatientFamilyVo>> queryPatientList(String idNumber, Long hospitalId) {
                return R.fail("远程调用失败");
            }

            @Override
            public R<Long> queryInfoByUserId(Long userId, Long hospitalId) {
                return R.fail("远程调用失败");
            }

            @Override
            public R<List<BusPatientFamilyVo>> listByPhoneNumber(String cellPhoneNumber, Long hospitalId) {
                return R.fail("根据手机号查询患者信息失败");
            }
        };
    }
}
