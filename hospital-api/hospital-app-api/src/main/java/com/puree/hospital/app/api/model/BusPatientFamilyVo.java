package com.puree.hospital.app.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 就诊人信息表
 */
@Data
public class BusPatientFamilyVo {

    private Long id;
    /** 姓名 */
    private String name;
    /** 手机号 */
    private String cellPhoneNumber;
    /** 婚姻状况（0 未婚 1 已婚） */
    private String maritalStatus;
    /** 工作单位 */
    private String workUnit;
    /** 住址所在地区 */
    private String location;
    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfBirth;
    /** 性别（0女 1男） */
    private String sex;
    /** 患者关系（字典表获取） */
    private String patientRelationship;
    /** 是否默认 */
    private Integer status;
    /** 患者ID */
    private Long patientId;
    /** 民族 */
    private String nation;
    /** 详细地址 */
    private String detailAddress;
    /** 身份证 */
    private String idNumber;
    /**
     * 职业
     */
    private String profession;
    /**
     * 文化程度
     */
    private String educationLevel;
    private String diagnosisArchives;
    /**
     * 门诊号
     */
    private String outpatientNumber;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 身高
     */
    private Integer height;
    /**
     * 体重
     */
    private Integer weight;

    /**
     * 就诊人年龄 （小于1岁时显示月和天）
     */
    private String familyAge;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 医院ID
     */
    private Long hospitalId;

    private Integer visible ;

}
