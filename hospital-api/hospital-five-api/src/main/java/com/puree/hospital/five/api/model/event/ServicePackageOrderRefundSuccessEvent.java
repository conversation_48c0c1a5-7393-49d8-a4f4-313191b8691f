package com.puree.hospital.five.api.model.event;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 服务包订单退款成功事件
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/6 16:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServicePackageOrderRefundSuccessEvent extends ServicePackageOrderEvent {

    private static final long serialVersionUID = 5757701163220350725L;

    public static final String TOPIC = "queue:service_package_order_refund_success_event_topic";
}
