package com.puree.hospital.five.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.RemoteServicePackImService;
import com.puree.hospital.five.api.model.BusDoctorPatientGroupRequest;
import com.puree.hospital.five.api.model.BusFiveServicePack;
import com.puree.hospital.five.api.model.dto.BusFiveServicePackDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

public class RemoteServicePackImFallbackFactory implements FallbackFactory<RemoteServicePackImService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteAssistantFallbackFactory.class);

    @Override
    public RemoteServicePackImService create(Throwable cause) {
        log.error("服务调用失败:{}", cause.getMessage());
        return new RemoteServicePackImService() {
            @Override
            public R<Long> createGroup(BusDoctorPatientGroupRequest busDoctorPatientGroupRequest) {
                return R.fail("创建群组失败");
            }

            @PostMapping("/service/pack/im/checkGroup")
            @Override
            public R checkGroup(BusDoctorPatientGroupRequest busDoctorPatientGroupRequest) {
                return R.fail("校验群组失败");
            }

            @Override
            public R listMemberInfo(Long groupId) {
                return R.fail("校验群组失败");
            }

            @Override
            public R<Boolean> refund(Long groupId) {
                return R.fail("发送退款消息失败");
            }

            @Override
            public R<Boolean> renewal(Long groupId, String startTime,String endTime) {
                return R.fail("服务包续购失败");
            }

            @Override
            public R<Boolean> expire(Long groupId) {
                return R.fail("服务包设置过期失败");
            }

            @Override
            public R<Boolean> multipleOrderRefund(Long groupId, String startTime, String endTime) {
                return R.fail("多个激活订单退款失败");
            }

            /**
             * 修改im群组激活状态
             *
             * @param groupId
             * @return
             */
            @Override
            public R<Boolean> updateActivation(Long groupId) {
                return R.fail("更新im群组激活状态失败");
            }

            /**
             * 续购添加医生和医助关联群组
             * @param busDoctorPatientGroupRequest
             * @return
             */
            @Override
            public R renewalDoctorAndAssistantGroup(BusDoctorPatientGroupRequest busDoctorPatientGroupRequest) {
                 return R.fail("续购添加医生和医助关联群组失败");
            }

            @Override
            public R<Boolean> againCreateGroup(BusDoctorPatientGroupRequest busDoctorPatientGroup) {
                return R.fail("重新创建群组");
            }

            @Override
            public R<List<BusFiveServicePack>> feignQueryPatientPackList(BusFiveServicePackDTO dto) {
                return R.fail("患者查询服务包失败");
            }
        };
    }
}
