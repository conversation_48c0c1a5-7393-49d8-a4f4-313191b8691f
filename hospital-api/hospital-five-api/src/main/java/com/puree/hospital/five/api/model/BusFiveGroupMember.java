package com.puree.hospital.five.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14 18:01
 */
@Data
public class BusFiveGroupMember {
    /** 工作组ID */
    private Long workGroupId;
    /** 成员ID */
    private Long memberId;
    /** 身份（0成员 1管理员） */
    private String identity;
    /** 入组状态（0待入组 1已入组） */
    private String status;
    /** 入组时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enrollmentTime;
    /** 类型（0医生 1药师 2医助 3护士 4健康管理师 5心理咨询师） */
    private String type;
    /** 医院ID */
    private Long hospitalId;
    private Long departmentId;
}
