package com.puree.hospital.five.api.model.event;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 服务包相关事件
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/4 10:58
 */
@Data
public class ServicePackageOrderEvent implements Serializable {

    private static final long serialVersionUID = -40420967284074791L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 服务包订单id
     */
    private Long orderId;

    /**
     * 事件类型
     */
    private EventType eventType;

    /**
     * 附加参数
     */
    private Map<String, Object> attachments;

    /**
     * 事件时间
     */
    private Date eventTime;

    /**
     * 事件类型
     */
    public enum EventType {

        /**
         * 自动取消
         */
        AUTO_CANCEL,

        /**
         * 退款退款
         */
        REFUND_SUCCESS,

    }

}
