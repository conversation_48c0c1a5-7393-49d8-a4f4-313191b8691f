package com.puree.hospital.five.api.model.event;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 服务包订单自动取消事件
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/6 12:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServicePackageOrderAutoCancelEvent extends ServicePackageOrderEvent {

    private static final long serialVersionUID = -2891349567994455743L;

    /**
     * 自动取消事件
     */
    public static final String TOPIC = "queue:service_package_order_auto_cancel_event_topic";
}
