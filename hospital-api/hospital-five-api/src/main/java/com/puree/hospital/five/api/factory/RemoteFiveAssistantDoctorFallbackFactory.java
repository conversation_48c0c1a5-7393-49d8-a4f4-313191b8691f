package com.puree.hospital.five.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.RemoteFiveAssistantDoctorService;
import com.puree.hospital.five.api.model.BusFiveAssistantDoctor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @ClassName RemoteFiveAssistantDoctorFallbackFactory
 * <AUTHOR>
 * @Description 医生助手
 * @Date 2024/3/13 15:58
 * @Version 1.0
 */
@Slf4j
@Component
public class RemoteFiveAssistantDoctorFallbackFactory implements FallbackFactory<RemoteFiveAssistantDoctorService> {

    @Override
    public RemoteFiveAssistantDoctorService create(Throwable cause) {
        log.error("服务调用失败:{}", cause.getMessage());
        return new RemoteFiveAssistantDoctorService() {
            @Override
            public R<BusFiveAssistantDoctor> getAssistantDoctor(BusFiveAssistantDoctor busFiveAssistantDoctor) {
                return R.fail("远程查询医助信息失败");
            }
        };
    }
}
