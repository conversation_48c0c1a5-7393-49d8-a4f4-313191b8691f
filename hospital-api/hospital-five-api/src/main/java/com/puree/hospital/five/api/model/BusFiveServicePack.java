package com.puree.hospital.five.api.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 服务包表
 */
@Data
public class BusFiveServicePack extends Entity {

    /** 医院ID */
    private Long hospitalId;
    /** 服务包类型 */
    private String servicePackType;
    /** 服务包名称 */
    private String servicePackName;
    /** 服务包配图 */
    private String servicePackImg;
    /** 服务包视频 */
    private String servicePackVideo;
    /** 疾病种类 */
    private String diseaseType;
    /** 适宜人群 */
    private String suitablePopulation;
    /** 详细介绍 */
    private String detailedIntroduction;
    /** 服务工作组ID */
    private Long workingGroupId;
    /** 自动建群（0否 1是） */
    private String autoGroupBuilding;
    /** 状态（0未上架 1上架） */
    private String status;
    /** 销量 */
    private Integer salesVolume;
    /** 上架时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfTime;
    /** 是否删除（0否 1是） */
    private String whetherDelete;
    /** 服务期金额 */
    private String servicePeriodAmount;
    /** 服务包周期 */
    @TableField(exist = false)
    private String serviceCycleLabel;
    /** 服务包价格 */
    @TableField(exist = false)
    private Double servicePackPrice;
    /** 工作组ID */
    @TableField(exist = false)
    private Long workGroupId;
    /** 工作组名 */
    @TableField(exist = false)
    private String groupName;
    /** 组长ID */
    @TableField(exist = false)
    private Long leaderId;
    /**
     * 服务包订单ID
     */
    @TableField(exist = false)
    private List<Long> orderIds;
    /**
     * 工作组状态
     */
    @TableField(exist = false)
    private String workGroupStatus;

}
