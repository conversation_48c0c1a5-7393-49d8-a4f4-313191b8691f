package com.puree.hospital.five.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class BusFiveServicePackVO {

    private Long id;
    private String servicePackType;
    private String servicePackName;
    private String servicePackImg;
    private String servicePackVideo;
    private Double servicePackPrice;
    private String diseaseType;
    private String suitablePopulation;
    private Long serviceCycle;
    private Long salesVolume;
    private String detailedIntroduction;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfTime;
    private String status;
    private Long leaderId;
    private String doctorName;
    private List<String> photos;
    private Long groupId;
    private String groupName;
    private String serviceCycleLabel;
    private Long workingGroupId;
    private String servicePeriodAmount;
    private Integer totalNumber;
    private Long roleId;
    private String workGroupStatus;
    private String partnersName;
    private String whetherDelete;
    private BusFiveWorkGroupVO workGroupVo;
    private List<BusFiveGroupMember> groupMembers;
    private List<BusDoctorVO> groupDoctors;
    private List<BusFiveGroupMemberVO> groupMemberVos;
    private List<BusPatientVo> patientList;

}
