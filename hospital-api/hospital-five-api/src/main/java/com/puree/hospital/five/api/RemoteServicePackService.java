package com.puree.hospital.five.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.factory.RemoteServicePackFallbackFactory;
import com.puree.hospital.five.api.model.BusFiveServicePack;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "RemoteServicePackService", value = ServiceNameConstants.FIVE_SERVICE, fallbackFactory = RemoteServicePackFallbackFactory.class)
public interface RemoteServicePackService {


    /**
     *根据服务包id列表获取服务包列表信息
     *
     * @param servicePackIds
     * @return
     */
    @GetMapping("/service/pack/query-ids")
    R<List<BusFiveServicePackVO>> getServicePacksByIds(@RequestParam("servicePackIds") String servicePackIds);

    @GetMapping("/service/pack/feign/query-info")
    R<BusFiveServicePackVO> feignQueryInfo(@SpringQueryMap BusFiveServicePack busFiveServicePack) ;
    @GetMapping("/service/pack/doctor/query/info")
    R<BusFiveServicePackVO> queryServicePack(@SpringQueryMap BusFiveServicePack busFiveServicePack);
}
