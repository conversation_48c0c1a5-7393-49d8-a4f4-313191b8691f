package com.puree.hospital.five.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.five.api.model.BusFiveServicePackOrder;
import com.puree.hospital.five.api.model.ServicePackOrderRequest;
import com.puree.hospital.five.api.model.ServicePackOrderResponse;
import com.puree.hospital.five.api.model.ServicePackPatientInfoVo;
import com.puree.hospital.five.api.model.dto.ServicePackAndPatientDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RemoteServicePackOrderFallbackFactory implements FallbackFactory<RemoteServicePackOrderService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteServicePackOrderFallbackFactory.class);

    @Override
    public RemoteServicePackOrderService create(Throwable cause) {
        log.error("服务调用失败:{}", cause.getMessage());
        return new RemoteServicePackOrderService() {
            @Override
            public R<ServicePackOrderResponse> queryInfo(Long id, String orderNo) {
                return R.fail("查询服务包订单信息失败");
            }

            @Override
            public R<Integer> remoteModifyOrderStatus(ServicePackOrderRequest request, String source) {
                return R.fail("修改服务包订单信息失败");
            }

            @Override
            public R<Integer> modifySalesVolume(Long id) {
                return R.fail("修改服务包销量失败");
            }

            @Override
            public R<BusFiveServicePackOrder> queryOne(Long patientId, Long hospitalId, Long familyId, Long serviceId) {
                return R.fail("查询服务包订单失败");
            }


            @Override
            public R<List<ServicePackOrderResponse>> repeatServicePack(ServicePackOrderRequest request, String source) {
                return R.fail("查询服务包订单失败");
            }

            @Override
            public R<ServicePackOrderResponse> queryLatestOrder(ServicePackOrderRequest request, String source) {
                return R.fail("查询最新服务包订单失败");
            }

            @Override
            public R<List<ServicePackPatientInfoVo>> getPatientsByPackIds(ServicePackAndPatientDTO dto) {
                return R.fail("查询患者列表失败");
            }

            @Override
            public void paySuccessServicePackOrderHandler(String orderNo) {
                log.error("支付成功后处理失败,订单号:{}",orderNo, cause);
            }


            /**
             * 远程调用，处理服务包退款成功后的服务包服务记录表的更新
             *
             * @param orderNo - 服务包订单号
             */
            @Override
            public void remoteUpdateServicePackPatientRecord(String orderNo) {
                log.error("退款成功后更新服务包服务记录表失败,订单号:{}",orderNo, cause);
            }

        };
    }
}
