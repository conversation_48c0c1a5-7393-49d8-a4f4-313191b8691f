package com.puree.hospital.five.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14 18:01
 */
public class BusPatientVo {
    /**
     * 群组ID
     */
    private Long groupId;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 就诊人ID
     */
    private Long familyId;
    /**
     * 就诊人姓名
     */
    private String name;
    /**
     * 就诊人性别
     */
    private String sex;
    /**
     * 就诊人出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfBirth;

    /**
     * 就诊人年龄
     */
    private String familyAge;
}
