package com.puree.hospital.five.api.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Date;

@Data
public class BusDoctorPatientGroupRequest extends Entity {
    /** 医生id */
    private Long doctorId;
    /** 患者id */
    private Long patientId;
    /** 科室id */
    private Long departmentId;
    /** 会话id */
    private String conversationId;
    /** 医院id */
    private Long hospitalId;
    /** 诊疗人id*/
    private Long familyId;
    /** 群组类型 0医生 1服务包*/
    private String type;
    /** 服务包id(服务包所有)*/
    private Long serviceId;
    /**是否禁用*/
    private Integer disable;
    /** 等级*/
    @TableField(exist = false)
    private Integer grade;
    /** 诊疗人name*/
    @TableField(exist = false)
    private String familyName;
    /** 旧的诊疗人id*/
    @TableField(exist = false)
    private Long oldFamilyId;
    @TableField(exist = false)
    private String departmentName;
    @TableField(exist = false)
    private String familyIds;
    @TableField(exist = false)
    private Date startTime;
    @TableField(exist = false)
    private Date endTime;
    /**
     * 是否自动激活 0否 1是
     */
    private String autoActivate;
    /**
     * 订单id
     */
    private Long orderId;
    /**是否删除*/
    private String delFlag;
}
