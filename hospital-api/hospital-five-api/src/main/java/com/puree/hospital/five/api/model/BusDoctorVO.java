package com.puree.hospital.five.api.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14 18:01
 */
public class BusDoctorVO {
    @TableId(type= IdType.AUTO)
    private Long id;
    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 医生擅长 */
    private String beGoodAt;
    /** 医生编号 */
    private String doctorNumber;
    /** 医生姓名 */
    private String fullName;
    /** 身份证号码 */
    private String idCardNo;
    /** 医生身份证正反面图片 */
    private String idCardNoImg;
    /** 医生介绍 */
    private String introduce;
    /** 手机号码 */
    private String phoneNumber;
    /** 医生照片 */
    private String photo;
    /** 医生执业证 */
    private String practiceCertificate;
    /** 医生二维码 */
    private String qrCode;
    /** 医生资格证 */
    private String qualificationCertificate;
    /** 医生性别 0 男 1 女 */
    private Integer sex;
    /** 状态 0停用 1启用 */
    private Integer status;
    /** 医生职称(字典表关联数据) */
    private Long title;
    /** 医生职称证 */
    private String titleCertificate;

    private  String descriptionDetails;


    /**
     * 入驻时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settledTime;

    /**
     * 职称名称
     */
    private String titleValue;

    /**第一任职医院*/
    private String firstHospital;

    /**医院id*/
    private Long hospitalId;

    private Integer auditStatus;
    /**
     * 是否是中医
     */
    private Integer isTcm;

    private String departmentName;
    private String doctorTitle;
    /**
     * 是否医助（0否 1是）
     */
    private String assistantFlag;
    /**
     * 医生执业方式 0多点 1本院 2特约专家
     */
    private Integer isThisCourt;
    /**
     * 角色 0:医生 1:审方药师 2:护士 4:健康管理师  5:心理咨询师 9康复师 10营养师
     */
    private String role;
}
