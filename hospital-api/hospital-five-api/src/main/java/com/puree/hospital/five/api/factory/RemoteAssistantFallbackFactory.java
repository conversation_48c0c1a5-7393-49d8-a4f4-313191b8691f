package com.puree.hospital.five.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.RemoteAssistantService;
import com.puree.hospital.five.api.model.AssistantRequest;
import com.puree.hospital.five.api.model.AssistantResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

public class RemoteAssistantFallbackFactory implements FallbackFactory<RemoteAssistantService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteAssistantFallbackFactory.class);

    @Override
    public RemoteAssistantService create(Throwable cause) {
        log.error("服务调用失败:{}", cause.getMessage());
        return new RemoteAssistantService() {
            @Override
            public R<AssistantResponse> getAssistantByPhone(String phone, String source) {
              return R.fail("获取医师失败:" + cause.getMessage());
            }

            @Override
            public R<Integer> updateAssistantByPhone(AssistantRequest request, String source) {
                return R.fail("更新医师失败:" + cause.getMessage());
            }
        };
    }
}
