package com.puree.hospital.five.api;

import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.factory.RemoteServicePackOrderFallbackFactory;
import com.puree.hospital.five.api.model.BusFiveWorkGroupResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2022/8/23 10:39
 */
@FeignClient(contextId = "RemoteWorkGroupService", value = ServiceNameConstants.FIVE_SERVICE, fallbackFactory =
        RemoteServicePackOrderFallbackFactory.class)
public interface RemoteWorkGroupService {
    @GetMapping("/work/group/query/check")
    R<Boolean> queryWorkGroupInfo(@RequestParam("hospitalId") Long hospitalId,
                                  @RequestParam("doctorId") Long doctorId,
                                  @RequestParam("departmentId") Long departmentId,
                                  @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * ���ݷ������ȡ��������Ϣ
     * @param servicePackId
     * @return
     */
    @GetMapping("/work/group/getWorkGroupInfo")
    R<BusFiveWorkGroupResponse> getWorkGroupInfoBySerivcePack(@RequestParam("servicePackId") Long servicePackId);
}
