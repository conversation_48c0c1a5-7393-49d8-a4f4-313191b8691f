package com.puree.hospital.five.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.RemoteServicePackService;
import com.puree.hospital.five.api.model.BusFiveServicePack;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;

public class RemoteServicePackFallbackFactory implements FallbackFactory<RemoteServicePackService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteServicePackFallbackFactory.class);

    @Override
    public RemoteServicePackService create(Throwable cause) {
        log.error("服务调用失败:{}", cause.getMessage());
        return new RemoteServicePackService() {
            @Override
            public R<List<BusFiveServicePackVO>> getServicePacksByIds(String servicePackIds) {
                return R.fail("查询服务包列表失败");
            }

            @Override
            public R<BusFiveServicePackVO> feignQueryInfo(BusFiveServicePack busFiveServicePack) {
                return R.fail("feignQueryInfo失败");
            }

            @Override
            public R<BusFiveServicePackVO> queryServicePack(BusFiveServicePack busFiveServicePack) {
                return R.fail("queryServicePack失败");
            }

        };
    }
}
