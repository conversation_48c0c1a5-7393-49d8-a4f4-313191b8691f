package com.puree.hospital.five.api.model;


import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName BusDoctorPatientGroup
 * <AUTHOR>
 * @Description 群组相关
 * @Date 2024/3/13 11:07
 * @Version 1.0
 */
@Data
public class BusDoctorPatientGroup extends Entity {
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 科室id
     */
    private Long departmentId;
    /**
     * 会话id
     */
    private String conversationId;
    /**
     * 医院id
     */
    private Long hospitalId;
    /**
     * 诊疗人id
     */
    private Long familyId;
    /**
     * 群组类型 0医生 1服务包
     */
    private String type;
    /**
     * 服务包id
     */
    private Long serviceId;
    /**
     * 是否修改就诊人 0否 1是
     */
    private String modifyFlag;
    /**
     * 是否禁用
     */
    private Integer disable;
    /**
     * 等级
     */
    private Integer grade;
    /**
     * 诊疗人name
     */
    private String familyName;
    /**
     * 旧的诊疗人id
     */
    private Long oldFamilyId;
    private String departmentName;
    private String familyIds;
    private Date startTime;
    private Date endTime;
    /**
     * 角色
     */
    private String role;
    /**
     * 角色ID
     */
    private Long personnelId;
    /**
     * 预定单id
     **/
    private Long preorderPatientId;
    /**
     * 是否删除
     */
    private String delFlag;
    /**
     * 患者报告健管师绑定表Id
     */
    private Long patientReportId;
    /**
     * 群组是否标记（0否 1是）
     */
    private Integer mark;

}
