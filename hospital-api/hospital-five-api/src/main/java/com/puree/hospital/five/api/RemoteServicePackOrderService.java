package com.puree.hospital.five.api;

import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.factory.RemoteServicePackOrderFallbackFactory;
import com.puree.hospital.five.api.model.BusFiveServicePackOrder;
import com.puree.hospital.five.api.model.ServicePackOrderRequest;
import com.puree.hospital.five.api.model.ServicePackOrderResponse;
import com.puree.hospital.five.api.model.ServicePackPatientInfoVo;
import com.puree.hospital.five.api.model.dto.ServicePackAndPatientDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "RemoteServicePackOrderService", value = ServiceNameConstants.FIVE_SERVICE, fallbackFactory = RemoteServicePackOrderFallbackFactory.class)
public interface RemoteServicePackOrderService {

    @GetMapping("/service/pack/order/info")
    R<ServicePackOrderResponse> queryInfo(@RequestParam(value = "id", required = false) Long id,
                                          @RequestParam(value = "orderNo", required = false) String orderNo);

    @PostMapping("/service/pack/order/modify/info")
    R<Integer> remoteModifyOrderStatus(@RequestBody ServicePackOrderRequest request,
                                      @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/service/pack/modify/volume")
    R<Integer> modifySalesVolume(@RequestParam("id") Long id);

    /**
     * @Param busFiveServicePackOrder
     * @Return com.puree.hospital.common.api.domain.R<com.puree.hospital.five.api.model.BusFiveServicePackOrder>
     * @Description 查询服务包订单
     * <AUTHOR>
     * @Date 2024/3/14 15:56
     **/
    @GetMapping("/service/pack/order/query-one")
    R<BusFiveServicePackOrder> queryOne(@RequestParam(value = "patientId", required = false) Long patientId,
                                        @RequestParam(value = "hospitalId", required = false) Long hospitalId,
                                        @RequestParam(value = "familyId", required = false) Long familyId,
                                        @RequestParam(value = "serviceId", required = false) Long serviceId);

    @PostMapping("/service/pack/order/repeat")
    R<List<ServicePackOrderResponse>> repeatServicePack(@RequestBody ServicePackOrderRequest request,
                                                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/service/pack/order/latest")
    R<ServicePackOrderResponse> queryLatestOrder(@RequestBody ServicePackOrderRequest request,
                                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据服务包id列表，获取购买这些服务包的患者列表
     *
     * @param dto 服务包id列表
     * @return 患者列表
     */
    @PostMapping("/service/pack/order/buyers")
    R<List<ServicePackPatientInfoVo>> getPatientsByPackIds(@RequestBody ServicePackAndPatientDTO dto);

    /**
     * 远程调用，处理服务包支付成功后的业务逻辑
     * @param orderNo 订单号
     */
    @GetMapping("/service/pack/order/pay-update")
    void paySuccessServicePackOrderHandler(@RequestParam(value = "orderNo")String orderNo);

    /**
     * 远程调用，处理服务包退款成功后的服务包服务记录表的更新--加入了im处理
     * @param orderNo 订单号
     */
    @GetMapping("/service/pack/order/refund-update-record")
    void remoteUpdateServicePackPatientRecord(@RequestParam(value = "orderNo") String orderNo);

}
