package com.puree.hospital.five.api;

import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.factory.RemoteAssistantFallbackFactory;
import com.puree.hospital.five.api.model.AssistantRequest;
import com.puree.hospital.five.api.model.AssistantResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(contextId = "RemoteAssistantService", value = ServiceNameConstants.FIVE_SERVICE, fallbackFactory = RemoteAssistantFallbackFactory.class)
public interface RemoteAssistantService {
    /**
     * 根据手机号获取其他医师信息
     *
     * @param phone
     * @param source
     * @return
     */
    @GetMapping("/fivePhysician/getAssistantByPhone")
    R<AssistantResponse> getAssistantByPhone(@RequestParam("phone") String phone, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * 根据手机号获取其他医师信息
     *
     * @param request
     * @param source
     * @return
     */
    @PostMapping("/fivePhysician/updateAssistantByPhone")
    R<Integer> updateAssistantByPhone(@RequestBody AssistantRequest request, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
