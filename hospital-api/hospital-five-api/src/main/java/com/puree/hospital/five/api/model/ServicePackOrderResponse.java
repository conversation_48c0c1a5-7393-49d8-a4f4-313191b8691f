package com.puree.hospital.five.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ServicePackOrderResponse {

    /** ID */
    private Long id;
    /** 订单类型 */
    private String orderType;
    /** 订单编号 */
    private String orderNo;
    /** 订单金额 */
    private String amount;
    /** 0待支付，1已取消，2已支付待激活，3已支付已激活（服务中），4已结束（服务到期），5售后中，6已退款 */
    private String status;
    /** 工作组ID */
    private Long workGroupId;
    /** 工作组名称 */
    private String groupName;
    /** 服务包ID */
    private Long servicePackId;
    /** 服务包名称 */
    private String servicePackName;
    /** 服务包价格 */
    private String servicePackPrice;
    /** 疾病种类 */
    private String diseaseType;
    /** 适宜人群 */
    private String suitablePopulation;
    /** 服务包配图 */
    private String servicePackImg;
    /** 服务周期 */
    private String serviceCycle;
    /** 医院ID */
    private Long hospitalId;
    /** 医生ID */
    private Long doctorId;
    /** 主治医生 */
    private String doctorName;
    /** 患者ID */
    private Long patientId;
    /** 就诊人ID */
    private Long familyId;
    /** 就诊人姓名 */
    private String familyName;
    /** 就诊人性别 */
    private String familySex;
    /** 就诊人年龄 */
    private String familyAge;
    /** 支付方式 0在线支付 */
    private String payWay;
    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    /** 单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;
    /** 退款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;
    /** 到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;
    /** 机构code */
    private String partnersCode;
    /** 激活时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activationTime;
    /** 是否删除（0否 1是） */
    private String delFlag;
    /**
     * 是否自动激活（0否 1是）
     */
    private String autoActivate;

    private Integer invoiceStatus ;

}
