package com.puree.hospital.five.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.RemoteFivePhysicianService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * @ClassName: RemoteFivePhysicianFallbackFactory
 * @Date 2023/1/31 16:23
 * <AUTHOR>
 * @Description: 五师服务
 * @Version 1.0
 */
public class RemoteFivePhysicianFallbackFactory implements FallbackFactory<RemoteFivePhysicianService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteFivePhysicianFallbackFactory.class);
    @Override
    public RemoteFivePhysicianService create(Throwable cause) {
        return new RemoteFivePhysicianService() {
            @Override
            public R getRCAssistantByPhone(String phone,Long hospitalId) {
                return R.fail("远程查询五师角色信息失败");
            }
        };
    }
}
