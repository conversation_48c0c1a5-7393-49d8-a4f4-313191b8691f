package com.puree.hospital.five.api.model;

import lombok.Data;

@Data
public class AssistantResponse {
    private Long id;
    /** 医师姓名 */
    private String fullName;
    /** 手机号码 */
    private String phoneNumber;
    /** 职称 */
    private Long title;
    /** 医师头像 */
    private String photo;
    /** 医师性别 0 女 1男 */
    private Integer sex;
    /** 医师身份证正反面图片 */
    private String idCardNoImg;
    /** 医师身份证号码 */
    private String idCardNo;
    /** 医师执业证 */
    private String practiceCertificate;
    /** 医师职称证 */
    private String titleCertificate;
    /** 医师擅长 */
    private String beGoodAt;
    /** 医师擅长详细 */
    private String descriptionDetails;
    /** 医师介绍 */
    private String introduce;
    /** 状态 0停用 1启用 */
    private String status;
    /** 医院ID */
    private Long hospitalId;
    /**医师编号*/
    private  String physicianNumber;
    /**
     * 0:医生 1:药师 2:护士 3:医助 4:健康管理师  5:心理咨询师,6:患者
     * 医师编号*/
    private  String physicianType;
    /** token*/
    private String token;
    private String hospitalName;
}
