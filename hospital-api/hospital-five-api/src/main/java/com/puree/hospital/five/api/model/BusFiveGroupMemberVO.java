package com.puree.hospital.five.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14 18:01
 */
@Data
public class BusFiveGroupMemberVO {
    private Long memberId;
    private String memberName;
    private String memberPortrait;
    private String memberTitle;
    private String departmentName;
    private Long departmentId;
    private String assistantDepartmentId;
    private Long workGroupId;
    private String beGoodAt;
    private String photo;
    private String type;
    private String identity;
    private String status;
    private Boolean check;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enrollmentTime;
    private Long hospitalId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfTime;
    private String pharmacistCategory;
    private Integer isThisCourt;
    private Long doctorId;
    /**
     * 是否医助
     */
    private String assistantFlag;
    /**
     * 医生介绍
     */
    private String introduce;
    /**
     * 医院名称
     */
    private String hospitalName;
}
