package com.puree.hospital.five.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14 18:01
 */
@Data
public class BusFiveWorkGroupVO {
    /** ID */
    private Long id;
    /** 工作组ID */
    private Long workGroupId;
    /** 工作组名称 */
    private String groupName;
    /** 工作组简介 */
    private String briefIntroduction;
    /**
     * 组长姓名
     */
    private String fullName;
    /**
     * 职称
     */
    private String title;
    /**
     * 组长ID
     */
    private Long leaderId;
    /** 状态 */
    private String status;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 组长头像
     */
    private String photo;

    /**
     * 组长擅长
     */
    private String beGoodAt;

    /**
     * 组长所在部门ID
     */
    private Long departmentId;
    /**
     * 科室名称
     */
    private String departmentName;
    /**
     * 组长信息
     */
    private List<BusFiveGroupMemberVO> leaderVos;
//    private List<BusFiveGroupMemberVO> list;

    /**
     * 组员信息
     */
    private List<BusFiveGroupMemberVO> memberVos;

    /**
     * 服务包数量
     */
    private Long servicePackageSize;

    private List<String> photos;

    private List<BusFiveGroupMember> groupMembers;

    private List<BusFiveServicePackVO> servicePackVOS;

    private Long hospitalId;
    private String flag;
    /**
     * 是否解散工作组
     * 0否
     * 1是
     */
    private Integer dissolution;
}
