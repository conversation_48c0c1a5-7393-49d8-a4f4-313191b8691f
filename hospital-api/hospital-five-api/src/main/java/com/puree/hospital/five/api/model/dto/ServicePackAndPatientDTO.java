package com.puree.hospital.five.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 服务包和患者信息封装
 * <AUTHOR>
 * @date 2024/6/5 15:03
 */
@Data
public class ServicePackAndPatientDTO {
    /**
     * 服务包id列表
     */
    private List<Long> packIds;

    /**
     * 就诊人id列表
     */
    private List<Long> familyIds;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date queryBeginDate;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date queryEndDate;

}
