package com.puree.hospital.five.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.factory.RemoteServicePackImFallbackFactory;
import com.puree.hospital.five.api.model.BusDoctorPatientGroupRequest;
import com.puree.hospital.five.api.model.BusFiveServicePack;
import com.puree.hospital.five.api.model.dto.BusFiveServicePackDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "RemoteServicePackImService", value = ServiceNameConstants.FIVE_SERVICE, fallbackFactory = RemoteServicePackImFallbackFactory.class)
public interface RemoteServicePackImService {

    /**
     * 创建群组
     * @param busDoctorPatientGroupRequest
     * @return
     */
    @PostMapping("/service/pack/im/createGroup")
    R<Long> createGroup(@RequestBody BusDoctorPatientGroupRequest busDoctorPatientGroupRequest);

    /**
     * 校验群组
     * @param busDoctorPatientGroupRequest
     * @return
     */
    @PostMapping("/service/pack/im/checkGroup")
    R checkGroup(@RequestBody BusDoctorPatientGroupRequest busDoctorPatientGroupRequest);

    /**
     * 获取群成员信息
     * @param groupId
     * @return
     */
    @GetMapping("/service/pack/im/listMemberInfo")
    R listMemberInfo(@RequestParam("groupId") Long groupId);

    /**
     * 服务包退款
     * @param groupId
     * @return
     */
    @GetMapping("/service/pack/im/refund")
    R<Boolean> refund(@RequestParam("groupId") Long groupId);

    /**
     * 服务包续购
     * @param groupId
     * @return
     */
    @GetMapping("/service/pack/im/renewal")
    R<Boolean> renewal(@RequestParam("groupId") Long groupId, @RequestParam(required = false,value = "startTime")String startTime, @RequestParam("endTime") String endTime);

    /**
     * 服务包过期
     * @param groupId
     * @return
     */
    @GetMapping("/service/pack/im/expire")
    R<Boolean> expire(@RequestParam("groupId") Long groupId);

    /**
     * 多个激活订单退款
     * @param groupId
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/service/pack/im/multipleOrderRefund")
    R<Boolean> multipleOrderRefund(@RequestParam("groupId") Long groupId,@RequestParam("startTime") String startTime,@RequestParam("endTime") String endTime);

    /**
     * 修改im群组激活状态
     * @param groupId
     * @return
     */
    @GetMapping("/service/pack/im/updateActivation")
    R<Boolean> updateActivation(@RequestParam("groupId") Long groupId);

    /**
     * 续购添加医生和医助关联群组
     * @param busDoctorPatientGroupRequest
     * @return
     */
    @PostMapping("/service/pack/im/renewalAssistantGroup")
    R renewalDoctorAndAssistantGroup(@RequestBody BusDoctorPatientGroupRequest busDoctorPatientGroupRequest);

    /**
     * 重新创建群组
     * @param busDoctorPatientGroup
     * @return
     */
    @PostMapping("/service/pack/im/againCreateGroup")
    R<Boolean> againCreateGroup(@RequestBody BusDoctorPatientGroupRequest busDoctorPatientGroup);

    /**
     * 查询患者端服务包列表
     *
     * @param dto
     * @return
     */
    @GetMapping("/service/pack/feign/patient/list")
    R<List<BusFiveServicePack>> feignQueryPatientPackList(@SpringQueryMap BusFiveServicePackDTO dto);
}
