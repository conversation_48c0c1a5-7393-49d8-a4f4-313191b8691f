package com.puree.hospital.five.api.factory;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.RemoteWorkGroupService;
import com.puree.hospital.five.api.model.BusFiveWorkGroupResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/23 10:41
 */
@Component
public class RemoteWorkGroupFallbackFactory implements FallbackFactory<RemoteWorkGroupService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteWorkGroupFallbackFactory.class);

    @Override
    public RemoteWorkGroupService create(Throwable cause) {
        log.error("服务调用失败:{}", cause.getMessage());
        return new RemoteWorkGroupService() {
            @Override
            public R<Boolean> queryWorkGroupInfo(Long hospitalId, Long doctorId, Long departmentId, String source) {
                return R.fail("查询工作组信息失败");
            }

            @Override
            public R<BusFiveWorkGroupResponse> getWorkGroupInfoBySerivcePack(Long servicePackId) {
                return R.fail("根据服务包id查询工作组基本信息失败");
            }
        };
    }
}
