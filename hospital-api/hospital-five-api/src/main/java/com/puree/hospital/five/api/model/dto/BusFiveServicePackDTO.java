package com.puree.hospital.five.api.model.dto;

import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * 服务包表
 */
@Data
public class BusFiveServicePackDTO extends Entity {

    /** 医院ID */
    private Long hospitalId;
    /** 类型 */
    private String servicePackType;
    /** 服务包名称 */
    private String servicePackName;
    /** 医生ID */
    private Long doctorId;
    /** 医生姓名 */
    private String doctorName;
    /** 状态（0未上架 1上架） */
    private String status;
    /** 医生职称 */
    private Long title;
    /** 科室ID */
    private Long departmentId;
    /** 是否为本院医生 0 否 1 是 */
    private Integer isThisCourt;
    /** 医生状态 0 停用 1 启用 */
    private Integer accountStatus;
    /** 工作组id*/
    private Long workGroupId;
    /**
     * 角色类型
     */
    private String roleType;
}
