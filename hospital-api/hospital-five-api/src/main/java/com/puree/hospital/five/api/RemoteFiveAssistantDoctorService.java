package com.puree.hospital.five.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.factory.RemoteFiveAssistantDoctorFallbackFactory;
import com.puree.hospital.five.api.model.BusFiveAssistantDoctor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @ClassName RemoteFiveAssistantDoctorService
 * <AUTHOR>
 * @Description 医助
 * @Date 2024/3/12 17:49
 * @Version 1.0
 */
@FeignClient(contextId = "RemoteFiveAssistantDoctorService", value = ServiceNameConstants.FIVE_SERVICE, fallbackFactory = RemoteFiveAssistantDoctorFallbackFactory.class)
public interface RemoteFiveAssistantDoctorService {
    /**
     * @Param busFiveAssistantDoctor
     * @Return com.puree.hospital.common.api.domain.R<com.puree.hospital.five.api.model.BusFiveAssistantDoctor>
     * @Description 查询医助
     * <AUTHOR>
     * @Date 2024/3/13 16:01
     **/
    @GetMapping("/assistant-doctor")
    R<BusFiveAssistantDoctor> getAssistantDoctor(@SpringQueryMap BusFiveAssistantDoctor busFiveAssistantDoctor);
}
