package com.puree.hospital.five.api;

import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.five.api.factory.RemoteFivePhysicianFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: RemoteFivePhysicianService
 * @Date 2023/1/31 16:22
 * <AUTHOR>
 * @Description: 五师角色管理
 * @Version 1.0
 */
@FeignClient(contextId = "RemoteFivePhysicianService", value = ServiceNameConstants.FIVE_SERVICE, fallbackFactory = RemoteFivePhysicianFallbackFactory.class)
public interface RemoteFivePhysicianService {
    /**
     * 根据手机号查询五师信息
     * @param phone
     * @return
     */
    @GetMapping("/fivePhysician/getRCAssistantByPhone")
    R getRCAssistantByPhone(@RequestParam("phone") String phone,@RequestParam("hospitalId") Long hospitalId);
}
