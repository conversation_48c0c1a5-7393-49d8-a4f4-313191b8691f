package com.puree.hospital.followup.api.model.medical.upload.dto;

import com.puree.hospital.followup.api.model.medical.constant.UniversalAbnormalEnum;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * @ClassName RegularExam
 * <AUTHOR>
 * @Description 常规检查
 * @Date 2024/3/26 12:18
 * @Version 1.0
 */
@Data
public class RegularExamDTO {

    /**
     * 身高,示例：166.6 单位：cm
     */
    @Min(value = 1,message = "身高数值错误")
    @Max(value = 250,message = "身高数值错误")
    private float tall;
    /**
     * 体重,示例：50.5 单位：KG
     */
    @Min(value = 1,message = "体重数值错误")
    @Max(value = 300,message = "体重数值错误")
    private float weight;
    /**
     * 体重指数,示例：18.1
     */
    @Min(value = 0,message = "BMI数值错误")
    @Max(value = 100,message = "BMI数值错误")
    private float bmi;
    /**
     * 收缩压，单位 ：mmHg,示例：115
     */
    @Min(value = 30,message = "收缩压数值错误")
    @Max(value = 300,message = "收缩压数值错误")
    private float systolic;
    /**
     * 舒张压，单位 ：mmHg,示例：69
     */
    @Min(value = 30,message = "舒张压数值错误")
    @Max(value = 300,message = "舒张压数值错误")
    private float diastolic;
    /**
     * 脉搏,示例：88，每分钟/次
     */
    @Min(value = 1,message = "脉搏数值错误")
    @Max(value = 250,message = "脉搏数值错误")
    private int pulseRate;
    /**
     * 血糖
     */
    private BloodSugarDTO bloodSugarDTO[];
    /**
     * 体温
     */
    private float bodyTemperature;
    /**
     * 总胆固醇
     */
    private float totalCholesterol;
    /**
     * 甘油三脂
     */
    private float triglycerides;
    /**
     * 高密度脂蛋白
     */
    private float hdl;
    /**
     * 低密度脂蛋白
     */
    private float ldl;
    /**
     * 尿酸
     */
    private float uricAcid;
    /**
     * 血氧
     */
    private float bloodOxygen;
    /**
     * 心率
     */
    private int heartRate;
    /**
     * 体重指数异常提示,示例：normal: 正常，high：偏高，low：偏低，other_abnormalities：其他异常
     */
    private UniversalAbnormalEnum bmiAbnormal;
    /**
     * 收缩压异常提示,返回：normal: 正常，high：偏高，low：偏低，other_abnormalities：其他异常
     */
    private UniversalAbnormalEnum systolicAbnormal;
    /**
     * 舒张压异常提示,返回：normal: 正常，high：偏高，low：偏低，other_abnormalities：其他异常
     */
    private UniversalAbnormalEnum diastolicAbnormal;
    /**
     * 结论,示例：正常
     */
    @NotBlank(message = "常规检查 结论 不能为空")
    private String summary;
    /**
     * 血尿酸
     */
    private Integer bloodUricAcid;
    /**
     * 尿酸
     */
    private Double urineGlucose;
    /**
     * 血红蛋白
     */
    private Double hemoglobin;
    /**
     * 血糖类型,1随机，2餐后，3空腹
     */
    private int type;
}
