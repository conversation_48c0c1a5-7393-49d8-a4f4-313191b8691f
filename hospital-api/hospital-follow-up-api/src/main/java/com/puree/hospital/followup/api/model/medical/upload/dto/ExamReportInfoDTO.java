package com.puree.hospital.followup.api.model.medical.upload.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @ClassName physicalExamReportInfo
 * <AUTHOR>
 * @Description 体检报告信息结构
 * @Date 2024/3/26 11:02
 * @Version 1.0
 */
@Data
public class ExamReportInfoDTO {

    /**
     * 机构 ID  我方给出的唯一值，上线前提供
     */
    @NotNull(message = "机构 ID 不能为空")
    private Long orgId;
    /**
     * 业务 ID
     */
    private String bizId;
    /**
     * CheckOrganizationEnum
     */
    private Integer dataSource;
    /**
     * XK 或 EHOME 的数据来源 ID
     */
    private Long dataSourceId;
    /**
     * 检测ID
     */
    @NotBlank(message = "检测 ID 不能为空")
    private String examId;
    /**
     * 设备编号
     */
    private String deviceId;
    /**
     * 检测日期,体检报告出具时间,格式 ：YYYY-MM-DD,示例 ：2023-06-01,或者 yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "检测日期 不能为空")
    private String examDate;
    /**
     * 体检报告 ID
     */
    @NotBlank(message = "体检报告 ID 不能为空")
    private String reportId;
    /**
     * 体检报告版本号，例如首次上传的报告为1，下次如有更新，则必须+1 （跟bizId关联）
     */
    @NotNull(message = "体检报告 版本号 不能为空")
    @Min(value = 1, message = "体检报告 版本号不能小于1")
    private Long reportVer;
    /**
     * 体检报告日期，格 式 ： YYYY-MM-DD,示例 ：2023-06-01
     */
    @NotBlank(message = "体检报告日期 不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "体检报告日期 格式错误")
    private String reportDate;
    /**
     * 原始报告文件OSS上传后返回的ID，如：PDF/DOC 等
     */
    private String reportFile;
    /**
     * 我方的患者ID / 乐尔的患者ID
     */
    private Long patientId;
    /**
     * 乐尔的患者匿名ID
     */
    private Long visitorId;
    /**
     * 体检患者
     */
//    @NotNull(message = "体检患者 不能为空")
//    @Valid
    private ExamPatientInfoDTO patientInfo;
    /**
     * 体检结论，总检建议
     */
    @NotNull(message = "体检结论 不能为空")
    @Valid
    private ExamConclusionDTO conclusions;
    /**
     * 常规检查
     */
//    @Valid
    private RegularExamDTO regularExam;
    /**
     * 数值类-检验综合，例如血液/尿液/大便/痰/视力 对应医院项目名称
     */
    @Valid
    private InspectSummaryDTO[] inspectSummaries;
    /**
     * 检查综合，对应医院项目名称
     */
    @Valid
    private ExamSummaryDTO[] examSummaries;

    /**
     * 医院的appId
     */
    private String appId;
    /**
     * 医院ID 通过 appId查出来后写入
     */
    private Long hospitalId;

    /**
     * 设备编码
     */
    private String deviceNo;
}
