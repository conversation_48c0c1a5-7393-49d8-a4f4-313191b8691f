package com.puree.hospital.five.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.puree.hospital.five.domain.BusCommunicationMessage;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * 会话列表VO
 * <AUTHOR>
 */
@Data
public class ConversationInfoVO {
    /**
     * 群组id
     */
    private Long groupId;
    /**
     * 群组名称
     */
    private String groupName;
    /**
     * 群组最新消息
     */
    private BusCommunicationMessage busCommunicationMessage;
    /**
     * 消息时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date msgTime;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 科室id
     */
    private Long departmentId;
    /**
     * 患者id
     */
    private Long patientId;
    /**
     * 就诊人id
     */
    private Long familyId;
    /**
     * 医院id
     */
    private Long hospitalId;
    /**
     * 医生名称
     */
    private String doctorName;
    /**
     * 科室名称
     */
    private String departmentName;
    /**
     * 就诊人名称
     */
    private String familyName;
    /**
     * 医生职称
     */
    private String title;
    /**
     * 患者性别
     */
    private String sex;
    /**
     * 患者出生日期
     */
    private LocalDate dateOfBirth;

    /**
     * 服务包群组开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date groupStartTime;

    /**
     * 服务包群组结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date groupEndTime;

    /**
     * 群组状态
     */
    private String groupStatus;

    /**
     * 医生照片
     */
    private String photo;

    /**
     * 服务包id
     */
    private Long serviceId;

    /**群组类型*/
    private String type;

    /**是否vip*/
    private String grade;

    /**
     * 过往病例
     */
    private String diagnosisArchives;

    /**
     * 是否禁用
     */
    private Integer disable;

    /**
     * 医助照片
     */
    private String assistantPhoto;


    /**
     * 医助名称
     */
    private String assistantName;

    /**
     * 医助id
     */
    private Long assistantId;
    /**
     * 年龄
     */
    private String familyAge;
    /**医生执业方式 0多点 1本院 2特约专家*/
    private Integer isThisCourt;
    /**特邀默认开方医生*/
    private Long defaultDoctorId;
    /**是否删除*/
    private String delFlag;
    /**
     * 身份证号
     */
    private String idCardNo;
    /**
     * 群组是否标记（0否 1是）
     */
    private String mark;
    /**
     * 医生患者表ID
     */
    private Long doctorPatientId;

    /**
     * 字典编码
     */
    @JsonIgnore
    private Long dictCode;

    /**
     * 群组最新消息发送时间
     */
    private String lastMessageTime;
}
