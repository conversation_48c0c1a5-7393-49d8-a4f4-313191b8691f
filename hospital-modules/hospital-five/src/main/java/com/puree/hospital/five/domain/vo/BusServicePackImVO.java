package com.puree.hospital.five.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 服务包im信息VO
 * <AUTHOR>
 */
@Data
public class BusServicePackImVO {

    /** 医院id */
    private Long hospitalId;
    /** 服务包id(服务包所有)*/
    private Long serviceId;
    /** 服务包状态*/
    private String serviceStatus;
    /** doctorId*/
    private Long doctorId;
    /**订单id **/
    private Long orderId;
    /** 订单no */
    private String orderNo;
    /** 订单金额**/
    private String amount;
    /** 订单状态**/
    private String status;
    /** 订单支付时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    /** 订单售后数据 1代表是*/
    private Integer exitAfterSale;
    /** 服务包类型*/
    private String servicePackType;
    /** 服务包名称*/
    private String servicePackName;
    /**服务包价格*/
    private Double servicePackPrice;
    /**服务包周期*/
    private String serviceCycleLabel;
    /** 疾病种类*/
    private String diseaseType;
    /**适合宜人群*/
    private String suitablePopulation;
    /**服务包图片*/
    private String servicePackImg;

    /**群组信息*/
    private ConversationInfoVO conversationInfoVO;
    /**
     * 身份证号
     */
    private String idCardNo;
}
