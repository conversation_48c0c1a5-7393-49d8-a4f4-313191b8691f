package com.puree.hospital.five.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.RemoteCommunicationMessageService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.business.api.RemoteBizDepartmentService;
import com.puree.hospital.business.api.model.BusBizDepartment;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderPayTypeEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.CycleEnum;
import com.puree.hospital.common.core.enums.ImGroupType;
import com.puree.hospital.common.core.enums.ImMemberEnum;
import com.puree.hospital.common.core.enums.ServicePackImStatusEnum;
import com.puree.hospital.common.core.enums.ServicePackOrderStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SpringUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.utils.file.FileUtils;
import com.puree.hospital.common.oss.OSSSaveDirectory;
import com.puree.hospital.five.domain.BusAfterSale;
import com.puree.hospital.five.domain.BusCommunicationMessage;
import com.puree.hospital.five.domain.BusConsultationOrder;
import com.puree.hospital.five.domain.BusConsultationPackage;
import com.puree.hospital.five.domain.BusDoctorConsultation;
import com.puree.hospital.five.domain.BusDoctorPatientGroup;
import com.puree.hospital.five.domain.BusFiveAssistantDoctor;
import com.puree.hospital.five.domain.BusFiveServicePack;
import com.puree.hospital.five.domain.BusFiveServicePackOrder;
import com.puree.hospital.five.domain.BusFiveWorkGroup;
import com.puree.hospital.five.domain.BusHospital;
import com.puree.hospital.five.domain.BusImGroupMember;
import com.puree.hospital.five.domain.BusPatientFamily;
import com.puree.hospital.five.domain.ServicePackPatientRecord;
import com.puree.hospital.five.domain.dto.BusFiveGroupMemberDTO;
import com.puree.hospital.five.domain.enums.ServicePackRecordStatusEnum;
import com.puree.hospital.five.domain.enums.ServicePackSourceEnum;
import com.puree.hospital.five.domain.query.ImGroupQuery;
import com.puree.hospital.five.domain.query.ServicePackPatientRecordQuery;
import com.puree.hospital.five.domain.vo.BusDoctorConsultationVO;
import com.puree.hospital.five.domain.vo.BusDoctorVO;
import com.puree.hospital.five.domain.vo.BusFiveGroupMemberVO;
import com.puree.hospital.five.domain.vo.BusFiveServicePackVO;
import com.puree.hospital.five.domain.vo.BusFiveWorkGroupVO;
import com.puree.hospital.five.domain.vo.BusImGroupMemberVO;
import com.puree.hospital.five.domain.vo.BusServicePackImVO;
import com.puree.hospital.five.domain.vo.ConversationInfoVO;
import com.puree.hospital.five.domain.vo.PatientImVO;
import com.puree.hospital.five.domain.vo.PrivateChatImData;
import com.puree.hospital.five.mapper.BusAfterSaleMapper;
import com.puree.hospital.five.mapper.BusCommunicationMessageMapper;
import com.puree.hospital.five.mapper.BusConsultationOrderMapper;
import com.puree.hospital.five.mapper.BusConsultationPackageMapper;
import com.puree.hospital.five.mapper.BusDoctorConsultationMapper;
import com.puree.hospital.five.mapper.BusDoctorMapper;
import com.puree.hospital.five.mapper.BusDoctorPatientGroupMapper;
import com.puree.hospital.five.mapper.BusFiveAssistantDoctorMapper;
import com.puree.hospital.five.mapper.BusFiveServicePackMapper;
import com.puree.hospital.five.mapper.BusFiveServicePackOrderMapper;
import com.puree.hospital.five.mapper.BusHospitalMapper;
import com.puree.hospital.five.mapper.BusImGroupMemberMapper;
import com.puree.hospital.five.mapper.BusPatientFamilyMapper;
import com.puree.hospital.five.mapper.ServicePackPatientRecordMapper;
import com.puree.hospital.five.queue.producer.ServicePackOrderSyncLogisticsProducer;
import com.puree.hospital.five.queue.producer.event.ImSendMessageEventProducer;
import com.puree.hospital.five.service.IBusFiveServicePackOrderService;
import com.puree.hospital.five.service.IBusFiveServicePackService;
import com.puree.hospital.five.service.IBusFiveWorkGroupService;
import com.puree.hospital.five.service.IBusImGroupMemberService;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.AppDefinedData;
import com.puree.hospital.im.api.model.AppMemberDefinedData;
import com.puree.hospital.im.api.model.FollowUpImWxDTO;
import com.puree.hospital.im.api.model.ImGroupRequest;
import com.puree.hospital.im.api.model.Member;
import com.puree.hospital.im.api.model.MsgBody;
import com.puree.hospital.im.api.model.MsgContent;
import com.puree.hospital.im.api.model.SendMessageRequest;
import com.puree.hospital.im.api.model.event.ImSendMessageEvent;
import com.puree.hospital.order.api.model.event.ServicePackOrderSyncLogisticsEvent;
import com.puree.hospital.system.api.RemoteSysDictDataService;
import com.puree.hospital.system.api.model.SysDictData;
import com.puree.hospital.system.api.model.constant.SysDictTypeConstant;
import com.puree.hospital.tool.api.RemoteOssService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 群组成员服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusImGroupMemberServiceImpl implements IBusImGroupMemberService {

    private final BusImGroupMemberMapper busImGroupMemberMapper;
    private final BusDoctorPatientGroupMapper busDoctorPatientGroupMapper;
    private final RemoteImService remoteImService;
    private final BusDoctorMapper busDoctorMapper;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final BusCommunicationMessageMapper busCommunicationMessageMapper;
    private final IBusFiveServicePackService busFiveServicePackService;
    private final BusFiveServicePackOrderMapper busFiveServicePackOrderMapper;
    private final BusAfterSaleMapper busAfterSaleMapper;
    private final BusFiveServicePackMapper busFiveServicePackMapper;
    private final RemoteOssService remoteOssService;
    private final RemoteCommunicationMessageService remoteCommunicationMessageService;

    @Lazy
    @Resource
    private IBusFiveServicePackOrderService busFiveServicePackOrderService;

    @Autowired
    private ServicePackPatientRecordMapper servicePackPatientRecordMapper;

    @Autowired
    @Lazy
    private IBusFiveWorkGroupService busFiveWorkGroupService;

    @Autowired
    @Lazy
    private BusFiveAssistantDoctorMapper fiveAssistantDoctorMapper;

    @Resource
    private BusDoctorConsultationMapper busDoctorConsultationMapper;

    @Lazy
    @Resource
    private ImSendMessageEventProducer imSendMessageEventProducer;

    @Lazy
    @Resource
    private ServicePackOrderSyncLogisticsProducer servicePackOrderSyncLogisticsProducer;

    @Autowired
    private BusHospitalMapper hospitalMapper;
    @Lazy
    @Resource
    private ImGroupService imGroupService;

    @Resource
    private BusConsultationOrderMapper busConsultationOrderMapper;

    @Resource
    private BusConsultationPackageMapper busConsultationPackageMapper;

    @Resource
    private RemoteSysDictDataService remoteSysDictDataService;

    @Autowired
    private RemoteBizDepartmentService remoteBizDepartmentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createGroup(BusDoctorPatientGroup busDoctorPatientGroup) {
        if (Objects.nonNull(busDoctorPatientGroup.getId())) {
            busDoctorPatientGroup.setId(null);
        }
        BusPatientFamily interrogator = busPatientFamilyMapper.selectById(busDoctorPatientGroup.getFamilyId());
        BusFiveServicePackVO fiveServicePackVo = busFiveServicePackService.queryPatientPackInfo(busDoctorPatientGroup.getServiceId());
        if (Objects.isNull(fiveServicePackVo)) {
            throw new ServiceException("服务包信息缺失");
        }
        busDoctorPatientGroup.setCreateTime(DateUtils.getNowDate());
        // 判断是否自动激活
        if (CodeEnum.NO.getCode().equals(busDoctorPatientGroup.getAutoActivate())) {
            busDoctorPatientGroup.setGroupStatus(ServicePackImStatusEnum.TO_BE_ACTIVATED.getCode());
        } else {
            BusFiveServicePackOrder packOrder = busFiveServicePackOrderMapper.selectOne(new LambdaQueryWrapper<BusFiveServicePackOrder>()
                    .eq(BusFiveServicePackOrder::getId, busDoctorPatientGroup.getOrderId()));
            busDoctorPatientGroup.setGroupStatus(ServicePackImStatusEnum.ACTIVATED.getCode());
            Date startTime = DateUtils.getNowDate();
            busDoctorPatientGroup.setGroupStartTime(startTime);
            String serviceCycle = packOrder.getServiceCycle();
            CycleEnum cycleEnum = CycleEnum.getTypeByInfo(serviceCycle);
            String endTime;
            if (Objects.nonNull(cycleEnum)) {
                endTime = DateUtils.calculateInterval(startTime, cycleEnum);
            } else {
                Integer num = Integer.valueOf(serviceCycle.substring(0, serviceCycle.indexOf("天")));
                endTime = DateUtils.calculateIntervalDay(startTime, num);
            }
            busDoctorPatientGroup.setGroupEndTime(DateUtils.parseDate(endTime));
        }
        busDoctorPatientGroupMapper.insert(busDoctorPatientGroup);
        //批量插入
        this.batchInsert(fiveServicePackVo, busDoctorPatientGroup.getId(), interrogator.getId());
        ImGroupRequest imGroupRequest = new ImGroupRequest();
        // 填充im群组信息
        imGroupRequest.setType(TencentyunImConstants.IM_GROUP_TYPE);
        imGroupRequest.setGroupId(String.valueOf(busDoctorPatientGroup.getId()));
        if (StringUtils.lengthOfUTF8(fiveServicePackVo.getServicePackName()) > 30) {
            String s = StringUtils.substringString(fiveServicePackVo.getServicePackName(), 27);
            imGroupRequest.setName(s + "...");
        } else {
            imGroupRequest.setName(fiveServicePackVo.getServicePackName());
        }
        //添加自定义字段信息
        List<AppDefinedData> appDefinedDataList = new ArrayList<>();
        //医院id
        AppDefinedData hospitalId = new AppDefinedData();
        hospitalId.setKey("hospital_id");
        hospitalId.setValue(busDoctorPatientGroup.getHospitalId() + "");
        appDefinedDataList.add(hospitalId);
        //群组类型
        AppDefinedData title = new AppDefinedData();
        title.setKey("title");
        title.setValue(ImGroupType.SERVICE_PACK.getCode());
        appDefinedDataList.add(title);
        imGroupRequest.setAppDefinedData(appDefinedDataList);
        //是否删除
        AppDefinedData isBinding = new AppDefinedData();
        isBinding.setKey("is_binding");
        isBinding.setValue(CodeEnum.NO.getCode());
        appDefinedDataList.add(isBinding);
        imGroupRequest.setAppDefinedData(appDefinedDataList);

        // 填充群组人员信息
        List<Member> memberList = new ArrayList<>();
        // 创建群主(医生)信息
        imGroupRequest.setOwner_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctorPatientGroup.getDoctorId());
        // 创建群成员包括群主
        this.setMember(memberList, imGroupRequest, fiveServicePackVo, interrogator);
        R<JSONObject> group = remoteImService.createGroup(imGroupRequest);
        if (Constants.FAIL.equals(group.getCode())) {
            JSONObject jsonObject = group.getData();
            Integer errorCode = jsonObject.getInteger("ErrorCode");
            if (errorCode == 10037) {
                throw new ServiceException("医生创建和加入的群组数量超过了限制，无法创建，请联系医院管理员！");
            }
            if (errorCode == 10058) {
                throw new ServiceException("腾讯IM版本群组创建上限，无法创建，请联系医院管理员！");
            }
            if (errorCode == 10036) {
                throw new ServiceException("创建的音视频聊天室数量超过限制，无法创建，请联系医院管理员！");
            }
        } else {
            //发送消息
            this.sendMsg(busDoctorPatientGroup.getId(), busDoctorPatientGroup.getAutoActivate(), memberList);
        }
        return busDoctorPatientGroup.getId();
    }

    private void sendMsg(Long groupId, String autoActivate, List<Member> memberList) {
        //发送系统消息群组人员进入群组
        memberList.forEach(m -> {
            String value = m.getAppMemberDefinedData().get(0).getValue();
            JSONObject valueJson = JSONObject.parseObject(value);
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.SERVIEC_PACK_PEOPLE_JOIN);
            data.put("personnel_id", m.getMember_Account());
            data.put("msg", valueJson.getString("full_name") + valueJson.getString("title") + " 已加入群组");
            this.sendGeneralMsg(groupId, TencentyunImConstants.TIM_CUSTOM_ELEM, data);
        });
        //自动激活发送信息
        if (CodeEnum.YES.getCode().equals(autoActivate)) {
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.SERVIEC_PACK_ACTIVATE);
            data.put("msg", "服务包已激活");
            this.sendGeneralMsg(groupId, TencentyunImConstants.TIM_CUSTOM_ELEM, data);
        }
    }

    @Override
    public BusDoctorPatientGroup queryGroup(BusDoctorPatientGroup busDoctorPatientGroup) {
        LambdaQueryWrapper<BusDoctorPatientGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusDoctorPatientGroup::getDoctorId, busDoctorPatientGroup.getDoctorId());
        queryWrapper.eq(BusDoctorPatientGroup::getPatientId, busDoctorPatientGroup.getPatientId());
        queryWrapper.eq(BusDoctorPatientGroup::getFamilyId, busDoctorPatientGroup.getFamilyId());
        queryWrapper.eq(Objects.nonNull(busDoctorPatientGroup.getHospitalId()), BusDoctorPatientGroup::getHospitalId, busDoctorPatientGroup.getHospitalId());
        queryWrapper.eq(BusDoctorPatientGroup::getType, ImGroupType.SERVICE_PACK.getCode());
        queryWrapper.eq(BusDoctorPatientGroup::getServiceId, busDoctorPatientGroup.getServiceId());
        queryWrapper.last("limit 1");
        return busDoctorPatientGroupMapper.selectOne(queryWrapper);
    }

    @Override
    public Long checkImGroup(BusDoctorPatientGroup busDoctorPatientGroup) {
        BusDoctorPatientGroup group = queryGroup(busDoctorPatientGroup);
        if (Objects.isNull(group)) {
            return 0L;
        }
        R<Boolean> objectR = remoteImService.checkImGroup(group.getId() + "");
        if (Constants.SUCCESS.equals(objectR.getCode()) && !(boolean) objectR.getData()) {
            return 0L;
        }
        return group.getId();
    }

    @Override
    public int update(BusImGroupMember busImGroupMember) {
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int activationServicePackage(BusDoctorPatientGroup busDoctorPatientGroup) {
        BusDoctorPatientGroup group = busDoctorPatientGroupMapper.selectById(Long.valueOf(busDoctorPatientGroup.getGroupId()));
        log.debug("查询群组信息 group={}", group);
        if (!Objects.isNull(group) && CodeEnum.YES.getCode().equals(group.getDelFlag())) {
            this.activateGroup(group);
        }
        //查询订单信息--未激活订单是个list
        List<BusFiveServicePackOrder> packOrders = busFiveServicePackOrderMapper.selectToBeActivatedList(busDoctorPatientGroup);
        if (log.isDebugEnabled()) {
            log.debug("查询服务包订单信息:data={}", JSON.toJSONString(packOrders));
        }
        if (packOrders == null || packOrders.isEmpty()) {
            throw new ServiceException("没有激活订单，无法激活");
        }
        BusFiveServicePackOrder packOrder = packOrders.get(0);
        BusFiveWorkGroup workGroup = busFiveWorkGroupService.getWorkGroupInfoBySerivcePack(busDoctorPatientGroup.getServiceId());
        log.debug("查询服务包工作组信息:data={}", workGroup);
        if (workGroup == null) {
            throw new ServiceException("查询工作组信息失败");
        }
        //更新订单信息
        BusFiveServicePackOrder busFiveServicePackOrder = new BusFiveServicePackOrder();
        busFiveServicePackOrder.setStatus(ServicePackOrderStatusEnum.ACTIVATED.getCode());
        busFiveServicePackOrder.setId(packOrder.getId());
        // 计算服务包到期时间
        Date startTime = DateUtils.getNowDate();
        busFiveServicePackOrder.setActivationTime(startTime);
        //舍弃对过期时间的维护
        busFiveServicePackOrderMapper.updateById(busFiveServicePackOrder);

        //更新服务包记录-先获取-获取不到走创建流程
        ServicePackPatientRecord servicePackPatientRecord = servicePackPatientRecordMapper.selectByServicePackOrderId(busFiveServicePackOrder.getId());
        BusFiveServicePackOrder busFiveServicePackOrderNew = busFiveServicePackOrderService.queryInfo(busFiveServicePackOrder.getId(), null);
        if (servicePackPatientRecord == null) {
            servicePackPatientRecord = busFiveServicePackOrderService.addServicePackPatientRecord(busFiveServicePackOrderNew, busFiveServicePackOrder::getId,ServicePackSourceEnum.GROUP);
            busFiveServicePackOrderService.updateServicePackPatientRecordService(servicePackPatientRecord);
        } else {
            busFiveServicePackOrderService.updateServicePackPatientRecordService(servicePackPatientRecord);
            //更新对应的服务包服务统计表
            busFiveServicePackOrderService.checkServicePackStatistics(servicePackPatientRecord);
        }
        //发送消息--Im相关-激活就是需要切换为服务中
        checkActivateServicePackIm(busDoctorPatientGroup.getGroupId(), workGroup.getDepartmentId(), busFiveServicePackOrderNew.getId());
        try {
            // 服务包订单同步物流信息
            ServicePackOrderSyncLogisticsEvent syncLogisticsEvent = new ServicePackOrderSyncLogisticsEvent();
            syncLogisticsEvent.setOrderNo(packOrder.getOrderNo());
            servicePackOrderSyncLogisticsProducer.send(syncLogisticsEvent);
        } catch (Exception e) {
            log.error("服务包订单:{}同步物流事件发送失败", packOrder.getOrderNo(), e);
        }
        return 1;
    }

    /**
     * 检查是否需要激活服务包并修改人员
     *
     * @param groupId      群组id
     * @param departmentId 科室id
     * @param orderId      服务包订单id
     */
    @Override
    public void checkActivateServicePackIm(String groupId, Long departmentId, Long orderId) {
        Date startTime = DateUtils.getNowDate();
        ServicePackPatientRecord servicePackPatientRecord;
        BusDoctorPatientGroup patientGroup = busDoctorPatientGroupMapper.selectById(Long.valueOf(groupId));
        if (Objects.isNull(patientGroup)) {
            throw new ServiceException("查询群组为空");
        }
        //判断是否是第一次激活
        Date groupEndTime = patientGroup.getGroupEndTime();
        BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
        updateGroup.setId(patientGroup.getId());
        updateGroup.setDepartmentId(departmentId);
        updateGroup.setUpdateTime(DateUtils.getNowDate());
        boolean isNull = Objects.isNull(groupEndTime);
        if (isNull) {
            updateGroup.setGroupStatus(ServicePackImStatusEnum.ACTIVATED.getCode());
            updateGroup.setGroupStartTime(startTime);
        } else {
            // 需要检查需要不需要更换起始时间----过期的时候将起始时间滞空
            if (patientGroup.getStartTime() == null) {
                updateGroup.setGroupStartTime(startTime);
            }
            updateGroup.setGroupStatus(ServicePackImStatusEnum.ACTIVATED.getCode());
        }
        servicePackPatientRecord = servicePackPatientRecordMapper.selectByServicePackOrderId(orderId);
        Long end = calculateExpireTime(servicePackPatientRecord);
        Date endDate = new Date(end);
        log.debug("重新计算对应的结束时间为{},订单id为{}", endDate, orderId);
        updateGroup.setGroupEndTime(endDate);
        busDoctorPatientGroupMapper.updateById(updateGroup);
        boolean b = personnelChanges(patientGroup);
        if (!b) {
            throw new ServiceException("修改人员失败");
        }
        //发送激活消息
        JSONObject data = new JSONObject();
        data.put("type", CustomMsgConstants.SERVIEC_PACK_ACTIVATE);
        data.put("msg", "服务包已激活");

        boolean sendGeneralMsg = this.sendGeneralMsg(Long.valueOf(groupId), TencentyunImConstants.TIM_CUSTOM_ELEM, data);
        if (!sendGeneralMsg) {
            throw new ServiceException("发送激活消息失败");
        }
    }

    /**
     * 计算新的服务包的到期时间
     *
     * @param servicePackPatientRecord 服务包患者就诊记录
     * @return 新的服务包到期时间
     */
    public Long calculateExpireTime(ServicePackPatientRecord servicePackPatientRecord) {
        //统计最新的服务包时间记录
        ServicePackPatientRecord servingRecord = servicePackPatientRecordMapper.selectServingRecord(servicePackPatientRecord);
        if (servingRecord == null) {
            //没有正在服务中的记录--最新
            Date endTimeOfDayWithTime = busFiveServicePackOrderService.getEndTimeOfDayWithTime(servicePackPatientRecord.getActualEffectiveTime().getTime() + servicePackPatientRecord.getServicePackCycle() * 24 * 60 * 60 * 1000L);
            return endTimeOfDayWithTime.getTime();
        }
        //统计还未服务的时间
        Integer days = servicePackPatientRecordMapper.selectCountDayByActivityRecordNotServing(servicePackPatientRecord);
        if (days == null) {
            days = 0;
        }
        Date endTimeOfDayWithTime = busFiveServicePackOrderService.getEndTimeOfDayWithTime(servingRecord.getActualEffectiveTime().getTime() + (servingRecord.getServicePackCycle() + days) * 24 * 60 * 60 * 1000L);
        //有对应的未服务时间
        return endTimeOfDayWithTime.getTime();
    }

    @Override
    public boolean refund(Long groupId) {
        BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
        updateGroup.setId(groupId);
        updateGroup.setGroupStatus(ServicePackImStatusEnum.REFUNDED.getCode());
        updateGroup.setUpdateTime(DateUtils.getNowDate());
        //增加群组结束时间的修改
        updateGroup.setGroupEndTime(DateUtils.getNowDate());
        int updateById = busDoctorPatientGroupMapper.updateById(updateGroup);
        //发送消息参数
        if (updateById > 0) {
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.SERVIEC_PACK_REFUND);
            data.put("msg", "患者申请退款，平台同意退款，该群已解散");
            return sendGeneralMsg(groupId, TencentyunImConstants.TIM_CUSTOM_ELEM, data);
        }
        return true;
    }

    /**
     * 修改服务包的激活状态
     *
     * @param groupId    群组id
     * @param statusEnum 状态枚举
     * @return 修改状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateActivation(Long groupId, ServicePackImStatusEnum statusEnum) {
        BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
        updateGroup.setId(groupId);
        updateGroup.setGroupStatus(statusEnum.getCode());
        int updateById = busDoctorPatientGroupMapper.updateGroupStatusById(updateGroup);
        return updateById > 0;
    }

    @Override
    public boolean multipleOrderRefund(Long groupId, String startTime, String endTime) {
        BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
        updateGroup.setId(groupId);
        updateGroup.setGroupStatus(ServicePackImStatusEnum.ACTIVATED.getCode());
        updateGroup.setGroupStartTime(DateUtils.parseDate(startTime));
        updateGroup.setGroupEndTime(DateUtils.parseDate(endTime));
        updateGroup.setUpdateTime(DateUtils.getNowDate());
        int updateById = busDoctorPatientGroupMapper.updateById(updateGroup);
        return updateById > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean renewal(Long groupId, String startTime, String endTime) {
        BusDoctorPatientGroup patientGroup = busDoctorPatientGroupMapper.selectById(groupId);
        if (CodeEnum.YES.getCode().equals(patientGroup.getDelFlag())) {
            this.activateGroup(patientGroup);
        }
        BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
        updateGroup.setId(groupId);
        if (StringUtils.isNotEmpty(startTime)) {
            updateGroup.setGroupStartTime(DateUtils.parseDate(startTime));
        }
        updateGroup.setGroupStatus(ServicePackImStatusEnum.ACTIVATED.getCode());
        updateGroup.setGroupEndTime(DateUtils.parseDate(endTime));
        updateGroup.setUpdateTime(DateUtils.getNowDate());
        busDoctorPatientGroupMapper.updateById(updateGroup);
        if (!personnelChanges(patientGroup)) {
            throw new ServiceException("修改人员失败");
        }
        //发送激活消息
        JSONObject data = new JSONObject();
        data.put("type", CustomMsgConstants.SERVIEC_PACK_ACTIVATE);
        data.put("msg", "服务包已续");
        boolean sendGeneralMsg = this.sendGeneralMsg(groupId, TencentyunImConstants.TIM_CUSTOM_ELEM, data);
        if (!sendGeneralMsg) {
            throw new ServiceException("发送激活消息失败");
        }
        return true;
    }

    @Override
    public boolean expire(Long groupId) {
        BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
        updateGroup.setId(groupId);
        updateGroup.setGroupStatus(ServicePackImStatusEnum.ENDED.getCode());
        busDoctorPatientGroupMapper.updateGroupStatusById(updateGroup);
        //发送消息参数
        JSONObject data = new JSONObject();
        data.put("type", CustomMsgConstants.SERVIEC_PACK_EXPIRE);
        data.put("msg", "服务包已到期，未有后续服务包，该群组已禁言");
        return sendGeneralMsg(groupId, TencentyunImConstants.TIM_CUSTOM_ELEM, data);
    }

    @Override
    public List<ConversationInfoVO> selectConversationListV1(Long personnelId, String role, Long hospitalId, String type) {
        //患者角色特殊处理
        List<ConversationInfoVO> conversationInfoList;
        BusDoctorPatientGroup queryWrapper = new BusDoctorPatientGroup();
        if (ImMemberEnum.PATIENT.getCode().equals(role)) {
            queryWrapper.setHospitalId(hospitalId);
            queryWrapper.setPatientId(personnelId);
            conversationInfoList = busDoctorPatientGroupMapper.listConversationV1(queryWrapper);
        } else {
            queryWrapper.setPatientId(personnelId);
            queryWrapper.setRole(role);
            queryWrapper.setHospitalId(hospitalId);
            queryWrapper.setType(ImGroupType.SERVICE_PACK.getCode());
            conversationInfoList = busImGroupMemberMapper.listImGroupMemberV1(queryWrapper);
        }

        List<ConversationInfoVO> conversationInfoVOList = dealConversationInfoList(conversationInfoList, false);
        //按照发送消息时间排序
        return conversationInfoVOList.stream().sorted(Comparator.comparing(ConversationInfoVO::getMsgTime, Comparator.nullsFirst(Date::compareTo).reversed())).collect(Collectors.toList());
    }

    @Override
    public List<ConversationInfoVO> allConversationListV1(Long personnelId, String role, Long hospitalId, String type) {
        long startTime = System.currentTimeMillis();
        log.debug("allConversationListV1 开始时间：{}ms", startTime);
        List<ConversationInfoVO> conversationInfoVOList;
        //患者角色特殊处理
        BusDoctorPatientGroup queryWrapper = new BusDoctorPatientGroup();
        if (ImMemberEnum.PATIENT.getCode().equals(role)) {
            queryWrapper.setHospitalId(hospitalId);
            queryWrapper.setPatientId(personnelId);
            queryWrapper.setType(type);
            conversationInfoVOList = busDoctorPatientGroupMapper.listConversationV1(queryWrapper);

        } else {
            queryWrapper.setPatientId(personnelId);
            queryWrapper.setRole(role);
            queryWrapper.setHospitalId(hospitalId);
            queryWrapper.setType(type);
            conversationInfoVOList = busImGroupMemberMapper.listImGroupMemberV1(queryWrapper);
        }

        List<ConversationInfoVO> conversationInfoList = dealConversationInfoList(conversationInfoVOList, true);
        //医助消息放在最上面,然后按照 发送消息时间 倒序排序；
        List<ConversationInfoVO> collect = conversationInfoList.stream().sorted(Comparator.comparingInt(
                                (ConversationInfoVO vo) -> ImGroupType.ASSISTANT.getCode().equals(vo.getType()) ? 0 : 1)
                        .thenComparing(ConversationInfoVO::getMsgTime, Comparator.nullsFirst(Date::compareTo).reversed()))
                .collect(Collectors.toList());

        long endTime = System.currentTimeMillis();
        log.info("allConversationListV1 结束时间：{}ms", endTime - startTime);
        return collect;
    }

    protected List<ConversationInfoVO> dealConversationInfoList(List<ConversationInfoVO> conversationInfoVOList, Boolean needAssistant) {
        if (CollectionUtil.isEmpty(conversationInfoVOList)) {
            return new ArrayList<>();
        }
        List<String> groupIdList = conversationInfoVOList.stream().map(vo -> String.valueOf(vo.getGroupId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIdList)) {
            return new ArrayList<>();
        }

        Map<Long, ConversationInfoVO> assistantMap = new HashMap<>();
        if (needAssistant) {
            //查询医助群组列表信息
            List<ConversationInfoVO> listAssistantList = busImGroupMemberMapper.listAssistantV1(groupIdList);
            if (CollectionUtils.isNotEmpty(listAssistantList)) {
                assistantMap = listAssistantList.stream()
                        .collect(Collectors.toMap(ConversationInfoVO::getGroupId, Function.identity()));
            }
        }

        //查询最新消息
        List<BusCommunicationMessage> communicationMessages = busCommunicationMessageMapper.selectMultipleNewestV1(groupIdList);
        Map<Long, BusCommunicationMessage> messageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(communicationMessages)) {
            messageMap = communicationMessages.stream()
                    .collect(Collectors.toMap(message -> Long.parseLong(message.getGroupId()), Function.identity()));
        }

        Map<Long, SysDictData> doctorDictMap = getPhysicianTitleMap();

        boolean assistantMapExist = MapUtils.isNotEmpty(assistantMap);
        boolean messageMapExist = MapUtils.isNotEmpty(messageMap);
        boolean doctorDictMapExist = MapUtils.isNotEmpty(doctorDictMap);

        //填充 医助群组列表信息
        for (ConversationInfoVO vo : conversationInfoVOList) {

            //填充医生职称数据
            SysDictData doctorDictData = null;
            if (doctorDictMapExist) {
                doctorDictData = doctorDictMap.get(vo.getDictCode());
            }
            if (Objects.nonNull(doctorDictData)) {
                vo.setTitle(doctorDictData.getDictLabel());
            }

            //填充assistant
            ConversationInfoVO assistant = null;
            if (assistantMapExist) {
                assistant = assistantMap.get(vo.getGroupId());
            }
            if (Objects.nonNull(assistant)) {
                vo.setAssistantName(assistant.getAssistantName());
                vo.setAssistantPhoto(assistant.getAssistantPhoto());
                vo.setAssistantId(assistant.getAssistantId());
            }

            //填充 最新消息
            BusCommunicationMessage message = null;
            if (messageMapExist) {
                message = messageMap.get(vo.getGroupId());
            }
            if (Objects.nonNull(message)) {
                vo.setBusCommunicationMessage(message);
                vo.setMsgTime(message.getCreateTime());
            }
            //将dictCode还原为空，保持和原来的sql一致
            vo.setDictCode(null);
        }
        return conversationInfoVOList;
    }

    /**
     * 修改群组成员
     *
     * @param groupId            群组id
     * @param busFiveWorkGroupVO 工作群组
     */
    private void personnelChanges(Long groupId, BusFiveWorkGroupVO busFiveWorkGroupVO) {
        log.debug("personnel changes groupId:{}, busFiveWorkGroupVO={}", groupId, busFiveWorkGroupVO);
        BusDoctorPatientGroup busDoctorPatientGroup = busDoctorPatientGroupMapper.selectById(groupId);
        if (Objects.isNull(busFiveWorkGroupVO)) {
            return;
        }
        List<BusFiveGroupMemberVO> busFiveGroupMembers = busFiveWorkGroupVO.getMemberVos();
        LambdaQueryWrapper<BusImGroupMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusImGroupMember::getGroupId, busDoctorPatientGroup.getId());
        List<BusImGroupMember> groupMembers = busImGroupMemberMapper.selectList(queryWrapper);
        busImGroupMemberMapper.delete(queryWrapper);
        //批量插入信息
        List<BusImGroupMember> list = new ArrayList<>();
        BusImGroupMember ownerMember = new BusImGroupMember();
        ownerMember.setIsInto(YesNoEnum.YES.getCode());
        ownerMember.setGroupId(busDoctorPatientGroup.getId());
        ownerMember.setPersonnelId(busDoctorPatientGroup.getDoctorId());
        ownerMember.setRole(ImMemberEnum.DOCTOR.getCode());
        ownerMember.setGroupRole("Owner");
        ownerMember.setMemberName(busFiveWorkGroupVO.getFullName());
        ownerMember.setCreateTime(DateUtils.getNowDate());
        list.add(ownerMember);
        for (BusFiveGroupMemberVO g : busFiveGroupMembers) {
            if (CodeEnum.NO.getCode().equals(g.getStatus())) {
                continue;
            }
            BusImGroupMember member = new BusImGroupMember();
            member.setIsInto(YesNoEnum.YES.getCode());
            member.setGroupId(busDoctorPatientGroup.getId());
            member.setPersonnelId(g.getMemberId());
            member.setGroupRole(CodeEnum.NO.getCode().equals(g.getIdentity()) ? "Member" : "Admin");
            member.setCreateTime(DateUtils.getNowDate());
            member.setMemberName(g.getMemberName());
            ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(g.getType());
            switch (Objects.requireNonNull(typeByValue)) {
                case DOCTOR:
                    member.setRole(ImMemberEnum.DOCTOR.getCode());
                    break;
                case PSYCHOLOGY_CONSULT:
                    member.setRole(ImMemberEnum.PSYCHOLOGY_CONSULT.getCode());
                    break;
                case NURSE:
                    member.setRole(ImMemberEnum.NURSE.getCode());
                    break;
                case ASSISTANT:
                    member.setRole(ImMemberEnum.ASSISTANT.getCode());
                    break;
                case HEALTH_MANAGER:
                    member.setRole(ImMemberEnum.HEALTH_MANAGER.getCode());
                    break;
                case PSYCHOLOGICAL_CONSULTATION_TEACHER:
                    member.setRole(ImMemberEnum.PSYCHOLOGICAL_CONSULTATION_TEACHER.getCode());
                    break;
                case REHABILITATION_SPECIALIST:
                    member.setRole(ImMemberEnum.REHABILITATION_SPECIALIST.getCode());
                    break;
                case DIETITIAN:
                    member.setRole(ImMemberEnum.DIETITIAN.getCode());
                    break;
                default:
                    break;
            }
            list.add(member);
        }
        //患者信息
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(busDoctorPatientGroup.getFamilyId());
        //添加患者角色
        BusImGroupMember patientMember = new BusImGroupMember();
        patientMember.setIsInto(YesNoEnum.YES.getCode());
        patientMember.setGroupId(busDoctorPatientGroup.getId());
        patientMember.setPersonnelId(busDoctorPatientGroup.getFamilyId());
        patientMember.setRole(ImMemberEnum.PATIENT.getCode());
        patientMember.setMemberName(busPatientFamily.getName());
        patientMember.setGroupRole("Member");
        patientMember.setCreateTime(DateUtils.getNowDate());
        list.add(patientMember);
        busImGroupMemberMapper.batchInsert(list);
        //添加群组人员
        List<JSONObject> gruopUserIds = new ArrayList<>();
        //删除工作组群组人员
        List<JSONObject> workGroupUserIds = new ArrayList<>();
        //获取添加新成员
        list.forEach(m -> {
            ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(m.getRole());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("role", typeByValue.getCode());
            jsonObject.put("memberId", m.getPersonnelId());
            jsonObject.put("memberName", m.getMemberName());
            jsonObject.put("groupRole", m.getGroupRole());
            gruopUserIds.add(jsonObject);
        });
        //获取删除工作组成员
        groupMembers.forEach(g -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("role", g.getRole());
            jsonObject.put("groupRole", g.getGroupRole());
            jsonObject.put("memberId", g.getPersonnelId());
            workGroupUserIds.add(jsonObject);
        });
        //添加和删除群组人员
        this.addMember(gruopUserIds, busDoctorPatientGroup.getId());
    }


    /**
     * 添加或修改群组
     *
     * @param busDoctorPatientGroup 群组信息
     * @return 修改结果
     */
    private boolean personnelChanges(BusDoctorPatientGroup busDoctorPatientGroup) {
        BusFiveServicePackVO fiveServicePackVo = busFiveServicePackService.queryPatientPackInfo(busDoctorPatientGroup.getServiceId());
        log.debug("personnel changes fiveServicePackVo={}", fiveServicePackVo);
        if (Objects.isNull(fiveServicePackVo)) {
            return false;
        }
        List<BusFiveGroupMemberVO> busFiveGroupMembers = fiveServicePackVo.getWorkGroupVo().getMemberVos();
        LambdaQueryWrapper<BusImGroupMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusImGroupMember::getGroupId, busDoctorPatientGroup.getId());
        List<BusImGroupMember> groupMembers = busImGroupMemberMapper.selectList(queryWrapper);
        busImGroupMemberMapper.delete(queryWrapper);
        //批量插入信息
        List<BusImGroupMember> list = new ArrayList<>();
        BusImGroupMember ownerMember = new BusImGroupMember();
        ownerMember.setIsInto(YesNoEnum.YES.getCode());
        ownerMember.setGroupId(busDoctorPatientGroup.getId());
        ownerMember.setPersonnelId(busDoctorPatientGroup.getDoctorId());
        ownerMember.setRole(ImMemberEnum.DOCTOR.getCode());
        ownerMember.setGroupRole("Owner");
        ownerMember.setMemberName(fiveServicePackVo.getWorkGroupVo().getFullName());
        ownerMember.setCreateTime(DateUtils.getNowDate());
        list.add(ownerMember);
        for (BusFiveGroupMemberVO g : busFiveGroupMembers) {
            if (CodeEnum.NO.getCode().equals(g.getStatus())) {
                continue;
            }
            BusImGroupMember member = new BusImGroupMember();
            member.setIsInto(YesNoEnum.YES.getCode());
            member.setGroupId(busDoctorPatientGroup.getId());
            member.setPersonnelId(g.getMemberId());
            member.setGroupRole(CodeEnum.NO.getCode().equals(g.getIdentity()) ? "Member" : "Admin");
            member.setCreateTime(DateUtils.getNowDate());
            member.setMemberName(g.getMemberName());
            ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(g.getType());
            switch (Objects.requireNonNull(typeByValue)) {
                case DOCTOR:
                    member.setRole(ImMemberEnum.DOCTOR.getCode());
                    break;
                case PSYCHOLOGY_CONSULT:
                    member.setRole(ImMemberEnum.PSYCHOLOGY_CONSULT.getCode());
                    break;
                case NURSE:
                    member.setRole(ImMemberEnum.NURSE.getCode());
                    break;
                case ASSISTANT:
                    member.setRole(ImMemberEnum.ASSISTANT.getCode());
                    break;
                case HEALTH_MANAGER:
                    member.setRole(ImMemberEnum.HEALTH_MANAGER.getCode());
                    break;
                case PSYCHOLOGICAL_CONSULTATION_TEACHER:
                    member.setRole(ImMemberEnum.PSYCHOLOGICAL_CONSULTATION_TEACHER.getCode());
                    break;
                case REHABILITATION_SPECIALIST:
                    member.setRole(ImMemberEnum.REHABILITATION_SPECIALIST.getCode());
                    break;
                case DIETITIAN:
                    member.setRole(ImMemberEnum.DIETITIAN.getCode());
                    break;
                default:
            }
            list.add(member);
        }
        //患者信息
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(busDoctorPatientGroup.getFamilyId());
        //添加患者角色
        BusImGroupMember patientMember = new BusImGroupMember();
        patientMember.setIsInto(YesNoEnum.YES.getCode());
        patientMember.setGroupId(busDoctorPatientGroup.getId());
        patientMember.setPersonnelId(busDoctorPatientGroup.getFamilyId());
        patientMember.setRole(ImMemberEnum.PATIENT.getCode());
        patientMember.setMemberName(busPatientFamily.getName());
        patientMember.setGroupRole("Member");
        patientMember.setCreateTime(DateUtils.getNowDate());
        list.add(patientMember);
        busImGroupMemberMapper.batchInsert(list);
        //添加群组人员
        List<JSONObject> gruopUserIds = new ArrayList<>();
        //删除工作组群组人员
        List<JSONObject> workGroupUserIds = new ArrayList<>();
        //获取添加新成员
        list.forEach(m -> {
            ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(m.getRole());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("role", typeByValue.getCode());
            jsonObject.put("memberId", m.getPersonnelId());
            jsonObject.put("memberName", m.getMemberName());
            jsonObject.put("groupRole", m.getGroupRole());
            gruopUserIds.add(jsonObject);
        });
        //获取删除工作组成员
        groupMembers.forEach(g -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("role", g.getRole());
            jsonObject.put("groupRole", g.getGroupRole());
            jsonObject.put("memberId", g.getPersonnelId());
            workGroupUserIds.add(jsonObject);
        });
        if (log.isDebugEnabled()) {
            log.debug("删除工作组人员 data={}", JSON.toJSONString(workGroupUserIds));
            log.debug("添加工作组人员 data={}", JSON.toJSONString(gruopUserIds));
        }
        //添加和删除群组人员
        this.addMember(gruopUserIds, busDoctorPatientGroup.getId());
        return true;
    }

    /**
     * 添加或修改本地群组
     *
     * @param busDoctorPatientGroup 群组信息
     * @return 修改结果
     */
    private boolean localPersonnelChanges(BusDoctorPatientGroup busDoctorPatientGroup) {
        BusFiveServicePackVO fiveServicePackVo = busFiveServicePackService.queryPatientPackInfo(busDoctorPatientGroup.getServiceId());
        log.info("local personnel changes fiveServicePackVo={}", fiveServicePackVo);
        if (Objects.isNull(fiveServicePackVo)) {
            return false;
        }
        List<BusFiveGroupMemberVO> busFiveGroupMembers = fiveServicePackVo.getWorkGroupVo().getMemberVos();
        QueryWrapper<BusImGroupMember> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", busDoctorPatientGroup.getId());
        List<BusImGroupMember> groupMembers = busImGroupMemberMapper.selectList(queryWrapper);
        busImGroupMemberMapper.delete(queryWrapper);
        //批量插入信息
        List<BusImGroupMember> list = new ArrayList<>();
        BusImGroupMember ownerMember = new BusImGroupMember();
        ownerMember.setIsInto(YesNoEnum.YES.getCode());
        ownerMember.setGroupId(busDoctorPatientGroup.getId());
        ownerMember.setPersonnelId(busDoctorPatientGroup.getDoctorId());
        ownerMember.setRole(ImMemberEnum.DOCTOR.getCode());
        ownerMember.setGroupRole("Owner");
        ownerMember.setMemberName(fiveServicePackVo.getWorkGroupVo().getFullName());
        ownerMember.setCreateTime(DateUtils.getNowDate());
        list.add(ownerMember);
        for (BusFiveGroupMemberVO g : busFiveGroupMembers) {
            if (CodeEnum.NO.getCode().equals(g.getStatus())) {
                continue;
            }
            ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(g.getType());
            BusImGroupMember member = new BusImGroupMember();
            member.setIsInto(YesNoEnum.YES.getCode());
            member.setGroupId(busDoctorPatientGroup.getId());
            member.setPersonnelId(g.getMemberId());
            member.setGroupRole(CodeEnum.NO.getCode().equals(g.getIdentity()) ? "Member" : "Admin");
            member.setCreateTime(DateUtils.getNowDate());
            member.setMemberName(g.getMemberName());
            switch (Objects.requireNonNull(typeByValue)) {
                case DOCTOR:
                    member.setRole(ImMemberEnum.DOCTOR.getCode());
                    break;
                case PSYCHOLOGY_CONSULT:
                    member.setRole(ImMemberEnum.PSYCHOLOGY_CONSULT.getCode());
                    break;
                case NURSE:
                    member.setRole(ImMemberEnum.NURSE.getCode());
                    break;
//                case ASSISTANT:
//                    member.setRole(ImMemberEnum.ASSISTANT.getCode());
//                    break;
                case HEALTH_MANAGER:
                    member.setRole(ImMemberEnum.HEALTH_MANAGER.getCode());
                    break;
                case PSYCHOLOGICAL_CONSULTATION_TEACHER:
                    member.setRole(ImMemberEnum.PSYCHOLOGICAL_CONSULTATION_TEACHER.getCode());
                    break;
                case REHABILITATION_SPECIALIST:
                    member.setRole(ImMemberEnum.REHABILITATION_SPECIALIST.getCode());
                    break;
                case DIETITIAN:
                    member.setRole(ImMemberEnum.DIETITIAN.getCode());
                    break;
                default:
            }
            list.add(member);
        }
        //患者信息
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(busDoctorPatientGroup.getFamilyId());
        //添加患者角色
        BusImGroupMember patientMember = new BusImGroupMember();
        patientMember.setIsInto(YesNoEnum.YES.getCode());
        patientMember.setGroupId(busDoctorPatientGroup.getId());
        patientMember.setPersonnelId(busDoctorPatientGroup.getFamilyId());
        patientMember.setRole(ImMemberEnum.PATIENT.getCode());
        patientMember.setMemberName(busPatientFamily.getName());
        patientMember.setGroupRole("Member");
        patientMember.setCreateTime(DateUtils.getNowDate());
        list.add(patientMember);
        busImGroupMemberMapper.batchInsert(list);
        return true;
    }

    @Override
    public int insertBusCommunicationMessage(BusCommunicationMessage currentMessage) {
        log.debug("储存消息信息:{}", currentMessage);
        long startTime = System.currentTimeMillis();
        currentMessage.setCreateTime(DateUtils.getNowDate());
        String type = currentMessage.getType();
        String payload = currentMessage.getPayload();
        JSONObject jsnnObject = JSONObject.parseObject(payload);
        String url = "";
        //文件后缀
        String suffix = "";
        //0 app消息 1 web端消息
        if (CodeEnum.YES.getCode().equals(currentMessage.getMsgType())) {
            //文件消息做处理
            switch (type) {
                //图片
                case TencentyunImConstants.TIM_IMAGE_ELEM:
                    JSONArray parseArray = JSON.parseArray(jsnnObject.getString("imageInfoArray"));
                    for (int i = 0; i < parseArray.size(); i++) {
                        if (StringUtils.isNotEmpty(parseArray.getJSONObject(i).getString("imageUrl"))) {
                            url = parseArray.getJSONObject(i).getString("imageUrl");
                            JSONObject object = parseArray.getJSONObject(i);
                            String[] split = url.split("\\?");
                            suffix = FileUtils.simpleSuffix(ArrayUtil.isNotEmpty(split) ? split[0] : url);
                            R<String> stringR = remoteOssService.networkUpload(url, OSSSaveDirectory.IM.getValue(), "." + suffix);
                            object.put("imageUrl", stringR.getMsg());
                        }
                    }
                    jsnnObject.put("imageInfoArray", parseArray);
                    break;
                //音频
                case TencentyunImConstants.TIM_FILE_ELEM:
                    url = jsnnObject.getString("fileUrl");
                    suffix = FileUtils.simpleSuffix(url);
                    R<String> stringR = remoteOssService.networkUpload(url, OSSSaveDirectory.IM.getValue(), "." + suffix);
                    jsnnObject.put("fileUrl", stringR.getMsg());
                    break;
                default:
                    break;
            }
        }
        currentMessage.setPayload(jsnnObject.toJSONString());
        //推送消息给患者
        if (StringUtils.isNotEmpty(currentMessage.getFrom())) {
            String messageFrom = currentMessage.getFrom();
            String[] messageFromArray = messageFrom.split("_");
            //判断是否是患者发送的消息
            if (ArrayUtil.isNotEmpty(messageFromArray) && !TencentyunImConstants.PATIENT_IM_ACCOUNT_PREFIX.equals(messageFromArray[0])) {
                // 不是患者发的
                BusCommunicationMessage queryWrapper = new BusCommunicationMessage();
                queryWrapper.setGroupId(currentMessage.getGroupId());
                // 查询最后一条信息
                BusCommunicationMessage lastMessage = busCommunicationMessageMapper.selectNewest(queryWrapper);
                if (Objects.nonNull(lastMessage) && StringUtils.isNotEmpty(lastMessage.getFrom())) {
                    String from = lastMessage.getFrom();
                    String[] split = from.split("_");
                    //不是患者发的消息,同时最后一条消息又是患者或者administrator发的,则推送消息
                    if (ArrayUtil.isNotEmpty(split) && (TencentyunImConstants.PATIENT_IM_ACCOUNT_PREFIX.equals(split[0]) || TencentyunImConstants.ADMINISTRATOR.equals(split[0]))) {
                        sendMsg(Long.valueOf(currentMessage.getGroupId()),messageFromArray);
                    }
                    if (TencentyunImConstants.DOCTOR_IM_ACCOUNT_PREFIX.equals(split[0]) && TencentyunImConstants.DOCTOR_IM_ACCOUNT_PREFIX.equals(messageFromArray[0])) {
                        imGroupService.pushToPatientAfterMinute(Long.valueOf(currentMessage.getGroupId()), lastMessage,messageFromArray) ;
                    }
                }

            } else {
                //是患者发的
                // 推送消息给群组成员
                R<?> r = remoteCommunicationMessageService.pushMsg(currentMessage.getGroupId());
                if (Constants.FAIL.equals(r.getCode())) {
                    log.error("远程调用个推失败原因={}", r.getMsg());
                }
            }
        }
        int i = busCommunicationMessageMapper.insertIgnoreNull(currentMessage);
        long endTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("写入患者消息成功条数：{}，保存到本地程序运行时间：{}ms", i, endTime - startTime);
        }
        return 1;
    }

    /**
     * 发送患者信息
     *
     * @param groupId      聊天群组
     * @param msgFromArray 消息参数
     */
    @Override
    public void sendMsg(Long groupId, String[] msgFromArray) {
        ImSendMessageEvent event = new ImSendMessageEvent();
        event.setEventType(ImSendMessageEvent.EventType.SEND_NORMAL);
        event.setGroupId(groupId);
        if (msgFromArray != null && msgFromArray.length > 1 && TencentyunImConstants.DOCTOR_IM_ACCOUNT_PREFIX.equals(msgFromArray[0])) {
            Long doctorId = Long.valueOf(msgFromArray[1]);
            String doctorName = busDoctorMapper.findDoctorNameById(doctorId);
            event.setDoctorName(doctorName);
        }
        Map<String, Object> attachment = new HashMap<>();
        attachment.put("status", 7);
        event.setAttachment(attachment);
        imSendMessageEventProducer.send(event);
    }

    @Override
    public List<ConversationInfoVO> getConversationsList(ImGroupQuery imGroupQuery) {
        long startTime = System.currentTimeMillis();
        log.debug("getConversationsList 开始时间：{}ms", startTime);
        List<ConversationInfoVO> conversationInfoVOList;
        //患者角色特殊处理
        BusDoctorPatientGroup queryWrapper = new BusDoctorPatientGroup();
        if (ImMemberEnum.PATIENT.getCode().equals(imGroupQuery.getRole())) {
            queryWrapper.setHospitalId(imGroupQuery.getHospitalId());
            queryWrapper.setPatientId(imGroupQuery.getUserId());
            queryWrapper.setType(imGroupQuery.getType());
            queryWrapper.setLastMessageTime(!StringUtils.hasText(imGroupQuery.getLastMessageTime()) ? LocalDateTime.now().toString() : imGroupQuery.getLastMessageTime());
            conversationInfoVOList = busDoctorPatientGroupMapper.listConversationV1(queryWrapper);
        } else {
            queryWrapper.setPatientId(imGroupQuery.getUserId());
            queryWrapper.setRole(imGroupQuery.getRole());
            queryWrapper.setHospitalId(imGroupQuery.getHospitalId());
            queryWrapper.setType(imGroupQuery.getType());
            queryWrapper.setLastMessageTime(!StringUtils.hasText(imGroupQuery.getLastMessageTime()) ? LocalDateTime.now().toString() : imGroupQuery.getLastMessageTime());
            queryWrapper.setMark(imGroupQuery.getMark());
            queryWrapper.setFamilyName(imGroupQuery.getPatientName());
            queryWrapper.setPageSize(imGroupQuery.getPageSize());
            conversationInfoVOList = busImGroupMemberMapper
                    .listImGroupInfo(queryWrapper);
        }
        List<ConversationInfoVO> conversationInfoList = assembleImGroupInfo(conversationInfoVOList, imGroupQuery);
        //医助消息放在最上面,然后按照 发送消息时间 倒序排序；
        List<ConversationInfoVO> collect = conversationInfoList.stream().sorted(Comparator.comparingInt(
                                (ConversationInfoVO vo) -> ImGroupType.ASSISTANT.getCode().equals(vo.getType()) ? 0 : 1)
                        .thenComparing(ConversationInfoVO::getMsgTime, Comparator.nullsFirst(Date::compareTo).reversed()))
                .collect(Collectors.toList());
        long endTime = System.currentTimeMillis();
        log.info("getConversationsList 结束时间：{}ms", endTime - startTime);
        return collect;
    }




    private List<ConversationInfoVO> assembleImGroupInfo(List<ConversationInfoVO> conversationInfoVOList,ImGroupQuery imGroupQuery) {
        if (CollectionUtils.isEmpty(conversationInfoVOList)) {
            return conversationInfoVOList;
        }
        List<Long> doctorIds = new ArrayList<>();
        List<Long> familyIds = new ArrayList<>();
        List<Long> serviceIds = new ArrayList<>();
        List<Long> patientId = new ArrayList<>();
        List<Long> departmentIds = new ArrayList<>();
        Long hospitalId = conversationInfoVOList.get(0).getHospitalId();
        conversationInfoVOList.forEach(vo -> {
            if (vo.getDoctorId() != null) {
                doctorIds.add(vo.getDoctorId());
            }
            if (vo.getFamilyId() != null) {
                familyIds.add(vo.getFamilyId());
            }
            if (vo.getServiceId() != null) {
                serviceIds.add(vo.getServiceId());
            }
            if (vo.getPatientId() != null) {
                patientId.add(vo.getPatientId());
            }
            if (vo.getDepartmentId() != null) {
                departmentIds.add(vo.getDepartmentId());
            }
        });
        Map<Long, BusDoctorVO> doctorMap = new HashMap<>();
        //查询医生信息
        if (CollectionUtils.isNotEmpty(doctorIds)) {
            List<BusDoctorVO> busDoctorVOS = busDoctorMapper.selectDoctorsByIds(doctorIds,hospitalId);
            doctorMap = busDoctorVOS.stream().collect(Collectors.toMap(BusDoctorVO::getId, Function.identity()));
        }

        //查询患者信息
        Map<Long, BusPatientFamily> familyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(familyIds)) {
            LambdaQueryWrapper<BusPatientFamily> queryWrapper = Wrappers.lambdaQuery(BusPatientFamily.class)
                    .like(org.springframework.util.StringUtils.hasText(imGroupQuery.getPatientName()),BusPatientFamily::getName, imGroupQuery.getPatientName())
                    .in(BusPatientFamily::getId, familyIds);
            List<BusPatientFamily> busPatientFamilies = busPatientFamilyMapper.selectList(queryWrapper);
            familyMap = busPatientFamilies.stream().collect(Collectors.toMap(BusPatientFamily::getId, Function.identity()));
            //一些数据没有 familyId，需要补充一个空的
            familyMap.put(null, new BusPatientFamily());
        }
        //查询服务包信息
        Map<Long, BusFiveServicePack> packOrderMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(serviceIds)) {
            List<BusFiveServicePack> busFiveServicePackOrders = busFiveServicePackMapper.selectBatchIds(serviceIds);
            packOrderMap = busFiveServicePackOrders.stream().collect(Collectors.toMap(BusFiveServicePack::getId, Function.identity()));
        }
        //查询部门信息
        R<List<BusBizDepartment>> busBizDepartmentR = remoteBizDepartmentService.listByDepartmentIds(departmentIds);
        if (!busBizDepartmentR.isSuccess()){
            log.error("远程调用业务部门查询失败原因={}", busBizDepartmentR.getMsg());

        }
        Map<Long, BusBizDepartment> departmentMap = new HashMap<>();
        List<BusBizDepartment> bizDepartmentData = busBizDepartmentR.getData();
        if (CollectionUtils.isNotEmpty(bizDepartmentData)){
            departmentMap = bizDepartmentData.stream().collect(Collectors.toMap(BusBizDepartment::getId, Function.identity()));
        }
        Iterator<ConversationInfoVO> iterator = conversationInfoVOList.iterator();
        while (iterator.hasNext()){
            ConversationInfoVO conversationInfoVO = iterator.next();
            Long doctorId = conversationInfoVO.getDoctorId();
            if (doctorMap.containsKey(doctorId)){
                BusDoctorVO busDoctorVO = doctorMap.get(doctorId);
                conversationInfoVO.setDoctorName(busDoctorVO.getFullName());
                conversationInfoVO.setPhoto(busDoctorVO.getPhoto());
                conversationInfoVO.setTitle(busDoctorVO.getTitleValue());
                conversationInfoVO.setIsThisCourt(busDoctorVO.getIsThisCourt());
            }
            if (familyMap.containsKey(conversationInfoVO.getFamilyId())){
                BusPatientFamily busPatientFamily = familyMap.get(conversationInfoVO.getFamilyId());
                conversationInfoVO.setFamilyName(busPatientFamily.getName());
                conversationInfoVO.setSex(busPatientFamily.getSex());
                // 转换为 Instant，再转为 LocalDate
                LocalDate localDate = busPatientFamily.getDateOfBirth() == null ? null : busPatientFamily.getDateOfBirth().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                conversationInfoVO.setDateOfBirth(localDate);
            }else {
                iterator.remove();
            }
            if (packOrderMap.containsKey(conversationInfoVO.getServiceId())){
                conversationInfoVO.setGroupName(packOrderMap.get(conversationInfoVO.getServiceId()).getServicePackName());
            }
            if(departmentMap.containsKey(conversationInfoVO.getDepartmentId())){
                conversationInfoVO.setDepartmentName(departmentMap.get(conversationInfoVO.getDepartmentId()).getDepartmentName());
            }
        }
        dealConversationInfoList(conversationInfoVOList, true);
        return conversationInfoVOList;
    }

    @Override
    public List<BusImGroupMemberVO> listMemberInfo(Long groupId, String type) {
        List<BusImGroupMemberVO> imGroupMemberVOList = new ArrayList<>();
        //查询群组人员
        List<BusImGroupMember> list = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                .eq(BusImGroupMember::getGroupId, groupId));
        //群组信息
        BusDoctorPatientGroup busDoctorPatientGroup = busDoctorPatientGroupMapper.selectById(groupId);
        Long serviceId = busDoctorPatientGroup.getServiceId();
        //查询服务包工作组信息
        BusFiveServicePackVO busFiveServicePackVO = busFiveServicePackService.queryPatientPackInfo(serviceId);
        if (Objects.isNull(busFiveServicePackVO)) {
            throw new ServiceException("服务包查询失败");
        }
        BusFiveWorkGroupVO workGroupVo = busFiveServicePackVO.getWorkGroupVo();
        if (Objects.isNull(workGroupVo)) {
            throw new ServiceException("工作组查询失败");
        }
        List<BusFiveGroupMemberVO> groupMembers = workGroupVo.getMemberVos();
        //拼接群组组员信息
        list.forEach(l -> {
            BusImGroupMemberVO busImGroupMemberVO = OrikaUtils.convert(l, BusImGroupMemberVO.class);
            Long personnelId = l.getPersonnelId();
            String role = l.getRole();
            ImMemberEnum typeByCode = ImMemberEnum.getTypeByCode(role);
            switch (Objects.requireNonNull(typeByCode)) {
                case DOCTOR:
                    String ownerRole = "Owner";
                    BusDoctorVO busDoctorVO = busDoctorMapper.selectDoctorDetailInfoById(personnelId);
                    busImGroupMemberVO.setFullName(busDoctorVO.getFullName());
                    busImGroupMemberVO.setPhoto(busDoctorVO.getPhoto());
                    if (ownerRole.equals(l.getGroupRole())) {
                        busImGroupMemberVO.setTitle(busDoctorVO.getTitleValue());
                        busImGroupMemberVO.setDepartmentId(workGroupVo.getDepartmentId());
                        busImGroupMemberVO.setDepartmentName(workGroupVo.getDepartmentName());
                    } else {
                        groupMembers.forEach(g -> {
                            if (ImMemberEnum.DOCTOR.getCode().equals(g.getType()) && l.getPersonnelId().equals(g.getMemberId())) {
                                busImGroupMemberVO.setTitle(busDoctorVO.getTitleValue());
                                busImGroupMemberVO.setDepartmentId(g.getDepartmentId());
                                busImGroupMemberVO.setDepartmentName(g.getDepartmentName());
                            }
                        });
                    }
                    break;
                case PSYCHOLOGY_CONSULT:
                    BusDoctorVO psychologyConsult = busDoctorMapper.selectDoctorDetailInfoById(l.getPersonnelId());
                    busImGroupMemberVO.setFullName(psychologyConsult.getFullName());
                    busImGroupMemberVO.setPhoto(psychologyConsult.getPhoto());
                    break;
                case PATIENT:
                    BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(l.getPersonnelId());
                    busImGroupMemberVO.setFullName(busPatientFamily.getName());
                    break;
                case NURSE:
                    BusDoctorVO nurse = busDoctorMapper.selectDoctorDetailInfoById(l.getPersonnelId());
                    busImGroupMemberVO.setFullName(nurse.getFullName());
                    busImGroupMemberVO.setPhoto(nurse.getPhoto());
                    busImGroupMemberVO.setDepartmentName(nurse.getDepartmentName());
                    break;
                default:
                    BusDoctorVO defaultRole = busDoctorMapper.selectDoctorDetailInfoById(l.getPersonnelId());
                    busImGroupMemberVO.setFullName(defaultRole.getFullName());
                    busImGroupMemberVO.setPhoto(defaultRole.getPhoto());
                    break;
            }
            imGroupMemberVOList.add(busImGroupMemberVO);
        });
        if (CodeEnum.YES.getCode().equals(type)) {
            imGroupMemberVOList.removeIf(a -> a.getRole().equals(ImMemberEnum.PATIENT.getCode()));
        }
        return imGroupMemberVOList;
    }

    @Override
    public BusServicePackImVO getServiceInfo(Long groupId) {
        BusServicePackImVO packImVO = new BusServicePackImVO();
        //查询群组信息
        ConversationInfoVO conversationInfoVO = busDoctorPatientGroupMapper.getConversation(groupId);
        packImVO.setServiceId(conversationInfoVO.getServiceId());
        packImVO.setHospitalId(conversationInfoVO.getHospitalId());
        packImVO.setDoctorId(conversationInfoVO.getDoctorId());
        packImVO.setIdCardNo(conversationInfoVO.getIdCardNo());

        //查询医生绑定医助信息
        BusFiveAssistantDoctor assistantDoctor = fiveAssistantDoctorMapper.selectOne(new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                .eq(BusFiveAssistantDoctor::getHospitalId, conversationInfoVO.getHospitalId())
                .eq(BusFiveAssistantDoctor::getDoctorId, conversationInfoVO.getDoctorId())
                .orderByDesc(BusFiveAssistantDoctor::getId)
                .last("limit 1"));
        if (Objects.nonNull(assistantDoctor)) {
            BusDoctorVO doctorVO = busDoctorMapper.selectDoctorDetailInfoById(assistantDoctor.getAssistantId());
            conversationInfoVO.setAssistantName(doctorVO.getFullName());
            conversationInfoVO.setAssistantPhoto(doctorVO.getPhoto());
            conversationInfoVO.setAssistantId(doctorVO.getId());
        }
        LocalDate dateOfBirth = conversationInfoVO.getDateOfBirth();
        if (Objects.nonNull(dateOfBirth)) {
            conversationInfoVO.setFamilyAge(AgeUtil.getAgeDetail(dateOfBirth.toString()));
        }
        packImVO.setConversationInfoVO(conversationInfoVO);
        //查询订单信息
        List<BusFiveServicePackOrder> packOrders = busFiveServicePackOrderMapper.selectList(new LambdaQueryWrapper<BusFiveServicePackOrder>()
                .eq(BusFiveServicePackOrder::getServicePackId, conversationInfoVO.getServiceId())
                .eq(BusFiveServicePackOrder::getHospitalId, conversationInfoVO.getHospitalId())
                .eq(BusFiveServicePackOrder::getDoctorId, conversationInfoVO.getDoctorId())
                .eq(BusFiveServicePackOrder::getPatientId, conversationInfoVO.getPatientId())
                .eq(BusFiveServicePackOrder::getFamilyId, conversationInfoVO.getFamilyId())
                .eq(BusFiveServicePackOrder::getStatus, ServicePackOrderStatusEnum.TO_BE_ACTIVATED.getCode())
                .orderByAsc(BusFiveServicePackOrder::getCreateTime));
        if (CollectionUtil.isNotEmpty(packOrders)) {
            BusFiveServicePackOrder packOrder = packOrders.get(0);
            packImVO.setAmount(packOrder.getAmount());
            packImVO.setOrderNo(packOrder.getOrderNo());
            packImVO.setStatus(packOrder.getStatus());
            packImVO.setOrderId(packOrder.getId());
            packImVO.setPaymentTime(packOrder.getPaymentTime());
            // 查询是否存申请过退款
            BusAfterSale afterSale = busAfterSaleMapper.selectOne(new LambdaQueryWrapper<BusAfterSale>()
                    .eq(BusAfterSale::getOrderNo, packOrder.getOrderNo()));
            packImVO.setExitAfterSale(Objects.nonNull(afterSale) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        }
        // 服务包信息
        BusFiveServicePackVO busFiveServicePackVO = busFiveServicePackService.queryPatientPackInfo(conversationInfoVO.getServiceId());
        packImVO.setServicePackName(busFiveServicePackVO.getServicePackName());
        packImVO.setServicePackType(busFiveServicePackVO.getServicePackType());
        packImVO.setDiseaseType(busFiveServicePackVO.getDiseaseType());
        packImVO.setSuitablePopulation(busFiveServicePackVO.getSuitablePopulation());
        packImVO.setServicePackImg(busFiveServicePackVO.getServicePackImg());
        packImVO.setServiceStatus(busFiveServicePackVO.getStatus());
        String servicePeriodAmount = busFiveServicePackVO.getServicePeriodAmount();
        JSONArray jsonArray = JSONArray.parseArray(servicePeriodAmount);
        jsonArray.sort(Comparator.comparing(obj -> ((JSONObject) obj).getBigDecimal("price")));
        if (CollectionUtil.isEmpty(jsonArray)) {
            throw new ServiceException("服务包信息缺失");
        }
        JSONObject jsonObject = (JSONObject) jsonArray.get(0);
        packImVO.setServicePackPrice(jsonObject.getDouble("price"));
        if (jsonObject.containsKey("customDate")) {
            packImVO.setServiceCycleLabel(jsonObject.getString("customDate") + "天");
        } else {
            packImVO.setServiceCycleLabel(jsonObject.getString("dictLabel"));
        }
        return packImVO;
    }

    @Override
    public ConversationInfoVO getAssistantInfo(Long groupId) {
        log.debug("群组ID={}", groupId);
        ConversationInfoVO conversationInfoVO = new ConversationInfoVO();
        BusDoctorPatientGroup patientGroup = busDoctorPatientGroupMapper.selectById(groupId);
        Long doctorId = patientGroup.getDoctorId();
        Long hospitalId = patientGroup.getHospitalId();
        // 查询该医生医助
        BusFiveAssistantDoctor assistantDoctor = fiveAssistantDoctorMapper.selectOne(new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                .eq(BusFiveAssistantDoctor::getDoctorId, doctorId)
                .eq(BusFiveAssistantDoctor::getHospitalId, hospitalId));
        // 查询医生
        BusDoctorVO doctorVO = busDoctorMapper.selectDoctorInfo(patientGroup.getHospitalId(), assistantDoctor.getDoctorId());
        conversationInfoVO.setDoctorId(doctorVO.getId());
        conversationInfoVO.setDoctorName(doctorVO.getFullName());
        conversationInfoVO.setPhoto(doctorVO.getPhoto());
        conversationInfoVO.setTitle(doctorVO.getTitleValue());
        log.debug("医生信息 doctorInfo={}", doctorVO);
        // 查询医助
        BusDoctorVO assistantVO = busDoctorMapper.selectDoctorInfo(patientGroup.getHospitalId(), assistantDoctor.getAssistantId());
        conversationInfoVO.setAssistantId(assistantVO.getId());
        conversationInfoVO.setAssistantName(assistantVO.getFullName());
        conversationInfoVO.setAssistantPhoto(assistantVO.getPhoto());
        log.debug("医助信息 assistantInfo={}", assistantVO);
        conversationInfoVO.setGroupId(groupId);
        conversationInfoVO.setDisable(patientGroup.getDisable());
        conversationInfoVO.setType(patientGroup.getType());
        return conversationInfoVO;
    }

    @Override
    public boolean updateServicePackImMember(Long workGroupId) {
        log.debug("查询工作组id info={}", workGroupId);
        try {
            //查询跟workGroupId相关联所有服务包
            List<BusFiveServicePack> fiveServicePackList = busFiveServicePackMapper.selectList(
                    new LambdaQueryWrapper<BusFiveServicePack>()
                            .eq(BusFiveServicePack::getWorkingGroupId, workGroupId)
                            .eq(BusFiveServicePack::getWhetherDelete, CodeEnum.NO.getCode()));
            if (log.isDebugEnabled()) {
                log.debug("服务包详情 info={}", JSON.toJSONString(fiveServicePackList));
            }
            List<Long> idsList = fiveServicePackList.stream().map(BusFiveServicePack::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(idsList)) {
                return true;
            }
            //查询所有关联群组
            List<BusDoctorPatientGroup> busDoctorPatientGroups = busDoctorPatientGroupMapper.selectList(
                    new LambdaQueryWrapper<BusDoctorPatientGroup>()
                            .in(BusDoctorPatientGroup::getServiceId, idsList)
                            .eq(BusDoctorPatientGroup::getType, ImGroupType.SERVICE_PACK.getCode()));
            log.info("关联群组info={}", JSON.toJSONString(busDoctorPatientGroups));
            List<BusDoctorPatientGroup> groupList = new ArrayList<>();
            for (BusDoctorPatientGroup g : busDoctorPatientGroups) {
                //查询订单信息 只更新激活中的
                String status = "3,5";
                List<String> result = Arrays.asList(status.split(","));
                List<BusFiveServicePackOrder> packOrderList = busFiveServicePackOrderMapper.selectList(new LambdaQueryWrapper<BusFiveServicePackOrder>()
                        .eq(BusFiveServicePackOrder::getServicePackId, g.getServiceId())
                        .eq(BusFiveServicePackOrder::getHospitalId, g.getHospitalId())
                        .eq(BusFiveServicePackOrder::getDoctorId, g.getDoctorId())
                        .eq(BusFiveServicePackOrder::getPatientId, g.getPatientId())
                        .eq(BusFiveServicePackOrder::getFamilyId, g.getFamilyId())
                        //只更新待激活售后中
                        .in(BusFiveServicePackOrder::getStatus, result));
                if (log.isDebugEnabled()) {
                    log.debug("激活中订单{}", JSON.toJSONString(packOrderList));
                }
                if (CollectionUtil.isNotEmpty(packOrderList)) {
                    groupList.add(g);
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("激活中群组info={}", JSON.toJSONString(groupList));
            }
            //查询工作组详情
            BusFiveWorkGroupVO busFiveWorkGroupVO = busFiveServicePackService.queryWorkGroupInfo(workGroupId);
            if (log.isDebugEnabled()) {
                log.info("工作组详情 info={}", JSON.toJSONString(busFiveWorkGroupVO));
            }
            groupList.forEach(g -> {
                this.personnelChanges(g.getId(), busFiveWorkGroupVO);
            });
        } catch (Exception e) {
            log.error("im修改人员失败 e={}", e.getMessage(), e);
            return false;
        }
        return true;
    }

    @Override
    public void updateGroupAssistant(List<BusFiveAssistantDoctor> assistantDoctors, String type) {
        if (log.isDebugEnabled()) {
            log.debug("修改医生医助群组信息 ass={},type={}", JSON.toJSONString(assistantDoctors), type);
        }

        try {
            //判断新增或删除 0新增 1删除
            for (BusFiveAssistantDoctor d : assistantDoctors) {
                List<BusDoctorPatientGroup> patientGroups = busDoctorPatientGroupMapper.selectList(new LambdaQueryWrapper<BusDoctorPatientGroup>()
                        .eq(BusDoctorPatientGroup::getDoctorId, d.getDoctorId())
                        .eq(BusDoctorPatientGroup::getHospitalId, d.getHospitalId())
                        .eq(BusDoctorPatientGroup::getDelFlag, CodeEnum.NO.getCode())
                        .eq(BusDoctorPatientGroup::getType, CodeEnum.NO.getCode()));
                if (log.isDebugEnabled()) {
                    log.info("修改群组 patientGroups={}", JSON.toJSONString(patientGroups));
                }
                if (CodeEnum.NO.getCode().equals(type)) {
                    for (BusDoctorPatientGroup g : patientGroups) {
                        // 查询是否有相同数据 防止脏数据
                        List<BusImGroupMember> imGroupMemberList = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                                .eq(BusImGroupMember::getGroupId, g.getId())
                                .eq(BusImGroupMember::getPersonnelId, d.getAssistantId())
                                .ne(BusImGroupMember::getRole, ImMemberEnum.DOCTOR.getCode()));
                        if (imGroupMemberList.isEmpty()) {
                            BusImGroupMember imGroupMember = new BusImGroupMember();
                            imGroupMember.setGroupId(g.getId());
                            imGroupMember.setRole(ImMemberEnum.DOCTOR.getCode());
                            imGroupMember.setPersonnelId(d.getAssistantId());
                            imGroupMember.setGroupRole("Member");
                            imGroupMember.setIsInto(YesNoEnum.YES.getCode());
                            imGroupMember.setCreateTime(DateUtils.getNowDate());
                            busImGroupMemberMapper.insert(imGroupMember);
                        }
                        //im导入医助
                        List<Member> memberList = new ArrayList<>();
                        Member member = new Member();
                        member.setRole("Member");
                        member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getAssistantId());
                        memberList.add(member);
                        ImGroupRequest imGroupRequest = new ImGroupRequest();
                        imGroupRequest.setGroupId(g.getId() + "");
                        imGroupRequest.setMemberList(memberList);
                        R<Boolean> importGroupMember = remoteImService.importGroupMember(imGroupRequest);
                        if (Constants.FAIL.equals(importGroupMember.getCode()) || !(boolean) importGroupMember.getData()) {
                            throw new ServiceException("更新im群组人员失败");
                        }
                    }
                    //判断是否创建过关联
                    Long assistantGroup = busDoctorPatientGroupMapper.getDoctorAndAssistantGroup(d.getDoctorId(), d.getAssistantId(), d.getHospitalId());
                    if (Objects.isNull(assistantGroup)) {
                        BusDoctorPatientGroup doctorPatientGroup = new BusDoctorPatientGroup();
                        doctorPatientGroup.setDoctorId(d.getDoctorId());
                        doctorPatientGroup.setType(ImGroupType.ASSISTANT.getCode());
                        doctorPatientGroup.setCreateTime(DateUtils.getNowDate());
                        doctorPatientGroup.setHospitalId(d.getHospitalId());
                        busDoctorPatientGroupMapper.insert(doctorPatientGroup);
                        List<BusImGroupMember> imGroupMemberList = new ArrayList<>();
                        BusImGroupMember doctorMember = new BusImGroupMember(d.getDoctorId(), doctorPatientGroup.getId(),
                                ImMemberEnum.DOCTOR.getCode(), "Owner");
                        imGroupMemberList.add(doctorMember);
                        BusImGroupMember assistantMember = new BusImGroupMember(d.getAssistantId(), doctorPatientGroup.getId(),
                                ImMemberEnum.DOCTOR.getCode(), "Member");
                        imGroupMemberList.add(assistantMember);
                        busImGroupMemberMapper.batchInsert(imGroupMemberList);
                        //创建群组并导入人员
                        ImGroupRequest groupRequest = new ImGroupRequest();
                        // 填充im群组信息
                        groupRequest.setType(TencentyunImConstants.IM_GROUP_TYPE);
                        groupRequest.setGroupId(String.valueOf(doctorPatientGroup.getId()));
                        groupRequest.setName("医生医助聊天组");
                        groupRequest.setOwner_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getDoctorId());
                        //添加自定义字段信息
                        List<AppDefinedData> appDefinedDataList = new ArrayList<>();
                        //医院id
                        AppDefinedData hospitalId = new AppDefinedData();
                        hospitalId.setKey("hospital_id");
                        hospitalId.setValue(doctorPatientGroup.getHospitalId() + "");
                        appDefinedDataList.add(hospitalId);
                        //群组类型
                        AppDefinedData title = new AppDefinedData();
                        title.setKey("title");
                        title.setValue(ImGroupType.ASSISTANT.getCode());
                        appDefinedDataList.add(title);
                        //是否删除
                        AppDefinedData is_binding = new AppDefinedData();
                        is_binding.setKey("is_binding");
                        is_binding.setValue(CodeEnum.NO.getCode());
                        appDefinedDataList.add(is_binding);
                        groupRequest.setAppDefinedData(appDefinedDataList);

                        // 填充群组人员信息
                        List<Member> aMemberList = new ArrayList<>();
                        Member imDoctorMember = new Member(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getDoctorId(), null, "Owner");
                        aMemberList.add(imDoctorMember);
                        Member imAssistantMember = new Member(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getAssistantId(), null, "Member");
                        aMemberList.add(imAssistantMember);
                        groupRequest.setMemberList(aMemberList);
                        R<JSONObject> group = remoteImService.createGroup(groupRequest);
                        if (Constants.FAIL.equals(group.getCode())) {
                            // 腾讯IM添加失败时 手动回滚
//                            throw new ServiceException("添加群组失败");
                            JSONObject jsonObject = group.getData();
                            Integer errorCode = jsonObject.getInteger("ErrorCode");
                            if (errorCode == 10037) {
                                throw new ServiceException("医生创建和加入的群组数量超过了限制，无法创建，请联系医院管理员！");
                            }
                            if (errorCode == 10058) {
                                throw new ServiceException("腾讯IM版本群组创建上限，无法创建，请联系医院管理员！");
                            }
                            if (errorCode == 10036) {
                                throw new ServiceException("创建的音视频聊天室数量超过限制，无法创建，请联系医院管理员！");
                            }
                        }
                        //创建会话消息
                        JSONObject data = new JSONObject();
                        data.put("text", "会话创建成功");
                        sendSessionMsg(doctorPatientGroup.getId() + "", data, TencentyunImConstants.TIM_TEXT_ELEM, false);
                    } else {
                        BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
                        busDoctorPatientGroup.setId(assistantGroup);
                        busDoctorPatientGroup.setDisable(YesNoEnum.NO.getCode());
                        busDoctorPatientGroup.setUpdateTime(DateUtils.getNowDate());
                        busDoctorPatientGroupMapper.updateById(busDoctorPatientGroup);
                        //im修改
                        ImGroupRequest request = new ImGroupRequest();
                        request.setGroupId(busDoctorPatientGroup.getId() + "");
                        List<AppDefinedData> appDefinedDataList = new ArrayList<AppDefinedData>();
                        AppDefinedData appDefinedData = new AppDefinedData();
                        appDefinedData.setKey("is_binding");
                        appDefinedData.setValue(CodeEnum.NO.getCode());
                        appDefinedDataList.add(appDefinedData);
                        request.setAppDefinedData(appDefinedDataList);
                        R<Boolean> baseInfo = remoteImService.modifyGroupBaseInfo(request);
                        if (Constants.FAIL.equals(baseInfo.getCode())) {
                            // 腾讯IM添加失败时 手动回滚
                            throw new ServiceException("填充群组信息失败");
                        }
                        JSONObject jsonObject = new JSONObject();
                        JSONObject data = new JSONObject();
                        data.put("type", CustomMsgConstants.OPEN_SESSION);
                        jsonObject.put("data", data);
                        sendSessionMsg(assistantGroup + "", jsonObject, TencentyunImConstants.TIM_CUSTOM_ELEM, true);
                    }
                } else {
                    for (BusDoctorPatientGroup g : patientGroups) {
                        busImGroupMemberMapper.delete(new LambdaQueryWrapper<BusImGroupMember>()
                                .eq(BusImGroupMember::getGroupId, g.getId())
                                .eq(BusImGroupMember::getPersonnelId, d.getAssistantId())
                                .eq(BusImGroupMember::getRole, ImMemberEnum.DOCTOR.getCode()));
                        R<Boolean> exitGroup = remoteImService.exitGroup(g.getId() + "", TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getAssistantId(), "");
                        if (Constants.FAIL.equals(exitGroup.getCode()) || !(boolean) exitGroup.getData()) {
                            throw new ServiceException("更新im群组人员失败");
                        }
                    }
                    //医生医助处理
                    Long assistantGroup = busDoctorPatientGroupMapper.getDoctorAndAssistantGroup(d.getDoctorId(), d.getAssistantId(), d.getHospitalId());
                    if (Objects.nonNull(assistantGroup)) {
                        BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
                        busDoctorPatientGroup.setId(assistantGroup);
                        busDoctorPatientGroup.setDisable(YesNoEnum.YES.getCode());
                        busDoctorPatientGroup.setUpdateTime(DateUtils.getNowDate());
                        busDoctorPatientGroupMapper.updateById(busDoctorPatientGroup);
                        //im修改
                        ImGroupRequest request = new ImGroupRequest();
                        request.setGroupId(busDoctorPatientGroup.getId() + "");
                        List<AppDefinedData> appDefinedDataList = new ArrayList<AppDefinedData>();
                        AppDefinedData appDefinedData = new AppDefinedData();
                        appDefinedData.setKey("is_binding");
                        appDefinedData.setValue(CodeEnum.YES.getCode());
                        appDefinedDataList.add(appDefinedData);
                        request.setAppDefinedData(appDefinedDataList);
                        R<Boolean> baseInfo = remoteImService.modifyGroupBaseInfo(request);
                        if (Constants.FAIL.equals(baseInfo.getCode())) {
                            // 腾讯IM添加失败时 手动回滚
                            throw new ServiceException("填充群组信息失败");
                        }
                        JSONObject jsonObject = new JSONObject();
                        JSONObject data = new JSONObject();
                        data.put("type", CustomMsgConstants.CLOSE_SESSION);
                        jsonObject.put("data", data);
                        sendSessionMsg(assistantGroup + "", jsonObject, TencentyunImConstants.TIM_CUSTOM_ELEM, true);
                    }
                }
            }
        } catch (Exception e) {
            log.error("医助修改医生群组修改失败 e={}", e.getMessage(), e);
        }
    }

    /**
     * 发送开启或关闭会话消息
     *
     * @param groupId     群组id
     * @param jsonObject  消息内容
     * @param msgType     消息类型
     * @param isSendIm    是否发送im消息
     */
    private void sendSessionMsg(String groupId, JSONObject jsonObject, String msgType, boolean isSendIm) {
        //发送im消息
        if (isSendIm) {
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            int i = (int) ((Math.random() * 9 + 1) * 100000);
            sendMessageRequest.setRandom(i + "");
            sendMessageRequest.setGroupId(groupId);
            sendMessageRequest.setFrom_Account(TencentyunImConstants.ADMINISTRATOR);
            List<MsgBody> msgBodyList = new ArrayList<MsgBody>();
            MsgBody msgBody = new MsgBody();
            msgBody.setMsgType(msgType);
            MsgContent msgContent = new MsgContent();
            msgContent.setData(jsonObject.getJSONObject("data").toJSONString());
            msgBody.setMsgContent(msgContent);
            msgBodyList.add(msgBody);
            sendMessageRequest.setMsgBody(msgBodyList);
            R<Boolean> groupMsg = remoteImService.sendGroupMsg(sendMessageRequest);
            if (Constants.FAIL.equals(groupMsg.getCode())) {
                log.error("发送im消息失败");
            }
        }

        //创建会话消息
        BusCommunicationMessage communicationMessage = new BusCommunicationMessage();
        communicationMessage.setType(msgType);
        communicationMessage.setPayload(jsonObject.toJSONString());
        communicationMessage.setGroupId(groupId);
        communicationMessage.setConversationType("GROUP");
        communicationMessage.setFrom(TencentyunImConstants.ADMINISTRATOR);
        communicationMessage.setFlow("out");
        communicationMessage.setTime(System.currentTimeMillis());
        communicationMessage.setStatus("success");
        communicationMessage.setPriority("Normal");
        communicationMessage.setNick("administrator");
        communicationMessage.setIsPeerRead("true");
        communicationMessage.setCreateTime(DateUtils.getNowDate());
        busCommunicationMessageMapper.insertIgnoreNull(communicationMessage);
    }

    @Override
    public void createDoctorAndAssistantGroup(List<BusFiveAssistantDoctor> assistantDoctors) {
        //医生和医助新增成功群组
        List<String> groupIds = new ArrayList<>();
        //医生图文问诊新增医助角色成功群组
        List<Map<String, String>> assistantGroupIds = new ArrayList<>();
        try {
            for (BusFiveAssistantDoctor d : assistantDoctors) {
                BusDoctorPatientGroup doctorPatientGroup = new BusDoctorPatientGroup();
                doctorPatientGroup.setDoctorId(d.getDoctorId());
                doctorPatientGroup.setType(ImGroupType.ASSISTANT.getCode());
                doctorPatientGroup.setCreateTime(DateUtils.getNowDate());
                doctorPatientGroup.setHospitalId(d.getHospitalId());
                busDoctorPatientGroupMapper.insert(doctorPatientGroup);
                List<BusImGroupMember> imGroupMemberList = new ArrayList<>();
                BusImGroupMember doctorMember = new BusImGroupMember(d.getDoctorId(), doctorPatientGroup.getId(),
                        ImMemberEnum.DOCTOR.getCode(), "Owner");
                imGroupMemberList.add(doctorMember);
                BusImGroupMember assistantMember = new BusImGroupMember(d.getAssistantId(), doctorPatientGroup.getId(),
                        ImMemberEnum.DOCTOR.getCode(), "Member");
                imGroupMemberList.add(assistantMember);
                busImGroupMemberMapper.batchInsert(imGroupMemberList);
                //创建群组并导入人员
                ImGroupRequest imGroupRequest = new ImGroupRequest();
                // 填充im群组信息
                imGroupRequest.setType(TencentyunImConstants.IM_GROUP_TYPE);
                imGroupRequest.setGroupId(String.valueOf(doctorPatientGroup.getId()));
                imGroupRequest.setName("医生医助聊天组");
                imGroupRequest.setOwner_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getDoctorId());
                List<AppDefinedData> appDefinedDataList = new ArrayList<>();
                //医院id
                AppDefinedData hospitalId = new AppDefinedData();
                hospitalId.setKey("hospital_id");
                hospitalId.setValue(d.getHospitalId() + "");
                appDefinedDataList.add(hospitalId);
                //群组类型
                AppDefinedData title = new AppDefinedData();
                title.setKey("title");
                title.setValue(ImGroupType.ASSISTANT.getCode());
                appDefinedDataList.add(title);
                //是否删除
                AppDefinedData is_binding = new AppDefinedData();
                is_binding.setKey("is_binding");
                is_binding.setValue(CodeEnum.NO.getCode());
                appDefinedDataList.add(is_binding);
                imGroupRequest.setAppDefinedData(appDefinedDataList);
                // 填充群组人员信息
                List<Member> memberList = new ArrayList<>();
                Member imDoctorMember = new Member(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getDoctorId(), null, "Owner");
                memberList.add(imDoctorMember);
                Member imAssistantMember = new Member(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getAssistantId(), null, "Member");
                memberList.add(imAssistantMember);
                imGroupRequest.setMemberList(memberList);
                imGroupRequest.setDelFlag(CodeEnum.NO.getCode());
                R<JSONObject> group = remoteImService.createGroup(imGroupRequest);
                if (Constants.FAIL.equals(group.getCode())) {
                    //im群组创建失败异常返回
                    JSONObject jsonObject = group.getData();
                    Integer errorCode = jsonObject.getInteger("ErrorCode");
                    if (errorCode == 10037) {
                        throw new ServiceException("医生创建和加入的群组数量超过了限制，无法创建，请联系医院管理员！");
                    }
                    if (errorCode == 10058) {
                        throw new ServiceException("腾讯IM版本群组创建上限，无法创建，请联系医院管理员！");
                    }
                    if (errorCode == 10036) {
                        throw new ServiceException("创建的音视频聊天室数量超过限制，无法创建，请联系医院管理员！");
                    }
                }
                //图文问诊包处理
                List<Long> progressOrder = busDoctorPatientGroupMapper.getInProgressOrder(d.getDoctorId(), d.getHospitalId());
                for (Long p : progressOrder) {
                    //先删除原有群组医助
                    busImGroupMemberMapper.delete(new LambdaQueryWrapper<BusImGroupMember>()
                            .eq(BusImGroupMember::getGroupId, p)
                            .eq(BusImGroupMember::getRole, ImMemberEnum.DOCTOR.getCode()));
                    // 查询是否有相同数据 防止脏数据
                    List<BusImGroupMember> imGroupMemberQueryList = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                            .eq(BusImGroupMember::getGroupId, p)
                            .eq(BusImGroupMember::getPersonnelId, d.getAssistantId())
                            .ne(BusImGroupMember::getRole, ImMemberEnum.DOCTOR.getCode()));
                    if (imGroupMemberQueryList.isEmpty()) {
                        BusImGroupMember imGroupMember = new BusImGroupMember();
                        imGroupMember.setGroupId(p);
                        imGroupMember.setRole(ImMemberEnum.DOCTOR.getCode());
                        imGroupMember.setGroupRole("Member");
                        imGroupMember.setPersonnelId(d.getAssistantId());
                        imGroupMember.setIsInto(YesNoEnum.YES.getCode());
                        imGroupMember.setCreateTime(DateUtils.getNowDate());
                        busImGroupMemberMapper.insert(imGroupMember);
                    }
                    //im导入医助
                    List<Member> assistantMemberList = new ArrayList<>();
                    Member member = new Member();
                    member.setRole("Member");
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + d.getAssistantId());
                    assistantMemberList.add(member);
                    ImGroupRequest groupRequest = new ImGroupRequest();
                    groupRequest.setGroupId(p + "");
                    groupRequest.setMemberList(assistantMemberList);
                    R<Boolean> importGroupMember = remoteImService.importGroupMember(groupRequest);
                    if (Constants.FAIL.equals(importGroupMember.getCode()) || !(boolean) importGroupMember.getData()) {
                        throw new ServiceException("更新im群组人员失败");
                    }
                    Map<String, String> map = new HashMap<>();
                    map.put("groupId", p + "");
                    map.put("assistantId", d.getAssistantId() + "");
                    assistantGroupIds.add(map);
                }
                groupIds.add(doctorPatientGroup.getId() + "");

            }
        } catch (Exception e) {
            //回滚im群组创建
            for (String g : groupIds) {
                remoteImService.destroyGroup(g);
            }
            for (Map<String, String> a : assistantGroupIds) {
                R<Boolean> exitGroup = remoteImService.exitGroup(a.get("groupId"), TencentyunImConstants.DOCTOR_IM_ACCOUNT + a.get("assistantId"), "");
                if (Constants.FAIL.equals(exitGroup.getCode()) || !(boolean) exitGroup.getData()) {
                    log.error("图文问诊数据回滚失败 e={}", e.getMessage());
                }
            }
            log.error("医助关联医生群组创建失败 e={}", e.getMessage());
        }
    }

    @Override
    public void renewalDoctorAndAssistantGroup(BusDoctorPatientGroup busDoctorPatientGroup) {
        BusImGroupMember imGroupMember = busImGroupMemberMapper.selectOne(new LambdaQueryWrapper<BusImGroupMember>()
                .eq(BusImGroupMember::getGroupId, busDoctorPatientGroup.getId())
                .ne(BusImGroupMember::getPersonnelId, busDoctorPatientGroup.getDoctorId())
                .eq(BusImGroupMember::getRole, ImMemberEnum.DOCTOR.getCode()).last("limit 1"));
        BusFiveAssistantDoctor fiveAssistantDoctor = fiveAssistantDoctorMapper.selectOne(new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                .eq(BusFiveAssistantDoctor::getHospitalId, busDoctorPatientGroup.getHospitalId())
                .eq(BusFiveAssistantDoctor::getDoctorId, busDoctorPatientGroup.getDoctorId()));
        if (Objects.isNull(imGroupMember) && Objects.nonNull(fiveAssistantDoctor)) {
            BusDoctorVO doctorVO = busDoctorMapper.selectDoctorInfo(fiveAssistantDoctor.getHospitalId(), fiveAssistantDoctor.getAssistantId());
            // 查询是否有相同数据 防止脏数据
            List<BusImGroupMember> imGroupMemberQueryList = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                    .eq(BusImGroupMember::getGroupId, busDoctorPatientGroup.getId())
                    .eq(BusImGroupMember::getPersonnelId, fiveAssistantDoctor.getAssistantId())
                    .ne(BusImGroupMember::getRole, doctorVO.getRole()));
            if (imGroupMemberQueryList.isEmpty()) {
                BusImGroupMember groupMember = new BusImGroupMember();
                groupMember.setGroupId(busDoctorPatientGroup.getId());
                groupMember.setRole(doctorVO.getRole());
                groupMember.setGroupRole("Member");
                groupMember.setPersonnelId(fiveAssistantDoctor.getAssistantId());
                groupMember.setIsInto(YesNoEnum.YES.getCode());
                groupMember.setCreateTime(DateUtils.getNowDate());
                busImGroupMemberMapper.insert(groupMember);
            }
            //im导入医助
            List<Member> assistantMemberList = new ArrayList<>();
            Member member = new Member();
            member.setRole("Member");
            member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + fiveAssistantDoctor.getAssistantId());
            assistantMemberList.add(member);
            ImGroupRequest groupRequest = new ImGroupRequest();
            groupRequest.setGroupId(busDoctorPatientGroup.getId() + "");
            groupRequest.setMemberList(assistantMemberList);
            R<Boolean> importGroupMember = remoteImService.importGroupMember(groupRequest);
            if (Constants.FAIL.equals(importGroupMember.getCode()) || !(boolean) importGroupMember.getData()) {
                log.error("更新im群组失败 e={}", importGroupMember.getData());
            }
        } else {
            //先删除原有群组医助
            if (!Objects.isNull(imGroupMember)) {
                busImGroupMemberMapper.delete(new LambdaQueryWrapper<BusImGroupMember>()
                        .ne(BusImGroupMember::getPersonnelId, busDoctorPatientGroup.getDoctorId())
                        .eq(BusImGroupMember::getGroupId, busDoctorPatientGroup.getId())
                        .ne(BusImGroupMember::getRole, ImMemberEnum.PATIENT.getCode()));
            }
            if (!Objects.isNull(fiveAssistantDoctor)) {
                BusDoctorVO doctorVO = busDoctorMapper.selectDoctorInfo(fiveAssistantDoctor.getHospitalId(), fiveAssistantDoctor.getAssistantId());
                // 查询是否有相同数据 防止脏数据
                List<BusImGroupMember> imGroupMemberQueryList = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                        .eq(BusImGroupMember::getGroupId, busDoctorPatientGroup.getId())
                        .eq(BusImGroupMember::getPersonnelId, fiveAssistantDoctor.getAssistantId())
                        .ne(BusImGroupMember::getRole, doctorVO.getRole()));
                if (imGroupMemberQueryList.isEmpty()) {
                    BusImGroupMember groupMember = new BusImGroupMember();
                    groupMember.setGroupId(busDoctorPatientGroup.getId());
                    groupMember.setRole(doctorVO.getRole());
                    groupMember.setGroupRole("Member");
                    groupMember.setPersonnelId(fiveAssistantDoctor.getAssistantId());
                    groupMember.setIsInto(YesNoEnum.YES.getCode());
                    groupMember.setCreateTime(DateUtils.getNowDate());
                    busImGroupMemberMapper.insert(groupMember);
                }
                //im导入医助
                List<Member> assistantMemberList = new ArrayList<>();
                Member member = new Member();
                member.setRole("Member");
                member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + fiveAssistantDoctor.getAssistantId());
                assistantMemberList.add(member);
                ImGroupRequest groupRequest = new ImGroupRequest();
                groupRequest.setGroupId(busDoctorPatientGroup.getId() + "");
                groupRequest.setMemberList(assistantMemberList);
                R<Boolean> importGroupMember = remoteImService.importGroupMember(groupRequest);
                if (Constants.FAIL.equals(importGroupMember.getCode()) || !(boolean) importGroupMember.getData()) {
                    log.error("更新im群组失败 e={}", importGroupMember.getData());
                }
            }
        }
    }

    @Override
    public void deleteDoctorAndAssistantGroup(Long assistantId) {
        //查询医生和医助角色
        List<BusImGroupMember> imGroupMemberList = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                .eq(BusImGroupMember::getPersonnelId, assistantId)
                .eq(BusImGroupMember::getRole, ImMemberEnum.DOCTOR.getCode()));
        List<Long> groupIds = imGroupMemberList.stream().map(BusImGroupMember::getGroupId).collect(Collectors.toList());
        //查询跟医助关联的群组有哪些
        List<BusDoctorPatientGroup> patientGroupList = busDoctorPatientGroupMapper.selectList(new LambdaQueryWrapper<BusDoctorPatientGroup>()
                .in(BusDoctorPatientGroup::getId, groupIds)
                .eq(BusDoctorPatientGroup::getType, ImGroupType.ASSISTANT.getCode()));
        //查询医生医助关联
        List<Long> ids = patientGroupList.stream().map(BusDoctorPatientGroup::getId).collect(Collectors.toList());
        busDoctorPatientGroupMapper.delete(new LambdaQueryWrapper<BusDoctorPatientGroup>()
                .in(BusDoctorPatientGroup::getId, ids));
        for (Long id : ids) {
            R<Boolean> objectR = remoteImService.destroyGroup(id + "");
            if (Constants.FAIL.equals(objectR.getCode()) || !(boolean) objectR.getData()) {
                log.error("删除群组失败 id={}", id);
            }
        }
    }

    @Override
    public void signOutWorkGroup(BusFiveGroupMemberDTO busFiveGroupMemberDto) {
        //查询跟workGroupId相关联所有服务包
        List<BusFiveServicePack> fiveServicePackList = busFiveServicePackMapper.selectList(
                new LambdaQueryWrapper<BusFiveServicePack>()
                        .eq(BusFiveServicePack::getWorkingGroupId, busFiveGroupMemberDto.getWorkGroupId())
                        .eq(BusFiveServicePack::getWhetherDelete, CodeEnum.NO.getCode()));
        if (log.isDebugEnabled()) {
            log.debug("服务包详情 info={}", JSON.toJSONString(fiveServicePackList));
        }
        List<Long> idsList = fiveServicePackList.stream().map(BusFiveServicePack::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(idsList)) {
            //查询所有关联群组
            List<BusDoctorPatientGroup> busDoctorPatientGroups = busDoctorPatientGroupMapper.selectList(
                    new LambdaQueryWrapper<BusDoctorPatientGroup>()
                            .in(BusDoctorPatientGroup::getServiceId, idsList)
                            .eq(BusDoctorPatientGroup::getType, CodeEnum.YES.getCode()));
            if (log.isDebugEnabled()) {
                log.debug("关联群组info={}", JSON.toJSONString(busDoctorPatientGroups));
            }
            List<BusDoctorPatientGroup> groupList = new ArrayList<>();
            for (BusDoctorPatientGroup g : busDoctorPatientGroups) {
                //查询订单信息 只更新激活中的
                List<BusFiveServicePackOrder> packOrderList = busFiveServicePackOrderMapper.selectList(new LambdaQueryWrapper<BusFiveServicePackOrder>()
                        .eq(BusFiveServicePackOrder::getServicePackId, g.getServiceId())
                        .eq(BusFiveServicePackOrder::getHospitalId, g.getHospitalId())
                        .eq(BusFiveServicePackOrder::getDoctorId, g.getDoctorId())
                        .eq(BusFiveServicePackOrder::getPatientId, g.getPatientId())
                        .eq(BusFiveServicePackOrder::getFamilyId, g.getFamilyId())
                        .eq(BusFiveServicePackOrder::getStatus, ServicePackOrderStatusEnum.ACTIVATED.getCode()));
                if (CollectionUtil.isNotEmpty(packOrderList)) {
                    groupList.add(g);
                }
            }
            //删除本地群组人员
            groupList.forEach(g -> {
                busImGroupMemberMapper.delete(new LambdaQueryWrapper<BusImGroupMember>()
                        .eq(BusImGroupMember::getGroupId, g.getId())
                        .eq(BusImGroupMember::getPersonnelId, busFiveGroupMemberDto.getMemberId())
                        .eq(BusImGroupMember::getRole, busFiveGroupMemberDto.getType()));
                String member = "";
//                ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(busFiveGroupMemberDto.getType());
                member = TencentyunImConstants.DOCTOR_IM_ACCOUNT + busFiveGroupMemberDto.getMemberId();
//                switch (typeByValue) {
//                    case DOCTOR:
//                        member = TencentyunImConstants.DOCTOR_IM_ACCOUNT + busFiveGroupMemberDto.getMemberId();
//                        break;
//                    case PSYCHOLOGY_CONSULT:
//                        member = TencentyunImConstants.PHARMACIST_IM_ACCOUNT + busFiveGroupMemberDto.getMemberId();
//                        break;
//                    default:
//                        member = TencentyunImConstants.PHYSICIAN_IM_ACCOUNT + busFiveGroupMemberDto.getMemberId();
//                        break;
//                }
                R<Boolean> exitGroup = remoteImService.exitGroup(g.getId() + "", member, "");
                log.debug("退出群组返回值,msg={}", exitGroup.getData());
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean againCreateGroup(BusDoctorPatientGroup busDoctorPatientGroup) {
        log.debug("服务包群组信息 group={}", busDoctorPatientGroup);
        BusDoctorPatientGroup patientGroup = this.queryGroup(busDoctorPatientGroup);
        if ((!Objects.isNull(patientGroup) && CodeEnum.YES.getCode().equals(patientGroup.getDelFlag()))) {
            //重新创建群组并激活
            this.activateGroup(patientGroup);
            //更换人员
            return this.localPersonnelChanges(patientGroup);
        }
        return false;
    }

    /**
     * 添加群组人员
     *
     * @param groupUserIds     添加人员
     * @param groupId          群组id
     */
    private void addMember(List<JSONObject> groupUserIds, Long groupId) {
        remoteImService.exitGroup(groupId + "", groupUserIds.toString(), "");
        List<Member> memberList = new ArrayList<>();
        //添加人员
        if (CollectionUtil.isNotEmpty(groupUserIds)) {
            for (JSONObject json : groupUserIds) {
                if ("Owner".equals(json.getString("groupRole"))) {
                    continue;
                }
                Member member = new Member();
                member.setRole(CodeEnum.NO.getCode().equals(json.getString("groupRole")) ? "Member" : "Admin");
                member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + json.getString("memberId"));
                if (!ImMemberEnum.PATIENT.getCode().equals(json.getString("role"))) {
                    R<Boolean> returnData = remoteImService.accountCheck(member.getMember_Account());
                    log.debug("member={}", member);
                    if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
                        remoteImService.accountImport(member.getMember_Account(), json.getString("memberName"), null);
                    }
                    memberList.add(member);
                }
            }
        }
        ImGroupRequest imGroupRequest = new ImGroupRequest();
        imGroupRequest.setGroupId(groupId + "");
        imGroupRequest.setMemberList(memberList);
        remoteImService.importGroupMember(imGroupRequest);
    }

    /**
     * 发送信息到服务群组
     */
    private boolean sendGeneralMsg(Long groupId, String msgType, JSONObject data) {
        try {
            //查询群组信息
            BusDoctorPatientGroup patientGroup = busDoctorPatientGroupMapper.selectById(groupId);
            if (Objects.isNull(patientGroup)) {
                log.error("查询群组为空");
                return false;
            }
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            int i = (int) ((Math.random() * 9 + 1) * 100000);
            sendMessageRequest.setRandom(i + "");
            sendMessageRequest.setGroupId(patientGroup.getId() + "");
            sendMessageRequest.setFrom_Account(TencentyunImConstants.ADMINISTRATOR);
            List<MsgBody> msgBodyList = new ArrayList<MsgBody>();
            MsgBody msgBody = new MsgBody();
            msgBody.setMsgType(msgType);
            MsgContent msgContent = new MsgContent();
            msgContent.setData(data.toJSONString());
            msgBody.setMsgContent(msgContent);
            msgBodyList.add(msgBody);
            sendMessageRequest.setMsgBody(msgBodyList);
            R<Boolean> groupMsg = remoteImService.sendGroupMsg(sendMessageRequest);
            if (Constants.FAIL.equals(groupMsg.getCode())) {
                return false;
            }
            //添加消息记录
            insertMsg(data, groupId, msgType);
            return true;
        } catch (Exception e) {
            log.error("发送信息失败 e={}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 插入消息
     *
     * @param jsonObject 消息内容
     * @param groupId    群组id
     * @param msgType    消息类型
     */
    private void insertMsg(JSONObject jsonObject, Long groupId, String msgType) {
        JSONObject payload = new JSONObject();
        payload.put("data", jsonObject);
        BusCommunicationMessage communicationMessage = new BusCommunicationMessage();
        communicationMessage.setType(msgType);
        communicationMessage.setPayload(payload.toJSONString());
        communicationMessage.setGroupId(groupId + "");
        communicationMessage.setConversationType("GROUP");
        communicationMessage.setFrom(TencentyunImConstants.ADMINISTRATOR);
        communicationMessage.setFlow("out");
        communicationMessage.setTime(System.currentTimeMillis());
        communicationMessage.setStatus("success");
        communicationMessage.setPriority("Normal");
        communicationMessage.setNick("administrator");
//        communicationMessage.setIsPeerRead("true");
        communicationMessage.setCreateTime(DateUtils.getNowDate());
        busCommunicationMessageMapper.insertIgnoreNull(communicationMessage);
    }

    /**
     * 添加群组人员信息
     *
     * @param memberList     群成员信息
     * @param imGroupRequest im请求类
     */
    private void setMember(List<Member> memberList, ImGroupRequest imGroupRequest, BusFiveServicePackVO fiveServicePackVo, BusPatientFamily interrogator) {
        //查询出服务包角色有哪些 循环服务包
        BusFiveWorkGroupVO workGroupVo = fiveServicePackVo.getWorkGroupVo();
        log.debug("workGroupVo={}", workGroupVo);
        //添加群组群主信息
        List<AppMemberDefinedData> appMemberDefinedData = new ArrayList<>();
        AppMemberDefinedData memberDefinedData = new AppMemberDefinedData();
        memberDefinedData.setKey("menu_info");
        JSONObject menuInfo = new JSONObject();
        menuInfo.put("title", workGroupVo.getTitle());
        menuInfo.put("full_name", workGroupVo.getFullName());
        memberDefinedData.setValue(menuInfo.toJSONString());
        appMemberDefinedData.add(memberDefinedData);
        Member zMember = new Member(TencentyunImConstants.DOCTOR_IM_ACCOUNT + workGroupVo.getLeaderId(), appMemberDefinedData
                , "Owner");
        log.debug("zMember={}", zMember);
        R<Boolean> objectR = remoteImService.accountCheck(zMember.getMember_Account());
        if (Constants.SUCCESS.equals(objectR.getCode()) && !(boolean) objectR.getData()) {
            remoteImService.accountImport(zMember.getMember_Account(), workGroupVo.getFullName(), null);
        }
        memberList.add(zMember);
        //添加群成员信息
        List<BusFiveGroupMemberVO> memberVos = workGroupVo.getMemberVos();
        for (BusFiveGroupMemberVO g : memberVos) {
            if (CodeEnum.NO.getCode().equals(g.getStatus())) {
                continue;
            }
            List<AppMemberDefinedData> definedDataList = new ArrayList<>();
            AppMemberDefinedData definedData = new AppMemberDefinedData();
            definedData.setKey("menu_info");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("full_name", g.getMemberName());
            Member member = new Member();
            member.setRole(CodeEnum.NO.getCode().equals(g.getIdentity()) ? "Member" : "Admin");
            ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(g.getType());
            switch (Objects.requireNonNull(typeByValue)) {
                case DOCTOR:
                    jsonObject.put("title", ImMemberEnum.DOCTOR.getInfo());
                    definedData.setValue(jsonObject.toJSONString());
                    definedDataList.add(definedData);
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + g.getMemberId());
                    member.setAppMemberDefinedData(definedDataList);
                    break;
                case PSYCHOLOGY_CONSULT:
                    jsonObject.put("title", ImMemberEnum.PSYCHOLOGY_CONSULT.getInfo());
                    definedData.setValue(jsonObject.toJSONString());
                    definedDataList.add(definedData);
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + g.getMemberId());
                    member.setAppMemberDefinedData(definedDataList);
                    break;
                case NURSE:
                    jsonObject.put("title", ImMemberEnum.NURSE.getInfo());
                    definedData.setValue(jsonObject.toJSONString());
                    definedDataList.add(definedData);
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + g.getMemberId());
                    member.setAppMemberDefinedData(definedDataList);
                    break;
//                case ASSISTANT:
//                    jsonObject.put("title", ImMemberEnum.ASSISTANT.getInfo());
//                    definedData.setValue(jsonObject.toJSONString());
//                    definedDataList.add(definedData);
//                    member.setMember_Account(TencentyunImConstants.PHYSICIAN_IM_ACCOUNT + g.getMemberId());
//                    member.setAppMemberDefinedData(definedDataList);
//                    break;
                case HEALTH_MANAGER:
                    jsonObject.put("title", ImMemberEnum.HEALTH_MANAGER.getInfo());
                    definedData.setValue(jsonObject.toJSONString());
                    definedDataList.add(definedData);
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + g.getMemberId());
                    member.setAppMemberDefinedData(definedDataList);
                    break;
                case PSYCHOLOGICAL_CONSULTATION_TEACHER:
                    jsonObject.put("title", ImMemberEnum.PSYCHOLOGICAL_CONSULTATION_TEACHER.getInfo());
                    definedData.setValue(jsonObject.toJSONString());
                    definedDataList.add(definedData);
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + g.getMemberId());
                    member.setAppMemberDefinedData(definedDataList);
                    break;
                case REHABILITATION_SPECIALIST:
                    jsonObject.put("title", ImMemberEnum.REHABILITATION_SPECIALIST.getInfo());
                    definedData.setValue(jsonObject.toJSONString());
                    definedDataList.add(definedData);
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + g.getMemberId());
                    member.setAppMemberDefinedData(definedDataList);
                    break;
                case DIETITIAN:
                    jsonObject.put("title", ImMemberEnum.DIETITIAN.getInfo());
                    definedData.setValue(jsonObject.toJSONString());
                    definedDataList.add(definedData);
                    member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + g.getMemberId());
                    member.setAppMemberDefinedData(definedDataList);
                    break;
                default:
            }
            //导入im账号
            if (StringUtils.isNotEmpty(member.getMember_Account())) {
                R<Boolean> returnData = remoteImService.accountCheck(member.getMember_Account());
                if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) objectR.getData()) {
                    remoteImService.accountImport(member.getMember_Account(), g.getMemberName(), null);
                }
                memberList.add(member);
            }
        }
        //添加患者角色
        List<AppMemberDefinedData> definedDataList = new ArrayList<>();
        AppMemberDefinedData definedData = new AppMemberDefinedData();
        definedData.setKey("menu_info");
        JSONObject info = new JSONObject();
        info.put("title", ImMemberEnum.PATIENT.getInfo());
        info.put("full_name", interrogator.getName());
        definedData.setValue(info.toJSONString());
        definedDataList.add(definedData);
        Member pMember = new Member(TencentyunImConstants.PATIENT_IM_ACCOUNT + interrogator.getPatientId(), definedDataList, "Member");
        log.info("pMember={}", pMember);
        R<Boolean> returnData = remoteImService.accountCheck(pMember.getMember_Account());
        if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
            remoteImService.accountImport(pMember.getMember_Account(), "", null);
        }
        memberList.add(pMember);
        imGroupRequest.setMemberList(memberList);
    }

    /**
     * 批量插入
     *
     * @param fiveServicePackVo 服务包信息
     * @param groupId           群组id
     * @param familyId          就诊人id
     */
    public void batchInsert(BusFiveServicePackVO fiveServicePackVo, Long groupId, Long familyId) {
        //查询出服务包角色有哪些
        BusFiveWorkGroupVO workGroupVo = fiveServicePackVo.getWorkGroupVo();
        //添加医生信息
        List<BusImGroupMember> list = new ArrayList<>();
        BusImGroupMember zMember = new BusImGroupMember();
        zMember.setGroupId(groupId);
        zMember.setPersonnelId(workGroupVo.getLeaderId());
        zMember.setRole(ImMemberEnum.DOCTOR.getCode());
        zMember.setGroupRole("Owner");
        zMember.setIsInto(YesNoEnum.YES.getCode());
        zMember.setCreateTime(DateUtils.getNowDate());
        list.add(zMember);
        //添加群成员信息
        List<BusFiveGroupMemberVO> memberVos = workGroupVo.getMemberVos();
        for (BusFiveGroupMemberVO g : memberVos) {
            if (CodeEnum.NO.getCode().equals(g.getStatus())) {
                continue;
            }
            BusImGroupMember member = new BusImGroupMember();
            member.setIsInto(YesNoEnum.YES.getCode());
            member.setGroupId(groupId);
            member.setPersonnelId(g.getMemberId());
            member.setGroupRole(CodeEnum.NO.getCode().equals(g.getIdentity()) ? "Member" : "Admin");
            member.setCreateTime(DateUtils.getNowDate());
            ImMemberEnum typeByValue = ImMemberEnum.getTypeByCode(g.getType());
            switch (Objects.requireNonNull(typeByValue)) {
                case DOCTOR:
                    member.setRole(ImMemberEnum.DOCTOR.getCode());
                    break;
                case PSYCHOLOGY_CONSULT:
                    member.setRole(ImMemberEnum.PSYCHOLOGY_CONSULT.getCode());
                    break;
                case NURSE:
                    member.setRole(ImMemberEnum.NURSE.getCode());
                    break;
//                case ASSISTANT:
//                    member.setRole(ImMemberEnum.ASSISTANT.getCode());
//                    break;
                case HEALTH_MANAGER:
                    member.setRole(ImMemberEnum.HEALTH_MANAGER.getCode());
                    break;
                case PSYCHOLOGICAL_CONSULTATION_TEACHER:
                    member.setRole(ImMemberEnum.PSYCHOLOGICAL_CONSULTATION_TEACHER.getCode());
                    break;
                case REHABILITATION_SPECIALIST:
                    member.setRole(ImMemberEnum.REHABILITATION_SPECIALIST.getCode());
                    break;
                case DIETITIAN:
                    member.setRole(ImMemberEnum.DIETITIAN.getCode());
                    break;
                default:
            }
            list.add(member);
        }
        //添加患者角色
        BusImGroupMember pMember = new BusImGroupMember();
        pMember.setGroupId(groupId);
        pMember.setPersonnelId(familyId);
        pMember.setRole(ImMemberEnum.PATIENT.getCode());
        pMember.setGroupRole("Member");
        pMember.setIsInto(YesNoEnum.YES.getCode());
        pMember.setCreateTime(DateUtils.getNowDate());
        list.add(pMember);
        busImGroupMemberMapper.batchInsert(list);
    }

    /**
     * 重新创建激活群组
     *
     * @param doctorPatientGroup 聊天群组
     */
    private void activateGroup(BusDoctorPatientGroup doctorPatientGroup) {
        BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
        updateGroup.setId(doctorPatientGroup.getId());
        updateGroup.setDelFlag(CodeEnum.NO.getCode());
        busDoctorPatientGroupMapper.updateById(updateGroup);
        BusPatientFamily interrogator = busPatientFamilyMapper.selectById(doctorPatientGroup.getFamilyId());
        BusFiveServicePackVO fiveServicePackVo = busFiveServicePackService.queryPatientPackInfo(doctorPatientGroup.getServiceId());
        if (Objects.isNull(fiveServicePackVo)) {
            throw new ServiceException("服务包信息缺失");
        }
        ImGroupRequest imGroupRequest = new ImGroupRequest();
        // 填充im群组信息
        imGroupRequest.setType(TencentyunImConstants.IM_GROUP_TYPE);
        imGroupRequest.setGroupId(String.valueOf(doctorPatientGroup.getId()));
        if (StringUtils.lengthOfUTF8(fiveServicePackVo.getServicePackName()) > 30) {
            String s = StringUtils.substringString(fiveServicePackVo.getServicePackName(), 27);
            imGroupRequest.setName(s + "...");
        } else {
            imGroupRequest.setName(fiveServicePackVo.getServicePackName());
        }
        //添加自定义字段信息
        List<AppDefinedData> appDefinedDataList = new ArrayList<>();
        //医院id
        AppDefinedData hospitalId = new AppDefinedData();
        hospitalId.setKey("hospital_id");
        hospitalId.setValue(doctorPatientGroup.getHospitalId() + "");
        appDefinedDataList.add(hospitalId);
        //群组类型
        AppDefinedData title = new AppDefinedData();
        title.setKey("title");
        title.setValue(ImGroupType.SERVICE_PACK.getCode());
        appDefinedDataList.add(title);
        imGroupRequest.setAppDefinedData(appDefinedDataList);
        //是否删除
        AppDefinedData isBinding = new AppDefinedData();
        isBinding.setKey("is_binding");
        isBinding.setValue(CodeEnum.NO.getCode());
        appDefinedDataList.add(isBinding);
        imGroupRequest.setAppDefinedData(appDefinedDataList);

        // 填充群组人员信息
        List<Member> memberList = new ArrayList<>();
        //创建群主(医生)信息
        imGroupRequest.setOwner_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + doctorPatientGroup.getDoctorId());
        //创建群成员包括群主
        this.setMember(memberList, imGroupRequest, fiveServicePackVo, interrogator);
        imGroupRequest.setDelFlag(CodeEnum.YES.getCode());
        R<JSONObject> group = remoteImService.createGroup(imGroupRequest);
        if (Constants.FAIL.equals(group.getCode())) {
            log.warn("创建群组失败 group={}", group);
            JSONObject jsonObject = group.getData();
            Integer errorCode = jsonObject.getInteger("ErrorCode");
            if (errorCode == 10037) {
                throw new ServiceException("医生创建和加入的群组数量超过了限制，无法创建，请联系医院管理员！");
            }
            if (errorCode == 10058) {
                throw new ServiceException("腾讯IM版本群组创建上限，无法创建，请联系医院管理员！");
            }
            if (errorCode == 10036) {
                throw new ServiceException("创建的音视频聊天室数量超过限制，无法创建，请联系医院管理员！");
            }
        }
    }

    @Override
    public List<PatientImVO> getPatientImListV2(Long patientId, Long hospitalId, Long groupId) {
        // 查询该用户所有群组
        List<PatientImVO> groups = busDoctorPatientGroupMapper.listPatientGroupV2(hospitalId, patientId, groupId);
        if (CollectionUtil.isNotEmpty(groups)) {
            if (Objects.isNull(hospitalId)) {
                hospitalId = groups.get(0).getHospitalId();
            }
            //查询消息
            List<Long> groupIds = groups.stream().map(PatientImVO::getGroupId).filter(Objects::nonNull).collect(Collectors.toList());
            List<BusCommunicationMessage> msgList = busCommunicationMessageMapper.queryMsgByGroupId(groupIds);
            // 查就诊人服务包记录 用来统计剩余天数
            List<Long> serviceIdList = groups.stream().map(PatientImVO::getServiceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<Long> familyIdList = groups.stream().map(PatientImVO::getFamilyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            ServicePackPatientRecordQuery query = new ServicePackPatientRecordQuery();
            query.setHospitalId(hospitalId);
            query.setServicePackIdList(serviceIdList);
            query.setFamilyIdList(familyIdList);
            //查询服务包就诊记录
            List<ServicePackPatientRecord> recordList = servicePackPatientRecordMapper.selectActivityRecord(query);
            Map<String, List<ServicePackPatientRecord>> patientRecordMap = new HashMap<>(recordList.size());
            if (CollectionUtil.isNotEmpty(recordList)) {
                patientRecordMap.putAll(recordList.stream().collect(Collectors.groupingBy(s -> s.getServicePackId() + "_" + s.getFamilyId())));
            }
            //查询五师头像 排除问诊群组
            List<Long> serviceGroupIds = groups.stream().filter(g -> !ImGroupType.isInquiries(g.getType() + "")).map(PatientImVO::getGroupId).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, List<ConversationInfoVO>> photoMap = getDoctorPhotoMap(serviceGroupIds);
            List<Long> doctorIds = groups.stream().map(PatientImVO::getDoctorId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, List<BusDoctorConsultationVO>> doctorConsultationMap = getDoctorConsultationMap(hospitalId, doctorIds);
            List<Long> departmentIds = groups.stream().map(PatientImVO::getDepartmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

            Map<String, List<BusConsultationPackage>> consultationPackageMap = getConsultationPackageMap(hospitalId, doctorIds, familyIdList, departmentIds);
            Map<Long, BusConsultationOrder> consultationOrderMap = new HashMap<>(consultationPackageMap.size());
            if (MapUtils.isNotEmpty(consultationPackageMap)) {
                List<Long> consultationOrderIds = consultationPackageMap.values().stream().flatMap(List::stream).map(BusConsultationPackage::getOrderId).collect(Collectors.toList());
                consultationOrderMap.putAll(getConsultationOrderMap(consultationOrderIds));
            }
            //医师标题map
            Map<Long, SysDictData> physicianTitleMap = getPhysicianTitleMap();
            Map<String, List<BusFiveServicePackOrder>> servicePackOrderMap = getServicePackOrderMap(serviceIdList, familyIdList);
            //参数组装
            for (PatientImVO group : groups) {
                assembleImGroup(group, msgList, physicianTitleMap, photoMap, consultationPackageMap, consultationOrderMap, patientRecordMap, doctorConsultationMap, servicePackOrderMap);
            }
            groups.sort(Comparator.comparing(PatientImVO::getLastMessageTime).reversed());
        }
        return groups;
    }

    /**
     * 获取医生头像map
     *
     * @param serviceGroupIds 服务包群组id
     * @return 医生头像map
     */
    private Map<Long, List<ConversationInfoVO>> getDoctorPhotoMap(List<Long> serviceGroupIds) {
        List<ConversationInfoVO> photoList = Collections.emptyList();
        if (CollectionUtil.isNotEmpty(serviceGroupIds)) {
            photoList = busImGroupMemberMapper.getDoctorPhotoByGroupIds(serviceGroupIds);
        }
        Map<Long, List<ConversationInfoVO>> photoMap = new HashMap<>(photoList.size());
        if (CollectionUtil.isNotEmpty(photoList)) {
            photoMap.putAll(photoList.stream().collect(Collectors.groupingBy(ConversationInfoVO::getGroupId)));
        }
        return photoMap;
    }

    /**
     * 组装IM群组
     *
     * @param group                   患者群组Im信息
     * @param msgList                 消息列表
     * @param physicianTitleMap       医师职称
     * @param photoMap                医生头像map
     * @param consultationPackageMap  问诊包map
     * @param consultationOrderMap    问诊订单map
     * @param patientRecordMap        医生纠正记录map
     * @param doctorConsultationMap   医生问诊设置map
     * @param servicePackOrderMap     服务包订单map
     */
    private void assembleImGroup(PatientImVO group,
                                 List<BusCommunicationMessage> msgList,
                                 Map<Long, SysDictData> physicianTitleMap,
                                 Map<Long, List<ConversationInfoVO>> photoMap,
                                 Map<String, List<BusConsultationPackage>> consultationPackageMap,
                                 Map<Long, BusConsultationOrder> consultationOrderMap,
                                 Map<String, List<ServicePackPatientRecord>> patientRecordMap,
                                 Map<Long, List<BusDoctorConsultationVO>> doctorConsultationMap,
                                 Map<String, List<BusFiveServicePackOrder>> servicePackOrderMap) {
        List<BusCommunicationMessage> messagesList = msgList.stream().filter(msg -> msg.getGroupId().equals(String.valueOf(group.getGroupId()))).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(messagesList)) {
            group.setLastMessageTime(messagesList.get(0).getCreateTime());
        } else {
            group.setLastMessageTime(group.getCreateTime());
        }
        group.setMsgList(messagesList);
        //设置标题
        if (StringUtils.isNotEmpty(group.getTitle())) {
            SysDictData sysDictData = physicianTitleMap.get(Long.valueOf(group.getTitle()));
            if (sysDictData != null) {
                group.setTitle(sysDictData.getDictLabel());
            }
        }
        //头像组
        assembleGroupMemberPhotos(group, photoMap.get(group.getGroupId()));
        // 问诊订单、问诊包
        String key = group.getFamilyId() + "_" + group.getDoctorId() + "_" + group.getDepartmentId();
        assembleConsultationOrder(group, consultationPackageMap.get(key), consultationOrderMap);
        // 0-不是义诊
        group.setVolunteerClinic(0);
        if (ConsultationOrderPayTypeEnum.PATIENT_PAY.getStatus().equals(group.getPayType())
                && 0 == BigDecimal.ZERO.compareTo(group.getConsultationOrderAmount())
                && null == group.getServiceId() && !group.getVip()) {
            // 1-是义诊
            group.setVolunteerClinic(1);
        }
        if (ImGroupType.isServicePack(group.getType() + "")) {
            List<BusFiveServicePackOrder> servicePackOrders = servicePackOrderMap.get(group.getServiceId() + "_" + group.getFamilyId());
            if (CollectionUtil.isNotEmpty(servicePackOrders)) {
                group.setServicePkgName(servicePackOrders.get(0).getServicePackName());
            }
            // 如果是服务包才执行这个
            calcRemainingDays(group, patientRecordMap.get(group.getServiceId() + "_" + group.getFamilyId()));
        }
        //如果被标记为无法使用则返回次数为零
        if (Boolean.TRUE.equals(group.getMarkChanges())) {
            group.setTimes(0);
        }
        group.setDoctorConsultationList(doctorConsultationMap.get(group.getDoctorId()));
    }

    /**
     * 获取医生问诊设置
     *
     * @param hospitalId 医院id
     * @param doctorIds  医生id列表
     * @return 医生问诊设置
     */
    private Map<Long, List<BusDoctorConsultationVO>> getDoctorConsultationMap(Long hospitalId, List<Long> doctorIds) {
        Map<Long, List<BusDoctorConsultationVO>> doctorConsultationMap = new HashMap<>(doctorIds.size());
        if (CollectionUtil.isNotEmpty(doctorIds)) {
            List<BusDoctorConsultation> consultations = busDoctorConsultationMapper.queryByDoctorIds(hospitalId, doctorIds);
            if (CollectionUtil.isNotEmpty(consultations)) {
                doctorConsultationMap.putAll(consultations.stream().map(s -> {
                    BusDoctorConsultationVO vo = new BusDoctorConsultationVO();
                    BeanUtils.copyProperties(s, vo);
                    return vo;
                }).collect(Collectors.groupingBy(BusDoctorConsultationVO::getDoctorId)));
            }
        }
        return doctorConsultationMap;
    }

    /**
     * 组装群组成员头像
     *
     * @param group           患者群组信息
     * @param conversationList 群组中其他成员头像
     */
    private void assembleGroupMemberPhotos(PatientImVO group, List<ConversationInfoVO> conversationList) {
        List<String> photoList = Lists.newArrayList();
        photoList.add(group.getPhoto());
        //如果不是问诊群组，则需要将群组中的其他成员头像加入集合中
        if (!ImGroupType.isInquiries(group.getType() + "") && CollectionUtil.isNotEmpty(conversationList)) {
            photoList.addAll(conversationList.stream().map(ConversationInfoVO::getPhoto).filter(p -> !Objects.equals(p, group.getPhoto())).collect(Collectors.toList()));
        }
        group.setPhotos(photoList);
    }

    /**
     * 组装问诊订单信息
     *
     * @param group                患者群组信息
     * @param packages             问诊包列表
     * @param consultationOrderMap 问诊订单列表
     */
    private void assembleConsultationOrder(PatientImVO group, List<BusConsultationPackage> packages, Map<Long, BusConsultationOrder> consultationOrderMap) {
        group.setFree(false);
        group.setConsultationOrderAmount(BigDecimal.ZERO);
        group.setTimes(0);
        if (CollectionUtil.isEmpty(packages) || MapUtils.isEmpty(consultationOrderMap)) {
            return;
        }
        //获取问诊包的信息
        BusConsultationPackage consultationPackage = packages.get(0);
        if (Objects.isNull(consultationPackage)) {
            return;
        }
        if (ImGroupType.isInquiries(group.getType() + "")) {
            BigDecimal payAmount = Objects.nonNull(consultationPackage.getPayAmount()) ? consultationPackage.getPayAmount() : BigDecimal.ZERO;
            group.setFree(payAmount.compareTo(BigDecimal.ZERO) == 0);
        }
        Integer totalTimes = Objects.nonNull(consultationPackage.getTotalTimes()) ? consultationPackage.getTotalTimes() : 0;
        Integer useTimes = Objects.nonNull(consultationPackage.getUseTimes()) ? consultationPackage.getUseTimes() : 0;
        int remainingTimes = totalTimes - useTimes;
        BusConsultationOrder order = consultationOrderMap.get(consultationPackage.getOrderId());
        if (Objects.nonNull(order)) {
            group.setPayType(order.getPayType());
            group.setConsultationOrderAmount(order.getAmount());
            //问诊剩余次数
            if (ConsultationOrderStatusEnum.isInProgress(order.getStatus())
                    || ConsultationOrderStatusEnum.isInProgress(order.getVideoStatus())) {
                group.setTimes(Math.max(remainingTimes, 0));
            }
        }
    }

    /**
     * 计算剩余天数
     *
     * @param group            患者群组信息
     * @param recordList       服务包记录列表
     */
    private void calcRemainingDays(PatientImVO group, List<ServicePackPatientRecord> recordList) {
        int days = 0;

        if (CollectionUtil.isEmpty(recordList)) {
            group.setTimes(days);
            return;
        }
        List<ServicePackPatientRecord> activatedList = recordList.stream().filter(s -> ServicePackRecordStatusEnum.isActivated(s.getStatus())).collect(Collectors.toList());
        List<ServicePackPatientRecord> servingList = recordList.stream().filter(s -> ServicePackRecordStatusEnum.isServing(s.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(activatedList)) {
            //激活未使用直接相加
            days += activatedList.stream().filter(t -> t.getServicePackCycle() != null).mapToInt(ServicePackPatientRecord::getServicePackCycle).sum();
        }
        if (CollectionUtil.isNotEmpty(servingList)) {
            //理论上一个就诊人一个服务包只有一条在使用中, 避免未来调整直接for循环计算
            for (ServicePackPatientRecord serving : servingList) {
                if (Objects.nonNull(serving.getActualEffectiveTime()) && Objects.nonNull(serving.getServicePackCycle())) {
                    LocalDateTime effectiveStartTime = LocalDateTime.ofInstant(serving.getActualEffectiveTime().toInstant(), ZoneId.systemDefault());
                    LocalDateTime effectiveEndTime = getEffectiveEndTime(serving.getServicePackCycle(), effectiveStartTime);
                    long betweenSeconds = Duration.between(LocalDateTime.now(), effectiveEndTime).getSeconds();
                    double effDays = (double) betweenSeconds / (24 * 60 * 60);
                    if (effDays > serving.getServicePackCycle()) {
                        days += serving.getServicePackCycle();
                    } else if (effDays > 0) {
                        days += (int) Math.ceil(effDays);
                    }
                }
            }

        }
        group.setTimes(days);
    }

    /**
     * 获取生效结束时间
     *
     * @param servicePackCycle   服务包周期
     * @param effectiveStartTime 生效开始时间
     * @return 生效结束时间
     */
    private LocalDateTime getEffectiveEndTime(Integer servicePackCycle, LocalDateTime effectiveStartTime) {
        //默认增加天数，减去1秒，避免计算天数时，时间进位
        long plusSeconds = servicePackCycle * 24 * 60 * 60 - 1;
        if (effectiveStartTime.getHour() != 0
                || effectiveStartTime.getMinute() != 0
                || effectiveStartTime.getSecond() != 0) {
            LocalDateTime actEffectiveStartTime = effectiveStartTime.plusDays(1).withHour(0).withMinute(0).withSecond(0);
            plusSeconds = plusSeconds + Duration.between(effectiveStartTime, actEffectiveStartTime).getSeconds();
        }
        return effectiveStartTime.plusSeconds(plusSeconds);
    }

    /**
     * 设置对应的天数
     *
     * @param hospitalId
     * @param group
     */
    private void groupAddEndTimes(Long hospitalId, PatientImVO group) {
        try {
            // 构建订单参数
            BusFiveServicePackOrder busFiveServicePackOrder = createServicePackOrderByPatientImVO(group, hospitalId);
            //计算剩余时间-返还时间为毫秒
            Long endDayTime = busFiveServicePackOrderService.calculateEndTime(busFiveServicePackOrder);
            //有计算值则覆盖
            int endDays = 1;
            if (endDayTime != null && endDayTime > 0) {
                long compareTime = busFiveServicePackOrderService.getEndTimeOfDayWithTime(endDayTime).getTime() - System.currentTimeMillis();
                if (compareTime > 0) {
                    endDays = (int) (compareTime * 1.0d / (1000 * 60 * 60 * 24)) + 1;
                    // 如果是今天非0点0分0秒生效的，时间不算今天，减去一天，如果不是，跳过
                    if (busFiveServicePackOrderService.todayServing(busFiveServicePackOrder)) {
                        endDays = endDays - 1;
                    }
                }
                // 根据时间戳计算出对应的剩余天数
                group.setTimes(endDays);
                log.debug("患者{}修改对应的剩余天数为{}", group.getFamilyId(), endDays);
            }
            if (!group.getGroupStatus().equals(YesNoEnum.YES.getCode() + "")) {
                group.setTimes(YesNoEnum.NO.getCode());
            }
        } catch (Exception e) {
            log.error("计算剩余天数异常{},对应的群组记录为{}", e, group.getGroupId());
        }
    }

    /**
     * 创建订单对象根据vo
     *
     * @param group
     * @param hospitalId
     * @return
     */
    public BusFiveServicePackOrder createServicePackOrderByPatientImVO(PatientImVO group, Long hospitalId) {
        BusFiveServicePackOrder busFiveServicePackOrder = new BusFiveServicePackOrder();
        busFiveServicePackOrder.setFamilyId(group.getFamilyId());
        busFiveServicePackOrder.setHospitalId(hospitalId);
        busFiveServicePackOrder.setServicePackId(group.getServiceId());
        return busFiveServicePackOrder;
    }

    @Override
    public PatientImVO getPatientImInfoV2(Long groupId) {
        List<PatientImVO> imList = this.getPatientImListV2(null, null, groupId);
        if (CollectionUtil.isEmpty(imList)) {
            return null;
        }
        return imList.stream().findFirst().orElse(null);
    }

    /**
     * 更新im群组,发送对应的过期消息
     *
     * @param busFiveServicePackOrder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateImGroup(BusFiveServicePackOrder busFiveServicePackOrder, ServicePackPatientRecord record) {
        //查询该患者是否有其他服务包订单（激活未使用，已经正在使用中的）
        //使用中的已经修改过了，所以这里相当于需要查询激活未使用的
        List<BusFiveServicePackOrder> orderList = busFiveServicePackOrderService.queryServicePackOrderActiveAndService(busFiveServicePackOrder);
        // 查询群组ID - 沿用旧方式
        LambdaQueryWrapper<BusDoctorPatientGroup> queryWrapper =
                new LambdaQueryWrapper<BusDoctorPatientGroup>()
                        .eq(BusDoctorPatientGroup::getHospitalId, busFiveServicePackOrder.getHospitalId())
                        .eq(BusDoctorPatientGroup::getDoctorId, busFiveServicePackOrder.getDoctorId())
                        .eq(BusDoctorPatientGroup::getPatientId, busFiveServicePackOrder.getPatientId())
                        .eq(BusDoctorPatientGroup::getFamilyId, busFiveServicePackOrder.getFamilyId())
                        .eq(BusDoctorPatientGroup::getServiceId, busFiveServicePackOrder.getServicePackId())
                        .last("LIMIT 1");
        BusDoctorPatientGroup busDoctorPatientGroup = busDoctorPatientGroupMapper.selectOne(queryWrapper);
        log.debug("查询群组信息={}", busDoctorPatientGroup);
        if (CollectionUtil.isEmpty(orderList)) {
            if (busDoctorPatientGroup != null) {
                // 发送服务包到期消息，后续没有其他服务包已经激活的订单了
                Boolean r = expire(busDoctorPatientGroup.getId());
                log.debug("调用im接口,发送服务包到期消息:结果={}", r);
            }
            return;
        }
        // 有激活订单，但是却没有对应的im群组
        if (busDoctorPatientGroup == null) {
            //新建群组-先检查一下（自动激活），如果要生效的话
            busFiveServicePackOrderService.checkCreateImGroup("1", busFiveServicePackOrder);
        }
        // 订单中都没有激活的订单，需要设置未激活
        List<String> status =
                orderList.stream().map(BusFiveServicePackOrder::getStatus).collect(Collectors.toList());
        IBusImGroupMemberService busImGroupMemberService = SpringUtils.getBean(IBusImGroupMemberService.class);
        if (!status.contains(ServicePackOrderStatusEnum.ACTIVATED.getCode()) && busDoctorPatientGroup != null) {
            // 发送激活服务包消息-需要去激活了
            Boolean r = busImGroupMemberService.updateActivation(busDoctorPatientGroup.getId(), ServicePackImStatusEnum.TO_BE_ACTIVATED);
            log.debug("修改激活状态-需要激活:结果={}", r);
            return;
        }
        // 下一个续上-发送续购消息
        // 根据服务包订单往后的所有待服务的记录，计算服务包的到期时间
        Long aLong = calculateExpireTime(record);
        // 只修改对应的过期时间
        BusDoctorPatientGroup patientGroup = busDoctorPatientGroupMapper.selectOneByServicePack(busFiveServicePackOrder);
        Boolean r = busImGroupMemberService.renewal(patientGroup.getId(), null, DateUtils.parse_YYYY_MM_DD_HH_MM_SS(new Date(aLong)));
        log.debug("修改激活状态-激活-续购:结果={}", r);
    }

    /**
     * 根据订单更新im群组信息
     *
     * @param orderNo 服务包订单号
     */
    @Override
    public void updateServicePackIm(String orderNo) {
        // 查询服务包订单信息6
        BusFiveServicePackOrder busFiveServicePackOrder = busFiveServicePackOrderService.queryInfo(null, orderNo);
        log.debug("服务包订单查询结果：{}", busFiveServicePackOrder);
        if (busFiveServicePackOrder == null) {
            log.error("服务包订单{}不存在", orderNo);
            return;
        }
        // 查询群组ID
        // 校验是否已创建群组
        BusDoctorPatientGroup busDoctorPatientGroup = busDoctorPatientGroupMapper.selectOneByServicePack(busFiveServicePackOrder);
        log.debug("群组信息=" + busDoctorPatientGroup);
        if (busDoctorPatientGroup == null) {
            Long groupId = busFiveServicePackOrderService.checkCreateImGroup("1", busFiveServicePackOrder);
            busDoctorPatientGroup = busDoctorPatientGroupMapper.selectById(groupId);
        }
        // 查询正在服务中的服务包订单
        List<BusFiveServicePackOrder> busFiveServicePackOrders = busFiveServicePackOrderService.queryServicePackOrderActiveAndService(busFiveServicePackOrder);
        // 存在多个服务中的订单
        if (CollectionUtil.isNotEmpty(busFiveServicePackOrders)) {
            // 多个激活订单退款-只修改对应的结束时间
            busFiveServicePackOrderService.updateGroupEndTime(false, busDoctorPatientGroup.getId(), busFiveServicePackOrder);
        } else {
            //查询是否还有其他未激活的服务包
            List<ServicePackPatientRecord> recordList = servicePackPatientRecordMapper.selectActivityRecordNoServing(busFiveServicePackOrder);
            if (CollectionUtil.isNotEmpty(recordList)) {
                // 更新im群组-为未激活状态
                SpringUtils.getBean(IBusImGroupMemberService.class).updateActivation(busDoctorPatientGroup.getId(), ServicePackImStatusEnum.TO_BE_ACTIVATED);
                return;
            }
            //若没有其他未激活的服务包
            // 发送服务包退款消息
            refund(busDoctorPatientGroup.getId());
        }
    }

    @Override
    public List<PrivateChatImData> privateChatUserList(Long patientId, Long hospitalId) {

        List<PrivateChatImData> list = new ArrayList<>();

        BusCommunicationMessage req = new BusCommunicationMessage();
        req.setTo(String.valueOf(patientId));
        req.setHospitalId(hospitalId);
        List<BusCommunicationMessage> communicationMessageList = busCommunicationMessageMapper.groupByUserAndPatient(req);
        if (null == communicationMessageList || communicationMessageList.isEmpty()) {
            return list;
        }

        for (BusCommunicationMessage communicationMessage : communicationMessageList) {
            communicationMessage.setHospitalId(hospitalId);
            List<BusCommunicationMessage> latestMsgRecord = busCommunicationMessageMapper.findLatestMsgRecord(communicationMessage);
            PrivateChatImData privateChatImData = new PrivateChatImData();
            privateChatImData.setMsgList(latestMsgRecord);
            privateChatImData.setPatientId(Long.valueOf(communicationMessage.getTo()));
            privateChatImData.setFamilyId(communicationMessage.getFamilyId());
            BusPatientFamily patientFamily = busPatientFamilyMapper.selectById(communicationMessage.getFamilyId());
            if (null == patientFamily) {
                continue;
            }
            privateChatImData.setFamilyName(patientFamily.getName());

            if (null != latestMsgRecord && !latestMsgRecord.isEmpty()) {
                BusCommunicationMessage busCommunicationMessage = latestMsgRecord.get(0);

                FollowUpImWxDTO followUpImWxDTO = BusCommunicationMessage.deserializePayload(busCommunicationMessage.getPayload());
                privateChatImData.setFollowUpId(followUpImWxDTO.getFollowUpId());
                privateChatImData.setFollowUpName(followUpImWxDTO.getFollowUpName());
                privateChatImData.setDesc(followUpImWxDTO.getDesc());

                Boolean isAssistant = busCommunicationMessage.getIsAssistant();
                if (Boolean.FALSE.equals(isAssistant)) {
                    privateChatImData.setHospitalId(followUpImWxDTO.getHospitalId());
                    BusHospital busHospital = hospitalMapper.selectById(followUpImWxDTO.getHospitalId());
                    privateChatImData.setHospitalName(busHospital.getHospitalName());
                }
                privateChatImData.setId(latestMsgRecord.get(0).getId());
            }

            int unReadMsg = busCommunicationMessageMapper.countUnReadMsg(communicationMessage);
            privateChatImData.setUnreadCount(unReadMsg);

            list.add(privateChatImData);
        }

        list.sort(Comparator.comparing(PrivateChatImData::getId).reversed());

        return list;

    }

    private Map<Long, BusConsultationOrder> getConsultationOrderMap(List<Long> orderIds) {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BusConsultationOrder::getId, orderIds);
        List<BusConsultationOrder> list = busConsultationOrderMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(BusConsultationOrder::getId, Function.identity()));
    }

    private Map<String, List<BusConsultationPackage>> getConsultationPackageMap(Long hospitalId,
                                                                                List<Long> doctorIds,
                                                                                List<Long> familyIds,
                                                                                List<Long> departmentIds) {
        LambdaQueryWrapper<BusConsultationPackage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusConsultationPackage::getHospitalId, hospitalId);
        queryWrapper.in(BusConsultationPackage::getDoctorId, doctorIds);
        queryWrapper.in(BusConsultationPackage::getFamilyId, familyIds);
        queryWrapper.in(BusConsultationPackage::getDepartmentId, departmentIds);
        queryWrapper.orderByDesc(BusConsultationPackage::getCreateTime);
        List<BusConsultationPackage> list = busConsultationPackageMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        //更具familyId_doctorId_departmentId分组
        return list.stream().collect(Collectors.groupingBy(s -> s.getFamilyId() + "_" + s.getDoctorId() + "_" + s.getDepartmentId()));
    }

    /**
     * 获取医师职称字典数据
     *
     * @return 医师职称字典数据
     */
    private Map<Long, SysDictData> getPhysicianTitleMap() {
        SysDictData req = new SysDictData();
        req.setDictTypeList(Lists.newArrayList(SysDictTypeConstant.DOCTOR_TITLE, SysDictTypeConstant.PHARMACIST_TITLE, SysDictTypeConstant.NURSE_TILE));
        R<List<SysDictData>> result = remoteSysDictDataService.getDictList(req, SecurityConstants.INNER);
        if (Objects.isNull(result) || !result.isSuccess()) {
            log.error("远程调用获取医师职称字典数据失败:{}", result);
            return Collections.emptyMap();
        }
        return result.getData().stream().collect(Collectors.toMap(SysDictData::getDictCode, Function.identity()));
    }

    /**
     * 获取服务包订单分组列表
     *
     * @param serviceIds 服务包id集合
     * @param familyIds  就诊人id集合
     * @return 服务包订单分组列表
     */
    private Map<String, List<BusFiveServicePackOrder>> getServicePackOrderMap(List<Long> serviceIds,
                                                                              List<Long> familyIds) {
        if (CollectionUtil.isEmpty(serviceIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<BusFiveServicePackOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BusFiveServicePackOrder::getServicePackId, serviceIds);
        queryWrapper.in(BusFiveServicePackOrder::getFamilyId, familyIds);
        queryWrapper.isNotNull(BusFiveServicePackOrder::getPaymentTime);
        queryWrapper.orderByDesc(BusFiveServicePackOrder::getCreateTime);
        List<BusFiveServicePackOrder> list = busFiveServicePackOrderMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(s -> s.getServicePackId() + "_" + s.getFamilyId()));
    }


}
