<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.five.mapper.BusDoctorPatientGroupMapper">
    <resultMap id="BaseResultMap" type="com.puree.hospital.five.domain.vo.ConversationInfoVO">
        <result column="groupId" property="groupId"/>
        <result column="groupName" property="groupName"/>
        <result column="type" property="type"/>
        <result column="doctor_id" property="doctorId"/>
        <result column="department_id" property="departmentId"/>
        <result column="family_id" property="familyId"/>
        <result column="patient_id" property="patientId"/>
        <result column="service_id" property="serviceId"/>
        <result column="hospital_id" property="hospitalId"/>
        <result column="doctorName" property="doctorName"/>
        <result column="department_name" property="departmentName"/>
        <result column="familyName" property="familyName"/>
        <result column="sex" property="sex"/>
        <result column="photo" property="photo"/>
        <result column="date_of_birth" property="dateOfBirth"/>
        <result column="id_number" property="idCardNo"/>
        <result column="title" property="title"/>
        <result column="del_flag" property="delFlag"/>
        <result column="mark" property="mark"/>
        <result column="doctor_patient_id" property="doctorPatientId"/>
    </resultMap>
    <update id="updateGroupStatusById">
        update bus_doctor_patient_group
        set group_end_time = now(),
        group_start_time = null,
        group_status = #{groupStatus}
        where id = #{id}
    </update>

    <select id="selectPatientList" parameterType="java.lang.Long" resultType="com.puree.hospital.five.domain.vo.BusPatientVo">
        SELECT t1.id group_id,
               t1.family_id,
               t2.`name`,
               t2.sex,
               t2.date_of_birth,
               t2.patient_id
        FROM bus_doctor_patient_group t1
        LEFT JOIN bus_patient_family t2 ON t1.patient_id = t2.patient_id
            AND t1.family_id = t2.id
        WHERE t1.hospital_id = #{hospitalId}
          AND t1.type = '1'
          AND t1.doctor_id = #{doctorId}
          AND t1.group_status = '1'
          AND t1.service_id = #{id}
    </select>

    <select id="getConversation" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
            t1.id groupId,
            t1.doctor_id,
            t1.department_id,
            t1.family_id,
            t1.patient_id,
            t1.hospital_id,
            t1.service_id,
            t1.type,
            t1.del_flag,
            t1.group_status,
            t1.group_start_time,
            t1.group_end_time,
            t2.full_name doctorName,
            t3.department_name,
            t4.name familyName,
            t2.photo,
            t4.sex,
            t4.date_of_birth,
            t4.id_number,
            t5.dict_label title,
            t8.service_pack_name groupName,
            t9.diagnosis_archives
        FROM `bus_doctor_patient_group` t1
                 LEFT JOIN `bus_doctor` t2 ON t1.doctor_id = t2.id
                 LEFT JOIN `sys_dict_data` t5 ON t2.title = t5.dict_code
                 LEFT JOIN `bus_patient_family` t4 ON t1.family_id = t4.id
                 LEFT JOIN `bus_doctor_patient` t6 ON t1.doctor_id = t6.doctor_id AND t1.patient_id = t6.patient_id AND
                                                      t1.family_id = t6.family_id AND t1.hospital_id = t6.hospital_id
                 LEFT JOIN `bus_five_service_pack` t8 ON t1.service_id = t8.id
                 LEFT JOIN `bus_biz_department` t3 ON t1.department_id = t3.id
                 LEFT JOIN `bus_hospital_family` t9
                           ON t1.hospital_id = t9.hospital_id AND t1.patient_id = t9.patient_id AND
                              t1.family_id = t9.family_id
        where t1.`type` = '1'
          and t1.id = #{groupId}
    </select>

    <select id="getDoctorAndAssistantGroup" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT t1.id groupId
        FROM `bus_doctor_patient_group` t1
                 LEFT JOIN `bus_im_group_member` t2 on t1.id = t2.group_id
        where t1.doctor_id = #{doctorId}
          and t2.role = '3'
          and t2.personnel_id = #{assistantId}
          and t1.type = '2'
          and t1.hospital_id = #{hospitalId}
    </select>

    <select id="getInProgressOrder" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT t1.id groupId
        FROM `bus_doctor_patient_group` t1
--                  LEFT JOIN
--              (SELECT t5.*
--               FROM bus_consultation_order t5
--               WHERE t5.id IN (SELECT MAX(id)
--                               FROM bus_consultation_order t3
--                               WHERE (t3.status IN (0, 1, 2, 3, 9) OR t3.video_status IN (0, 2, 3, 9, 10, 11, 12))
--                               GROUP BY t3.hospital_id, t3.doctor_id, t3.patient_id, t3.family_id, t3.department_id)) t2
--              ON t1.hospital_id = t2.hospital_id
--                  AND t1.doctor_id = t2.doctor_id AND t1.patient_id = t2.patient_id AND t1.family_id = t2.family_id AND
--                 t1.department_id = t2.department_id
        where t1.doctor_id = #{doctorId}
--           AND t2.id IS NOT NULL
          and t1.type = '0'
          and t1.hospital_id = #{hospitalId}
    </select>

    <resultMap id="patientGroupMap" type="com.puree.hospital.five.domain.vo.PatientImVO">
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="doctorName" property="doctorName"/>
        <result column="title" property="title"/>
        <result column="doctor_id" property="doctorId"/>
        <result column="family_f_id" property="familyId"/>
        <result column="groupId" property="groupId"/>
        <result column="photo" property="photo"/>
        <result column="service_id" property="serviceId"/>
        <result column="groupType" property="type"/>
        <result column="assistant_id" property="assistantId"/>
        <result column="grade" property="vip"/>
        <result column="service_pack_name" property="servicePkgName"/>
        <result column="free" property="free"/>
        <result column="times" property="times"/>
        <result column="family_hospital_id" property="hospitalId"/>
        <result column="payType" property="payType"/>
        <result column="consultationOrderAmount" property="consultationOrderAmount"/>
        <result column="group_status" property="groupStatus"/>
        <result column="mark_changes" property="markChanges"/>
        <result column="group_create_time" property="createTime"/>
        <collection property="msgList" ofType="com.puree.hospital.five.domain.BusCommunicationMessage">
            <id column="id" property="id"/>
            <result column="msg_id" property="msgId"/>
            <result column="type" property="type"/>
            <result column="payload" property="payload"/>
            <result column="conversation_id" property="conversationId"/>
            <result column="conversation_type" property="conversationType"/>
            <result column="to" property="to"/>
            <result column="from" property="from"/>
            <result column="flow" property="flow"/>
            <result column="time" property="time"/>
            <result column="status" property="status"/>
            <result column="is_revoked" property="isRevoked"/>
            <result column="priority" property="priority"/>
            <result column="nick" property="nick"/>
            <result column="avatar" property="avatar"/>
            <result column="is_peer_read" property="isPeerRead"/>
            <result column="name_card" property="nameCard"/>
            <result column="at_user_list" property="atUserList"/>
            <result column="cloud_custom_data" property="cloudCustomData"/>
            <result column="tree_type" property="treeType"/>
            <result column="message_text" property="messageText"/>
            <result column="group_id" property="groupId"/>
            <result column="create_time" property="createTime"/>
        </collection>
    </resultMap>


    <select id="listPatientGroupV2" resultMap="patientGroupMap">
        SELECT
            g.id AS groupId,
            g.family_id AS family_f_id,
            g.doctor_id,
            g.department_id,
            g.type as groupType,
            g.mark_changes,
            g.service_id,
            g.group_status,
            g.hospital_id AS family_hospital_id,
            g.create_time as group_create_time,
            d.full_name AS doctorName,
            d.photo,
            d.title,
            dept.department_name,
            dp.grade,
            ad.assistant_id
        FROM bus_doctor_patient_group g
        LEFT JOIN bus_doctor d ON d.id = g.doctor_id
        LEFT JOIN bus_doctor_patient dp ON g.family_id = dp.family_id and g.doctor_id = dp.doctor_id
        <if test="hospitalId != null">
            AND dp.hospital_id = #{hospitalId}
        </if>
        LEFT JOIN bus_biz_department dept ON dept.id = g.department_id
        LEFT JOIN bus_five_assistant_doctor ad ON g.doctor_id = ad.doctor_id
        <if test="hospitalId != null">
            AND ad.hospital_id = #{hospitalId}
        </if>
        <where>
            <if test="groupId != null">
                AND g.id = #{groupId}
            </if>
            <if test="hospitalId != null">
                AND g.hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null">
                AND g.patient_id = #{patientId}
            </if>
        </where>
    </select>

    <select id="selectOneByServicePack" resultType="com.puree.hospital.five.domain.BusDoctorPatientGroup">
        select * from bus_doctor_patient_group
        where hospital_id = #{hospitalId}
          and doctor_id = #{doctorId}
          and service_id = #{servicePackId}
          and patient_id = #{patientId}
          and family_id = #{familyId}
          and type = '1'
        limit 1
    </select>

    <select id="listConversationV1" parameterType="com.puree.hospital.five.domain.BusDoctorPatientGroup" resultMap="BaseResultMap">
        SELECT
        pg.id groupId,
        pg.doctor_id,
        pg.department_id,
        pg.family_id,
        pg.patient_id,
        pg.hospital_id,
        pg.group_status,
        pg.group_start_time,
        pg.group_end_time,
        pg.type,
        pg.del_flag,
        doctor.photo,
        doctor.full_name doctorName,
        dept.department_name,
        pm.name familyName,
        pm.sex,
        pm.date_of_birth,
        pack.service_pack_name groupName,
        dp.grade,
        dp.id as doctor_patient_id,
        hosp.is_this_court,
        doctor.title dictCode,
        pg.create_time as msgTime,
        pg.last_message_time lastMessageTime
        FROM `bus_doctor_patient_group` pg
        LEFT JOIN `bus_doctor` doctor ON pg.doctor_id = doctor.id
        LEFT JOIN `bus_patient_family` pm ON pg.family_id = pm.id
        LEFT JOIN `bus_doctor_patient` dp ON pg.doctor_id=dp.doctor_id AND pg.patient_id = dp.patient_id AND
        pg.family_id =dp.family_id AND pg.hospital_id=dp.hospital_id
        LEFT JOIN `bus_five_service_pack` pack ON pg.service_id=pack.id
        LEFT JOIN `bus_biz_department` dept ON pg.department_id = dept.id
        LEFT JOIN `bus_doctor_hospital` hosp ON pg.doctor_id=hosp.doctor_id and pg.hospital_id=hosp.hospital_id
        <where>
            <if test="type!=null and type!=''">
                and pg.`type` = #{type}
            </if>
            <if test="doctorId !=null">
                AND pg.doctor_id = #{doctorId}
            </if>
            <if test="patientId !=null">
                AND pg.patient_id = #{patientId}
                AND pg.type !='2'
            </if>
            <if test="hospitalId !=null">
                AND pg.hospital_id = #{hospitalId}
            </if>
            <if test="lastMessageTime !=null and lastMessageTime!=''">
                AND pg.last_message_time <![CDATA[<=]]> #{lastMessageTime}
            </if>
        </where>
        order by last_message_time desc
    </select>
</mapper>