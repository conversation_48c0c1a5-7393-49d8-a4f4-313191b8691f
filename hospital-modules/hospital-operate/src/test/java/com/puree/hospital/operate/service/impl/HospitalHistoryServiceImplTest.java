package com.puree.hospital.operate.service.impl;


import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.setting.domain.entity.hospital.HospitalHistory;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import com.puree.hospital.setting.domain.vo.hospital.HistoryVo;
import com.puree.hospital.setting.mapper.hospital.HospitalHistoryMapper;
import com.puree.hospital.setting.mapper.platform.PlatformHistoryMapper;
import com.puree.hospital.setting.service.hospital.HospitalHistoryService;
import com.puree.hospital.setting.service.hospital.impl.HospitalHistoryServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HospitalHistoryServiceImplTest {

    @Mock
    private HospitalHistoryMapper hospitalHistoryMapper;

    @Mock
    private PlatformHistoryMapper platformHistoryMapper;

    @InjectMocks
    private HospitalHistoryServiceImpl hospitalHistoryService;

    private HospitalTemplate newData;
    private HospitalTemplate oldData;

    @BeforeEach
    void setUp() {
        newData = new HospitalTemplate();
        newData.setId(1L);
        newData.setKey("testKey");
        newData.setMenuId(1L);
        newData.setType(1);
        newData.setRevision(1);
        newData.setItemRevision(1);
        newData.setItemValue("newValue");
        newData.setUpdateBy("newUser");
        newData.setCreateTime(new Date());

        oldData = new HospitalTemplate();
        oldData.setId(1L);
        oldData.setKey("testKey");
        oldData.setMenuId(1L);
        oldData.setType(1);
        oldData.setItemValue("oldValue");
        oldData.setCreateBy("oldUser");
        oldData.setCreateTime(new Date());
    }

    @Test
    void testInsertHistory_正常情况() {
        when(platformHistoryMapper.queryByItemId(1L)).thenReturn(new ArrayList<>());
        when(platformHistoryMapper.insert(any(PlatformHistory.class))).thenReturn(1);

        assertTrue(hospitalHistoryService.insertHistory(newData, oldData));
        verify(platformHistoryMapper, times(1)).queryByItemId(1L);
        verify(platformHistoryMapper, times(1)).insert(any(PlatformHistory.class));
    }

    @Test
    void testInsertHistory_itemsId为空() {
        oldData.setId(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            hospitalHistoryService.insertHistory(newData, oldData);
        });
        assertEquals("itemsId 不能为空", exception.getMessage());
    }

    @Test
    void testInsertHistory_itemKey为空() {
        oldData.setKey(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            hospitalHistoryService.insertHistory(newData, oldData);
        });
        assertEquals("itemKey 不能为空", exception.getMessage());
    }

    @Test
    void testInsertHistory_MenuId为空() {
        oldData.setMenuId(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            hospitalHistoryService.insertHistory(newData, oldData);
        });
        assertEquals("MenuId 不能为空", exception.getMessage());
    }

    @Test
    void testInsertHistory_Type为空() {
        oldData.setType(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            hospitalHistoryService.insertHistory(newData, oldData);
        });
        assertEquals("Type 不能为空", exception.getMessage());
    }

    @Test
    void testInsertHospitalHistory_正常情况() {
        when(hospitalHistoryMapper.queryByItemIdAndHospitalId(1L, 1L)).thenReturn(new ArrayList<>());
        when(hospitalHistoryMapper.insert(any(HospitalHistory.class))).thenReturn(1);
        newData.setHospitalId(1L);
        assertTrue(hospitalHistoryService.insertHospitalHistory(newData, oldData, 1L));
        verify(hospitalHistoryMapper, times(1)).queryByItemIdAndHospitalId(1L, 1L);
        verify(hospitalHistoryMapper, times(1)).insert(any(HospitalHistory.class));
    }

    @Test
    void testInsertHospitalHistory_templateId为空() {
        oldData.setId(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            hospitalHistoryService.insertHospitalHistory(newData, oldData, 1L);
        });
        assertEquals("templateId 不能为空", exception.getMessage());
    }

    @Test
    void testInsertHospitalHistory_hospitalId为空() {
        newData.setHospitalId(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            hospitalHistoryService.insertHospitalHistory(newData, oldData, 1L);
        });
        assertEquals("HospitalId 不能为空", exception.getMessage());
    }

    @Test
    void testInsertHospitalHistory_itemKey为空() {
        oldData.setKey(null);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            newData.setHospitalId(1L);
            hospitalHistoryService.insertHospitalHistory(newData, oldData, 1L);
        });
        assertEquals("itemKey 不能为空", exception.getMessage());
    }

    @Test
    void testList_正常情况() {
        List<HospitalHistory> hospitalHistories = new ArrayList<>();
        hospitalHistories.add(new HospitalHistory());
        when(hospitalHistoryMapper.queryByKeyCount(1, "testKey")).thenReturn(1L);
        when(hospitalHistoryMapper.queryByKey("testKey", 1, 10, 0)).thenReturn(hospitalHistories);

        Paging<List<HospitalHistory>> result = hospitalHistoryService.list("testKey", 1, 10, 1);
        assertNotNull(result.getData());
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
    }

    @Test
    void testList_无历史数据() {
        when(hospitalHistoryMapper.queryByKeyCount(1, "testKey")).thenReturn(0L);

        Paging<List<HospitalHistory>> result = hospitalHistoryService.list("testKey", 1, 10, 1);
        assertNull(result.getData());
        assertEquals(0, result.getTotal());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
    }

    @Test
    void testList_pageSize为空() {
        List<HospitalHistory> hospitalHistories = new ArrayList<>();
        hospitalHistories.add(new HospitalHistory());
        when(hospitalHistoryMapper.queryByKeyCount(1, "testKey")).thenReturn(1L);
        when(hospitalHistoryMapper.queryByKey("testKey", 1, 10, 0)).thenReturn(hospitalHistories);

        Paging<List<HospitalHistory>> result = hospitalHistoryService.list("testKey", 1, null, 1);
        assertNotNull(result.getData());
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
    }

    @Test
    void testList_pageNum为空() {
        List<HospitalHistory> hospitalHistories = new ArrayList<>();
        hospitalHistories.add(new HospitalHistory());
        when(hospitalHistoryMapper.queryByKeyCount(1, "testKey")).thenReturn(1L);
        when(hospitalHistoryMapper.queryByKey("testKey", 1, 10, 0)).thenReturn(hospitalHistories);

        Paging<List<HospitalHistory>> result = hospitalHistoryService.list("testKey", 1, 10, null);
        assertNotNull(result.getData());
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
    }

    @Test
    void testList_pageSizeAndPageNum为空() {
        List<HospitalHistory> hospitalHistories = new ArrayList<>();
        hospitalHistories.add(new HospitalHistory());
        when(hospitalHistoryMapper.queryByKeyCount(1, "testKey")).thenReturn(1L);
        when(hospitalHistoryMapper.queryByKey("testKey", 1, 10, 0)).thenReturn(hospitalHistories);

        Paging<List<HospitalHistory>> result = hospitalHistoryService.list("testKey", 1, null, null);
        assertNotNull(result.getData());
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
    }
}
