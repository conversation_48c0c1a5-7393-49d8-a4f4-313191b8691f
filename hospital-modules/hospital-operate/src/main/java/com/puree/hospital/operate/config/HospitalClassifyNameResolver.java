package com.puree.hospital.operate.config;

import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospital;
import com.puree.hospital.setting.model.event.SettingChangeEvent;
import com.puree.hospital.setting.notify.resolver.ClassifyNameResolver;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 医院分类名称解析器
 *
 * <AUTHOR>
 * @date 2025/8/23
 */
public class HospitalClassifyNameResolver implements ClassifyNameResolver {

    @Resource
    private RemoteHospitalService busHospitalService;

    /**
     * 根据事件获取医院名称
     *
     * @param event 配置变更事件，包含 hospitalId
     * @return 医院名称或默认分类值
     */
    @Override
    public String resolveClassifyName(SettingChangeEvent event) {
        if (SettingChangeEvent.Classify.HOSPITAL.equals(event.getClassify()) && event.getHospitalId() != null) {
            BusHospital busHospital = busHospitalService.getHospitalInfo(event.getHospitalId()).getData();
            return busHospital.getHospitalName();
        }
        return event.getClassify().getValue();
    }
}