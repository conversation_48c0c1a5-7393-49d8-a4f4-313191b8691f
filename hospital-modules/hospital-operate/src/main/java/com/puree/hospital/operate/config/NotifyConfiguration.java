package com.puree.hospital.operate.config;

import com.puree.hospital.setting.notify.resolver.ClassifyNameResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 通知相关配置
 *
 * <AUTHOR>
 * @date 2025/8/23
 */
@Configuration
public class NotifyConfiguration {

    /**
     * 提供医院分类名称解析器
     *
     * @return 医院分类名称解析器
     */
    @Bean
    @Primary
    public ClassifyNameResolver hospitalClassifyNameResolver() {
        return new HospitalClassifyNameResolver();
    }
}