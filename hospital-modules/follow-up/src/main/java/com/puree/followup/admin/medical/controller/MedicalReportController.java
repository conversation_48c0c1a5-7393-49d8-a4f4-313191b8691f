package com.puree.followup.admin.medical.controller;

import com.alibaba.fastjson.JSON;
import com.puree.followup.admin.medical.service.MedicalReportService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.security.annotation.SignatureVerify;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @ClassName MedicalReportController
 * <AUTHOR>
 * @Description 体检上传
 * @Date 2024/3/31 15:29
 * @Version 1.0
 */
@Slf4j
@RequestMapping("/medical-report")
@RestController
@AllArgsConstructor
public class MedicalReportController {

    private final MedicalReportService medicalReportService;

    /**
     * @Param request
     * @Param params
     * @Return com.puree.hospital.common.api.domain.AjaxResult<java.lang.Boolean>
     * @Description 体检报告上传
     * <AUTHOR>
     * @Date 2024/4/29 17:08
     **/
    @PostMapping("/upload")
    @SignatureVerify(signVersion = SignatureVerify.SignVersionEnum.V1)
    public AjaxResult<Boolean> upload(HttpServletRequest request, @RequestBody String params) {
        log.info("体检报告上传入参: {}", params);
        String appId = request.getHeader("X-App-Id");
        @Valid ExamReportInfoDTO dto = JSON.parseObject(params, ExamReportInfoDTO.class);
        dto.setAppId(appId);
        return AjaxResult.success(medicalReportService.upload(dto));
    }


    /**
     * @Param request
     * @Param file
     * @Param directoryKey
     * @Return com.puree.hospital.common.api.domain.AjaxResult<java.lang.Boolean>
     * @Description 体检 OSS 上传
     * <AUTHOR>
     * @Date 2024/4/25 17:33
     **/
    @PostMapping(value = "/oss-upload", consumes = "multipart/form-data")
    @SignatureVerify(signParam = SignatureVerify.SignParamEnum.NO_PARAMETERS,signVersion = SignatureVerify.SignVersionEnum.V1)
    public AjaxResult<String> ossUpload(HttpServletRequest request, @RequestParam(value = "file") MultipartFile file,
                                        @RequestParam("directoryKey") Integer directoryKey) {
        return AjaxResult.success(medicalReportService.ossUpload(file, directoryKey));
    }
}
