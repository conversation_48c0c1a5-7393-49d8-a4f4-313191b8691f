package com.puree.followup.admin.medical.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.puree.followup.admin.medical.mapper.ReportInspectRecordMapper;
import com.puree.followup.admin.medical.service.ReportInspectRecordService;
import com.puree.followup.domain.medical.dto.QuestionnaireInspectExamDTO;
import com.puree.followup.domain.medical.dto.ReportInspectRecordUpdateDTO;
import com.puree.followup.domain.medical.dto.ReportRepeatTodayDTO;
import com.puree.followup.domain.medical.model.ReportInspectRecord;
import com.puree.followup.domain.medical.model.ReportRecord;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.InspectSummaryDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ReportInspectServiceImpl
 * <AUTHOR>
 * @Description 体检报告检验项记录表
 * @Date 2024/5/13 18:41
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReportInspectRecordServiceImpl implements ReportInspectRecordService {

    private final ReportInspectRecordMapper reportInspectRecordMapper;


    /**
     * @Param id
     * @Return com.puree.followup.domain.medical.model.ReportInspectRecord
     * @Description 根据ID获取
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public ReportInspectRecord getById(Long id) {
        return reportInspectRecordMapper.getById(id);
    }

    /**
     * @Param reportInspectRecord
     * @Return java.util.List<com.puree.followup.domain.medical.model.ReportInspectRecord>
     * @Description 批量获取数据
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public List<ReportInspectRecord> getList(ReportInspectRecord reportInspectRecord) {
        return reportInspectRecordMapper.getList(reportInspectRecord);
    }

    /**
     * @Param reportInspectRecord
     * @Return com.puree.followup.domain.medical.model.ReportInspectRecord
     * @Description 新增
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public ReportInspectRecord insert(ReportInspectRecord reportInspectRecord) {
        reportInspectRecord.setIsDelete(false);
        reportInspectRecord.setIsDelete(false);
        Date checkTime = reportInspectRecord.getCheckTime();
        Date createTime = reportInspectRecord.getCreateTime();
        if (reportInspectRecord.getCreateBy() == null) {
            reportInspectRecord.setCreateBy(SecurityUtils.getUsername());
        }
        reportInspectRecord.setCheckTime(checkTime == null ? new Date() : checkTime);
        reportInspectRecord.setCreateTime(createTime == null ? new Date() : createTime);
        int insert = reportInspectRecordMapper.insert(reportInspectRecord);
        if (SqlHelper.retBool(insert)) {
            return reportInspectRecord;
        }
        log.warn("新增 体检报告检测项记录 失败:{}", reportInspectRecord);
        throw new ServiceException("新增 体检报告检测项记录 失败");
    }

    /**
     * @Param reportInspectRecord
     * @Return com.puree.followup.domain.medical.model.ReportInspectRecord
     * @Description 修改
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public ReportInspectRecord update(ReportInspectRecord reportInspectRecord) {
        reportInspectRecord.setUpdateTime(DateUtil.date());
        reportInspectRecord.setUpdateBy(SecurityUtils.getUsername());
        int update = reportInspectRecordMapper.update(reportInspectRecord);
        if (SqlHelper.retBool(update)) {
            return reportInspectRecord;
        }
        log.warn("修改 体检报告检测项记录 失败:{}", reportInspectRecord);
        throw new ServiceException("修改 体检报告检测项记录 失败");
    }

    /**
     * @Param id
     * @Return java.lang.Boolean
     * @Description 删除
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public Boolean delete(Long id) {
        int delete = reportInspectRecordMapper.delete(id);
        if (SqlHelper.retBool(delete)) {
            return true;
        }
        log.warn("删除 体检报告检测项记录 失败:{}", id);
        throw new ServiceException("删除 体检报告检测项记录 失败");
    }

    /**
     * @Param reportExamRecords
     * @Return java.util.List<com.puree.followup.domain.medical.model.ReportInspectRecord>
     * @Description 批量新增
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public List<ReportInspectRecord> insertBatch(List<ReportInspectRecord> reportInspectRecords) {
        int insert = reportInspectRecordMapper.insertBatch(reportInspectRecords);
        if (SqlHelper.retBool(insert)) {
            return reportInspectRecords;
        }
        log.warn("批量新增 体检报告检查项记录 失败:{}", reportInspectRecords);
        throw new ServiceException("批量新增 体检报告检查项记录 失败");
    }

    /**
     * @Param reportRecord
     * @Return java.util.List<com.puree.followup.domain.medical.model.ReportInspectRecord>
     * @Description 数据拆分后写入
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public List<ReportInspectRecord> dataPushSplitAndInsert(ReportRecord reportRecord) {
        if (reportRecord == null) {
            return null;
        }

        String reportJson = reportRecord.getReportJson();
        ExamReportInfoDTO dto = JSONObject.parseObject(reportJson, ExamReportInfoDTO.class);

        //数据拆分
        InspectSummaryDTO[] summaries = dto.getInspectSummaries();
        if (ArrayUtil.isEmpty(summaries)) {
            return null;
        }

        List<ReportInspectRecord> list = new ArrayList<>();
        for (InspectSummaryDTO summary : summaries) {
            ReportInspectRecord reportInspectRecord = dataPushSplit(summary, reportRecord);
            if (reportInspectRecord != null) {
                list.add(reportInspectRecord);
            }
        }
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        //批量写入
        return insertBatch(list);
    }

    /**
     * @Param recordId
     * @Return Boolean
     * @Description 根据 记录ID删除
     * <AUTHOR>
     * @Date 2024/9/6 15:10
     **/
    @Override
    public Boolean deleteByRecordId(Long recordId) {
        reportInspectRecordMapper.deleteByRecordId(recordId);
        return true;
    }

    /**
     * @Param dto
     * @Param reportRecord
     * @Return com.puree.followup.domain.medical.model.ReportInspectRecord
     * @Description 数据拆分
     * <AUTHOR>
     * @Date 2024/5/16 14:56
     **/
    @Override
    public ReportInspectRecord dataPushSplit(InspectSummaryDTO dto, ReportRecord reportRecord) {
        if (dto == null || reportRecord == null) {
            return null;
        }
        ReportInspectRecord record = new ReportInspectRecord();
        record.setSubject(dto.getSubject());
        record.setExamDate(DateUtil.parse(dto.getExamDate()));
        record.setSummary(dto.getSummary());
        record.setInspectItems(JSONObject.toJSONString(dto));
        record.setHospitalId(reportRecord.getHospitalId());
        record.setPatientId(reportRecord.getFamilyId());
        record.setPatientIdNumber(reportRecord.getPatientIdNumber());
        record.setRecordId(reportRecord.getId());
        record.setSource(RecordSourceEnum.DATA_PUSH);
        record.setIsDelete(false);

        return record;
    }

    /**
     * @Param patientId
     * @Param hospitalId
     * @Return java.util.List<com.puree.followup.domain.medical.model.ReportInspectRecord>
     * @Description 分页
     * <AUTHOR>
     * @Date 2024/5/24 18:21
     **/
    @Override
    public List<ReportInspectRecord> getPage(String patientIdNumber, Long hospitalId) {
        //身份证为空则返回空数组
        if (StrUtil.isEmpty(patientIdNumber)) {
            return new ArrayList<>();
        }
        PageUtil.startPage();
        return reportInspectRecordMapper.getPage(patientIdNumber, hospitalId, RecordSourceEnum.DATA_PUSH);
    }

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 修改
     * <AUTHOR>
     * @Date 2024/5/24 18:22
     **/
    @Override
    public Boolean updateRecord(ReportInspectRecordUpdateDTO dto) {
        ReportInspectRecord record = BeanUtil.copyProperties(dto, ReportInspectRecord.class);
        update(record);
        return true;
    }

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 新增
     * <AUTHOR>
     * @Date 2024/5/24 18:22
     **/
    @Override
    public Boolean insertRecord(ReportInspectRecord dto) {
        ReportInspectRecord record = BeanUtil.copyProperties(dto, ReportInspectRecord.class);
        insert(record);
        return true;
    }

    /**
     * @Param dto
     * @Return java.util.List<com.puree.followup.domain.medical.model.ReportInspectRecord>
     * @Description 获取今天重复数据
     * <AUTHOR>
     * @Date 2024/6/4 18:09
     **/
    @Override
    public List<ReportInspectRecord> getRepeatToday(ReportRepeatTodayDTO dto) {
        return reportInspectRecordMapper.getRepeatToday(dto);
    }

    /**
     * @Param dto
     * @Return List<ReportInspectRecord>
     * @Description 健康档案-检验单
     * <AUTHOR>
     * @Date 2024/7/11 16:36
     **/
    @Override
    public List<ReportInspectRecord> questionnaireInspectExam(QuestionnaireInspectExamDTO dto) {
        return reportInspectRecordMapper.questionnaireInspectExam(dto);
    }

}
