package com.puree.followup.admin.medical.service;

import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import com.puree.followup.domain.medical.model.ReportRecord;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName MedicalReportService
 * <AUTHOR>
 * @Description 体检报告
 * @Date 2024/4/25 17:24
 * @Version 1.0
 */
public interface MedicalReportService {
    /**
     * @Param file
     * @Param directoryKey
     * @Return java.lang.String
     * @Description OSS 上传
     * <AUTHOR>
     * @Date 2024/4/25 18:03
     **/
    String ossUpload(MultipartFile file, Integer directoryKey);

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 数据推送
     * <AUTHOR>
     * @Date 2024/4/28 16:17
     **/
    Boolean upload(ExamReportInfoDTO dto);

    /**
     * @Param reportRecord
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 写入体检报告
     * <AUTHOR>
     * @Date 2024/5/15 17:01
     **/
    ReportRecord insertReportRecord(ReportRecord reportRecord);

    /**
     * @Param reportRecord
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 修改体检报告记录
     * <AUTHOR>
     * @Date 2024/5/15 17:51
     **/
    ReportRecord updateReportRecord(ReportRecord reportRecord);

    /**
     * @Param dto
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 转换类型
     * <AUTHOR>
     * @Date 2024/5/15 17:06
     **/
    ReportRecord convertReportRecord(ExamReportInfoDTO dto, Long familyId, String idCard);

    /**
     * @Param reportRecord
     * @Return com.puree.followup.domain.medical.model.ReportRecord
     * @Description 校验数据
     * <AUTHOR>
     * @Date 2024/5/15 17:06
     **/
    ReportRecord reportRecordHandle(ReportRecord reportRecord);

    /**
     * @Param recordId
     * @Return void
     * @Description MQ调起数据拆分写入
     * <AUTHOR>
     * @Date 2024/5/20 17:29
     **/
    void dataPushHandler(Long recordId);
}
