package com.puree.hospital.tool.sign.szca.util;



import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfDictionary;
import com.itextpdf.text.pdf.PdfName;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfString;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.tool.api.model.dto.CaSignRequestDTO;
import com.puree.hospital.tool.sign.szca.service.SignService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;


import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/21 14:50
 */
@Slf4j
public class pdfSzcaSignTest {

    private final SignService signService = new SignService();
    private final OSSUtil ossUtil = new OSSUtil();
    @Test
    public void testSign(){
        // 有效id
        //String signId = "20250700015827";
        // 无效的id
        String signId = "20250700015820";
//        String review_signId = "20250300012961";
        String result = "prescription/1/2/2025/08/04/a3f61ceb987f4472b94cb87f8ed94abe.pdf";
        String signFile = "casignature/2025/07/29/b72b9642ed7443ec8eaf6878aa55c079.png";
        // 医生签名标志位
        String flag = "{{prescription_doctor}}";
        Map<String,Object> map = new HashMap<>();
        map.put("num",1);
        Map<String,Double> pos = new HashMap<>();
        pos.put("x", 100D);
        pos.put("y", 100D);
        map.put("pos", pos);
        CaSignRequestDTO caSignRequestDTO = new CaSignRequestDTO();
        caSignRequestDTO.setSignId(signId);
        caSignRequestDTO.setSignPath(signFile);
        caSignRequestDTO.setPdfFilePath(result);
        caSignRequestDTO.setPosition(map);
        caSignRequestDTO.setLocalSignDir("");
        caSignRequestDTO.setOssDir("casignature");
        try {
            signService.signature(caSignRequestDTO);
        } catch (Exception e) {
            log.error("签名失败"+e);
        }
    }


    @Test
    public void getPdfSignInfo() throws IOException {
        String pdfPath = "C:/Users/<USER>/Desktop/75097297580c4619b8138b86fc90ceab.pdf";
        PdfReader reader = new PdfReader(pdfPath);
        AcroFields fields = reader.getAcroFields();
        List<String> names = fields.getSignatureNames();

        for (String name : names) {
            PdfDictionary sigDict = fields.getSignatureDictionary(name);
            PdfString contents = sigDict.getAsString(PdfName.CONTENTS);
            PdfDictionary signatureDictionary = fields.getSignatureDictionary(name);
            System.out.println("签内容字段: " + signatureDictionary);
            System.out.println("签名字段: " + name);
            System.out.println("签名原始内容（十六进制）:");
            System.out.println(contents.toString());
        }


    }
}
