package com.puree.hospital.tool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.tool.wx.WxAccessTokenVO;
import com.puree.hospital.tool.wx.WxStableAccessTokenReqDTO;
import org.junit.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;

/**
 * <p>
 * 微信access toke单元测试
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/26 19:02
 */
public class WxAccessTokenTest {

    private static final String APPID = "wx2e972dea836ac087";

    private static final String SECRET = "a93b3e2a581e614281549c623973f88c";

    private static final String ACCESS_TOKEN_URL = "https://za.aidmed.net/stage-api/tool/base-wx/get-access-token?grant_type=client_credential&appid=%s&secret=%s";

    private static final String STABLE_ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/stable_token";

    private static final String GET_USER_SUMMARY_URL = "https://api.weixin.qq.com/datacube/getusersummary?access_token=%s";

    @Test
    public void testCompareAccessToken() {
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<WxAccessTokenVO> tokenRsp1 = restTemplate.getForEntity(String.format(ACCESS_TOKEN_URL, APPID, SECRET), WxAccessTokenVO.class);
        System.out.println(tokenRsp1.getBody());
        JSONObject param = new JSONObject();
        param.put("begin_date", LocalDate.now().minusDays(1).toString());
        param.put("end_date",LocalDate.now().minusDays(1).toString());
        ResponseEntity<String> result1 = restTemplate.postForEntity(String.format(GET_USER_SUMMARY_URL, tokenRsp1.getBody().getAccess_token()), param, String.class);
        System.out.println(result1.getBody());

        WxStableAccessTokenReqDTO reqDTO = new WxStableAccessTokenReqDTO();
        reqDTO.setAppid(APPID);
        reqDTO.setSecret(SECRET);
        ResponseEntity<WxAccessTokenVO> tokenRsp2 = restTemplate.postForEntity(STABLE_ACCESS_TOKEN, JSON.toJSONString(reqDTO), WxAccessTokenVO.class);
        System.out.println(tokenRsp2.getBody());
        ResponseEntity<String> result2 = restTemplate.postForEntity(String.format(GET_USER_SUMMARY_URL, tokenRsp2.getBody().getAccess_token()), param, String.class);
        System.out.println(result2.getBody());
    }

}
