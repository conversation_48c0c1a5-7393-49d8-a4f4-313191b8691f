package com.puree.hospital.tool.com.puree.hospital.tool.pdf.processor;

import com.puree.hospital.tool.pdf.processor.PageNumberTextProcessor;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * 测试 PageNumberTextProcessor 类
 * <AUTHOR>
 */
public class PageNumberTextProcessorTest {
    private final PageNumberTextProcessor processor = new PageNumberTextProcessor();

    @Test
    public void testNormalPageNumber() {
        String input = "第1页/共2页";
        String expected = "";
        assertEquals(expected, processor.process(input));
    }

    @Test
    public void testPageNumberWithSpaces() {
        String input = "第 1 页 / 共 2 页";
        String expected = "";
        assertEquals(expected, processor.process(input));
    }

    @Test
    public void testPageNumberWithDifferentSymbols() {
        String input = "第1页-共2页";
        String expected = "";
        assertEquals(expected, processor.process(input));
    }

    @Test
    public void testPageNumberWithParentheses() {
        String input = "第 1 页 (共 2 页)";
        String expected = "";
        assertEquals(expected, processor.process(input));
    }

    @Test
    public void testEmptyString() {
        String input = "";
        String expected = "";
        assertEquals(expected, processor.process(input));
    }

    @Test
    public void testNoPageNumber() {
        String input = "这是一个没有页码的文本";
        String expected = "这是一个没有页码的文本";
        assertEquals(expected, processor.process(input));
    }

    @Test
    public void testInvalidInput() {
        String input = "第A页/共B页";
        String expected = "第A页/共B页";
        assertEquals(expected, processor.process(input));
    }
}
