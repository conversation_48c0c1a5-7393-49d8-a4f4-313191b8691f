package com.puree.hospital.tool.wx.service;

import com.puree.hospital.tool.HospitalToolApplication;
import com.puree.hospital.tool.api.model.dto.QrCodeDTO;
import com.puree.hospital.tool.api.model.enums.QrCodeWxInterfaceEnum;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;

public class BaseWxServiceTest  {
    /**
     * 测试 BaseWxServiceImpl 的 getQrCodeUrl 方法
     */
    @Resource
    private IBaseWxService baseWxService;


    @Test
    public void getQrCodeUrl() {
        ApplicationContext context = SpringApplication.run(HospitalToolApplication.class);
        baseWxService = context.getBean(IBaseWxService.class);
        Assert.assertNotNull(baseWxService); // 如果这里通过，说明注入配置有问题
        QrCodeDTO qrCodeDto = new QrCodeDTO();
        qrCodeDto.setPath("pages/home/<USER>");
        qrCodeDto.setParam("id=9&idType=CHANNEL&ver=v1");
        qrCodeDto.setHospitalId(100L);
        qrCodeDto.setWxInterfaceDesc(QrCodeWxInterfaceEnum.GET_UNLIMITED_QRCODE.name());
        String qrCodeUrl = baseWxService.getQrCode(qrCodeDto);
        System.out.println("生成的二维码链接: " + qrCodeUrl);
    }
}
