package com.puree.hospital.tool.sign.szca.util;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.tool.sign.szca.infrastructure.util.ImageUtil;
import org.junit.Test;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/5 15:36
 */

public class ImageUtilsTest {
    private ImageUtil imageUtil = new ImageUtil();
    private OSSUtil ossUtil = new OSSUtil();

    private  InputStream getImgInputStream(String SignPath){
        return ossUtil.getObjectAsInputStream(SignPath);
    }

    private BufferedImage resizeImage(InputStream inputStream, int targetWidth, int targetHeight) throws IOException {
        // 1. 读取原图，获取原图的类型和特性
        BufferedImage originalImage = ImageIO.read(inputStream);
        if (originalImage == null) {
            throw new IOException("无法解析图片（格式不支持或文件损坏）");
        }
        // 2. 根据原图类型选择目标BufferedImage的类型
        int imageType = getTargetImageType(originalImage);

        // 3. 创建目标尺寸的缓冲图像（匹配原图类型）
        BufferedImage resizedImage = new BufferedImage(
                targetWidth,
                targetHeight,
                imageType
        );

        // 高质量缩放配置
        Graphics2D g2d = resizedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR); // 平滑插值
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY); // 高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON); // 抗锯齿

        // 绘制缩小后的图像
        g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    private int getTargetImageType(BufferedImage originalImage) {
        int originalType = originalImage.getType();

        // 情况1：原图是带透明通道的类型（如PNG、GIF）
        if (originalType == BufferedImage.TYPE_INT_ARGB || originalType == BufferedImage.TYPE_INT_ARGB_PRE) {
            return BufferedImage.TYPE_INT_ARGB; // 保留透明通道
        }

        // 情况2：原图是不透明类型（如JPG、BMP）
        if (originalType == BufferedImage.TYPE_INT_RGB || originalType == BufferedImage.TYPE_3BYTE_BGR) {
            return BufferedImage.TYPE_INT_RGB; // 无透明通道
        }

        // 情况3：其他未知类型（默认使用兼容类型，避免色彩失真）
        return BufferedImage.TYPE_INT_ARGB; // 兼容大多数格式，即使原图不透明也不会出错（只是多占一点内存）
    }
    @Test
    public void testResizeImage() throws IOException {
        String localSavePath = "C:/Users/<USER>/Desktop/resized_image.png"; // 本地保存路径
        String signPath = "C:/Users/<USER>/Desktop/b72b9642ed7443ec8eaf6878aa55c079.png";

//        InputStream imageStream = getImgInputStream(signPath);
        InputStream imageStream = new FileInputStream(signPath);
        // 缩小图片尺寸
        BufferedImage resizedImage = resizeImage(imageStream, 240, 120);

        // 将缩小后的图片写入内存流（避免临时文件）
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ImageIO.write(resizedImage, "png", outputStream);
            outputStream.flush();

            // 4. 将内存流中的字节数组转为Base64
            byte[] resizedBytes = outputStream.toByteArray();
            try (OutputStream outputStream1 = new FileOutputStream(localSavePath)) {
                outputStream1.write(resizedBytes);
            }
            System.out.println("图片处理成功，已保存到：" + localSavePath);
        } finally {
            // 关闭OSS输入流，释放资源
            imageStream.close();
        }
    }
    @Test
    public void test(){
        Map<String, Float> pos = new HashMap<>();
        pos.put("x", 100f);
        pos.put("y", 50f);

        JSONObject coordinate = new JSONObject();
        //以页面左下角为原点的x坐标  1.33为深圳CA签署的缩放变化比例，比如传参1.33，实际上在pdf上显示为1
        coordinate.put("left", ((pos.get("x")*1.33f)));
        //以页面左下角为原点的y坐标
        coordinate.put("bottom", ((pos.get("y")*1.33f)));
        System.out.println( coordinate);
    }


}

