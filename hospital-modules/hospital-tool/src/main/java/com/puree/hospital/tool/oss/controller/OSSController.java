package com.puree.hospital.tool.oss.controller;

import com.aliyuncs.exceptions.ClientException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.oss.OSSSaveDirectory;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.common.security.annotation.SignatureVerify;
import com.puree.hospital.common.sts.service.StsUtil;
import com.puree.hospital.common.sts.vo.StsTokenVO;
import com.puree.hospital.tool.oss.service.PureeStorageService;
import com.puree.storage.core.model.StorageSecurityTokenService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @date 2024/9/12 17:57
 */
@RequestMapping("oss")
@RestController
@AllArgsConstructor
public class OSSController extends BaseController {

    @Resource
    private OSSUtil ossUtil;

    @Resource
    private StsUtil stsUtil;

    private final PureeStorageService pureeStorageService;

    /**
     * 网络文件文件上传
     *
     * @param fileUrl 文件路径
     * @param prefix  文件前缀
     * @param suffix  文件后缀
     * @return AjaxResult
     */
    @PostMapping("network/upload")
    public AjaxResult networkUpload(@RequestParam(value = "fileUrl") String fileUrl,
                                    @RequestParam("prefix") String prefix,
                                    @RequestParam("suffix") String suffix) {
        String url = ossUtil.uploadNetworkFileToOSS(fileUrl, prefix, suffix);
        if (StringUtils.isEmpty(url)) {
            return AjaxResult.error("文件上传失败");
        }
        return AjaxResult.success(url);
    }


    /**
     * 文件上传
     *
     * @param filePath  文件路径
     * @param directory 文件目录
     * @param suffix    文件猴嘴
     * @return AjaxResult
     */
    @PostMapping("simple/upload")
    public AjaxResult ossUpload(@RequestParam("filePath") String filePath,
                                @RequestParam("directory") String directory,
                                @RequestParam("suffix") String suffix) throws IOException {
        if (StringUtils.isEmpty(directory)) {
            return AjaxResult.error("文件目录不存在");
        }
        InputStream inputStream = Files.newInputStream(Paths.get(filePath));
        String url = ossUtil.simpleUpload(inputStream, directory, suffix);
        if (StringUtils.isEmpty(url)) {
            return AjaxResult.error("文件上传失败");
        }
        return AjaxResult.success(url);
    }


    /**
     * 获取文件
     *
     * @param response HttpServletResponse
     * @param request  HttpServletRequest
     */
    @GetMapping(value = "/v2/file/**")
    public void getObject(HttpServletResponse response, HttpServletRequest request) {
        pureeStorageService.getObject(response,request);
    }

    /**
     * 获取文件全路径
     *
     * @param objectPath   文件路径
     * @return AjaxResult
     */
    @GetMapping(value = "/v2/fullName")
    public AjaxResult getFullName(@RequestParam("objectPath") String objectPath) {
        return AjaxResult.success(ossUtil.getFullPath(objectPath));
    }

    /**
     * 文件上传获取sts
     *
     * @param businessName 业务名称
     * @return AjaxResult<StorageSecurityTokenService>
     */
    @GetMapping("/v2/sts/{businessName}")
    public AjaxResult getSecurityTokenService(@PathVariable("businessName") String businessName) {
        StorageSecurityTokenService securityTokenService = pureeStorageService.getSecurityTokenService(businessName);
        return AjaxResult.success(securityTokenService);
    }

    /**
     * 文件上传
     *
     * @param file          文件
     * @param businessName  业务名称
     * @return AjaxResult
     */
    @PostMapping("/v2/upload")
    public AjaxResult ossUpload(@RequestParam(value = "file") MultipartFile file,
                                @RequestParam("businessName") String businessName) {
        String url = pureeStorageService.upload(file,businessName);
        if (StringUtils.isEmpty(url)) {
            return AjaxResult.error("文件上传失败");
        }
        return AjaxResult.success(url);
    }


    /**
     * 文件上传获取sts
     *
     * @return AjaxResult
     */
    @RequestMapping("/sts/token")
    public AjaxResult getStsToken( ) throws ClientException {
        StsTokenVO tokenVO = stsUtil.getToken("");
        return AjaxResult.success(tokenVO) ;
    }

    /**
     * 文件上传
     *
     * @param file         文件
     * @param directoryKey 文件目录关键字
     * @return AjaxResult
     */
    @PostMapping("upload")
    public AjaxResult ossUpload(@RequestParam(value = "file") MultipartFile file,
                                @RequestParam("directoryKey") Integer directoryKey) {
        String directory = OSSSaveDirectory.getDirectory(directoryKey);
        if (StringUtils.isEmpty(directory)) {
            return AjaxResult.error("文件目录不存在");
        }
        String url = ossUtil.simpleUpload(file, directory);
        if (StringUtils.isEmpty(url)) {
            return AjaxResult.error("文件上传失败");
        }
        return AjaxResult.success(url);
    }

    /**
     * 获取第三方文件上传的stsToken
     *
     * @param businessName 业务名称
     * @return AjaxResult
     * @throws ClientException 客户端异常
     */
    @GetMapping("/3rd/oss-token/{businessName}")
    @SignatureVerify(signParam = SignatureVerify.SignParamEnum.NO_PARAMETERS,signVersion = SignatureVerify.SignVersionEnum.V1)
    public AjaxResult get3rdStsToken(@PathVariable("businessName") String businessName) throws ClientException {
        StsTokenVO tokenVO = stsUtil.getToken(businessName);
        return AjaxResult.success(tokenVO);
    }

}
