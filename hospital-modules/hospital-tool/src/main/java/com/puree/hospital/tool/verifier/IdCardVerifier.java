package com.puree.hospital.tool.verifier;

import com.puree.hospital.common.api.domain.IdCardResult;

/**
 * <p>
 * 身份证验证器接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/24 18:08
 */
public interface IdCardVerifier {

    String SUFFIX = "IdCardVerifier";

    /**
     * <p>
     * 身份证校验
     * </p>
     *
     * @param idCardName 姓名
     * @param idCardNo   身份证号码
     * @return 身份证校验结果
     * @throws Exception 异常信息
     */
    IdCardResult verify(String idCardName, String idCardNo) throws Exception;
}
