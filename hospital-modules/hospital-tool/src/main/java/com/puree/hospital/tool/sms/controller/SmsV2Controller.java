package com.puree.hospital.tool.sms.controller;

import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.system.api.RemoteSystemUserService;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import com.puree.hospital.tool.sms.domain.enums.SMSMessageTemplateEnum;
import com.puree.hospital.tool.sms.service.SmsNotificationService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/12 17:48
 */
@RequestMapping("/v2/sms")
@RestController
public class SmsV2Controller {

    @Resource
    private SmsNotificationService smsNotificationService;
    @Resource
    private RemoteSystemUserService remoteSystemUserService;

    /**
     * 验证码短信
     * <p>发送验证码，会对前置条件进行判断：验证码错误次数，IP+UA次数校验，不判断手机号是否与医院关联</p>
     * @param smsNotificationDTO 业务参数
     * @return 发送结果
     */
    @PostMapping("/verify-code")
    @Log(title = "验证码短信", businessType = BusinessType.OTHER)
    public AjaxResult verifyCode(@RequestBody @Valid SmsNotificationDTO smsNotificationDTO) {
        // 进行发送条件校验
        AjaxResult ajaxResult = smsNotificationService.checkSendPreCondition(smsNotificationDTO.getPhoneNumber());
        if (!ajaxResult.isSuccess()) {
            return ajaxResult;
        }
        smsNotificationDTO.setTemplateType(SMSMessageTemplateEnum.VERIFY_CODE.getTemplateType());
        return smsNotificationService.sendSmsNotification(smsNotificationDTO);
    }

    /**
     * 内部验证码短信
     * <p>发送短信验证码，没有前置条件校验，不判断手机号是否与医院关联</p>
     * @param smsNotificationDTO 业务参数
     * @return 发送结果
     */
    @PostMapping("/verify-code/inner")
    @Log(title = "内部验证码短信", businessType = BusinessType.OTHER)
    public AjaxResult innerVerifyCode(@RequestBody @Valid SmsNotificationDTO smsNotificationDTO) {
        smsNotificationDTO.setTemplateType(SMSMessageTemplateEnum.VERIFY_CODE.getTemplateType());
        return smsNotificationService.sendSmsNotification(smsNotificationDTO);
    }

    /**
     * 医院后台发送验证码
     *  <p>发送短信验证码，没有前置条件校验，会判断手机号是否与医院关联</p>
     * @param smsNotificationDTO 业务参数
     * @return 发送结果
     */
    @PostMapping("/verify-code/hospital-backstage")
    @Log(title = "医院后台发送验证码", businessType = BusinessType.OTHER)
    public AjaxResult hospitalBackstageVerifyCode(@RequestBody @Valid SmsNotificationDTO smsNotificationDTO) {
        // 如果未传从请求头中获取医院id
        if (Objects.isNull(smsNotificationDTO.getHospitalId())) {
            smsNotificationDTO.setHospitalId(SecurityUtils.getHospitalId());
        }
        // 是否检查账号是否存在
        R<String> result = remoteSystemUserService.checkPhone(smsNotificationDTO.getPhoneNumber(), SecurityConstants.INNER, smsNotificationDTO.getHospitalId());
        if (Objects.isNull(result)) {
            return AjaxResult.error("手机号码不存在!!");
        } else if (Objects.equals(HttpStatus.ERROR, result.getCode())) {
            return AjaxResult.error(result.getMsg());
        }
        smsNotificationDTO.setTemplateType(SMSMessageTemplateEnum.VERIFY_CODE.getTemplateType());
        return smsNotificationService.sendSmsNotification(smsNotificationDTO);
    }

    /**
     * 发送模板消息
     *
     * @param baseSmsNotificationDTO 业务参数
     * @return 发送结果
     */
    @PostMapping("/template-message")
    @Log(title = "发送模板消息", businessType = BusinessType.OTHER)
    public AjaxResult templateMessage(@RequestBody @Valid SmsNotificationDTO baseSmsNotificationDTO) {
        return smsNotificationService.sendSmsNotification(baseSmsNotificationDTO);
    }

    /**
     * 校验短信验证码
     * @param phoneNumber 手机号
     * @param code        验证码
     * @return 校验结果
     */
    @GetMapping("/verify-code")
    @Log(title = "校验短信验证码", businessType = BusinessType.OTHER)
    public AjaxResult checkVerifyCode(@RequestParam("phoneNumber")String phoneNumber, @RequestParam("code") String code) {
        return smsNotificationService.checkVerifyCode(phoneNumber, code);
    }

}
