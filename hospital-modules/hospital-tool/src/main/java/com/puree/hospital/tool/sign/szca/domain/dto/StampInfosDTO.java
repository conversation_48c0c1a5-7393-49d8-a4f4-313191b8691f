package com.puree.hospital.tool.sign.szca.domain.dto;

import com.puree.hospital.tool.sign.szca.infrastructure.util.StreamConvertor;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/4 12:09
 */
@Data
public class StampInfosDTO {

    /**
     * 验证码
     */
    private String captcha = "";

    private String pin = StreamConvertor.base64Encode("123456".getBytes());
    /**
     * 签章类型 0：坐标盖章，1：关键字盖章
     */
    private String stampType = "0";

    /**
     * pdf的base64
     */
    private String base64File;

    private String fileName = "blank.pdf";

    /**
     * 签章的图片信息
     */
    private SignPicDTO signPic;

    /**
     * 签章对象ID
     */
    private String signerId;

    /**
     * 所有页都盖章？
     */
    private String pageAll = "false";

    /**
     * 签章时间是否显示？
     */
    private String showTime = "false";

    /**
     * 签章坐标信息
     */
    private List<Map<String,Object>> stampInfo;


}
