package com.puree.hospital.tool.sms.domain.enums;

import lombok.Getter;

/**
 * 消息模板
 * <AUTHOR>
 * @date 2025/3/13 18:19
 */
@Getter
public enum SMSMessageTemplateEnum {

    /**
     *  验证码短信 SMS_229365304
     */
    VERIFY_CODE("VERIFY_CODE", "您的验证码为：{code}，请勿泄露于他人！"),

    /**
     *  通知
     */
//        NOTIFY,

    /**
     *  审核
     */
//        AUDIT,

    /**
     *  通知医生回复患者消息 SMS_239310983
     */
    CONSULTATION_NO_REPLY("CONSULTATION_NO_REPLY", "您好，{doctorName} 医生，您已经有6个小时未回复患者 {patientName} 消息了，请及时进行回复。"),

    /**
     *  处方审核通知 SMS_465916673
     */
    PRESCRIPTION_APPROVE("PRESCRIPTION_APPROVE", "您好，医生已为您开具处方，请前往{name}，依次点击“个人中心-我的处方”下单购药。"),

    /**
     * 随访汇总提醒 SMS_465931345
     */
    FOLLOW_UP_SUMMARY("FOLLOW_UP_SUMMARY", "{hospital}健康提醒：您有新的随访任务待完成，详情请至{name}查看。"),

    /**
     *  随访消息提醒 SMS_465971317
     */
    FOLLOW_UP_EVENT("FOLLOW_UP_EVENT", "{hospital}温馨提示，您参加的随访计划有新消息未读，详情请至{name}查看。"),

    /**
     *  随访患教提醒 SMS_465911325
     */
    FOLLOW_UP_EVENT_TUTORIAL("FOLLOW_UP_EVENT_TUTORIAL", "{hospital}健康提醒：您参加的随访计划有新动态，详情请至{name}查看。"),

    /**
     *  随访问卷提醒V2 SMS_467195303
     */
    QUESTION("QUESTION", "健康提醒：您有新的问卷待完成，详情请至{name}查看。"),
    /**
     * 通知医生问诊接诊提醒 SMS_483425439
     */
    CONSULTATION_REMIND_DOCTOR_RECEIVE("CONSULTATION_REMIND_DOCTOR_RECEIVE", "您好，{doctorName}医生，{patientName}患者{number}小时前已向您发起问诊，请及时进行接诊。"),;

    private String templateType;

    private String templateContent;

    SMSMessageTemplateEnum(String templateType, String templateContent) {
        this.templateType = templateType;
        this.templateContent = templateContent;
    }

    public static SMSMessageTemplateEnum getByTemplateType(String templateType) {
        for (SMSMessageTemplateEnum templateEnum : SMSMessageTemplateEnum.values()) {
            if (templateEnum.getTemplateType().equals(templateType)) {
                return templateEnum;
            }
        }
        return null;
    }

    /**
     *  是否是通知医生回复患者消息
     * @param templateType  模板类型
     * @return  是否是通知医生回复患者消息
     */
    public static boolean isConsultationNoReplyType(String templateType) {
        return CONSULTATION_NO_REPLY.getTemplateType().equals(templateType);
    }

    /**
     * 是否是验证码短信
     * @param templateType  模板类型
     * @return  是否是验证码短信
     */
    public static boolean isVerifyCodeType(String templateType) {
        return VERIFY_CODE.getTemplateType().equals(templateType);
    }
}
