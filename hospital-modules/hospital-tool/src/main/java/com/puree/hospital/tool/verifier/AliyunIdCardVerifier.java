package com.puree.hospital.tool.verifier;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.http.AliyunHttpUtils;
import com.puree.hospital.common.api.domain.IdCardResult;
import com.puree.hospital.tool.idcard.constant.IdCardVerifyChannelConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.util.EntityUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 身份证本地核实
 * 接口文档：<a href="">...</a>
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/24 18:13
 */
@Slf4j
@Component(IdCardVerifyChannelConstant.ALIYUN + IdCardVerifier.SUFFIX)
public class AliyunIdCardVerifier extends BaseRemoteIdCardVerifier {

    private static final String VERIFY_CODE_PASSED = "1";

    @Resource
    private AliyunIdCardVerifyConfig aliyunIdCardVerifyConfig;

    @Override
    public IdCardResult remoteVerify(String idCardName, String idCardNo) throws Exception {
        //请求阿里云验证,设置请求Header
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + aliyunIdCardVerifyConfig.getAppcode());
        // 设置请求参数
        Map<String, String> querys = new HashMap<>(2);
        querys.put("identifyNum", idCardNo);
        querys.put("userName", idCardName);
        HttpResponse httpResponse = AliyunHttpUtils.doGet(aliyunIdCardVerifyConfig.getHost(), aliyunIdCardVerifyConfig.getPath(), "GET", headers, querys);
        //接口调用失败，未进入阿里云
        StatusLine statusLine = httpResponse.getStatusLine();
        if (Objects.isNull(statusLine)){
            log.error("{}身份证二要素接口请求结果为空", getChannel());
            return IdCardResult.error("系统错误");
        }
        if (HttpStatus.SUCCESS != statusLine.getStatusCode()) {
            log.error("{}身份证二要素接口请求失败，返回码为：{}，失败原因：{}", getChannel(), statusLine.getStatusCode(), statusLine.getReasonPhrase());
            return IdCardResult.error("系统错误");
        }
        HttpEntity entity = httpResponse.getEntity();
        if (Objects.isNull(entity)) {
            log.error("{}身份证验证请求响应结果内容为空", getChannel());
            return IdCardResult.error("系统错误");
        }
        String respStr = EntityUtils.toString(entity);
        if (StringUtils.isBlank(respStr)) {
            log.error("{}身份证校验请求返回为空", getChannel());
            return IdCardResult.error("系统错误");
        }
        log.info("阿里云身份证二要素接口返回：{}", respStr);
        //解析返回结果
        AliyunIdCardVerifyResponse verifyResult = JSON.parseObject(respStr, AliyunIdCardVerifyResponse.class);
        Integer code = verifyResult.getCode();
        if (Objects.isNull(code)) {
            log.error("{}身份证校验失败，返回码为空", getChannel());
            return IdCardResult.error("系统错误");
        }
        //阿里云返回码失败
        if (HttpStatus.SUCCESS != code) {
            List<Integer> errorCodes = aliyunIdCardVerifyConfig.getErrorCodes();
            if (CollectionUtil.isNotEmpty(errorCodes) && errorCodes.contains(code)){
                log.error("{}身份证校验失败，返回码为：{}，错误原因：{}", getChannel(), code, verifyResult.getMessage());
                return IdCardResult.error("系统错误");
            }
            return IdCardResult.fail("身份证校验失败，请核对身份证信息");
        }
        //判断身份证是否验证成功
        ResultData data = verifyResult.getData();
        if(Objects.isNull(data) || StringUtils.isBlank(data.getBizCode())){
            log.error("{}身份证校验失败，返回结果数据为空", getChannel());
            return IdCardResult.error("系统错误");
        }
        if(Objects.equals(VERIFY_CODE_PASSED, data.getBizCode())){
            return IdCardResult.pass("验证成功");
        }
        return IdCardResult.fail("身份证校验失败，请核对身份证信息");
    }

    @Override
    protected String getChannel() {
        return IdCardVerifyChannelConstant.ALIYUN;
    }

    @Component
    @ConfigurationProperties(prefix = "idcard.verify.aliyun")
    @Data
    private static class AliyunIdCardVerifyConfig {
        /**
         * 阿里云appcode
         */
        private String appcode;
        /**
         * 阿里云请求地址
         */
        private String host;
        /**
         * 阿里云请求路径
         */
        private String path;
        /**
         * 阿里云请求失败码
         */
        private List<Integer> errorCodes;
    }

    @Data
    public static class AliyunIdCardVerifyResponse implements Serializable {

        private static final long serialVersionUID = -5783824757682847922L;
        /**
         * 响应编码
         */
        private Integer code;
        /**
         * 相应信息
         */
        private String message;
        /**
         * 请求id
         */
        private String requestId;
        /**
         * 响应数据
         */
        private ResultData data;
    }

    @Data
    public static class ResultData {

        /**
         * 验证结果 1-成功
         */
        private String bizCode;
    }
}
