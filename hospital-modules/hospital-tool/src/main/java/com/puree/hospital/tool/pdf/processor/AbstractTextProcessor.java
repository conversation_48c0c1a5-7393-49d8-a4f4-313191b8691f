package com.puree.hospital.tool.pdf.processor;

/**
 * 文本处理器抽象类
 * <AUTHOR>
 */
public abstract class AbstractTextProcessor implements TextProcessor {
    /**
     * 下一个处理器
     */
    protected TextProcessor nextProcessor;

    /**
     * 上下文
     */
    protected TextProcessingContext context;

    @Override
    public TextProcessingContext getContext() {
        return context;
    }

    @Override
    public void setContext(TextProcessingContext context) {
        this.context = context;
    }


    @Override
    public void setNext(TextProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }


    @Override
    public TextProcessor getNext() {
        //传递上下文
        nextProcessor.setContext(context);
        return nextProcessor;
    }

    /**
     * 处理下一个处理器
     * @param text 上下文
     * @return 处理后的文本
     */
    protected String processNext(String text) {
        if (nextProcessor != null) {
            nextProcessor.setContext(context);
            return nextProcessor.process(text);
        }
        return text;
    }

}
