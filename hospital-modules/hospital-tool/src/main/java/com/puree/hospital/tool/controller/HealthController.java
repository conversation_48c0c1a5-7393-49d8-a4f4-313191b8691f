package com.puree.hospital.tool.controller;

import com.puree.hospital.common.security.annotation.SignatureVerify;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/4/11
 */
@Slf4j
@RestController
public class HealthController {

    @GetMapping("health")
    public String getHealth() {
        return "success!";
    }


    @PostMapping("/3rd/abc/test")
    @SignatureVerify(signParam = SignatureVerify.SignParamEnum.QUERY_STRING,signVersion = SignatureVerify.SignVersionEnum.V2)
    public String test(@RequestBody String body) {
        log.info("body:{}", body);
        return "success!";
    }
}
