package com.puree.hospital.tool.pdf.processor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "text.processor")
public class TextProcessorConfig {


    /* -------------------- ocr 触发配置  start -------------------- */

    /**
     * 触发 OCR 的最小文本长度阈值，低于此值尝试 OCR
     */
    private int minTextLengthThreshold = 50;

    /* -------------------- ocr 触发配置  end -------------------- */

}
