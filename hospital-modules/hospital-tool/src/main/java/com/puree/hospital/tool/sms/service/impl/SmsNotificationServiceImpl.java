package com.puree.hospital.tool.sms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.core.constant.LoginConstants;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.ServletUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import com.puree.hospital.tool.sms.handler.SmsNotificationHandler;
import com.puree.hospital.tool.sms.service.SmsNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/3/12 9:38
 */
@Service
@Slf4j
@RefreshScope
public class SmsNotificationServiceImpl implements SmsNotificationService {

    @Resource
    private RedisService redisService;

    @Resource
    private Map<String, SmsNotificationHandler> smsNotificationHandlerMap;

    /** 验证码错误次数达到上限，禁止获取验证码时间 单位秒 */
    @Value("${sms.code.forbidden-period:300}")
    private int limitGettingCodeTime;
    /** 单次验证码错误次数 */
    @Value("${sms.code.verify-count:3}")
    private int codeErrorLimit;
    /** 验证码错误最大次数 */
    @Value("${sms.code.max-retry-count:10}")
    private int codeErrorMaxLimit;
    @Value("${sms.sendLimitTime:5}")
    private long smsSendLimitTime;
    @Value("${sms.sendLimitCount:10}")
    private int smsSendLimitCount;

    /**
     *  指定渠道或者默认渠道
     */
    @Value("${sms.channel.designatedChannel:}")
    private String designatedChannel;
    @Value("${sms.channel.defaultChannel:ALIYUN}")
    private String defaultChannel;

    /**
     *  发送验证码消息
     * @param smsNotificationDTO 业务参数
     * @return 发送结果
     */
    @Override
    public AjaxResult sendSmsNotification(SmsNotificationDTO smsNotificationDTO) {
        SmsNotificationHandler smsNotificationHandler = getSmsNotificationHandler(smsNotificationDTO);
        AjaxResult ajaxResult = smsNotificationHandler.sendNotification(smsNotificationDTO);
        log.info("{} sendSmsNotification requestParam: {} responseResult: {}", smsNotificationHandler.getChannelName(), smsNotificationDTO, ajaxResult.isSuccess());
        return ajaxResult;
    }

    /**
     *  获取发信息处理类
     * @param smsNotificationDTO    业务参数
     * @return  处理类
     */
    private SmsNotificationHandler getSmsNotificationHandler(SmsNotificationDTO smsNotificationDTO) {
        String channelName = null;
        // 判断是否设置了指定渠道，如果未设置，判断是否由参数传入，未传则取默认渠道
        if (StrUtil.isNotBlank(designatedChannel)) {
            channelName = designatedChannel;
        } else if (Objects.nonNull(smsNotificationDTO.getChannel())) {
            channelName = smsNotificationDTO.getChannel().name();
        } else {
            channelName = defaultChannel;
        }
        SmsNotificationHandler smsNotification = smsNotificationHandlerMap.get(channelName + SmsNotificationHandler.SUFFIX);
        if (smsNotification == null) {
            throw new ServiceException("短信通道未配置");
        }
        return smsNotification;
    }

    /**
     * 检查发送前置条件
     *
     * @param phoneNumber 手机号
     * @return 检查结果
     */
    @Override
    public AjaxResult checkSendPreCondition(String phoneNumber) {
        // 判断是否错误次数达到限制
        Integer errorCount = redisService.getCacheObject(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber);
        if (errorCount != null && errorCount >= codeErrorMaxLimit) {
            //获取过期时间
            Long keyExpire = redisService.getKeyExpire(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber);
            return AjaxResult.error("验证码错误次数达到上限，请" + keyExpire + "秒后再试");
        }
        // 校验IP+agent限制发送次数
        String ip = ServletUtil.getClientIP(ServletUtils.getRequest(), "");
        String ua = ServletUtil.getHeader(ServletUtils.getRequest(), "User-Agent", "UTF-8");
        String limitKey = CacheConstants.SMS_SEND_LIMIT_KEY + ip + "_" + ua;
        int nowCount = (int) redisService.getIncr(limitKey) - 1;
        if (nowCount > smsSendLimitCount) {
            return AjaxResult.error("短信发送频繁，请稍后再试");
        }
        redisService.setCacheObject(limitKey, ++nowCount, smsSendLimitTime, TimeUnit.MINUTES);
        return AjaxResult.success();
    }

    /**
     * 校验短信验证码
     * @param phoneNumber 手机号
     * @param code        验证码
     * @return 校验结果
     */
    @Override
    public AjaxResult checkVerifyCode(String phoneNumber, String code) {
        log.debug("checkVerifyCode: phoneNumber:{} code:{}", phoneNumber, code);
        if (!StringUtils.hasText(code)) {
            return AjaxResult.error("验证码不能为空");
        }
        if (!StringUtils.hasText(phoneNumber)) {
            return AjaxResult.error("电话号码不能为空");
        }
        Integer errorCount = redisService.getCacheObject(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber);
        //判断是否 错误次数达到限制
        if (errorCount != null && errorCount >= codeErrorMaxLimit) {
            //获取过期时间
            Long keyExpire = redisService.getKeyExpire(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber);
            return AjaxResult.error("验证码错误次数达到上限，请" + keyExpire + "秒后再试");
        }
        String verifyKey = CacheConstants.SMS_VERIFY_KEY + phoneNumber;
        String cacheCode = redisService.getCacheObject(verifyKey);
        Integer singleErrorCount = redisService.getCacheObject(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber + ":" + cacheCode);
        //验证码错误次数超过3次，返回验证码已失效
        if (singleErrorCount != null && singleErrorCount >= codeErrorLimit) {
            redisService.deleteObject(verifyKey);
            return AjaxResult.error(LoginConstants.EXPIRED_MSG);
        }
        if (!code.equalsIgnoreCase(cacheCode)) {
            //记录单个验证码错误次数
            redisService.getIncr(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber + ":" + cacheCode, limitGettingCodeTime);
            //如果验证码错误 记录总错误次数
            redisService.getIncr(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber, limitGettingCodeTime);
            return AjaxResult.error("验证码错误");
        }
        redisService.deleteObject(verifyKey);
        return AjaxResult.success();
    }
}
