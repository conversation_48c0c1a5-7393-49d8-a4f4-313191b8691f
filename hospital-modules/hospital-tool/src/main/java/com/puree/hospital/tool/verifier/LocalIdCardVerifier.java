package com.puree.hospital.tool.verifier;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.tool.idcard.constant.IdCardVerifyChannelConstant;
import com.puree.hospital.common.api.domain.IdCardResult;
import com.puree.hospital.tool.idcard.domain.IdCardVerify;
import com.puree.hospital.tool.idcard.mapper.IdCardVerifyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <p>
 * 身份证本地核实
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/24 18:13
 */
@Slf4j
@Component(IdCardVerifyChannelConstant.LOCAL + IdCardVerifier.SUFFIX)
public class LocalIdCardVerifier implements IdCardVerifier {

    @Resource
    private IdCardVerifyMapper idCardVerifyMapper;

    @Override
    public IdCardResult verify(String idCardName, String idCardNo) {
        LambdaQueryWrapper<IdCardVerify> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdCardVerify::getIdCardNo, idCardNo);
        IdCardVerify idCardVerify = idCardVerifyMapper.selectOne(wrapper);
        if (idCardVerify != null) {
            if (idCardName.equals(idCardVerify.getIdCardName())) {
                return IdCardResult.pass("验证成功");
            } else {
                return IdCardResult.fail("身份证校验失败，请核对身份证信息");
            }
        }
        //数据库中无数据
        return IdCardResult.error("验证失败");
    }
}
