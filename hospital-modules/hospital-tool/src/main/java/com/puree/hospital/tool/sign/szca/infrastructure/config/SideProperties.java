package com.puree.hospital.tool.sign.szca.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "ca.side")
public class SideProperties {

    /**
     * 签名图片存放路径
     */
    private String ossDir;
    private String tempFileDir;
    private String backgroundImg;
}
