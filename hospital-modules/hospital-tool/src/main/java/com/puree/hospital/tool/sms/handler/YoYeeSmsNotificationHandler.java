package com.puree.hospital.tool.sms.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.ihuyi.HttpUtil;
import com.puree.hospital.common.core.utils.sign.MD5Util;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import com.puree.hospital.tool.sms.domain.YoYeeSmsProperties;
import com.puree.hospital.tool.sms.domain.SmsMessageBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *  有易短信通知处理类
 * <AUTHOR>
 * @date 2025/3/12 17:04
 */
@Slf4j
@Component(SmsNotificationHandler.YOYEE + SmsNotificationHandler.SUFFIX)
@ConditionalOnProperty(value = "yoyee.sms.endpoint")
public class YoYeeSmsNotificationHandler extends BaseSmsNotificationHandler {

    @Resource
    private YoYeeSmsProperties yoyeeSmsProperties;

    /**
     * 获取模板编号
     * @param smsNotificationDTO    入参
     * @return
     */
    @Override
    protected String getTemplateCode(SmsNotificationDTO smsNotificationDTO) {
        // 没有模板
        return null;
    }

    /**
     *  构建短信内容
     * @param smsNotificationDTO  消息内容
     * @param signName            签名
     * @return
     */
    @Override
    protected String buildMessageContent(SmsNotificationDTO smsNotificationDTO, String signName) {
        String messageContent = convertRemoteTemplateMessageContent(smsNotificationDTO);
        // 有易需要把短信签名加载消息体前面
        return String.format(SmsNotificationHandler.YOYEE_MESSAGE_FORMAT, signName, messageContent);
    }

    /**
     *  发送短信消息
     * @param messageBody 消息体
     */
    @Override
    protected void sendSMS(SmsMessageBody messageBody) {
        try {
            JSONObject requestParams = buildRequestParams(messageBody);
            log.debug("yoyee requestParams :{}", requestParams);
            String result = HttpUtil.httpPostRequest(yoyeeSmsProperties.getEndpoint(), requestParams.toJSONString());
            log.debug("yoyee sendSms result:{}", result);
            if (StrUtil.isEmpty(result)) {
                throw new ServiceException("短信发送失败");
            }
            JSONObject responseObj = JSONObject.parseObject(result);
            if (responseObj.getInteger("code") != 0) {
                log.error("yoyee send sms exception, result:{}", result);
                String message = responseObj.getString("message");
                throw new ServiceException(message);
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    @Override
    public String getChannelName() {
        return SmsNotificationHandler.YOYEE;
    }

    /**
     *  构建请求参数
     * @param messageBody
     * @return
     */
    private JSONObject buildRequestParams(SmsMessageBody messageBody) {
        long timeMillis = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userName", yoyeeSmsProperties.getUserName());
        jsonObject.put("messageList", buildMessageList(messageBody.getPhoneNumber(), messageBody.getMessageContent()));
        jsonObject.put("timestamp", timeMillis);
        jsonObject.put("sign", getSign(timeMillis));
        return jsonObject;
    }

    /**
     *  构建消息集合
     * @param phoneNumber  手机号
     * @param content   消息内容
     * @return  消息集合
     */
    private JSONArray buildMessageList(String phoneNumber, String content) {
        JSONArray messageList = new JSONArray();
        JSONObject messageBody = new JSONObject();
        messageBody.put("phone", phoneNumber);
        messageBody.put("content", content);
        messageList.add(messageBody);
        return messageList;
    }

    /**
     * 获取签名
     * <p>计算规则： MD5(userName + timestamp + MD5(password))  MD5 32位小写</p>
     * @return 签名
     */
    private String getSign(long timeMillis) {
        return MD5Util.md5Encrypt32Lower(yoyeeSmsProperties.getUserName() + timeMillis + MD5Util.md5Encrypt32Lower(yoyeeSmsProperties.getPassword()));
    }

}
