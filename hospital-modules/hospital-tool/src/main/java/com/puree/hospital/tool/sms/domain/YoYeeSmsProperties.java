package com.puree.hospital.tool.sms.domain;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 *  有易短信配置
 * <AUTHOR>
 * @date 2025/3/12 17:17
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "yoyee.sms")
public class YoYeeSmsProperties {

    /**
     *  短信发送地址
     */
    private String endpoint;

    /**
     *  账号
     */
    private String userName;

    /**
     *  密码
     */
    private String password;
}
