package com.puree.hospital.tool.sign.szca.infrastructure.util;


import com.puree.hospital.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;


/**
 * 字符与流的转换
 */
public class StreamConvertor {

    private static Logger LOGGER = LoggerFactory.getLogger(StreamConvertor.class);

    private static final String HEX_STRING = "0123456789ABCDEF";

    private static final int BUFFER_SIZE = 0x80000; // 缓冲大小512K

    private final static char[] BASE64_ENCODE_CHARS = new char[] {'A', 'B', 'C', 'D', 'E', 'F',
        'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
        'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p',
        'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7',
        '8', '9', '+', '/'};

    private final static byte[] BASE64_DECODE_CHARS = new byte[] { -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57,
        58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13,
        14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30,
        31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1,
        -1, -1, -1};

    /**
     * byte[]转HEX
     * @param bytes
     * @return
     */
    public static String hexEncode(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        // 将字节数组中每个字节拆解成2位16进制整数
        for (byte aByte : bytes) {
            sb.append(HEX_STRING.charAt((aByte & 0xf0) >> 4));
            sb.append(HEX_STRING.charAt((aByte & 0x0f)));
        }
        return sb.toString();
    }

    /**
     * HEX转byte[]
     *
     * @param hex
     * @return
     */
    public static byte[] hexDecode(String hex) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(hex.length() / 2);
        // 将每2位16进制整数组装成一个字节
        for (int i = 0, n = hex.length(); i < n; i += 2) {
            outputStream.write( (HEX_STRING.indexOf(hex.charAt(i)) << 4 | HEX_STRING.indexOf(hex.charAt(i + 1))));
        }
        byte[] data = outputStream.toByteArray();
        try {
            outputStream.close();
        } catch (IOException e) {
            LOGGER.error("关闭ByteArrayOutputStream失败", e);
        }
        return data;
    }

    /**
     * byte[]转base64
     * @param data
     * @return
     */
    public static String base64Encode(byte[] data)
    {
        StringBuilder buff = new StringBuilder(data.length * 4 / 3);
        int len = data.length;
        int i = 0;
        int b1, b2, b3;
        while (i < len)
        {
            b1 = data[i++ ] & 0xff;
            if (i == len)
            {
                buff.append(BASE64_ENCODE_CHARS[b1 >>> 2]);
                buff.append(BASE64_ENCODE_CHARS[ (b1 & 0x3) << 4]);
                buff.append("==");
                break;
            }
            b2 = data[i++ ] & 0xff;
            if (i == len)
            {
                buff.append(BASE64_ENCODE_CHARS[b1 >>> 2]);
                buff.append(BASE64_ENCODE_CHARS[ ( (b1 & 0x03) << 4) | ( (b2 & 0xf0) >>> 4)]);
                buff.append(BASE64_ENCODE_CHARS[ (b2 & 0x0f) << 2]);
                buff.append("=");
                break;
            }
            b3 = data[i++ ] & 0xff;
            buff.append(BASE64_ENCODE_CHARS[b1 >>> 2]);
            buff.append(BASE64_ENCODE_CHARS[ ( (b1 & 0x03) << 4) | ( (b2 & 0xf0) >>> 4)]);
            buff.append(BASE64_ENCODE_CHARS[ ( (b2 & 0x0f) << 2) | ( (b3 & 0xc0) >>> 6)]);
            buff.append(BASE64_ENCODE_CHARS[b3 & 0x3f]);
        }
        return buff.toString();
    }

    /**
     * base64转byte[]
     * @param b64
     * @return
     */
    public static byte[] base64Decode(String b64) {
        try
        {
            byte[] data = b64.getBytes(StandardCharsets.US_ASCII);
            int len = data.length;
            StringBuilder buff = new StringBuilder(len * 3 / 4);
            int i = 0;
            int b1, b2, b3, b4;
            while (i < len)
            {
                /* b1 */
                do
                {
                    b1 = BASE64_DECODE_CHARS[data[i++ ]];
                }
                while (i < len && b1 == -1);
                if (b1 == -1) {
                    break;
                }
                /* b2 */
                do
                {
                    b2 = BASE64_DECODE_CHARS[data[i++ ]];
                }
                while (i < len && b2 == -1);
                if (b2 == -1) {
                    break;
                }
                buff.append((char) ( (b1 << 2) | ( (b2 & 0x30) >>> 4)));
                /* b3 */
                do
                {
                    b3 = data[i++ ];
                    if (b3 == 61) {
                        return buff.toString().getBytes(StandardCharsets.ISO_8859_1);
                    }
                    b3 = BASE64_DECODE_CHARS[b3];
                }
                while (i < len && b3 == -1);
                if (b3 == -1) {
                    break;
                }
                buff.append((char) ( ( (b2 & 0x0f) << 4) | ( (b3 & 0x3c) >>> 2)));
                /* b4 */
                do
                {
                    b4 = data[i++ ];
                    if (b4 == 61) {
                        return buff.toString().getBytes(StandardCharsets.ISO_8859_1);
                    }
                    b4 = BASE64_DECODE_CHARS[b4];
                }
                while (i < len && b4 == -1);
                if (b4 == -1) {
                    break;
                }
                buff.append((char) ( ( (b3 & 0x03) << 6) | b4));
            }
            return buff.toString().getBytes(StandardCharsets.ISO_8859_1);
        } catch (Exception e) {
            if (b64 != null && b64.length() > 30) {
                b64 = b64.substring(0, 27) + "...";
            }
            throw new RuntimeException("Base64数据（" + b64 + "）编码格式错误", e);
        }
    }

    /**
     * 字符转utf-8编码
     * @param s
     * @return
     */
    public static String utf8Encode(String s) {
        StringBuilder sb = new StringBuilder(s.length() * 3 / 2);
        char c;
        byte[] b;
        for (int n = s.length(), i = 0; i < n; i++ )
        {
            c = s.charAt(i);
            if (c >= 0 && c <= 255)
            {
                sb.append(c);
            }
            else
            {
                try
                {
                    b = Character.toString(c).getBytes(StandardCharsets.UTF_8);
                } catch (Exception ex) {
                    b = new byte[0];
                }
                for (int value : b) {
                    int k = value;
                    if (k < 0) {
                        k += 256;
                    }
                    sb.append("%").append(Integer.toHexString(k).toUpperCase());
                }
            }
        }
        return sb.toString();
    }

    /**
     * byte[]存文件
     * @param buffer
     * @param targetPath
     * @throws IOException 
     */
    public static void bytes2file(byte[] buffer, String targetPath) throws IOException {
        OutputStream out = null;
        BufferedOutputStream bos = null;
        try {
            out = Files.newOutputStream(Paths.get(targetPath));
            bos = new BufferedOutputStream(out, BUFFER_SIZE);
            bos.write(buffer);
            bos.flush();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    LOGGER.error("输出流保存为文件关闭流时异常", e);
                }
            }

            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    LOGGER.error("byte输出流流保存为文件关闭流时异常", e);
                }
            }
        }
    }

    /**
     * 把文件转为byte数组
     * @param path
     * @return
     * @throws IOException
     */
    public static byte[] file2bytes(String path) throws IOException {
        return inputStream2bytes(Files.newInputStream(Paths.get(path)));
    }

    /** 
     * 将InputStream转换成byte数组 
     * @param in InputStream 
     * @return byte[] 
     * @throws IOException 
     */
    public static byte[] inputStream2bytes(InputStream in) throws IOException {
        ByteArrayOutputStream baos = null;
        try
        {
            baos = new ByteArrayOutputStream();
            byte[] data = new byte[BUFFER_SIZE];
            int count = -1;
            while ( (count = in.read(data, 0, BUFFER_SIZE)) != -1) {
                baos.write(data, 0, count);
            }
            return baos.toByteArray();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    LOGGER.error( "输入流关闭流时异常", e);
                }
            }
            if (baos != null)  {
                try {
                    baos.close();
                } catch (IOException e) {
                    LOGGER.error( "byte输入流关闭流时异常", e);
                }
            }
                
        }
    }

    /**
     * 将文件转成base64 字符串
     * @param path 文件路径
     * @return
     * @throws IOException 
     */
    public static String file2base64(String path) throws IOException {
        return base64Encode(file2bytes(path));
    }

    /**
     * 将base64字符解码保存文件
     * @param base64Code
     * @param targetPath
     * @throws IOException
     */
    public static void base64ToFile(String base64Code, String targetPath) throws IOException {
        bytes2file(base64Decode(base64Code), targetPath);
    }

    /**
     * 将字符串保存到文件
     * @param str
     * @param targetPath
     * @throws IOException 
     */
    public static void string2file(String str, String targetPath, String charset) throws IOException {
        BufferedWriter writer = null;
        OutputStreamWriter osw = null;
        FileOutputStream fos = null;
        try
        {
            fos = new FileOutputStream(targetPath);
            if (StringUtils.isNotBlank(charset)) {
                osw = new OutputStreamWriter(fos, charset);
            } else {
                osw = new OutputStreamWriter(fos);
            }
            writer = new BufferedWriter(osw, BUFFER_SIZE);
            writer.write(str);
            writer.flush();
        }
        finally
        {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    LOGGER.error("关闭输出流时异常", e);
                }
            }
            if (osw != null) {
                try {
                    osw.close();
                } catch (IOException e) {
                    LOGGER.error("关闭输出流时异常", e);
                }
            }
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    LOGGER.error("关闭输出流时异常", e);
                }
            }
        }
    }

    /**
     * 将字符串保存到文件
     * @param str
     * @param targetPath
     * @throws IOException
     */
    public static void string2file(String str, String targetPath) throws IOException {
        string2file(str, targetPath, null);
    }

    /**
     * 流转字符
     * @param in
     * @return
     * @throws IOException 
     */
    public static String inputStream2string(InputStream in, String charset) throws IOException {
        BufferedReader reader = null;
        InputStreamReader isr = null;
        try {
            if (StringUtils.isNotBlank(charset)) {
                isr = new InputStreamReader(in, charset);
            } else {
                isr = new InputStreamReader(in);
            }
            reader = new BufferedReader(isr, BUFFER_SIZE);
            StringBuffer buf = new StringBuffer();
            String s = null;
            while ( (s = reader.readLine()) != null)
            {
                buf.append(s);
            }
            return buf.toString();
        }
        finally
        {
            if (reader != null) {
                try
                {
                    reader.close();
                } catch (IOException e) {
                    LOGGER.error( "关闭输入流时异常", e);
                }
            }

            if (isr != null) {
                try {
                    isr.close();
                } catch (IOException e) {
                    LOGGER.error( "关闭输入流时异常", e);
                }
            }

            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    LOGGER.error("关闭输入流时异常", e);
                }
            }
        }
    }

    /**
     * 流转字符
     * @param in
     * @return
     * @throws IOException 
     */
    public static String inputStream2string(InputStream in) throws IOException {
        return inputStream2string(in, null);
    }

    /**
     * 读取文件数据为字符串
     * @param filePath
     * @return
     * @throws IOException
     */
    public static String file2string(String filePath) throws IOException {
        return inputStream2string(Files.newInputStream(Paths.get(filePath)), null);
    }

    /**
     * 流转字符
     * @param filePath
     * @param charset
     * @return
     * @throws IOException
     */
    public static String file2string(String filePath, String charset) throws IOException {
        return inputStream2string(Files.newInputStream(Paths.get(filePath)), charset);
    }
    
}