package com.puree.hospital.tool;

import com.puree.hospital.common.security.annotation.EnableHospitalFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@EnableHospitalFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class HospitalToolApplication {
    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod", "false");
        SpringApplication.run(HospitalToolApplication.class, args);
        System.out.println("工具模块启动成功");
    }
}
