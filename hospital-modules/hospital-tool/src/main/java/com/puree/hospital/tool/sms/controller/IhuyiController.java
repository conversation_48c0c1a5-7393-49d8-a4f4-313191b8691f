package com.puree.hospital.tool.sms.controller;

import com.puree.hospital.common.core.utils.ihuyi.IhuyiUtil;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.common.redis.service.RedisService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("ihuyi")
public class IhuyiController extends BaseController {

    @Resource
    private IhuyiUtil ihuyiUtil;
    @Resource
    private RedisService redisService;
//
//    /**
//     * 验证码短信
//     * @param phoneNumbers
//     * @return
//     */
//    @GetMapping("verifyCode")
//    public AjaxResult verifyCode(@RequestParam("phoneNumbers") String phoneNumbers) {
//        try {
//            String randomString = CommonUtils.getRandomString();
//            //缓存验证码
//            redisService.setCacheObject(CacheConstants.SMS_VERIFY_KEY + phoneNumbers, randomString, 300L, TimeUnit.SECONDS);
//            ihuyiUtil.sendSMS(phoneNumbers,randomString);
//            return AjaxResult.success("验证码发送成功，5分钟有效。");
//        } catch (Exception ex) {
//            return AjaxResult.error(ex.getMessage());
//        }
//    }


    /**
     * 通知短信
     * @param phoneNumbers
     * @return
     */
    @GetMapping("sendNotice")
    @Log(title = "互亿短信", businessType = BusinessType.OTHER)
    public AjaxResult sendNotice(@RequestParam("phoneNumbers") String phoneNumbers,@RequestParam("content") String content,@RequestParam("sign") String sign) {
        try {
            //发送通知短信
            ihuyiUtil.sendNotice(phoneNumbers,content,sign);
            return AjaxResult.success();
        } catch (Exception ex) {
            return AjaxResult.error(ex.getMessage());
        }
    }
}
