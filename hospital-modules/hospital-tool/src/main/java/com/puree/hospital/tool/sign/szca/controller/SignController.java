package com.puree.hospital.tool.sign.szca.controller;

import com.itextpdf.kernel.geom.Vector;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.JsonUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.oss.OSSSaveDirectory;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.tool.api.model.dto.CaSignRequestDTO;
import com.puree.hospital.tool.api.model.dto.CaSignReturnDTO;
import com.puree.hospital.tool.sign.szca.domain.vo.PersonFileInfoVO;
import com.puree.hospital.tool.sign.szca.domain.vo.SignFileInfoVO;
import com.puree.hospital.tool.sign.szca.infrastructure.config.SideProperties;
import com.puree.hospital.tool.sign.szca.infrastructure.config.SignatureProperties;
import com.puree.hospital.tool.sign.szca.infrastructure.util.FileUtil;
import com.puree.hospital.tool.sign.szca.service.SignService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/szca/sign")
public class SignController extends BaseController {

    @Resource
    private SideProperties sideProperties;

    @Resource
    private SignatureProperties signatureProperties;

    @Resource
    private SignService signService;

    @GetMapping("number")
    public AjaxResult getNumber() {
        try {
            return AjaxResult.success(signService.getNumber());
        } catch (Exception e) {
            logger.error(e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("apply")
    public AjaxResult applyCert(@RequestParam("videoFile") MultipartFile videoFile,
                                @RequestParam("personFileInfo") String personFileInfo) {
        try {
            //先判断临时文件目录是否存在，如果不存在，则需要创建
            File file = new File(sideProperties.getTempFileDir());
            if (!file.exists()) {
                file.mkdir();
            }
            PersonFileInfoVO fileInfo = JsonUtils.fromJson(personFileInfo, PersonFileInfoVO.class);
            //创建本地临时视频文件
//            MultipartFile videoFile =personFileInfo.getVideoFile();
            file = new File(sideProperties.getTempFileDir() + "/" + IdUtils.simpleUUID() + ".mp4");
            videoFile.transferTo(file);
            Map<String, String> map = signService.certApply(fileInfo.getOrderNo(), fileInfo, file);
            return AjaxResult.success(map);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("pdfSignature")
    public AjaxResult pdfSignature(SignFileInfoVO signFileInfo) throws Exception {
        try {
            //先判断临时文件目录是否存在，如果不存在，则需要创建
            File file = new File(sideProperties.getTempFileDir());
            if (!file.exists()) {
                file.mkdir();
            }

            //创建本地签名文件
            MultipartFile multipartFile = signFileInfo.getFile();
            String signFilePath = sideProperties.getTempFileDir() + "/" + IdUtils.simpleUUID() + ".png";
            File signFile = new File(signFilePath);
            multipartFile.transferTo(signFile);

            //创建本地底图文件
            String blankFilePath = sideProperties.getTempFileDir() + "/blank.pdf";
            File blankFile = new File(blankFilePath);
            if (!blankFile.exists()) {
                FileUtil.remoteFileToLocal(sideProperties.getBackgroundImg(), blankFilePath);
            }
//            String ossDir = OSSSaveDirectory.CASIGNATURE.getValue() + "/" + signFileInfo.getHospitalId();
            String ossDir = OSSSaveDirectory.CASIGNATURE.getValue();
            Map<String, Object> position = new HashMap<>();
            position.put("num",1);
            Vector pos = new Vector(80,110,0);
            position.put("pos",pos);
            CaSignRequestDTO caSignRequestDTO = new CaSignRequestDTO();
            caSignRequestDTO.setSignId(signFileInfo.getSignerId());
            caSignRequestDTO.setPdfFilePath(signFilePath);
            caSignRequestDTO.setPdfFilePath(blankFilePath);
            caSignRequestDTO.setPosition(position);
            caSignRequestDTO.setLocalSignDir(sideProperties.getTempFileDir());
            caSignRequestDTO.setOssDir(ossDir);
            CaSignReturnDTO signResult = signService.signature(caSignRequestDTO);
            return AjaxResult.success(signResult);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("feign/pdfSign")
    public AjaxResult casignature(@RequestBody CaSignRequestDTO carequestDTO) {
        try {
            CaSignReturnDTO result = signService.signature(carequestDTO);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PreAuthorize(hasPermi = "signature:pc:sign")
    @GetMapping("pcSign")
    public AjaxResult getPcSign(@RequestParam("applyName") String applyName,
                                @RequestParam("applyIdNo") String applyIdNo,
                                @RequestParam("applyPhone") String applyPhone,
                                @RequestParam("companyName") String companyName) throws Exception {
        String reqId = signatureProperties.getReqId();
        String pcProjectId = signatureProperties.getPcProjectId();
        String redirectUrl = signatureProperties.getPcCertUrl();
        applyName = URLEncoder.encode(applyName, "utf-8");
        companyName = URLEncoder.encode(companyName, "utf-8");
        String sign = signService.signString(signatureProperties.getReqId(), applyName, companyName, applyIdNo, applyPhone,
                signatureProperties.getKeyCode(), signatureProperties.getPcProjectId());
        String result = redirectUrl + "?reqId=" + reqId +
                // 考虑到get请求时，浏览器会自动进行一次解码，对参数加一层转码
                "&applyName=" + URLEncoder.encode(applyName, "utf-8") + "&applyIdNo=" + applyIdNo +
                "&applyPhone=" + applyPhone + "&sign=" + sign + "&projectid=" + pcProjectId + "&companyName=" + URLEncoder.encode(companyName, "utf-8");
        return AjaxResult.success(result);
    }
}
