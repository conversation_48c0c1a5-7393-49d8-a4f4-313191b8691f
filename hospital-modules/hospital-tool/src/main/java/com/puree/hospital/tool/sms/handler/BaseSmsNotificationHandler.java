package com.puree.hospital.tool.sms.handler;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.business.api.RemoteHospitalSmsConfigService;
import com.puree.hospital.business.api.model.BusHospitalSmsConfigVo;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.CommonUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import com.puree.hospital.tool.sms.domain.SmsMessageBody;
import com.puree.hospital.tool.sms.domain.enums.SMSMessageTemplateEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/3/12 9:43
 */
@Data
@Slf4j
@RefreshScope
public abstract class BaseSmsNotificationHandler implements SmsNotificationHandler {

    @Resource
    private RedisService redisService;
    @Resource
    private RemoteHospitalSmsConfigService remoteHospitalSmsConfigService;

    @Value("${default.sms.sendCode:zayy}")
    private String defaultSmsSendCode;

    /**
     * 发送消息
     *
     * @param smsNotificationDTO    发送参数
     * @return AjaxResult
     */
    @Override
    public AjaxResult sendNotification(SmsNotificationDTO smsNotificationDTO) {
        String phoneNumber = smsNotificationDTO.getPhoneNumber();
        if (!Validator.isMobile(phoneNumber)) {
            return AjaxResult.error("请输入正确的手机号码");
        }
        // 判断是否是验证码
        if (SMSMessageTemplateEnum.isVerifyCodeType(smsNotificationDTO.getTemplateType())) {
            return sendVerifyCodeMessage(smsNotificationDTO);
        } else {
            return sendTemplateMessage(smsNotificationDTO);
        }
    }

    /**
     *  发送验证码消息
     * @param smsNotificationDTO
     */
    private AjaxResult sendVerifyCodeMessage(SmsNotificationDTO smsNotificationDTO) {
        String phoneNumber = smsNotificationDTO.getPhoneNumber();
        // 生成验证码
        String verificationCode = CommonUtils.getRandomString();
        //缓存验证码
        redisService.setCacheObject(CacheConstants.SMS_VERIFY_KEY + phoneNumber, verificationCode, 300L, TimeUnit.SECONDS);
        // 回填参数
        JSONObject templateParam = new JSONObject();
        templateParam.put("code", verificationCode);
        smsNotificationDTO.setMessageContent(templateParam.toJSONString());
        // 构建消息
        SmsMessageBody smsMessageBody = buildMessageBody(smsNotificationDTO);
        // 发送短信
        sendSMS(smsMessageBody);
        // 存储验证码到数据库
        storeVerificationCode(phoneNumber, verificationCode);
        return AjaxResult.success(Boolean.TRUE, "验证码发送成功，5分钟有效。");
    }

    /**
     *  发送短信模板消息
     * @param smsNotificationDTO
     */
    private AjaxResult sendTemplateMessage(SmsNotificationDTO smsNotificationDTO) {
        // 构建消息
        SmsMessageBody smsMessageBody = buildMessageBody(smsNotificationDTO);
        // 发送短信
        sendSMS(smsMessageBody);
        return AjaxResult.success(Boolean.TRUE, "短信发送成功");
    }

    /**
     * 存储验证码
     *
     * @param phoneNumber      手机号
     * @param verificationCode 验证码
     */
    private void storeVerificationCode(String phoneNumber, String verificationCode) {
        JSONObject smsRecordDTO = new JSONObject();
        smsRecordDTO.put("code", verificationCode);
        smsRecordDTO.put("phoneNumbers", phoneNumber);
        remoteHospitalSmsConfigService.smsInsert(smsRecordDTO);
    }

    /**
     * 获取短信配置
     *
     * @param hospitalId
     * @return 短信配置
     */
    protected BusHospitalSmsConfigVo getSmsConfig(Long hospitalId) {
        R<BusHospitalSmsConfigVo> configVoR;
        if (hospitalId == null) {
            configVoR = remoteHospitalSmsConfigService.selectOne(null, defaultSmsSendCode);
        } else {
            configVoR = remoteHospitalSmsConfigService.selectOne(hospitalId, null);
        }
        if (Objects.isNull(configVoR) || Objects.isNull(configVoR.getData())) {
            throw new ServiceException("当前医院没有短信签名！");
        }
        return configVoR.getData();
    }

    /**
     *  构建消息体
     * @param smsNotificationDTO    入参
     * @return  发送消息对象
     */
    private SmsMessageBody buildMessageBody(SmsNotificationDTO smsNotificationDTO) {
        String signName = getSignName(smsNotificationDTO);
        String messageContent = buildMessageContent(smsNotificationDTO, signName);
        String templateCode = getTemplateCode(smsNotificationDTO);
        SmsMessageBody smsMessageBody = new SmsMessageBody();
        smsMessageBody.setPhoneNumber(smsNotificationDTO.getPhoneNumber());
        smsMessageBody.setSignName(signName);
        smsMessageBody.setTemplateCode(templateCode);
        smsMessageBody.setMessageContent(messageContent);
        return smsMessageBody;
    }

    /**
     *  获取签名信息
     * @param smsNotificationDTO    入参
     * @return
     */
    protected String getSignName(SmsNotificationDTO smsNotificationDTO) {
        BusHospitalSmsConfigVo busHospitalSmsConfigVo = getSmsConfig(smsNotificationDTO.getHospitalId());
        return busHospitalSmsConfigVo.getHospitalSign();
    }

    /**
     *  获取模板code
     * @param smsNotificationDTO    入参
     * @return 模板code
     */
    protected abstract String getTemplateCode(SmsNotificationDTO smsNotificationDTO);

    /**
     *  构建消息内容
     * @param smsNotificationDTO  消息内容
     * @param signName            签名
     * @return  消息内容
     */
    protected abstract String buildMessageContent(SmsNotificationDTO smsNotificationDTO, String signName);

    /**
     *  发送短信消息
     * @param messageBody 消息体
     */
    protected abstract void sendSMS(SmsMessageBody messageBody);

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    @Override
    public abstract String getChannelName();

    /**
     *  转换远程模板消息内容；目前阿里云短信是走的模板方式，互亿和有易是直发，如果直接切有易短信需要适配一下
     * @param smsNotificationDTO 入参
     * @return  转换好的消息内容
     */
    protected String convertRemoteTemplateMessageContent(SmsNotificationDTO smsNotificationDTO) {
        if (Objects.nonNull(smsNotificationDTO.getTemplateType()) && Objects.nonNull(smsNotificationDTO.getMessageContent())) {
            SMSMessageTemplateEnum byTemplateType = SMSMessageTemplateEnum.getByTemplateType(smsNotificationDTO.getTemplateType());
            if (Objects.nonNull(byTemplateType)) {
                JSONObject jsonObject = JSONObject.parseObject(smsNotificationDTO.getMessageContent());
                return StrUtil.format(byTemplateType.getTemplateContent(), jsonObject);
            }
        }
        return smsNotificationDTO.getMessageContent();
    }

}
