package com.puree.hospital.tool.pdf.processor;

/**
 * pdf 文本处理器
 * <AUTHOR>
 */
public interface TextProcessor {
    /**
     * 处理器
     * @param text 处理前
     * @return 结果
     */
    String process(String text);

    /**
     * 设置下一个处理器
     *
     * @param next 处理器
     */
    void setNext(TextProcessor next);

    /**
     * 写入上下文
     * @param context 上下文
     */
    void setContext(TextProcessingContext context);

    /**
     * 获取上下文
     * @return 上下文
     */
    TextProcessingContext getContext();

    /**
     * 获取下一个处理器
     * @return 下一个处理器
     */
    TextProcessor getNext();
}
