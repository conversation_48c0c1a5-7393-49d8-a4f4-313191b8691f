package com.puree.hospital.tool.sms.handler;

import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.ihuyi.HttpUtil;
import com.puree.hospital.common.core.utils.ihuyi.IhuyiProperties;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import com.puree.hospital.tool.sms.domain.SmsMessageBody;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 互亿短信通知处理类
 * <AUTHOR>
 * @date 2025/3/12 16:12
 */
@Slf4j
@Component(SmsNotificationHandler.HUYI + SmsNotificationHandler.SUFFIX)
@ConditionalOnProperty(value = "ihuyi.account")
public class HuYiSmsNotificationHandler extends BaseSmsNotificationHandler {

    @Resource
    private IhuyiProperties ihuyiProperties;
    private static final String URL = "http://106.ihuyi.cn/webservice/sms.php?method=Submit";

    /**
     *  获取模板code
     * @param smsNotificationDTO    入参
     * @return 模板code
     */
    @Override
    protected String getTemplateCode(SmsNotificationDTO smsNotificationDTO) {
        // 没有模板
        return null;
    }

    /**
     *  构建消息内容
     * @param smsNotificationDTO  消息内容
     * @param signName            签名
     * @return  消息内容
     */
    @Override
    protected String buildMessageContent(SmsNotificationDTO smsNotificationDTO, String signName) {
        return convertRemoteTemplateMessageContent(smsNotificationDTO);
    }

    /**
     *  发送短信消息
     * @param messageBody 消息体
     */
    @Override
    protected void sendSMS(SmsMessageBody messageBody) {
        try {
            Map<String, Object> requestParam = buildRequestParam(messageBody);
            String result = HttpUtil.httpPostRequest(URL, requestParam);
            Document doc = DocumentHelper.parseText(result);
            Element root = doc.getRootElement();
            String code = root.elementText("code");
            String msg = root.elementText("msg");
            String smsid = root.elementText("smsid");
            log.debug("互亿无线短信mobile={},content={},code={},msg={},smsid={}", messageBody.getPhoneNumber(), messageBody.getMessageContent(), code, msg, smsid);
            if (!"2".equals(code)) {
                throw new ServiceException(msg);
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            log.error("huyi send sms exception", e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    @Override
    public String getChannelName() {
        return SmsNotificationHandler.HUYI;
    }

    /**
     *  构建互译短信请求参数
     * @param messageBody 消息体
     * @return  Map
     */
    private Map<String, Object> buildRequestParam(SmsMessageBody messageBody) {
        log.debug("huyi sms request param: mobile:{} content:{} sign:{}", messageBody.getPhoneNumber(), messageBody.getMessageContent(), messageBody.getSignName());
        Map<String, Object> params = new HashMap<>();
        params.put("account", ihuyiProperties.getAccount());
        params.put("password", ihuyiProperties.getPassword());
        params.put("sign", messageBody.getSignName());
        params.put("mobile", messageBody.getPhoneNumber());
        params.put("content", messageBody.getMessageContent());
        return params;
    }
}
