package com.puree.hospital.tool.sms.handler;

import cn.hutool.core.util.StrUtil;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.sms.SMSProperties;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import com.puree.hospital.tool.sms.domain.SmsMessageBody;
import com.puree.hospital.tool.sms.domain.enums.SMSMessageTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 *  阿里云短信通知处理类
 * <AUTHOR>
 * @date 2025/3/12 11:29
 */
@Slf4j
@Component(SmsNotificationHandler.ALIYUN + SmsNotificationHandler.SUFFIX)
@ConditionalOnProperty(value = "aliyun.sms.accessKeyId")
public class AliYunSmsNotificationHandler extends BaseSmsNotificationHandler {

    @Resource
    private SMSProperties smsProperties;

    private final static String SM_CD = "该账号验证码获取冷却中，请一分钟后再试";
    private final static String SM_Error = "触发分钟级流控Permits:1";

    private Client createClient() throws Exception {
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(smsProperties.getAccessKeyId())
                // 您的AccessKey Secret
                .setAccessKeySecret(smsProperties.getAccessKeySecret())
                // 访问的域名
                .setEndpoint(smsProperties.getEndpoint());
        return new Client(config);
    }

    /**
     *  获取模板code
     * @param smsNotificationDTO    入参
     * @return 模板code
     */
    @Override
    protected String getTemplateCode(SmsNotificationDTO smsNotificationDTO) {
        String templateCode = null;
        if (Objects.nonNull(smsNotificationDTO.getTemplateType())) {
            SMSMessageTemplateEnum templateTypeEnum = SMSMessageTemplateEnum.getByTemplateType(smsNotificationDTO.getTemplateType());
            if (Objects.isNull(templateTypeEnum)) {
                throw new ServiceException("aliyun 短信模板类型错误");
            }
            switch (templateTypeEnum) {
                case VERIFY_CODE:
                    templateCode = smsProperties.getVerifyTemplateCode();
                    break;
                case CONSULTATION_NO_REPLY:
                    templateCode = smsProperties.getConsultationNoReplyTemplateCode();
                    break;
                case PRESCRIPTION_APPROVE:
                    templateCode = smsProperties.getPrescriptionApproveCode();
                    break;
                case FOLLOW_UP_SUMMARY:
                    templateCode = smsProperties.getFollowUpSummary();
                    break;
                case FOLLOW_UP_EVENT:
                    templateCode = smsProperties.getFollowUpEventMsgCode();
                    break;
                case FOLLOW_UP_EVENT_TUTORIAL:
                    templateCode = smsProperties.getFollowUpEventTutorialCode();
                    break;
                case QUESTION:
                    templateCode = smsProperties.getQuestionCode();
                    break;
                case CONSULTATION_REMIND_DOCTOR_RECEIVE:
                    templateCode = smsProperties.getRemindDoctorReceiveCode();
                    break;
            }
        }
        if (StrUtil.isBlank(templateCode)) {
            throw new ServiceException("aliyun 短信模板编码未配置");
        }
        return templateCode;
    }

    /**
     *  构建消息内容
     * @param smsNotificationDTO  消息内容
     * @param signName            签名
     * @return  消息内容
     */
    @Override
    protected String buildMessageContent(SmsNotificationDTO smsNotificationDTO, String signName) {
        return smsNotificationDTO.getMessageContent();
    }

    /**
     *  获取签名信息
     * @param smsNotificationDTO    入参
     * @return
     */
    @Override
    protected String getSignName(SmsNotificationDTO smsNotificationDTO) {
        if (SMSMessageTemplateEnum.isConsultationNoReplyType(smsNotificationDTO.getTemplateType())) {
            return smsProperties.getSignName();
        } else {
            return super.getSignName(smsNotificationDTO);
        }
    }

    /**
     *  发送短信消息
     * @param messageBody 消息体
     */
    @Override
    protected void sendSMS(SmsMessageBody messageBody) {
        SendSmsRequest sendSmsRequest = buildSendSmsRequest(messageBody);
        log.debug("aliyun sendSmsRequest:{}", sendSmsRequest);
        try {
            Client client = createClient();
            SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
            log.debug("aliyun sendSmsResponse:{}", sendSmsResponse);
            if (!"OK".equals(sendSmsResponse.getBody().getCode())) {
                String message = sendSmsResponse.getBody().getMessage();
                if (StrUtil.equals(message, SM_Error)) {
                    throw new ServiceException(SM_CD);
                }
                throw new ServiceException(message);
            }
        } catch (ServiceException se) {
            throw se;
        } catch (Exception e) {
            log.error("aliyun send sms exception", e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    @Override
    public String getChannelName() {
        return SmsNotificationHandler.ALIYUN;
    }

    /**
     *  构建发送短信请求体
     * @param messageBody 消息体
     * @return  SendSmsRequest请求体
     */
    private SendSmsRequest buildSendSmsRequest(SmsMessageBody messageBody) {
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(messageBody.getPhoneNumber())
                .setSignName(messageBody.getSignName())
                .setTemplateCode(messageBody.getTemplateCode())
                .setTemplateParam(messageBody.getMessageContent());
        return sendSmsRequest;
    }

}
