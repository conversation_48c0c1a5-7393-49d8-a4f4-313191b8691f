package com.puree.hospital.tool.sign.szca.domain.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class Certificate implements Serializable {

    private static final long serialVersionUID = -1219843110081398483L;

    private String applyId;
    private String subject;
    private String signCertSn;
    private String p12pwd;
    private String signP7b;
    private String signP12;
    private String resultCode;
    private String resultDesc;
    private String endDate;
}
