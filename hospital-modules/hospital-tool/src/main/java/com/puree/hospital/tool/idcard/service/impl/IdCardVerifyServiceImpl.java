package com.puree.hospital.tool.idcard.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.puree.hospital.common.api.domain.IdCardResult;
import com.puree.hospital.tool.idcard.service.IdCardVerifyService;
import com.puree.hospital.tool.verifier.IdCardVerifier;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * IdCardVerifyServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/24 18:05
 */
@Slf4j
@Service
public class IdCardVerifyServiceImpl implements IdCardVerifyService {

    @Resource
    private Map<String, IdCardVerifier> idCardVerifyHandlerMap;

    @Resource
    private IdCardVerifyProperties idCardVerifyProperties;

    @Override
    public IdCardResult verify(String idCardName, String idCardNo) {
        try {
            IdCardResult result = null;
            List<IdCardVerifier> verifierList = getIdCardVerifierList();
            if (CollectionUtil.isEmpty(verifierList)) {
                log.error("身份证二要素验证渠道未配置或配置错误");
                return IdCardResult.error("系统错误");
            }
            for (IdCardVerifier verifier : verifierList) {
                result = verifier.verify(idCardName, idCardNo);
                if (Objects.nonNull(result) && result.isMatched()) {
                    return result;
                }
            }
            return result;
        }catch (Exception e) {
            log.error("身份证二要素校验失败", e);
            return IdCardResult.error("系统错误");
        }

    }

    /**
     * 获取配置的idCardVerifyHandler
     *
     * @return 需要处理的handler
     */
    private List<IdCardVerifier> getIdCardVerifierList() {
        ArrayList<IdCardVerifier> verifierList = new ArrayList<>();
        idCardVerifyProperties.getChannels().forEach(channel -> {
            String key = channel + IdCardVerifier.SUFFIX;
            IdCardVerifier idCardVerifyHandler = idCardVerifyHandlerMap.get(key);
            if (Objects.nonNull(idCardVerifyHandler)) {
                verifierList.add(idCardVerifyHandler);
            }
        });
        return verifierList;
    }

    @Data
    @ConfigurationProperties(prefix = "idcard.verify")
    @Component
    public static class IdCardVerifyProperties {

        /**
         * 验证渠道顺序列表
         */
        private List<String> channels;

        /**
         * 远程接口最大重试次数
         */
        private long remoteMaxRetryCount = 5L;
    }
}
