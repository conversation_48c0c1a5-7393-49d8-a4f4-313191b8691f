package com.puree.hospital.tool.pdf.processor;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.tool.ocr.service.OcrService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * ocr 处理器
 * <AUTHOR>
 */
@Slf4j
@Component(TextProcessorConstant.OCR_TEXT_PROCESSOR)
@AllArgsConstructor
public class OcrTextProcessor extends AbstractTextProcessor {

    private final TextProcessorConfig config;
    private final OcrService ocrService;

    /**
     * ocr 处理
     * @param text 处理前
     * @return 处理后
     */
    @Override
    public String process(String text) {
        // 检查文本长度是否小于配置的最小值
        if (text == null || text.length() < config.getMinTextLengthThreshold()) {
            log.info("文本内容为空或长度小于配置值{}，尝试使用 aliOCR 识别", config.getMinTextLengthThreshold());
            String fileFullUrl = getContext().getFileFullUrl();
            if (fileFullUrl != null && !fileFullUrl.isEmpty()) {
                try {
                    String ocrText = ocrService.pdfOcr(fileFullUrl);
                    if (ocrText != null && !ocrText.isEmpty()) {
                        return processNext(ocrText);
                    }
                } catch (Exception e) {
                    log.error("OCR处理失败", e);
                }
            }
        }
        return processNext(text);
    }


}
