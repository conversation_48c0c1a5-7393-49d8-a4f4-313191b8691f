package com.puree.hospital.tool.sms.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.business.api.RemoteHospitalSmsConfigService;
import com.puree.hospital.business.api.model.BusHospitalSmsConfigVo;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.CommonUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.common.sms.SMSUtil;
import com.puree.hospital.common.sms.model.VerifyCodeSMSParam;
import com.puree.hospital.system.api.RemoteSystemUserService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * SMS控制器
 *
 * <AUTHOR>
 * @date 2025/01/10
 */
@RefreshScope
@RequestMapping("sms")
@RestController
public class SMSController extends BaseController {
    @Resource
    private SMSUtil smsUtil;
    @Resource
    private RedisService redisService;
    @Resource
    private RemoteHospitalSmsConfigService remoteHospitalSmsConfigService;
    @Resource
    private RemoteSystemUserService remoteSystemUserService;

    @Value("${sms.sendLimitTime:5}")
    private long smsSendLimitTime;

    @Value("${sms.sendLimitCount:10}")
    private int smsSendLimitCount;
    @Value("${default.sms.sendCode:zayy}")
    private String defaultSmsSendCode;

    /** 验证码错误最大次数 */
    @Value("${sms.code.max-retry-count:10}")
    private int codeErrorMaxLimit;

    /**
     * 验证码短信
     *
     * @param phoneNumber 手机号
     * @return 发送结果
     */
    @GetMapping("verifyCode")
    @Log(title = "验证码短信", businessType = BusinessType.OTHER)
    public AjaxResult verifyCode(HttpServletRequest request, @RequestParam("phoneNumber") String phoneNumber) {
        try {
            Long hospitalId = SecurityUtils.getHospitalId();

            //判断是否 错误次数达到限制
            Integer errorCount = redisService.getCacheObject(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber);
            if (errorCount != null && errorCount >= codeErrorMaxLimit){
                //获取过期时间
                Long keyExpire = redisService.getKeyExpire(CacheConstants.SMS_CODE_ERROR_LIMIT_KEY + phoneNumber);
                return AjaxResult.error("验证码错误次数达到上限，请"+keyExpire+"秒后再试");
            }
            verifySendSmsLimit(request);
            return sendVerifyCode(phoneNumber, hospitalId);
        } catch (Exception ex) {
            return AjaxResult.error(ex.getMessage());
        }
    }

    /**
     * 短信发送限制校验
     *
     * @param request http请求
     */
    private void verifySendSmsLimit(HttpServletRequest request) {
        // ip+agent限制发送次数
        String ip = ServletUtil.getClientIP(request, "");
        String ua = ServletUtil.getHeader(request, "User-Agent", "UTF-8");
        String limitKey = CacheConstants.SMS_SEND_LIMIT_KEY + ip + "_" + ua;
        int nowCount = (int) redisService.getIncr(limitKey) - 1;
        if (nowCount > smsSendLimitCount) {
            throw new ServiceException("短信发送频繁，请稍后再试");
        }
        redisService.setCacheObject(limitKey, ++nowCount, smsSendLimitTime, TimeUnit.MINUTES);
    }

    /**
     * 验证码短信
     *
     * @param phoneNumbers 手机号
     * @return AjaxResult
     */
    @GetMapping("innerVerifyCode")
    @Log(title = "验证码短信", businessType = BusinessType.OTHER)
    public AjaxResult verifyCode(@RequestParam("phoneNumbers") String phoneNumbers, @RequestParam(value = "hospitalId", required = false) Long hospitalId) {
        try {
            return sendVerifyCode(phoneNumbers, hospitalId);
        } catch (Exception ex) {
            return AjaxResult.error(ex.getMessage());
        }
    }

    /**
     * 医院后台发送验证码
     *
     * @param phoneNumbers 手机号
     * @return AjaxResult
     */
    @GetMapping("hospitalBackstageVerifyCode")
    @Log(title = "医院后台发送验证码", businessType = BusinessType.OTHER)
    public AjaxResult hospitalBackstageVerifyCode(@RequestParam("phoneNumbers") String phoneNumbers, @RequestParam(value = "hospitalId", required = false) Long hospitalId) {
        try {
            R<String> result = remoteSystemUserService.checkPhone(phoneNumbers, SecurityConstants.INNER,hospitalId);
            if (null != result) {
                if (HttpStatus.ERROR == result.getCode()) {
                    return AjaxResult.error(result.getMsg());
                }
            } else {
                return AjaxResult.error("手机号码不存在!!");
            }
            return sendVerifyCode(phoneNumbers, hospitalId);
        } catch (Exception ex) {
            return AjaxResult.error(ex.getMessage());
        }
    }

    private @NotNull AjaxResult sendVerifyCode(String phoneNumbers, Long hospitalId) throws Exception {
        logger.info("医院ID:{}" , hospitalId);
        R<BusHospitalSmsConfigVo> configVoR;
        if (hospitalId == null) {
            configVoR = remoteHospitalSmsConfigService.selectOne(null,  defaultSmsSendCode);
        } else {
            configVoR = remoteHospitalSmsConfigService.selectOne(hospitalId, null);
        }
        BusHospitalSmsConfigVo data = configVoR.getData();
        if (data == null) {
            return AjaxResult.error("当前医院没有短信签名！");
        }
        String randomString = CommonUtils.getRandomString();
        //缓存验证码
        redisService.setCacheObject(CacheConstants.SMS_VERIFY_KEY + phoneNumbers, randomString, 300L, TimeUnit.SECONDS);
        VerifyCodeSMSParam verifyCodeSmsParam = new VerifyCodeSMSParam()
                .setCode(randomString)
                .setPhoneNumbers(phoneNumbers)
                .setHospitalSign(data.getHospitalSign());
        smsUtil.sendVerifyCodeSMS(verifyCodeSmsParam);
        JSONObject smsRecordDTO = new JSONObject();
        smsRecordDTO.put("code", randomString);
        smsRecordDTO.put("phoneNumbers", phoneNumbers);
        remoteHospitalSmsConfigService.smsInsert(smsRecordDTO);
        return AjaxResult.success(null,"验证码发送成功，5分钟有效。");
    }
}
