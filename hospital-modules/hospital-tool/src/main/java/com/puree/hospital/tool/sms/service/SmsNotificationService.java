package com.puree.hospital.tool.sms.service;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;

/**
 * <AUTHOR>
 * @date 2025/3/11 17:27
 */
public interface SmsNotificationService {

    /**
     *  发送验证码消息
     * @param smsNotificationDTO 业务参数
     * @return 发送结果
     */
    AjaxResult sendSmsNotification(SmsNotificationDTO smsNotificationDTO);

    /**
     * 检查发送前置条件
     *
     * @param phoneNumber 手机号
     * @return 检查结果
     */
    AjaxResult checkSendPreCondition(String phoneNumber);

    /**
     * 校验短信验证码
     * @param phoneNumber 手机号
     * @param code        验证码
     * @return 校验结果
     */
    AjaxResult checkVerifyCode(String phoneNumber, String code);

}
