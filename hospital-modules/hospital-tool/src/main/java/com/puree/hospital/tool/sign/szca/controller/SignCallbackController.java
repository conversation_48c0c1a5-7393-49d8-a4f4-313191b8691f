package com.puree.hospital.tool.sign.szca.controller;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.RemoteSignatureService;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.SHA1Util;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.SystemClockUtil;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.tool.sign.szca.infrastructure.CaRedisKey;
import com.puree.hospital.tool.sign.szca.domain.vo.CertApplyCallbackInfoVO;
import com.puree.hospital.tool.sign.szca.infrastructure.config.SignatureProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/szca/callback")
@RestController
public class SignCallbackController extends BaseController {

    @Resource
    private RedisService redisService;

    @Resource
    private RemoteSignatureService remoteSignatureService;

    @Resource
    private SignatureProperties signatureProperties;

    @PostMapping("certApplyNotify")
    public AjaxResult pcCertApplyNotify(HttpServletRequest request,
                                        @RequestBody CertApplyCallbackInfoVO certApplyCallbackInfo) {
        try {
            Map<Integer, String> map = this.check(request);
            if (MapUtil.isNotEmpty(map)) {
                Map.Entry<Integer, String> entry = map.entrySet().stream().findFirst().orElse(null);
                if (Objects.nonNull(entry)) {
                    return AjaxResult.error(entry.getKey(), null, entry.getValue());
                }
            }
            if (StringUtils.isEmpty(certApplyCallbackInfo.getApplyIdNo())
                    && StringUtils.isEmpty(certApplyCallbackInfo.getCertApplyId())) {
                return AjaxResult.error(1005, null, "证书ID或身份证缺失！");
            }
            log.info("网厅回调返回参数={}", JSONObject.toJSONString(certApplyCallbackInfo));
            R<Integer> data = remoteSignatureService.insert(certApplyCallbackInfo.getApplyIdNo(),
                    certApplyCallbackInfo.getCertApplyId(), certApplyCallbackInfo.getCertExpire(),
                    SecurityConstants.INNER);
            logger.info("接口回调返回值：{}", JSON.toJSONString(data));
            if (data == null) {
                return AjaxResult.error(-1, null, "系统异常");
            } else {
                int code = data.getCode();
                if (code != HttpStatus.SUCCESS) {
                    logger.error(data.getMsg());
                    return AjaxResult.error(-1, null, "系统异常");
                }
            }
            return AjaxResult.success();
        } catch (Exception e) {
            logger.error("certApplyNotify异常：{}", e.getMessage(), e);
            return AjaxResult.error(-1, null, "系统异常");
        }
    }

    /**
     * 校验签名
     *
     * @param request 请求参数
     */
    private Map<Integer, String> check(HttpServletRequest request) {
        Map<Integer, String> map = new ConcurrentHashMap<>();
        String appId = request.getHeader("appId");
        String timestampStr = request.getHeader("timestamp");
        String sign = request.getHeader("sign");
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(timestampStr) || StringUtils.isBlank(sign)) {
            map.put(1001, "校验参数有误");
            return map;
        }

        String[] array = new String[]{appId, signatureProperties.getPcAppSecret(), timestampStr};
        Arrays.sort(array);
        String str = String.join("", array);
        String signSha = SHA1Util.getSHA1(str);
        long timestamp;
        try {
            timestamp = Long.parseLong(timestampStr.trim());
        } catch (Exception e) {
            map.put(1001, "校验参数有误");
            return map;
        }
        //允许前端传过来的时间与服务器时间相差60秒
        if (Math.abs((SystemClockUtil.currentTimeMillis() - timestamp) / 1000) > 60L) {
            map.put(1002, "timestamp无效");
            return map;
        }
        //检查当前请求是否是重复请求
        boolean exists = redisService.hasKey(CaRedisKey.CA_SIGNATURE_CALLBACK + timestampStr);
        if (exists) {
            map.put(1003, "重复请求");
            return map;
        }
        //后端MD5签名校验与前端签名sign值比对
        if (!(sign.equalsIgnoreCase(signSha))) {
            map.put(1004, "签名不通过");
            return map;
        }
        //将时间戳存进redis
        redisService.setCacheObject(CaRedisKey.CA_SIGNATURE_CALLBACK + timestampStr, signSha, 60L, TimeUnit.SECONDS);
        return map;
    }
}
