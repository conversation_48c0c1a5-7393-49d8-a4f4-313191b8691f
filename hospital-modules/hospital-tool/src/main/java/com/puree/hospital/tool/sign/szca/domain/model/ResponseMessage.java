package com.puree.hospital.tool.sign.szca.domain.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ResponseMessage<T> implements Serializable {

    private static final long serialVersionUID = 8097575089919281570L;

    private Integer code;

    private String msg;

    private Boolean success;

    private String orderno;
    /**
     * 每次发起签署时，自定义的业务单号
     */
    private String objectId;

    private T obj;
}
