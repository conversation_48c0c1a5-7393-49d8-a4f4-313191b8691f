package com.puree.hospital.tool.verifier;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.http.HttpUtils;
import com.puree.hospital.common.api.domain.IdCardResult;
import com.puree.hospital.tool.idcard.constant.IdCardVerifyChannelConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 身份证本地核实
 * 接口文档：<a href="https://user.ihuyi.com/new/data_service/real_name/overview">...</a>
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/24 18:13
 */
@Slf4j
@Component(IdCardVerifyChannelConstant.HUYI + IdCardVerifier.SUFFIX)
public class HuyiIdCardVerifier extends BaseRemoteIdCardVerifier {

    private static final int VERIFY_STATUS_PASSED = 2;

    @Resource
    private HuyiIdCardVerifyConfig huyiIdCardVerifyConfig;

    @Override
    protected IdCardResult remoteVerify(String idCardName, String idCardNo) throws Exception {
        //请求互亿验证
        String time = "" + (System.currentTimeMillis() / 1000L);
        String origin = huyiIdCardVerifyConfig.getAccount() + huyiIdCardVerifyConfig.getPassword()
                + huyiIdCardVerifyConfig.getApp() + idCardName + idCardNo+ time;
        String passwordMd5 = DigestUtils.md5Hex(origin);
        String param = "account=" + huyiIdCardVerifyConfig.getAccount() + "&password=" + passwordMd5 + "&app="
                + huyiIdCardVerifyConfig.getApp() + "&name=" + idCardName + "&id_card_no=" + idCardNo+"&time=" + time;
        String sendPost = HttpUtils.sendPost(huyiIdCardVerifyConfig.getUrl(), param);
        log.info("互亿身份证二要素接口返回：{}",sendPost);
        if(StringUtils.isBlank(sendPost)){
            log.error("{}请求接口返回为空", getChannel());
            return IdCardResult.error("系统错误");
        }
        HuyiIdCardVerifyResponse response = JSON.parseObject(sendPost, HuyiIdCardVerifyResponse.class);
        if (Objects.isNull(response)) {
            log.error("{}请求结果转化返回为空", getChannel());
            return IdCardResult.error("系统错误");
        }
        // 返回码
        Integer code = response.getCode();
        if (VERIFY_STATUS_PASSED != code) {
            List<Integer> errorCodes = huyiIdCardVerifyConfig.getErrorCodes();
            if (CollectionUtil.isNotEmpty(errorCodes) && errorCodes.contains(code)){
                log.error("{}请求错误，请求码为：{},错误原因：{}", getChannel(), code, response.getMsg());
                return IdCardResult.error(response.getIdCardId());
            }
            return IdCardResult.fail("身份证校验失败，请核对身份证信息");
        }
        // 验证结果
        ResultData data = response.getResult();
        if(Objects.isNull(data) || Objects.isNull(data.getStatus())){
            log.error("{}身份证校验状态为空", getChannel());
            return IdCardResult.fail("系统错误");
        }
        if (data.getStatus() == VERIFY_STATUS_PASSED){
            return IdCardResult.pass("验证成功");
        }
        return IdCardResult.fail("身份证校验失败，请核对身份证信息");
    }

    @Override
    protected String getChannel() {
        return IdCardVerifyChannelConstant.HUYI;
    }

    @Component
    @ConfigurationProperties(prefix = "idcard.verify.huyi")
    @Data
    private static class HuyiIdCardVerifyConfig {
        /**
         * 互亿验证接口地址
         */
        private String url;
        /**
         * APIID（用户中心【实名认证】-【产品总览】查看）
         */
        private String account;
        /**
         * 1、APIKEY（用户中心【实名认证】-【产品总览】查看）
         * 2、动态密码（生成动态密码方式请看该文档末尾的说明）
         */
        private String password;
        /**
         * 互亿验证接口app
         */
        private String app = "id2";

        /**
         * 互亿验证接口返回码
         */
        private List<Integer> errorCodes;
    }

    @Data
    private static class HuyiIdCardVerifyResponse implements Serializable {

        private static final long serialVersionUID = -5290251253116736754L;

        /**
         * 响应编码，返回值为2时，表示提交成功
         */
        private Integer code;

        /**
         * 提交结果描述
         */
        private String msg;

        /**
         * 当提交成功后，此字段为流水号，否则为0
         */
        private String idCardId;

        /**
         * 认证结果
         */
        private ResultData result;
    }

    @Data
    private static class ResultData implements Serializable {

        private static final long serialVersionUID = 4975644221784843919L;

        /**
         *认证状态（0未认证，1认证未通过，2认证通过
         */
        private Integer status;

        /**
         * 保留字段
         */
        private Object info;
    }
}
