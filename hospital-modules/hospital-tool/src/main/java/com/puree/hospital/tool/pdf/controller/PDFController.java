package com.puree.hospital.tool.pdf.controller;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.tool.pdf.service.PDFService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * pdf 处理
 * <AUTHOR>
 */
@RequestMapping("pdf")
@RestController
@AllArgsConstructor
public class PDFController {

    private final PDFService pdfService;

    /**
     * pdf to 文字
     * @param fileUrl
     * @return
     */
    @GetMapping("url-text")
    public AjaxResult readPdfFromUrlAndConvertToText(String fileUrl) {
        return AjaxResult.success(pdfService.readPdfFromUrlAndConvertToText(fileUrl));
    }

}
