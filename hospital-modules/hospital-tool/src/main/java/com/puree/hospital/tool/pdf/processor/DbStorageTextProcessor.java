package com.puree.hospital.tool.pdf.processor;

import com.puree.hospital.tool.ocr.constant.OcrTypeEnum;
import com.puree.hospital.tool.ocr.service.OcrDocumentsService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 内容缓存处理器
 * <AUTHOR>
 */
@Component(TextProcessorConstant.DB_STORAGE_TEXT_PROCESSOR)
@AllArgsConstructor
public class DbStorageTextProcessor extends AbstractTextProcessor {

    private final OcrDocumentsService ocrDocumentsService;

    @Override
    public String process(String text) {
        //写入数据库
        ocrDocumentsService.saveOcrDocument(getContext().getSignature(), OcrTypeEnum.PDF, text, getContext().getFileFullUrl());
        // 继续链中的下一个处理器
        return processNext(text);
    }
}
