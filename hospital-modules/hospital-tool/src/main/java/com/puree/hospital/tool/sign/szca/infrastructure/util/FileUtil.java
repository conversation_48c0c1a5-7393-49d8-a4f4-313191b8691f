package com.puree.hospital.tool.sign.szca.infrastructure.util;

import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jmimemagic.Magic;
import net.sf.jmimemagic.MagicMatch;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.cert.X509Certificate;
import java.util.Map;

/**
 * <p>
 * 文件上传
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/29 16:38
 */
@Slf4j
public class FileUtil {

    /**
     * 文件上传
     *
     * @param requestUrl 请求url
     * @param textMap    请求参数
     * @param fileMap    文件信息
     * @return 上传结果
     * @throws Exception 异常信息
     */
    public static String uploadFile(String requestUrl,
                                    Map<String, String> textMap,
                                    Map<String, File> fileMap) throws Exception {
        String res = "";
        HttpURLConnection conn = null;
        String boundary = "-----------------12345654321-----------";
        try {
            URL url = new URL(requestUrl);
            HostnameVerifier hv = (urlHostName, session) -> {
                log.info("Warning: URL Host: {} vs. {}", urlHostName, session.getPeerHost());
                return true;
            };
            trustAllHttpsCertificates();
            HttpsURLConnection.setDefaultHostnameVerifier(hv);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(15000);
            conn.setReadTimeout(60000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
            conn.setRequestProperty("Charset", "UTF-8");
            OutputStream out = new DataOutputStream(conn.getOutputStream());
            if (textMap != null) {
                StringBuilder strBuf = new StringBuilder();
                for (Map.Entry<String, String> entry : textMap.entrySet()) {
                    String inputName = entry.getKey();
                    String inputValue = entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    strBuf.append("\r\n").append("--").append(boundary).append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\"").append(inputName).append("\"\r\n\r\n");
                    strBuf.append(inputValue);
                }
                out.write(strBuf.toString().getBytes(StandardCharsets.UTF_8));
            }

            if (fileMap != null) {
                for (Map.Entry<String, File> entry : fileMap.entrySet()) {
                    String inputName = entry.getKey();
                    File file = entry.getValue();
                    if (file == null) {
                        continue;
                    }
                    byte[] fileByte = IoUtil.readBytes(Files.newInputStream(file.toPath()));
                    String filename = file.getName();

                    StringBuilder strBuf = new StringBuilder();
                    strBuf.append("\r\n").append("--").append(boundary).append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\"").append(inputName).append("\"; filename=\"").append(filename).append("\"\r\n");
                    MagicMatch match = Magic.getMagicMatch(file, false, true);
                    strBuf.append("Content-Type:").append(match.getMimeType()).append("\r\n\r\n");
                    out.write(strBuf.toString().getBytes());
                    DataInputStream in = new DataInputStream(new ByteArrayInputStream(fileByte));
                    int bytes = 0;
                    byte[] bufferOut = new byte[1024];
                    while ((bytes = in.read(bufferOut)) != -1) {
                        out.write(bufferOut, 0, bytes);
                    }
                    in.close();
                }
            }
            byte[] endData = ("\r\n--" + boundary + "--\r\n").getBytes();
            out.write(endData);
            out.flush();
            out.close();
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                // 读取返回数据
                StringBuilder strBuf = new StringBuilder();
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));

                String line;
                while ((line = reader.readLine()) != null) {
                    strBuf.append(line).append("\n");
                }
                res = strBuf.toString();
                reader.close();
            } else {
                StringBuilder error = new StringBuilder();
                BufferedReader bufferedReader = new BufferedReader(
                        new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8));
                String line1;
                while ((line1 = bufferedReader.readLine()) != null) {
                    error.append(line1).append("\n");
                }
                res = error.toString();
                bufferedReader.close();
            }
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        return res;
    }

    // 解决PKIX path building failed问题，在代码中必须要忽略证书信任问题
    private static void trustAllHttpsCertificates() throws Exception {
        javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[1];
        javax.net.ssl.TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s){

            }

            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {

            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };
        trustAllCerts[0] = trustManager;
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

    }

    /**
     * 将远程文件写入到本地
     * @param remoteUrl 远程文件地址
     * @param localUrl  本地文件地址
     */
    public static void remoteFileToLocal(String remoteUrl, String localUrl)  {
        File file = new File(localUrl);
        try( FileOutputStream fops = new FileOutputStream(file)) {
            byte[] bytes = getFileFromNetByUrl(remoteUrl);
            fops.write(bytes);
            fops.flush();
            fops.close();
            log.debug("文件已经写入:{}", localUrl);
        } catch (Exception e) {
            log.error("文件写入异常", e);
        }
    }

    /**
     * 根据地址获得数据的字节流
     *
     * @param strUrl 网络连接地址
     * @return 文件字节信息
     */
    public static byte[] getFileFromNetByUrl(String strUrl) throws Exception {
        HttpURLConnection conn = null;
        try {
            URL url = new URL(strUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            // 通过输入流获取图片数据
            InputStream inStream = conn.getInputStream();
            return IoUtil.readBytes(inStream);
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }

    }
}
