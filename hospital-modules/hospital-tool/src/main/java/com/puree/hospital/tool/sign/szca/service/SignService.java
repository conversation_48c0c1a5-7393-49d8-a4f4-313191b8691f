package com.puree.hospital.tool.sign.szca.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.SHA1Util;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.utils.http.HttpUtils;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.common.redis.service.RedisService;

import com.puree.hospital.tool.sign.szca.infrastructure.CaRedisKey;
import com.puree.hospital.tool.sign.szca.domain.model.Certificate;
import com.puree.hospital.tool.sign.szca.domain.model.NumberResult;
import com.puree.hospital.tool.sign.szca.domain.model.ResponseMessage;
import com.puree.hospital.tool.sign.szca.domain.vo.PersonFileInfoVO;
import com.puree.hospital.tool.sign.szca.infrastructure.config.SignatureProperties;
import com.puree.hospital.tool.sign.szca.infrastructure.util.FileUtil;
import com.puree.hospital.tool.sign.szca.infrastructure.util.PDFUtils;
import com.puree.hospital.tool.sign.szca.infrastructure.util.StreamConvertor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.activation.MimetypesFileTypeMap;
import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class SignService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private OSSUtil ossUtil;

    @Resource
    private SignatureProperties signatureProperties;

    @Resource
    private RedisService redisService;

    private final static String CA_SIGNATURE_TOKEN = CaRedisKey.CA_SIGNATURE_TOKEN;

    /**
     * 获取单号及活体视频口令码
     *
     * @return 获取单号及活体视频口令码
     * @throws Exception 异常信息
     */
    public Map<String, String> getNumber() throws Exception {
        String numberUrl = signatureProperties.getCertApplyUrlPre() + "core/api/get_random_number?token=" + getToken();
        String result = HttpUtils.sendGet(numberUrl);
        logger.info("getNumber()响应结果为：{}", result);
        ResponseMessage<NumberResult> responseMessage = JSON.parseObject(result, new TypeReference<ResponseMessage<NumberResult>>() {
        });
        Map<String, String> map = new HashMap<>();
        if (responseMessage != null) {
            map.put("code", String.valueOf(responseMessage.getCode()));
            map.put("msg", responseMessage.getMsg());
            if (responseMessage.getCode() == 0) {
                map.put("orderNo", responseMessage.getOrderno());
                map.put("randomNumber", responseMessage.getObj().getRandom_number());
            }
            //修复CA方token过期引起的bug
            if (responseMessage.getCode() == -1001) {
                redisService.deleteObject(CA_SIGNATURE_TOKEN);
            }
        }
        return map;
    }

    /**
     * 根据单号、活体视频、个人信息去申请数字证书
     *
     * @param orderNo         订单编号
     * @param personFileInfo  个人文件信息
     * @param videoFile       会提视频信息
     * @return 申请的证书信息
     * @throws Exception 异常信息
     */
    public Map<String, String> certApply(String orderNo, PersonFileInfoVO personFileInfo, File videoFile) throws Exception {
        //身份证号和手机号解密
        String decryptIdNo = DESUtil.decrypt(personFileInfo.getIdNo());
        personFileInfo.setIdNo(decryptIdNo);
        String decryptMobile = DESUtil.decrypt(personFileInfo.getMobileNo());
        personFileInfo.setMobileNo(decryptMobile);

        //授权信息
        JSONObject authInfo = new JSONObject();
        //997：平台_软证(SM2)
        authInfo.put("projectId", signatureProperties.getProjectId());
        authInfo.put("locationId", signatureProperties.getLocationId());
        authInfo.put("certsn", signatureProperties.getCertSn());
        authInfo.put("authCode", signatureProperties.getAuthCode());

        //证书申请基本信息
        String unixTime = Long.toString(System.currentTimeMillis() / 1000L);
        JSONObject certApplyInfo = new JSONObject();
        certApplyInfo.put("certType", "NATURAL");
        String certdn = "CN=" + personFileInfo.getCustName();
        certdn += ",OU=" + personFileInfo.getIdNo().substring(personFileInfo.getIdNo().length() - 8);
        certdn += ",O=" + personFileInfo.getHospitalName();
        certdn += ",T=" + unixTime;
        certdn += ",L=" + personFileInfo.getCity();
        certdn += ",ST=" + personFileInfo.getProvince();
        certdn += ",C=CN";
        certApplyInfo.put("certdn", certdn);
        certApplyInfo.put("applyValidate", signatureProperties.getValidity());
        certApplyInfo.put("certValidate", signatureProperties.getValidity());
        certApplyInfo.put("email", "<EMAIL>");

        //业务信息
        JSONObject businessInfo = new JSONObject();
        businessInfo.put("chargeMethod", "FREE");
        businessInfo.put("isPrecord", "N");
        businessInfo.put("deliverMethod", "SELF");

        //个人客户信息
        JSONObject personInfo = new JSONObject();
        personInfo.put("custName", personFileInfo.getCustName());
        personInfo.put("sex", personFileInfo.getSex() == 0 ? "F" : "M");
        personInfo.put("idType", "SF");
        personInfo.put("idNo", personFileInfo.getIdNo());
        personInfo.put("mobileNo", personFileInfo.getMobileNo());
        //选填
        personInfo.put("phoneNo", "075526588388");
        //选填
        personInfo.put("faxNo", "075586156366");
        //选填
        personInfo.put("company", personFileInfo.getHospitalName());
        //选填
        personInfo.put("companyCode", String.valueOf(personFileInfo.getHospitalId()));
        personInfo.put("address", personFileInfo.getAddress());
        personInfo.put("zipCode", personFileInfo.getZipCode());
        personInfo.put("city", personFileInfo.getCity());
        personInfo.put("province", personFileInfo.getProvince());

        //证书类型
        String keyType = "SM2";
        //自定义扩展项信息
        JSONArray selfExtResArr = new JSONArray();

        HashMap<String, String> textMap = new HashMap<>();
        textMap.put("orderno", orderNo);
        textMap.put("personInfo", personInfo.toString());
        textMap.put("authInfo", authInfo.toString());
        textMap.put("certApplyInfo", certApplyInfo.toString());
        textMap.put("businessInfo", businessInfo.toString());
        textMap.put("keyType", keyType);
        textMap.put("selfExtResArr", selfExtResArr.toString());

        HashMap<String, File> fileMap = new HashMap<>();
        fileMap.put("video", videoFile);

        String requestUrl = signatureProperties.getCertApplyUrlPre() + "core/api/livingbodyVerifyAndSupApplyPerson?token=" + getToken();
        logger.info("certApply()请求参数为：" + JSON.toJSONString(textMap));
        String response = FileUtil.uploadFile(requestUrl, textMap, fileMap);
        logger.info("certApply()响应结果为：" + response);
        try {
            //删除活体认证视频
            String path = videoFile.getAbsolutePath();
            File f = new File(path);
            if (f.exists()) {
                f.delete();
            }
        } catch (Exception ex) {
            System.out.println("活体文件删除异常：" + ex.getMessage());
        }

        ResponseMessage<Certificate> responseMessage = JSON.parseObject(response, new TypeReference<ResponseMessage<Certificate>>() {
        });
        Map<String, String> map = new HashMap<>();
        if (responseMessage != null) {
            if (responseMessage.getCode() == 0) {
                map.put("code", String.valueOf(0));
                map.put("msg", responseMessage.getMsg());
                map.put("applyId", responseMessage.getObj().getApplyId());
                map.put("resultCode", responseMessage.getObj().getResultCode());
                map.put("endDate", responseMessage.getObj().getEndDate());
            } else {
                map.put("code", String.valueOf(responseMessage.getCode()));
                map.put("msg", responseMessage.getMsg());
            }
        }
        return map;
    }

    /**
     * 根据证书号及要签署的文件进行签署
     *
     * @param signerId      签名id
     * @param signFilePath  签名文件路径
     * @param blankFilePath 空文件路径
     * @param localSignDir  本地签名文件夹
     * @param ossDir        oss文件夹
     * @return 签名结果
     * @throws Exception 异常信息
     */
    public Map<String, String> signature(String signerId, String signFilePath, String blankFilePath, String localSignDir, String ossDir) throws Exception {
        String signBase64 = StreamConvertor.file2base64(signFilePath);
        String blankBase64 = StreamConvertor.file2base64(blankFilePath);

        //图片信息(字体生成）
        JSONObject signPic = new JSONObject();
        //1为传入印章图片，2为字体生成图片
        signPic.put("type", "1");
        //图片base64字符
        signPic.put("imageBase64", signBase64);
        signPic.put("font", "楷体");
        signPic.put("fontSize", "40");
        signPic.put("fontColor", "black");
        signPic.put("content", "");

        JSONArray stampInfoArray = new JSONArray();
        JSONObject stampInfo = new JSONObject();
        //页码
        stampInfo.put("pageNum", 1);

        //坐标
        JSONArray coordinateArray = new JSONArray();
        JSONObject coordinate = new JSONObject();
        //以页面左下角为原点的x坐标
        coordinate.put("left", "80");
        //以页面左下角为原点的y坐标
        coordinate.put("bottom", "110");
        coordinateArray.add(coordinate);
        stampInfo.put("coordinate", coordinateArray.toString());
        stampInfoArray.add(stampInfo);

        //坐标盖章
        JSONArray stampInfos = new JSONArray();

        //签章信息
        JSONObject signInfo = new JSONObject();
        //手机验证码，设置为空
        signInfo.put("captcha", "");
        //PIN码，证书申请时设置的PIN码
        signInfo.put("pin", StreamConvertor.base64Encode("123456".getBytes()));
        //签章类型，0：坐标盖章，1：关键字盖章
        signInfo.put("stampType", "0");
        //文件base64字符串
        signInfo.put("base64File", blankBase64);
        //文件名称
        signInfo.put("fileName", "blank.pdf");
        //印章图片信息
        signInfo.put("signPic", signPic.toString());
        //证书序列号
        signInfo.put("signerId", signerId);
        //所有页都盖章
        signInfo.put("pageAll", "false");
        //盖章图片添加时间显示,显示时间格式：2021/04/25 02:51:33
        signInfo.put("showTime", "false");
        //盖章坐标信息
        signInfo.put("stampInfo", stampInfoArray.toString());
        stampInfos.add(signInfo);

        //参数集合
        JSONObject requestList = new JSONObject();
        //用户自己传入，如业务单号便于日志记录
        requestList.put("objectId", signerId);
        requestList.put("stampInfos", stampInfos.toString());

        stampInfos.add(signInfo);
        String res = requestList.toString().replace("\\", "").replace("\"[", "[").replace("\"{", "{").replace("]\"", "]").replace("}\"", "}");
        logger.info("签名入参信息={}", JSONObject.toJSONString(res));
//        String r = HttpRequest.httpPostWithjson(signatureProperties.getPdfSignatureUrl(), res);
        String response = HttpUtils.sendPostJson(signatureProperties.getPdfSignatureUrl(), res, null);
        logger.info("signature响应结果为：{}", response);
        JSONObject responseJson = JSONObject.parseObject(response);
        if (responseJson.getInteger("code") == 0) {
            JSONArray fileArray = (JSONArray) responseJson.get("obj");
            JSONObject fileJson = (JSONObject) fileArray.get(0);
            String localSignPdfPath = localSignDir + "/" + IdUtils.simpleUUID() + ".pdf";
            //生成文件
            StreamConvertor.base64ToFile(fileJson.get("signed").toString(), localSignPdfPath);

            //数字签名信息，暂先不存储
            //todo update  p7-签名值 signAlgorithm:签名算法  目前返回的p7签名值不符合我们的预期
            String p7 = fileJson.getString("p7");
            String signAlgorithm = fileJson.getString("signAlgorithm");
            String digest = fileJson.getString("digest");

            //保存签署的PDF文件到OSS
            String pdfFilePath = localFileToOss(localSignPdfPath, ossDir);
            //把签署的PDF文件转成img格式，保存到OSS（前端以img格式展示）
            String localSignImgPath = localSignDir + IdUtils.simpleUUID() + ".png";
            PDFUtils.pdfToImageAllToSingleImage(localSignPdfPath, localSignImgPath, 1);
            String imgFilePath = localFileToOss(localSignImgPath, ossDir);

            try {
                //删除本地文件
                File file1 = new File(localSignPdfPath);
                if (file1.exists()) {
                    file1.delete();
                }
                File file2 = new File(localSignImgPath);
                if (file2.exists()) {
                    file2.delete();
                }
                File file3 = new File(signFilePath);
                if (file3.exists()) {
                    file3.delete();
                }
            } catch (Exception ex) {
                logger.error("签署文件删除异常：{}", ex.getMessage(), ex);
            }
            //返回OSS上的文件路径
            Map<String, String> map = new HashMap<>();
            map.put("pdfFilePath", pdfFilePath);
            map.put("imgFilePath", imgFilePath);
            return map;
        }
        return null;
    }

    private synchronized String getToken() throws Exception {
        String token = redisService.getCacheObject(CA_SIGNATURE_TOKEN);
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        String url = signatureProperties.getCertApplyUrlPre() + "authServer/auth/token/{0}/{1}";
        url = MessageFormat.format(url, signatureProperties.getClientId(), signatureProperties.getClientKey());
        String result = HttpUtils.sendGet(url);
        JSONObject resultJson = JSONObject.parseObject(result);
        token = resultJson.getString("token");
        redisService.setCacheObject(CA_SIGNATURE_TOKEN, token, 7100L, TimeUnit.SECONDS);
        return token;
    }

    private String localFileToOss(String localFilePath, String ossDir) {
        try {
            File file = new File(localFilePath);
            MultipartFile multipartFile = toMultipartFile(file);
            return ossUtil.simpleUpload(multipartFile, ossDir);
        } catch (Exception ex) {
            logger.error("localFileToOss上传到OSS异常：{}", ex.getMessage(), ex);
        }
        return null;
    }

    private static MultipartFile toMultipartFile(File file) throws Exception {
        DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
        String contentType = new MimetypesFileTypeMap().getContentType(file);
        FileItem fileItem = diskFileItemFactory.createItem(file.getName(), contentType, false, file.getName());
        try (InputStream inputStream = new ByteArrayInputStream(FileCopyUtils.copyToByteArray(file));
             OutputStream outputStream = fileItem.getOutputStream()) {
            FileCopyUtils.copy(inputStream, outputStream);
        }
        return new CommonsMultipartFile(fileItem);
    }

    /**
     * 生成pc sign
     *
     * @param applyName     申请名称
     * @param applyIdNo     申请人身份证
     * @param applyPhone    申请人手机号
     * @param companyName   申请人公司名称
     * @return pc sign
     */
    public String getPcSign(String applyName, String applyIdNo, String applyPhone, String companyName) {
        String[] array = new String[]{applyName, applyIdNo, applyPhone,
                signatureProperties.getReqId(), signatureProperties.getProjectId(), companyName, signatureProperties.getKeyCode()};
        Arrays.sort(array);
        String str = String.join("", array);
        return SHA1Util.getSHA1(str);
    }

    /**
     * 获取PC签名
     *
     * @param agr 参数信息
     * @return 签名信息
     */
    public String signString(String... agr) {
        StringBuilder sb = new StringBuilder();
        Arrays.sort(agr);
        for (String s : agr) {
            sb.append(s);
        }
        return DigestUtils.sha1Hex(sb.toString().getBytes(StandardCharsets.UTF_8));
    }
}
