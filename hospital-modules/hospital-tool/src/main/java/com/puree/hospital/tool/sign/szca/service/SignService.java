package com.puree.hospital.tool.sign.szca.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfReader;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.SHA1Util;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.utils.http.HttpUtils;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.common.redis.service.RedisService;

import com.puree.hospital.tool.api.model.dto.CaSignRequestDTO;
import com.puree.hospital.tool.api.model.dto.CaSignReturnDTO;
import com.puree.hospital.tool.sign.szca.domain.dto.CASignatureDTO;
import com.puree.hospital.tool.sign.szca.domain.dto.SignPicDTO;
import com.puree.hospital.tool.sign.szca.domain.dto.StampInfosDTO;
import com.puree.hospital.tool.sign.szca.infrastructure.CaRedisKey;
import com.puree.hospital.tool.sign.szca.domain.model.Certificate;
import com.puree.hospital.tool.sign.szca.domain.model.NumberResult;
import com.puree.hospital.tool.sign.szca.domain.model.ResponseMessage;
import com.puree.hospital.tool.sign.szca.domain.vo.PersonFileInfoVO;
import com.puree.hospital.tool.sign.szca.infrastructure.config.SignatureProperties;
import com.puree.hospital.tool.sign.szca.infrastructure.util.FileUtil;
import com.puree.hospital.tool.sign.szca.infrastructure.util.ImageUtil;
import com.puree.hospital.tool.sign.szca.infrastructure.util.PDFUtils;
import com.puree.hospital.tool.sign.szca.infrastructure.util.StreamConvertor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.activation.MimetypesFileTypeMap;
import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class SignService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ImageUtil imageUtil;

    @Resource
    private OSSUtil ossUtil;

    @Resource
    private SignatureProperties signatureProperties;

    @Resource
    private RedisService redisService;

    private final static String CA_SIGNATURE_TOKEN = CaRedisKey.CA_SIGNATURE_TOKEN;

    /**
     * 获取单号及活体视频口令码
     *
     * @return 获取单号及活体视频口令码
     * @throws Exception 异常信息
     */
    public Map<String, String> getNumber() throws Exception {
        String numberUrl = signatureProperties.getCertApplyUrlPre() + "core/api/get_random_number?token=" + getToken();
        String result = HttpUtils.sendGet(numberUrl);
        logger.info("getNumber()响应结果为：{}", result);
        ResponseMessage<NumberResult> responseMessage = JSON.parseObject(result, new TypeReference<ResponseMessage<NumberResult>>() {
        });
        Map<String, String> map = new HashMap<>();
        if (responseMessage != null) {
            map.put("code", String.valueOf(responseMessage.getCode()));
            map.put("msg", responseMessage.getMsg());
            if (responseMessage.getCode() == 0) {
                map.put("orderNo", responseMessage.getOrderno());
                map.put("randomNumber", responseMessage.getObj().getRandom_number());
            }
            //修复CA方token过期引起的bug
            if (responseMessage.getCode() == -1001) {
                redisService.deleteObject(CA_SIGNATURE_TOKEN);
            }
        }
        return map;
    }

    /**
     * 根据单号、活体视频、个人信息去申请数字证书
     *
     * @param orderNo         订单编号
     * @param personFileInfo  个人文件信息
     * @param videoFile       会提视频信息
     * @return 申请的证书信息
     * @throws Exception 异常信息
     */
    public Map<String, String> certApply(String orderNo, PersonFileInfoVO personFileInfo, File videoFile) throws Exception {
        //身份证号和手机号解密
        String decryptIdNo = DESUtil.decrypt(personFileInfo.getIdNo());
        personFileInfo.setIdNo(decryptIdNo);
        String decryptMobile = DESUtil.decrypt(personFileInfo.getMobileNo());
        personFileInfo.setMobileNo(decryptMobile);

        //授权信息
        JSONObject authInfo = new JSONObject();
        //997：平台_软证(SM2)
        authInfo.put("projectId", signatureProperties.getProjectId());
        authInfo.put("locationId", signatureProperties.getLocationId());
        authInfo.put("certsn", signatureProperties.getCertSn());
        authInfo.put("authCode", signatureProperties.getAuthCode());

        //证书申请基本信息
        String unixTime = Long.toString(System.currentTimeMillis() / 1000L);
        JSONObject certApplyInfo = new JSONObject();
        certApplyInfo.put("certType", "NATURAL");
        String certdn = "CN=" + personFileInfo.getCustName();
        certdn += ",OU=" + personFileInfo.getIdNo().substring(personFileInfo.getIdNo().length() - 8);
        certdn += ",O=" + personFileInfo.getHospitalName();
        certdn += ",T=" + unixTime;
        certdn += ",L=" + personFileInfo.getCity();
        certdn += ",ST=" + personFileInfo.getProvince();
        certdn += ",C=CN";
        certApplyInfo.put("certdn", certdn);
        certApplyInfo.put("applyValidate", signatureProperties.getValidity());
        certApplyInfo.put("certValidate", signatureProperties.getValidity());
        certApplyInfo.put("email", "<EMAIL>");

        //业务信息
        JSONObject businessInfo = new JSONObject();
        businessInfo.put("chargeMethod", "FREE");
        businessInfo.put("isPrecord", "N");
        businessInfo.put("deliverMethod", "SELF");

        //个人客户信息
        JSONObject personInfo = new JSONObject();
        personInfo.put("custName", personFileInfo.getCustName());
        personInfo.put("sex", personFileInfo.getSex() == 0 ? "F" : "M");
        personInfo.put("idType", "SF");
        personInfo.put("idNo", personFileInfo.getIdNo());
        personInfo.put("mobileNo", personFileInfo.getMobileNo());
        //选填
        personInfo.put("phoneNo", "075526588388");
        //选填
        personInfo.put("faxNo", "075586156366");
        //选填
        personInfo.put("company", personFileInfo.getHospitalName());
        //选填
        personInfo.put("companyCode", String.valueOf(personFileInfo.getHospitalId()));
        personInfo.put("address", personFileInfo.getAddress());
        personInfo.put("zipCode", personFileInfo.getZipCode());
        personInfo.put("city", personFileInfo.getCity());
        personInfo.put("province", personFileInfo.getProvince());

        //证书类型
        String keyType = "SM2";
        //自定义扩展项信息
        JSONArray selfExtResArr = new JSONArray();

        HashMap<String, String> textMap = new HashMap<>();
        textMap.put("orderno", orderNo);
        textMap.put("personInfo", personInfo.toString());
        textMap.put("authInfo", authInfo.toString());
        textMap.put("certApplyInfo", certApplyInfo.toString());
        textMap.put("businessInfo", businessInfo.toString());
        textMap.put("keyType", keyType);
        textMap.put("selfExtResArr", selfExtResArr.toString());

        HashMap<String, File> fileMap = new HashMap<>();
        fileMap.put("video", videoFile);

        String requestUrl = signatureProperties.getCertApplyUrlPre() + "core/api/livingbodyVerifyAndSupApplyPerson?token=" + getToken();
        logger.info("certApply()请求参数为：" + JSON.toJSONString(textMap));
        String response = FileUtil.uploadFile(requestUrl, textMap, fileMap);
        logger.info("certApply()响应结果为：" + response);
        try {
            //删除活体认证视频
            String path = videoFile.getAbsolutePath();
            File f = new File(path);
            if (f.exists()) {
                f.delete();
            }
        } catch (Exception ex) {
            System.out.println("活体文件删除异常：" + ex.getMessage());
        }

        ResponseMessage<Certificate> responseMessage = JSON.parseObject(response, new TypeReference<ResponseMessage<Certificate>>() {
        });
        Map<String, String> map = new HashMap<>();
        if (responseMessage != null) {
            if (responseMessage.getCode() == 0) {
                map.put("code", String.valueOf(0));
                map.put("msg", responseMessage.getMsg());
                map.put("applyId", responseMessage.getObj().getApplyId());
                map.put("resultCode", responseMessage.getObj().getResultCode());
                map.put("endDate", responseMessage.getObj().getEndDate());
            } else {
                map.put("code", String.valueOf(responseMessage.getCode()));
                map.put("msg", responseMessage.getMsg());
            }
        }
        return map;
    }

    /**
     * 根据证书号及要签署的文件进行签署
     *
     * @param
     * @return 签名结果
     * @throws Exception 异常信息
     */
    public CaSignReturnDTO signature(CaSignRequestDTO caSignRequestDTO) throws Exception {
        // 缩放到高度为50像素，锐化度300
        String param = "image/resize,h_50/sharpen,399";
        InputStream objectAsInputStreamWithProcess = ossUtil.getObjectAsInputStreamWithProcess(caSignRequestDTO.getSignPath(), param);
        byte[] bytes = StreamConvertor.inputStream2bytes(objectAsInputStreamWithProcess);
        String signBase64 = StreamConvertor.base64Encode(bytes);

        byte[] objectAsByteArray = ossUtil.getObjectAsByteArray(caSignRequestDTO.getPdfFilePath());
        String pdfBase64 = StreamConvertor.base64Encode(objectAsByteArray);

        // CA签署请求封装对象
        CASignatureDTO caSignatureDTO = new CASignatureDTO();
        caSignatureDTO.setObjectId(caSignRequestDTO.getSignId());

        List<StampInfosDTO> stampInfosDTOS = new ArrayList<>();
        StampInfosDTO stampInfosDTO = new StampInfosDTO();
        SignPicDTO signPicDTO = new SignPicDTO();
        signPicDTO.setImageBase64(signBase64);

        stampInfosDTO.setSignPic(signPicDTO);

        stampInfosDTO.setBase64File(pdfBase64);
        stampInfosDTO.setSignerId(caSignRequestDTO.getSignId());
        stampInfosDTOS.add(stampInfosDTO);
        List<Map<String, Double>> corList = new ArrayList<>();
        List<Map<String,Object>> stampInfoList = new ArrayList<>();
        Map<String ,Object> cor = new HashMap<>();
        Map<String, Object> pos = caSignRequestDTO.getPosition();
        cor.put("pageNum",pos.get("num"));
        Map<String,Double> corXy = new HashMap<>();
        corXy.put("left",(((Map<String, Double>) pos.get("pos")).get("x")*1.33));
        // y坐标加上偏移量
        corXy.put("bottom",((Map<String,Double>)(pos.get("pos"))).get("y")*1.33-20);
        corList.add(corXy);
        cor.put("coordinate",corList);
        stampInfoList.add(cor);
        stampInfosDTO.setStampInfo(stampInfoList);
        caSignatureDTO.setStampInfos(stampInfosDTOS);

        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(caSignatureDTO);

        String response = HttpUtils.sendPostJson(signatureProperties.getPdfSignatureUrl(), json, null);
        JSONObject responseJson = JSONObject.parseObject(response);
        if (responseJson.getInteger("code") == 0 ) {
            JSONArray fileArray = (JSONArray) responseJson.get("obj");
            JSONObject fileJson = (JSONObject) fileArray.get(0);
            // 成功签名才往下执行
            if(fileJson.getInteger("resultCode") == 0){
                String localSignPdfPath = caSignRequestDTO.getLocalSignDir() + "/" + IdUtils.simpleUUID() + ".pdf";
                //生成文件
                StreamConvertor.base64ToFile(fileJson.get("signed").toString(), localSignPdfPath);

                // 获取数字签名
                List<String> digestSigned = getPdfDigest(fileJson.get("signed").toString());
                //数字签名信息，暂先不存储
                //todo update  p7-签名值 signAlgorithm:签名算法  目前返回的p7签名值不符合我们的预期
                String p7 = fileJson.getString("p7");
                String signAlgorithm = fileJson.getString("signAlgorithm");
                String digest = fileJson.getString("digest");

                //保存签署的PDF文件到OSS
                String pdfFilePath = localFileToOss(localSignPdfPath, caSignRequestDTO.getOssDir());
                //把签署的PDF文件转成img格式，保存到OSS（前端以img格式展示）
                String localSignImgPath = caSignRequestDTO.getLocalSignDir() + IdUtils.simpleUUID() + ".png";
                PDFUtils.pdfToImageAllToSingleImage(localSignPdfPath, localSignImgPath, 1);
                String imgFilePath = localFileToOss(localSignImgPath, caSignRequestDTO.getOssDir());

                try {
                    //删除本地文件
                    File file1 = new File(localSignPdfPath);
                    if (file1.exists()) {
                        file1.delete();
                    }
                    File file2 = new File(localSignImgPath);
                    if (file2.exists()) {
                        file2.delete();
                    }
                    File file3 = new File(caSignRequestDTO.getSignPath());
                    if (file3.exists()) {
                        file3.delete();
                    }
                } catch (Exception ex) {
                    logger.error("签署文件删除异常：{}", ex.getMessage(), ex);
                }
                //返回OSS上的文件路径
                CaSignReturnDTO result = new CaSignReturnDTO();
                result.setPdfPath(pdfFilePath);
                result.setImgPath(imgFilePath);
                result.setDigestSignature(digestSigned);
                return result;
            }
            throw new ServiceException("签名证书无效！");
        }
        return null;
    }

    private List<String> getPdfDigest(String base64Pdf ) throws IOException {
        byte[] pdfBytes = Base64.getDecoder().decode(base64Pdf);

        // 使用 ByteArrayInputStream 处理 PDF 数据
        ByteArrayInputStream pdfStream = new ByteArrayInputStream(pdfBytes);
        PdfReader reader = new PdfReader(pdfStream);
        AcroFields fields = reader.getAcroFields();

        return fields.getSignatureNames();
    }

    private synchronized String getToken() throws Exception {
        String token = redisService.getCacheObject(CA_SIGNATURE_TOKEN);
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        String url = signatureProperties.getCertApplyUrlPre() + "authServer/auth/token/{0}/{1}";
        url = MessageFormat.format(url, signatureProperties.getClientId(), signatureProperties.getClientKey());
        String result = HttpUtils.sendGet(url);
        JSONObject resultJson = JSONObject.parseObject(result);
        token = resultJson.getString("token");
        redisService.setCacheObject(CA_SIGNATURE_TOKEN, token, 7100L, TimeUnit.SECONDS);
        return token;
    }

    private String localFileToOss(String localFilePath, String ossDir) {
        try {
            File file = new File(localFilePath);
            MultipartFile multipartFile = toMultipartFile(file);
            return ossUtil.simpleUpload(multipartFile, ossDir);
        } catch (Exception ex) {
            logger.error("localFileToOss上传到OSS异常：{}", ex.getMessage(), ex);
        }
        return null;
    }

    private static MultipartFile toMultipartFile(File file) throws Exception {
        DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
        String contentType = new MimetypesFileTypeMap().getContentType(file);
        FileItem fileItem = diskFileItemFactory.createItem(file.getName(), contentType, false, file.getName());
        try (InputStream inputStream = new ByteArrayInputStream(FileCopyUtils.copyToByteArray(file));
             OutputStream outputStream = fileItem.getOutputStream()) {
            FileCopyUtils.copy(inputStream, outputStream);
        }
        return new CommonsMultipartFile(fileItem);
    }

    /**
     * 生成pc sign
     *
     * @param applyName     申请名称
     * @param applyIdNo     申请人身份证
     * @param applyPhone    申请人手机号
     * @param companyName   申请人公司名称
     * @return pc sign
     */
    public String getPcSign(String applyName, String applyIdNo, String applyPhone, String companyName) {
        String[] array = new String[]{applyName, applyIdNo, applyPhone,
                signatureProperties.getReqId(), signatureProperties.getProjectId(), companyName, signatureProperties.getKeyCode()};
        Arrays.sort(array);
        String str = String.join("", array);
        return SHA1Util.getSHA1(str);
    }

    /**
     * 获取PC签名
     *
     * @param agr 参数信息
     * @return 签名信息
     */
    public String signString(String... agr) {
        StringBuilder sb = new StringBuilder();
        Arrays.sort(agr);
        for (String s : agr) {
            sb.append(s);
        }
        return DigestUtils.sha1Hex(sb.toString().getBytes(StandardCharsets.UTF_8));
    }
}
