package com.puree.hospital.tool.sign.szca.infrastructure.util;

import com.puree.hospital.common.oss.OSSUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/31 12:05
 */
@Slf4j
@Component
public class ImageUtil {
    @Resource
    private OSSUtil ossUtil;

    public String resizeImgaToBase64(String SignPath, int targetWidth, int targetHeight, String imageFormat) throws IOException {
        InputStream imageStream = getImgInputStream(SignPath);

        // 缩小图片尺寸
        BufferedImage resizedImage = resizeImage(imageStream, targetWidth, targetHeight);

        // 将缩小后的图片写入内存流（避免临时文件）
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ImageIO.write(resizedImage, imageFormat, outputStream);
            outputStream.flush();

            // 4. 将内存流中的字节数组转为Base64
            byte[] resizedBytes = outputStream.toByteArray();
            return Base64.getEncoder().encodeToString(resizedBytes);
        } finally {
            // 关闭OSS输入流，释放资源
            imageStream.close();
        }

    }

    /**
     * 从OSS获取图片输入流
     * @param SignPath
     * @return
     */
    private  InputStream getImgInputStream(String SignPath){
        return ossUtil.getObjectAsInputStream(SignPath);
    }

    /**
     * 核心：缩小图片尺寸
     */
    private BufferedImage resizeImage(InputStream inputStream, int targetWidth, int targetHeight) throws IOException {
        // 1. 读取原图，获取原图的类型和特性
        BufferedImage originalImage = ImageIO.read(inputStream);
        if (originalImage == null) {
            throw new IOException("无法解析图片（格式不支持或文件损坏）");
        }
        // 2. 根据原图类型选择目标BufferedImage的类型
        int imageType = getTargetImageType(originalImage);

        // 3. 创建目标尺寸的缓冲图像（匹配原图类型）
        BufferedImage resizedImage = new BufferedImage(
                targetWidth,
                targetHeight,
                imageType
        );

        // 高质量缩放配置
        Graphics2D g2d = resizedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR); // 平滑插值
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY); // 高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON); // 抗锯齿

        // 绘制缩小后的图像
        g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();


        return resizedImage;
    }


    /**
     * 根据原图类型选择目标图像类型
     * @param originalImage 原图
     * @return 目标BufferedImage类型
     */
    private int getTargetImageType(BufferedImage originalImage) {
        int originalType = originalImage.getType();

        // 情况1：原图是带透明通道的类型（如PNG、GIF）
        if (originalType == BufferedImage.TYPE_INT_ARGB || originalType == BufferedImage.TYPE_INT_ARGB_PRE) {
            return BufferedImage.TYPE_INT_ARGB; // 保留透明通道
        }

        // 情况2：原图是不透明类型（如JPG、BMP）
        if (originalType == BufferedImage.TYPE_INT_RGB || originalType == BufferedImage.TYPE_3BYTE_BGR) {
            return BufferedImage.TYPE_INT_RGB; // 无透明通道
        }

        // 情况3：其他未知类型（默认使用兼容类型，避免色彩失真）
        return BufferedImage.TYPE_INT_ARGB; // 兼容大多数格式，即使原图不透明也不会出错（只是多占一点内存）
    }

}
