package com.puree.hospital.tool.pdf.processor;

import com.puree.hospital.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 处理器工厂
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class TextProcessorFactory {

    private final Map<String, TextProcessor> textProcessors;

    private final TextProcessorConfig config;

    /**
     * 获取处理器链
     * @param processingContext 上下文
     * @return 处理器链
     */
    public TextProcessor getProcessorChain(TextProcessingContext processingContext) {
        if (processingContext == null) {
          throw new ServiceException("fileUrl is null");
        }
        //写死处理器顺序，后续在优化

        //写入 ocr 处理器
        TextProcessor textProcessor = textProcessors.get(TextProcessorConstant.OCR_TEXT_PROCESSOR);
        textProcessor.setContext(processingContext);

        //写入 页码处理器
        textProcessor.setNext(textProcessors.get(TextProcessorConstant.PAGE_NUMBER_TEXT_PROCESSOR));

        //存储处理器
        textProcessor.setNext(textProcessors.get(TextProcessorConstant.DB_STORAGE_TEXT_PROCESSOR));


        return textProcessor;
    }

}
