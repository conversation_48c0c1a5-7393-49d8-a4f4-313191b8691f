package com.puree.hospital.tool.idcard.controller;

import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.common.api.domain.IdCardResult;
import com.puree.hospital.tool.idcard.service.IdCardVerifyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 身份证二要素核实
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/24 17:54
 */
@RestController
@RequestMapping("card")
public class IdCardVerifyController {

    @Resource
    private IdCardVerifyService idCardVerifyService;

    /**
     * 身份证二要素核实
     *
     * @param idCardName 姓名
     * @param idCardNo   身份证号
     * @return 验证结果
     */
    @PostMapping("verify")
    @Log(title = "身份证二要素核实", businessType = BusinessType.OTHER)
    public IdCardResult verify(@RequestParam("idCardName") String idCardName, @RequestParam("idCardNo") String idCardNo) {
        return idCardVerifyService.verify(idCardName,idCardNo);
    }
}
