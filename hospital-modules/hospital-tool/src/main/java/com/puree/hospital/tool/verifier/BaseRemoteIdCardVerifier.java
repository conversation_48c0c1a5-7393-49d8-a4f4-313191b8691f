package com.puree.hospital.tool.verifier;

import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.domain.IdCardResult;
import com.puree.hospital.common.security.token.TokenService;
import com.puree.hospital.tool.idcard.domain.IdCardVerify;
import com.puree.hospital.tool.idcard.mapper.IdCardVerifyMapper;
import com.puree.hospital.tool.idcard.service.impl.IdCardVerifyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 身份证校验
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/29 10:24
 */
@Slf4j
public abstract class BaseRemoteIdCardVerifier implements IdCardVerifier {

    @Resource
    private RedisTemplate<String, Number> redisTemplate;

    @Resource
    private TokenService tokenService;

    @Resource
    private IdCardVerifyMapper idCardVerifyMapper;

    @Resource
    private IdCardVerifyServiceImpl.IdCardVerifyProperties idCardVerifyProperties;

    @Override
    public IdCardResult verify(String idCardName, String idCardNo) throws Exception {
        Long maxRetryCount = idCardVerifyProperties.getRemoteMaxRetryCount();
        String key = CacheConstants.OCR_LIMIT_KEY + tokenService.getLoginUserId();
        //校验已调用远程服务次数是否超过最大尝试次数
        if(getRemoteCallCount(key) >= maxRetryCount ){
            return IdCardResult.fail("您今天提交的验证已超过" + maxRetryCount + "次，请核实并改天再尝试！");
        }
        // 执行身份证校验
        IdCardResult result = remoteVerify(idCardName, idCardNo);
        // 执行redis计数，
        if (Objects.nonNull(result) && result.isMatched()) {
            Long increment = redisIncrement(key);
            if (result.isFailed() && (increment + 1 == maxRetryCount)) {
                return IdCardResult.fail("您今天提交验证累计" + increment + "次，请核对，超过" + maxRetryCount + "次后将无法再提交验证");
            }
        }
        // 保存身份证校验记录
        if (Objects.nonNull(result) && result.isPassed()) {
            save(idCardName, idCardNo, getChannel());
        }
        return result;
    }

    /**
     * 保存验证成功的身份证号码
     *
     * @param idCardName 姓名
     * @param idCardNo 身份证号码
     * @param channel 验证渠道
     */
    public void save(String idCardName, String idCardNo, String channel) {
        try {
            IdCardVerify verify = new IdCardVerify();
            verify.setChannel(channel);
            verify.setIdCardName(idCardName);
            verify.setIdCardNo(idCardNo);
            idCardVerifyMapper.insert(verify);
        } catch (Exception e) {
            log.error("身份证二要素验证数据保存失败", e);
        }
    }

    /**
     * 已调用远程服务次数
     *
     * @return 已调用调用
     */
    protected Long getRemoteCallCount(String key) {
        Number count = redisTemplate.opsForValue().get(key);
        return count == null ? 0L : count.longValue();
    }

    /**
     * redis计数
     *
     * @param key key
     * 增加后返回值
     */
    protected Long redisIncrement(String key) {
        //计算redis过期时间
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 获取次日凌晨0点的时间
        LocalDateTime nextDayMidnight = LocalDateTime.of(currentTime.toLocalDate().plusDays(1), LocalTime.MIDNIGHT);
        // 计算毫秒差值
        Duration duration = Duration.between(currentTime, nextDayMidnight);
        long timeDifferenceMillis = duration.toMillis();
        Long result = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, timeDifferenceMillis, TimeUnit.MILLISECONDS);
        return result;
    }

    /**
     * 远程调用二要素验证接口
     *
     * @param idCardName 姓名
     * @param idCardNo 身份证号码
     * @return 身份证校验结果
     * @throws Exception 异常信息
     */
    protected abstract IdCardResult remoteVerify(String idCardName, String idCardNo) throws Exception;

    /**
     * 获取渠道
     *
     * @return 渠道
     */
    protected abstract String getChannel();
}
