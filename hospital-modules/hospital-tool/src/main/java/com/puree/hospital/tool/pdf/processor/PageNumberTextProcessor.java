package com.puree.hospital.tool.pdf.processor;

import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * 页码处理器
 * <AUTHOR>
 */
@Component(TextProcessorConstant.PAGE_NUMBER_TEXT_PROCESSOR)
public class PageNumberTextProcessor extends AbstractTextProcessor {

    private static final Pattern PAGE_NUMBER_PATTERN  = Pattern.compile("第\\s*\\d+\\s*页\\s*[/（(-]\\s*共\\s*\\d+\\s*页\\s*[）)]?");

    @Override
    public String process(String text) {

        // 处理页码
        /**
         * 第1页/共2页
         * 第 1 页 / 共 2 页
         * 第1页-共2页
         * 第 1 页 - 共 2 页
         * 第1页(共2页)
         * 第 1 页 (共 2 页)
         * 第1页（共2页）
         * 第 1 页 （共 2 页）
         */
        String processed = PAGE_NUMBER_PATTERN.matcher(text).replaceAll("").trim();
        // 继续链中的下一个处理器
        return processNext(processed);
    }
}
