package com.puree.hospital.tool.sign.szca.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "ca.sign")
public class SignatureProperties {
    private String clientId;
    private String clientKey;
    private String certApplyUrlPre;
    private String pdfSignatureUrl;
    private String projectId;
    private String locationId;
    private String certSn;
    private String authCode;
    private String reqId;
    private String keyCode;
    private String pcCertUrl;
    private String pcAppId;
    private String pcAppSecret;
    private String pcProjectId;
    /**
     * 签名时效
     */
    private String validity;
}
