package com.puree.hospital.tool.logistics.kdn.controller;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.utils.kdn.KDNUtil;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("kdn")
@RestController
public class KDNController extends BaseController {

    @Resource
    private KDNUtil kdnUtil;


    /**
     * 查询物流轨迹
     * @param shipperCode 快递编码 如SF
     * @param customerName 寄件人/收件人手机后4位
     * @param logisticCode 快递单号
     * @return
     */
    @GetMapping("realTimeQuery")
    @Log(title = "快递鸟", businessType = BusinessType.OTHER)
    public AjaxResult realTimeQuery(@RequestParam("shipperCode") String shipperCode,
                                    @RequestParam(value = "customerName",required = false) String customerName,
                                    @RequestParam("logisticCode") String logisticCode){
        String data= kdnUtil.realTimeQuery(shipperCode, customerName, logisticCode);
        return AjaxResult.success(data);
    }


    /**
     * 单号识别
     * @param logisticCode 快递单号
     * @return
     */
    @GetMapping("numberIdentification")
    @Log(title = "快递鸟", businessType = BusinessType.OTHER)
    public AjaxResult numberIdentification(
                                    @RequestParam("logisticCode") String logisticCode){
        String data= kdnUtil.numberIdentification(logisticCode);
        return AjaxResult.success(data);
    }

    @GetMapping("/searchKdInfo")
    public R searchKdInfo(@RequestParam("logisticCode") String logisticCode){
        return R.ok(kdnUtil.numberIdentification(logisticCode));
    }

    /**
     * 测试
     * @param encryptedCharacter 加密字符
     * @return
     */
    @GetMapping("des")
    @Log(title = "快递鸟", businessType = BusinessType.OTHER)
    public AjaxResult des(
            @RequestParam("encryptedCharacter") String encryptedCharacter){
        String encrypt = DESUtil.encrypt(encryptedCharacter);
        return AjaxResult.success(encrypt);
    }
}
