package com.puree.hospital.tool.sms.handler;

import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;

/**
 * <AUTHOR>
 * @date 2025/3/12 9:42
 */
public interface SmsNotificationHandler {

    /**
     *  阿里云平台
     */
    String ALIYUN = "ALIYUN";
    /**
     *  互亿平台
     */
    String HUYI = "HUYI";
    /**
     *  有易平台
     */
    String YOYEE = "YOYEE";
    /**
     *  短信通知处理器后缀
     */
    String SUFFIX = "SmsNotificationHandler";

    /**
     *  有易短信格式 - 需要在内容前面拼接签名：【签名】文本内容
     */
    String YOYEE_MESSAGE_FORMAT = "【%s】%s";

    /**
     * 发送消息
     * @param smsNotificationDTO    发送参数
     * @return AjaxResult
     */
    AjaxResult sendNotification(SmsNotificationDTO smsNotificationDTO);

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    String getChannelName();
}
