package com.puree.hospital.tool.pdf.service.impl;

import cn.hutool.core.io.IoUtil;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfTextExtractor;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.tool.ocr.constant.OcrTypeEnum;
import com.puree.hospital.tool.ocr.domain.OcrDocuments;
import com.puree.hospital.tool.ocr.service.OcrDocumentsService;
import com.puree.hospital.tool.pdf.processor.TextProcessingContext;
import com.puree.hospital.tool.pdf.processor.TextProcessorFactory;
import com.puree.hospital.tool.pdf.service.PDFService;
import com.puree.storage.core.model.StorageObjectModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.io.InputStream;

/**
 * PDF 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class PDFServiceImpl implements PDFService {


    private final OSSUtil ossUtil;

    private final TextProcessorFactory textProcessorFactory;

    private final OcrDocumentsService ocrDocumentsService;

    /**
     * 从 url 中读取 pdf 文本
     *
     * @param fileUrl PDF文件的URL地址
     * @return 提取的文本内容，如果处理失败返回空字符串
     */
    @Override
    public String readPdfFromUrlAndConvertToText(String fileUrl) {
        if (isInvalidUrl(fileUrl)) {
            log.error("PDF文件URL为空");
            return "";
        }

        StorageObjectModel object = ossUtil.getObject(fileUrl);
        try (InputStream inputStream = object.getStream()) {
            // 读取整个PDF文件的字节流
            byte[] pdfBytes = IoUtil.readBytes(inputStream);

            // 生成PDF文件的签名
            String pdfSignature = ossUtil.generateSignature(pdfBytes, object.getObjectSize());

            // 查询数据库是否存在签名
            String cachedText = queryDatabaseForText(pdfSignature);
            if (cachedText != null) {
                return cachedText;
            }
            return extractTextFromPdf(pdfBytes, fileUrl, pdfSignature);
        } catch (IOException e) {
            log.error("读取PDF文件时发生IO异常, URL: {}", fileUrl, e);
        } catch (Exception e) {
            log.error("处理PDF文件时发生异常, URL: {}", fileUrl, e);
        }

        return "";
    }


    /**
     * 前置校验
     *
     * @param fileUrl 文件url
     * @return 是否成功
     */
    private boolean isInvalidUrl(String fileUrl) {
        return fileUrl == null || fileUrl.trim().isEmpty();
    }


    /**
     * 从PDF提取文本
     *
     * @param pdfBytes     pdf字节流
     * @param fileUrl      文件 url
     * @param pdfSignature 签名
     * @return 处理后的文本
     * @throws IOException
     */
    private String extractTextFromPdf(byte[] pdfBytes, String fileUrl, String pdfSignature) throws IOException {
        PdfReader reader = new PdfReader(pdfBytes);
        int pages = reader.getNumberOfPages();
        if (pages == 0) {
            log.warn("PDF文件页数为0，文件URL: {}", fileUrl);
            return "";
        }

        StringBuilder textBuilder = new StringBuilder(pages * 1024);
        for (int i = 1; i <= pages; i++) {
            String pageText = PdfTextExtractor.getTextFromPage(reader, i);
            if (pageText != null && !pageText.isEmpty()) {
                textBuilder.append(pageText).append(System.lineSeparator());
            }
        }
        return processExtractedText(textBuilder.toString(), fileUrl, pdfSignature);
    }

    /**
     * 责任链
     *
     * @param extractedText 文本
     * @param fileUrl       文件url
     * @param pdfSignature  签名
     * @return 处理后的文本
     */
    private String processExtractedText(String extractedText, String fileUrl, String pdfSignature) {
        TextProcessingContext processingContext = new TextProcessingContext();
        processingContext.setFileFullUrl(ossUtil.getFullPath(fileUrl));
        processingContext.setSignature(pdfSignature);
        String processedText = textProcessorFactory.getProcessorChain(processingContext).process(extractedText);

        log.debug("成功从PDF提取文本，文件URL: {}", fileUrl);
        return processedText;
    }

    /**
     * 查询数据库尝试获取解析文本
     *
     * @param pdfSignature 签名
     * @return 解析文本
     */
    private String queryDatabaseForText(String pdfSignature) {
        OcrDocuments bySignatureAndType = ocrDocumentsService.findBySignatureAndType(pdfSignature, OcrTypeEnum.PDF);
        if (bySignatureAndType == null) {
            return null;
        }
        return bySignatureAndType.getContent();
    }

}
