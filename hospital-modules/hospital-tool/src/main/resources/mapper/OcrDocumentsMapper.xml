<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.puree.hospital.tool.ocr.mapper.OcrDocumentsMapper">

    <!-- 根据签名和类型查询 OCR 内容 -->
    <select id="findBySignatureAndType" resultType="com.puree.hospital.tool.ocr.domain.OcrDocuments">
        SELECT
        id, signature, type, content,file_url, create_time
        FROM
        ocr_documents
        WHERE
        signature = #{signature} AND type = #{type}
    </select>

    <!-- 插入 OCR 内容 -->
    <insert id="insertOcrDocument" >
        INSERT INTO ocr_documents (signature, type, content,file_url, create_time)
        VALUES (#{signature}, #{type}, #{content},#{fileUrl}, NOW())
    </insert>

</mapper>
