<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hospital-modules</artifactId>
        <groupId>com.puree</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hospital-tool</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-tool-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.2</version> <!-- 请使用最新版本 -->
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-business-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-im-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-system-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-log</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-redis</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-security</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-datasource</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-feign</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-logback</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-aliyun</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>30.1-jre</version>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.jmimemagic</groupId>
            <artifactId>jmimemagic</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <copy file="target/${project.artifactId}.jar" todir="../../deploy">
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>