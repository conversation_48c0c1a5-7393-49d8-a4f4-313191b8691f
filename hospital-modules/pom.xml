<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hospital</artifactId>
        <groupId>com.puree</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hospital-modules</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>hospital-system</module>
        <module>hospital-business</module>
        <module>hospital-operate</module>
        <module>hospital-app</module>
        <module>hospital-im</module>
        <module>hospital-tool</module>
        <module>hospital-supplier</module>
        <module>hospital-monitor</module>
        <module>hospital-five</module>
        <module>hospital-pay</module>
        <module>hospital-order</module>
        <module>hospital-ehr</module>
        <module>hospital-hardware</module>
        <module>hospital-shop</module>
        <module>hospital-suppliermanger</module>
        <module>hospital-insurance</module>
        <module>follow-up</module>
        <module>tutorial-v2</module>
        <module>hospital-setting</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>