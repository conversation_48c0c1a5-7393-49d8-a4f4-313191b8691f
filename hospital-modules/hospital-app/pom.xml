<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hospital-modules</artifactId>
        <groupId>com.puree</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hospital-app</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--引入业务模块api-->
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-business-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-tool-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-im-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-supplier-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-tutorial-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-follow-up-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-five-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-pay-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-ehr-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-order-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-shop-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-insurance-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-system-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--业务组件包-->
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-invoice</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-logback</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-security</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-log</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-datasource</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-job</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-supplier</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-rabbitmq</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-monitor-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-feign</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-aliyun</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-notification</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-log</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 三方包 -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-util</artifactId>
            <version>9.3.7.v20160115</version>
        </dependency>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
        </dependency>

        <dependency>
            <groupId>com.getui.push</groupId>
            <artifactId>restful-sdk</artifactId>
            <version>${getui.version}</version>
        </dependency>
        <!--html转pdf依赖-->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>${itextpdf.version}</version>
        </dependency>
        <!--中文字体-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>font-asian</artifactId>
            <version>${font.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--单元测试-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark</artifactId>
            <version>0.36.8</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.vladsch.flexmark/flexmark-ext-tables -->
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-ext-tables</artifactId>
            <version>0.36.8</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>puree-common-pdf</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <copy file="target/${project.artifactId}.jar" todir="../../deploy">
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
