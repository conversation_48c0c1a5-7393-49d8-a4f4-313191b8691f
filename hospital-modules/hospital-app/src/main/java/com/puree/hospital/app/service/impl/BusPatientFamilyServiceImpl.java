package com.puree.hospital.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.app.config.LeerRegisterConfig;
import com.puree.hospital.app.domain.BusChannelPatientAgentRelation;
import com.puree.hospital.app.domain.BusCommunicationMessage;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusDoctorPatient;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusGuardianDO;
import com.puree.hospital.app.domain.BusHospitalFamily;
import com.puree.hospital.app.domain.BusImGroupMember;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientHospital;
import com.puree.hospital.app.domain.BusPatientPartners;
import com.puree.hospital.app.domain.GuardianConfig;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.BusGuardianDTO;
import com.puree.hospital.app.domain.dto.LeerRegisterRequestDTO;
import com.puree.hospital.app.domain.vo.BaseChannelPartnerVO;
import com.puree.hospital.app.domain.vo.BusPatientFamilyVo;
import com.puree.hospital.app.domain.vo.ConversationInfoVO;
import com.puree.hospital.app.helper.GuardianHelper;
import com.puree.hospital.app.his.IPatientMedicalRecord;
import com.puree.hospital.app.his.domain.HisPatientInfo;
import com.puree.hospital.app.his.domain.query.HisPatientInfoQuery;
import com.puree.hospital.app.his.helper.HospitalHisHelper;
import com.puree.hospital.app.login.channel.partner.IChannelPartnerCompatible;
import com.puree.hospital.app.mapper.BusCommunicationMessageMapper;
import com.puree.hospital.app.mapper.BusGuardianMapper;
import com.puree.hospital.app.mapper.BusHospitalFamilyMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPatientHospitalMapper;
import com.puree.hospital.app.mapper.BusPatientMapper;
import com.puree.hospital.app.mapper.BusPatientPartnersMapper;
import com.puree.hospital.app.queue.producer.TerminateFollowUpProducer;
import com.puree.hospital.app.service.IBusChannelPartnerAgentService;
import com.puree.hospital.app.service.IBusChannelPatientAgentRelationService;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.app.service.IBusDoctorPatientService;
import com.puree.hospital.app.service.IBusPatientFamilyService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.IdCardNumberUtils;
import com.puree.hospital.common.core.utils.RegexValidateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.http.HttpUtils;
import com.puree.hospital.common.security.signature.util.LeerRegisterSignatureUtil;
import com.puree.hospital.setting.api.RemotePlatformSettingApi;
import com.puree.hospital.tool.api.RemoteSmsNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * 就诊人服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPatientFamilyServiceImpl extends ServiceImpl<BusPatientFamilyMapper, BusPatientFamily> implements IBusPatientFamilyService {
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final BusHospitalFamilyMapper busHospitalFamilyMapper;
    private final BusPatientHospitalMapper busPatientHospitalMapper;
    private final BusPatientPartnersMapper busPatientPartnersMapper;
    private final BusCommunicationMessageMapper busCommunicationMessageMapper;
    private final BusGuardianMapper busGuardianMapper;
    private final GuardianHelper guardianHelper;
    private final RemoteSmsNotificationService remoteSmsNotificationService;
    @Resource
    @Lazy
    private IChannelPartnerCompatible channelPartnerCompatible;

    @Resource
    @Lazy
    private IBusChannelPartnerAgentService busChannelPartnerAgentService;

    @Lazy
    @Resource
    private TerminateFollowUpProducer terminateFollowUpProducer;

    @Resource
    private IBusDoctorPatientService busDoctorPatientService;
    @Resource
    private IBusDoctorHospitalService busDoctorHospitalService;
    @Resource
    private BusDoctorPatientGroupServiceImpl busDoctorPatientGroupServiceImpl;
    @Resource
    private BusImGroupMemberServiceImpl busImGroupMemberServiceImpl;
    @Resource
    private IBusChannelPatientAgentRelationService busChannelPatientAgentRelationService;
    @Resource
    private HospitalHisHelper hospitalHisHelper;
    @Resource
    private BusPatientMedicalInfoServiceImpl busPatientMedicalInfoServiceImpl;
    @Resource
    private RemotePlatformSettingApi remotePlatformSettingApi;
    @Resource
    private BusPatientMapper busPatientMapper;

    private final String LEER_REGISTER_CONFIG_KEY = "leer.register.config";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addInterrogator(BusPatientFamily interrogator) {
        //校验身份证
        checkIdCard(interrogator);

        interrogator.setIdType(IdCardTypeEnum.getByLabel(interrogator.getIdType()).getCode());

        // 校验验证码是否正确
        checkCode(interrogator);
        if (ObjectUtil.isNull(interrogator.getDateOfBirth())) {
            Date dateOfBirth = null;
            try {
                dateOfBirth = IdCardNumberUtils.getBirthDayFromIdCard(interrogator.getIdNumber());
            } catch (ParseException e) {
                log.error("错误信息", e);
            }
            if (ObjectUtil.isNull(dateOfBirth)) {
                throw new ServiceException("出生日期为空！");
            }
            interrogator.setDateOfBirth(dateOfBirth);
        }
        if (StringUtils.isEmpty(interrogator.getSex())) {
            int sex = IdCardNumberUtils.getSexFromIdCard(interrogator.getIdNumber());
            interrogator.setSex(sex == 1 ? "1" : "0");
        }
        // 校验身份证号是否存在
        BusPatientFamily query = new BusPatientFamily();
        query.setIdNumber(interrogator.getIdNumber());
        query.setPatientId(interrogator.getPatientId());
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.getOne(query);
        Boolean isUpdate = false;
        if (ObjectUtil.isNotNull(busPatientFamily)) {
            if (busPatientFamily.getVisible().equals(YesNoEnum.NO.getCode())) {
                interrogator.setVisible(YesNoEnum.YES.getCode());
                interrogator.setId(busPatientFamily.getId());
                isUpdate = true;
            } else {
                throw new ServiceException("该就诊人已存在！");
            }
        }
        // 校验患者关系
        getPatientFamily(interrogator);
        // 判断是否设为默认
        checkStatus(interrogator);
        interrogator.setCreateTime(new Date());
        interrogator.setOutpatientNumber(String.valueOf(System.currentTimeMillis()));
        // 判断患者是否是经纪人扫码添加
        LambdaQueryWrapper<BusPatientHospital> wrapper = new LambdaQueryWrapper<BusPatientHospital>()
                .eq(BusPatientHospital::getHospitalId, interrogator.getHospitalId())
                .eq(BusPatientHospital::getPatientId, interrogator.getPatientId());
        BusPatientHospital busPatientHospital = busPatientHospitalMapper.selectOne(wrapper);
        LambdaQueryWrapper<BusChannelPatientAgentRelation> queryWrapper = new LambdaQueryWrapper<BusChannelPatientAgentRelation>()
                .eq(BusChannelPatientAgentRelation::getHospitalId, interrogator.getHospitalId())
                .eq(BusChannelPatientAgentRelation::getPatientId, interrogator.getPatientId());
        BusChannelPatientAgentRelation agentRelation = busChannelPatientAgentRelationService.getOne(queryWrapper);
        if (ObjectUtil.isNotNull(busPatientHospital) && ObjectUtil.isNotNull(agentRelation)) {
            interrogator.setSource("2");
        }
        log.info("添加就诊人信息={}", interrogator);
        if (isUpdate.equals(Boolean.TRUE)) {
            busPatientFamilyMapper.updateById(interrogator);
            return interrogator.getId();
        }
        busPatientFamilyMapper.insert(interrogator);

        // 儿童新增监护人
        GuardianConfig guardianConfig = guardianHelper.getGuardianConfig(interrogator.getHospitalId());
        if (guardianConfig != null && guardianConfig.isEnableChangeAge() && guardianHelper.isChild(interrogator.getDateOfBirth(), guardianConfig)
                && ObjectUtil.isNotNull(interrogator.getBusGuardianDTO())) {
            addGuardian(interrogator);
        }

        return interrogator.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void addGuardian(BusPatientFamily interrogator) {
        BusGuardianDTO guardian = interrogator.getBusGuardianDTO();
        if (Objects.isNull(guardian) || Objects.isNull(guardian.getName()) || Objects.isNull(guardian.getIdType()) || Objects.isNull(guardian.getIdCard()) || Objects.isNull(guardian.getPhoneNumber()) || Objects.isNull(guardian.getRelationship())) {
            throw new ServiceException("监护人信息未完善");
        }
        if (IdCardTypeEnum.isIdCard(interrogator.getIdType())) {
            boolean validIdCard = IdcardUtil.isValidCard(interrogator.getBusGuardianDTO().getIdCard());
            if (!validIdCard) {
                throw new ServiceException("监护人证件号码不合法");
            }
        }
        boolean isValid = Validator.isMobile(interrogator.getBusGuardianDTO().getPhoneNumber());
        if (!isValid) {
            throw new ServiceException("监护人手机号码不正确");
        }
        guardian.setPatientId(interrogator.getPatientId());
        guardian.setFamilyId(interrogator.getId());
        guardian.setIdType(IdCardTypeEnum.getByLabel(guardian.getIdType()).getCode());
        BusGuardianDO busGuardian = BeanUtil.copyProperties(guardian, BusGuardianDO.class);
        busGuardianMapper.insert(busGuardian);
    }


    private void checkIdCard(BusPatientFamily interrogator) {
        // 校验身份证号是否正确
        String reg18 = "^([1-6][1-9]|50)\\d{4}(18|19|20)\\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)" +
                "\\d{3}[0-9Xx]$";
        String reg15 = "^([1-6][1-9]|50)\\d{4}(18|19|20)\\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)" +
                "\\d{3}[0-9Xx]$";

        if ( StringUtils.isEmpty(interrogator.getIdType()) || IdCardTypeEnum.ID_CARD.equals(IdCardTypeEnum.getByLabel(interrogator.getIdType())) ) {
            boolean check18 = RegexValidateUtils.check(interrogator.getIdNumber(), reg18);
            boolean check15= RegexValidateUtils.check(interrogator.getIdNumber(), reg15);
            if (!(check18 || check15)) {
                throw new ServiceException("身份证号不合法");
            }
        }
    }

    @Override
    public BusPatientFamily getInterrogator(BusPatientFamily interrogator) {
        LambdaQueryWrapper<BusPatientFamily> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusPatientFamily::getPatientId, interrogator.getPatientId())
                .eq(BusPatientFamily::getId, interrogator.getId());
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectOne(lambdaQuery);
        if (ObjectUtil.isNotNull(busPatientFamily)) {
            if (ObjectUtil.isNotNull(busPatientFamily.getDateOfBirth())) {
                busPatientFamily.setSexStr(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd", busPatientFamily.getDateOfBirth())));
            }
            // 查询患者关系
            if (ObjectUtil.isNotNull(busPatientFamily.getPatientRelationship())) {
                String relation = busPatientFamilyMapper.selectRelation(busPatientFamily.getPatientRelationship());
                busPatientFamily.setPatientRelationshipValue(relation);
            }
            busPatientFamily.setIdType(IdCardTypeEnum.getByCode(busPatientFamily.getIdType()).getLabel());

            // 判断就诊人是否为儿童
            GuardianConfig guardianConfig = guardianHelper.getGuardianConfig(busPatientFamily.getHospitalId());
            if (guardianConfig == null) {
                return busPatientFamily;
            }
            if (guardianConfig.isEnableChangeAge() && guardianHelper.isChild(busPatientFamily.getDateOfBirth(), guardianConfig)) {
                // 查询儿童就诊人的监护人信息
                BusGuardianDO busGuardian = busGuardianMapper.selectOne(new LambdaQueryWrapper<BusGuardianDO>()
                        .eq(BusGuardianDO::getPatientId, busPatientFamily.getPatientId())
                        .eq(BusGuardianDO::getFamilyId, busPatientFamily.getId()));
                if (Objects.isNull(busGuardian) || Objects.isNull(busGuardian.getIdType())) {
                    busPatientFamily.setIsChild(true);
                    return busPatientFamily;
                }
                busGuardian.setIdType(IdCardTypeEnum.getByCode(busGuardian.getIdType()).getLabel());
                busPatientFamily.setBusGuardianDTO(BeanUtil.copyProperties(busGuardian, BusGuardianDTO.class));
                busPatientFamily.setIsChild(true);
            }
        }
        return busPatientFamily;
    }

    @Override
    public int edit(BusPatientFamily patientFamily) {
        // 校验验证码是否正确
        checkCode(patientFamily);
        // 校验患者关系
        getPatientFamily(patientFamily);
        // 判断是否设为默认
        checkStatus(patientFamily);
        // 校验身份证号是否存在
        QueryWrapper<BusPatientFamily> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id_number", patientFamily.getIdNumber());
        queryWrapper.eq("patient_id", patientFamily.getPatientId());
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotNull(busPatientFamily) && !busPatientFamily.getId().equals(patientFamily.getId())) {
            throw new ServiceException("该就诊人已存在！");
        }
        patientFamily.setUpdateTime(new Date());
        UpdateWrapper<BusPatientFamily> updateWrapper = new UpdateWrapper<>();
        updateWrapper
                .eq("patient_id", patientFamily.getPatientId())
                .eq("id", patientFamily.getId());
        patientFamily.setOutpatientNumber((null == busPatientFamily || CharSequenceUtil.isBlank(busPatientFamily.getOutpatientNumber())) ? String.valueOf(System.currentTimeMillis()) : busPatientFamily.getOutpatientNumber());

        // 编辑监护人信息
        Long hospitalId = SecurityUtils.getHospitalId();
        log.info("hospitalId:{}", hospitalId);
        GuardianConfig guardianConfig = guardianHelper.getGuardianConfig(hospitalId);
        BusPatientFamily family = busPatientFamilyMapper.selectById(patientFamily.getId());
        if (guardianConfig == null || (!guardianConfig.isEnableChangeAge()) || (!guardianHelper.isChild(family.getDateOfBirth(), guardianConfig))) {
            return busPatientFamilyMapper.update(patientFamily, updateWrapper);
        }
        if (patientFamily.getBusGuardianDTO() != null) {
            BusGuardianDTO busGuardianDTO = patientFamily.getBusGuardianDTO();
            if (Objects.isNull(busGuardianDTO.getName()) || Objects.isNull(busGuardianDTO.getIdType()) || Objects.isNull(busGuardianDTO.getIdCard()) || Objects.isNull(busGuardianDTO.getPhoneNumber()) || Objects.isNull(busGuardianDTO.getRelationship())) {
                throw new ServiceException("监护人信息未完善");
            }
            busGuardianDTO.setFamilyId(patientFamily.getId());
            busGuardianDTO.setIdType(IdCardTypeEnum.getByLabel(busGuardianDTO.getIdType()).getCode());
            BusGuardianDO busGuardian = BeanUtil.copyProperties(busGuardianDTO, BusGuardianDO.class);
            busGuardianMapper.insertOrUpdate(busGuardian);
        }

        return busPatientFamilyMapper.update(patientFamily, updateWrapper);
    }

    private void checkCode(BusPatientFamily patientFamily) {
        // 校验验证码是否正确
        if (StringUtils.isNotEmpty(patientFamily.getCode())) {
            AjaxResult result = remoteSmsNotificationService.checkVerifyCode(patientFamily.getPhone(), patientFamily.getCode());
            if (!result.isSuccess()) {
                throw new ServiceException("验证码错误");
            }
        }
    }

    @Override
    public BusPatientFamily getById(Long id) {
        return busPatientFamilyMapper.selectById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInterrogator(BusPatientFamily interrogator) {
        QueryWrapper<BusHospitalFamily> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_id", interrogator.getHospitalId());
        queryWrapper.eq("family_id", interrogator.getId());
        queryWrapper.eq("patient_id", interrogator.getPatientId());
        BusHospitalFamily family = busHospitalFamilyMapper.selectOne(queryWrapper.last("limit 1"));
        BusHospitalFamily busHospitalFamily = new BusHospitalFamily();
        if (ObjectUtil.isNull(family)) {
            busHospitalFamily.setFamilyId(interrogator.getId());
            busHospitalFamily.setHospitalId(interrogator.getHospitalId());
            busHospitalFamily.setPatientId(interrogator.getPatientId());
            busHospitalFamily.setDiagnosisArchives(interrogator.getDiagnosisArchives());
            busHospitalFamily.setCreateTime(new Date());
            return busHospitalFamilyMapper.insert(busHospitalFamily);
        }
        if (StringUtils.isNotEmpty(busHospitalFamily.getDiagnosisArchives())) {
            throw new ServiceException("您已上传过诊疗档案，无须重复上传");
        }
        UpdateWrapper<BusHospitalFamily> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("hospital_id", interrogator.getHospitalId());
        updateWrapper.eq("family_id", interrogator.getId());
        updateWrapper.eq("patient_id", interrogator.getPatientId());
        busHospitalFamily.setDiagnosisArchives(interrogator.getDiagnosisArchives());
        busHospitalFamily.setUpdateTime(new Date());
        return busHospitalFamilyMapper.update(busHospitalFamily, updateWrapper);
    }

    @Override
    public BusPatientFamilyVo selectPatientFamilyById(Long id, Long hospitalId) {
        log.info("患者ID={}，医院ID={}", id, hospitalId);
        BusPatientFamilyVo busPatientFamilyVo = busPatientFamilyMapper.selectPatientFamilyById(id, hospitalId);
        log.info("就诊人信息={}", busPatientFamilyVo);
        if (ObjectUtil.isNotNull(busPatientFamilyVo.getDateOfBirth())) {
            busPatientFamilyVo.setFamilyAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd", busPatientFamilyVo.getDateOfBirth())));
        }
        return busPatientFamilyVo;
    }

    @Override
    public List<BusPatientFamilyVo> interrogatorList(BusPatientFamily interrogator) {
        List<BusPatientFamilyVo> busPatientFamilyVos = busPatientFamilyMapper.selectInterrogatorList(interrogator);
        // HIS接入HIS 获取就诊卡号
        setHisPatientId(busPatientFamilyVos);
        busPatientFamilyVos.forEach(p -> {
            Date dateOfBirth = p.getDateOfBirth();
            if (ObjectUtil.isNotNull(dateOfBirth)) {
                p.setFamilyAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd", dateOfBirth)));
            }
            p.setIdType(IdCardTypeEnum.getByCode(p.getIdType()).getLabel());
            p.setGuardianId(p.getId());
        });

        if (interrogator.getIsChild()) {
            // 读配置
            if (CollectionUtil.isEmpty(busPatientFamilyVos)) {
                return Collections.emptyList();
            }
            GuardianConfig guardianConfig = guardianHelper.getGuardianConfig(busPatientFamilyVos.get(0).getHospitalId());
            if (guardianConfig == null || (!guardianConfig.isEnableChangeAge()) || guardianConfig.getChildrenAge() == null) {
                return Collections.emptyList();
            }
            // 满足条件的就诊人
            return busPatientFamilyVos.stream().filter(family -> {
                if (family.getDateOfBirth() == null || family.getIdNumber() == null || family.getCellPhoneNumber() == null) {
                    return false;
                }
                LocalDate birthDate = LocalDate.parse(DateUtils.parseDateToStr(DatePattern.NORM_DATE_PATTERN, family.getDateOfBirth()));
                int age = Period.between(birthDate, LocalDate.now()).getYears();
                return age >= guardianConfig.getChildrenAge();
            }).collect(Collectors.toList());
        }

        return busPatientFamilyVos;
    }

    /**
     * 获取就诊卡号
     *
     * @param busPatientFamilyVos 就诊人信息
     */
    private void setHisPatientId(List<BusPatientFamilyVo> busPatientFamilyVos) {
        IPatientMedicalRecord handler = busPatientMedicalInfoServiceImpl.getHisChannelHandler();
        if (Objects.isNull(handler)) {
            return;
        }
        for (BusPatientFamilyVo p : busPatientFamilyVos) {
            HisPatientInfoQuery query = new HisPatientInfoQuery();
            if (StringUtils.isEmpty(p.getIdNumber()) || StringUtils.isEmpty(p.getName())) {
                continue;
            }
            query.setPatientName(p.getName());
            query.setIDCardNO(p.getIdNumber());
            query.setHospitalId(SecurityUtils.getHospitalId());
            HisPatientInfo patientInfo = handler.getPatientInfo(query);
            if (Objects.isNull(patientInfo)) {
                continue;
            }
            p.setHisPatientNo(patientInfo.getPatientId());
        }
    }

    @Override
    public int deleteInterrogator(BusPatientFamily interrogator) {
        Assert.notNull(interrogator, "参数校验错误");

        Long patientId = interrogator.getPatientId();
        Assert.notNull(patientId, "patientId不能为空");

        LambdaQueryWrapper<BusPatientFamily> wrapper = Wrappers.lambdaQuery(BusPatientFamily.class);
        wrapper.eq(BusPatientFamily::getPatientId, patientId)
                .eq(BusPatientFamily::getVisible, YesNoEnum.YES.getCode());
        List<BusPatientFamily> busPatientFamilies = busPatientFamilyMapper.selectList(wrapper);
        Assert.notEmpty(busPatientFamilies, "就诊人信息不存在！");
        if (busPatientFamilies.size() <= 1){
            throw new ServiceException("最后一个就诊人不可删除");
        }


        BusPatientFamily patientFamily = busPatientFamilyMapper.selectById(interrogator.getId());
        if (patientFamily == null || YesNoEnum.NO.getCode().equals(patientFamily.getVisible())) {
            throw new ServiceException("就诊人不存在！");
        }

        BusPatientFamily delDTO = new BusPatientFamily();
        BeanUtil.copyProperties(patientFamily,delDTO);
        delDTO.setId(interrogator.getId());
        delDTO.setVisible(YesNoEnum.NO.getCode());
        int count = busPatientFamilyMapper.updateById(delDTO);
        //终止随访
        if (count > 0) {
            terminateFollowUpProducer.send(BeanUtil.copyProperties(patientFamily, BusPatientFamilyVo.class));
            //删除患者关系
        }
        return count;
    }

    private void checkStatus(BusPatientFamily interrogator) {
        Integer status = interrogator.getStatus();
        if (ObjectUtil.isNotNull(status) && YesNoEnum.YES.getCode().equals(status)) {
            BusPatientFamily busPatientFamily = new BusPatientFamily();
            busPatientFamily.setStatus(YesNoEnum.NO.getCode());
            UpdateWrapper<BusPatientFamily> updateWrapper = new UpdateWrapper<>();
            updateWrapper
                    .eq("patient_id", interrogator.getPatientId());
            busPatientFamilyMapper.update(busPatientFamily, updateWrapper);
        }
    }

    private void getPatientFamily(BusPatientFamily patientFamily) {
        // 查询患者关系
        String relation = busPatientFamilyMapper.selectRelation(patientFamily.getPatientRelationship());
        if ("本人".equals(relation) || "配偶".equals(relation)) {
            QueryWrapper<BusPatientFamily> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(BusPatientFamily::getPatientId, patientFamily.getPatientId())
                    .eq(BusPatientFamily::getVisible, YesNoEnum.YES.getCode())
                    .eq(BusPatientFamily::getPatientRelationship, patientFamily.getPatientRelationship());

            BusPatientFamily existingPatientFamily = busPatientFamilyMapper.selectOne(queryWrapper);
            if (Objects.nonNull(existingPatientFamily) &&
                    (patientFamily.getId() == null ||
                    !patientFamily.getId().equals(existingPatientFamily.getId()))) {
                throw new ServiceException("患者关系已存在！");

            }
        }
    }

    @Override
    public boolean check(Long id, Long hospitalId, Long groupId) {
        BusPatientFamily patientFamily = busPatientFamilyMapper.selectById(id);
        log.info("参数id={},参数hospitalId={},就诊人信息={}", id, hospitalId, patientFamily);
        Assert.notNull(patientFamily,"查询患者失败");

        QueryWrapper<BusHospitalFamily> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_id", hospitalId);
        queryWrapper.eq("family_id", id);
        queryWrapper.eq("patient_id", patientFamily.getPatientId());
        BusHospitalFamily family = busHospitalFamilyMapper.selectOne(queryWrapper.last("limit 1"));
        BusCommunicationMessage busCommunicationMessage = busCommunicationMessageMapper.selectDiagnosisArchives(groupId);
        if (ObjectUtil.isNull(family) && ObjectUtil.isNull(busCommunicationMessage)) {
            return true;
        }
        return ObjectUtil.isNotNull(family) &&
                StringUtils.isEmpty(family.getDiagnosisArchives()) &&
                ObjectUtil.isNull(busCommunicationMessage);
    }

    @Override
    public int addFamily(BusPatientFamily busPatientFamily) {
        busPatientFamily.setCreateTime(new Date());
        return busPatientFamilyMapper.insert(busPatientFamily);
    }

    @Override
    public Long selectRelationShip(String relation) {
        return busPatientFamilyMapper.selectRelationShip(relation);
    }

    /**
     * 根据身份证号查询就诊人信息
     *
     * @param patientFamily 身份证号码
     * @return 患者信息
     */
    @Override
    public com.puree.hospital.app.api.model.BusPatientFamilyVo
    getInfoByIdNumber(BusPatientFamily patientFamily) {

        //根据用户身份证查询患者的时候，可能存在查询结果为空的情况
        Long relationId = busPatientFamilyMapper.selectRelationShip("本人");
        LambdaQueryWrapper<BusPatientFamily> lambdaQuery = Wrappers.lambdaQuery();
        if (relationId != null) {
            lambdaQuery.eq(BusPatientFamily::getIdNumber, patientFamily.getIdNumber());
            lambdaQuery.eq(BusPatientFamily::getHospitalId, patientFamily.getHospitalId());
            lambdaQuery.eq(BusPatientFamily::getPatientRelationship, relationId);
            lambdaQuery.last(" limit 1");
            BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectOne(lambdaQuery);
            if (busPatientFamily != null) {
                return dataConvert(busPatientFamily);
            }
        }
        lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusPatientFamily::getIdNumber, patientFamily.getIdNumber());
        lambdaQuery.orderByAsc(BusPatientFamily::getCreateTime);
        lambdaQuery.last(" limit 1");
        return dataConvert(busPatientFamilyMapper.selectOne(lambdaQuery));
    }

    @Override
    public List<com.puree.hospital.app.api.model.BusPatientFamilyVo> getList(BusPatientFamily patientFamily) {
        if (Objects.isNull(patientFamily) || (StringUtils.isBlank(patientFamily.getIdNumber())
                && StringUtils.isBlank(patientFamily.getCellPhoneNumber()))) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BusPatientFamily> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(StringUtils.isNotEmpty(patientFamily.getIdNumber()), BusPatientFamily::getIdNumber, patientFamily.getIdNumber());
        lambdaQuery.eq(StringUtils.isNotEmpty(patientFamily.getCellPhoneNumber()), BusPatientFamily::getCellPhoneNumber, patientFamily.getCellPhoneNumber());
        lambdaQuery.orderByAsc(BusPatientFamily::getCreateTime);
        List<BusPatientFamily> list = busPatientFamilyMapper.selectList(lambdaQuery);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        if(Objects.isNull(patientFamily.getHospitalId())) {
            return list.stream().map(this::dataConvert).collect(Collectors.toList());
        }
        //如果查询条件医院id不为空需要增加如下条件
        List<Long> patientIds = list.stream().map(BusPatientFamily::getPatientId).distinct().collect(Collectors.toList());
        //查询患者跟医院的绑定关系
        LambdaQueryWrapper<BusPatientHospital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BusPatientHospital::getPatientId, patientIds);
        queryWrapper.eq(BusPatientHospital::getHospitalId, patientFamily.getHospitalId());
        List<BusPatientHospital> patientHospitalList = busPatientHospitalMapper.selectList(queryWrapper);
        //查询患者跟合作机构的绑定关系
        LambdaQueryWrapper<BusPatientPartners> partnerPartners = new LambdaQueryWrapper<>();
        partnerPartners.in(BusPatientPartners::getPatientId, patientIds);
        partnerPartners.eq(BusPatientPartners::getHospitalId, patientFamily.getHospitalId());
        List<BusPatientPartners> patientPartnersList = busPatientPartnersMapper.selectList(partnerPartners);
        patientIds.clear();
        if (CollectionUtil.isNotEmpty(patientHospitalList)) {
            patientIds.addAll(patientHospitalList.stream().map(BusPatientHospital::getPatientId).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(patientPartnersList)) {
            patientIds.addAll(patientPartnersList.stream().map(BusPatientPartners::getPatientId).collect(Collectors.toList()));
        }
        //响应按照与患者关系进行排序
        return list.stream().filter(s -> patientIds.contains(s.getPatientId()))
                //如果患者关系为空，则按照9999进行排序
                .sorted(Comparator.comparing(s -> Objects.isNull(s.getPatientRelationship()) ? 9999 : s.getPatientRelationship()))
                .map(this::dataConvert).collect(Collectors.toList());
    }

    /**
     * 查询就诊人信息
     *
     * @param userId     账户id
     * @param hospitalId 医院id
     * @return userID
     */
    @Override
    public Long queryInfoByUserId(Long userId, Long hospitalId) {
        //首先获取关系为“本人”的就诊人
        BusPatientFamily busPatientFamily;
        Long relationId = busPatientFamilyMapper.selectRelationShip("本人");
        BusPatientFamily query = new BusPatientFamily();
        if (relationId != null) {
            query.setPatientId(userId);
            query.setPatientRelationship(relationId);
            query.setVisible(YesNoEnum.YES.getCode());
            busPatientFamily = busPatientFamilyMapper.getOne(query);
            if (busPatientFamily != null) {
                return busPatientFamily.getId();
            }
        }
        //如果获取不到“本人”关系的就诊人，就获取最近注册的那个就诊人
        query = new BusPatientFamily();
        query.setPatientId(userId);
        query.setVisible(YesNoEnum.YES.getCode());
        busPatientFamily = busPatientFamilyMapper.getOne(query);
        if (busPatientFamily != null)
            return busPatientFamily.getId();
        return null;
    }

    @Override
    public boolean allowPatientIdByAppRole(@NotNull SMSLoginUser loginUser, @NotNull BusPatientFamily patientFamily) {
        // 1.审方药师
        if (AppRoleEnum.PHARMACIST.getCode().equals(loginUser.getIdentity())) {
            // 这里不做校验 - 外层需要过滤 - 审方药师只能查询本院的患者本人信息
            return true;
        }
        // 2.如果是医生
        if (AppRoleEnum.DOCTOR.getCode().equals(loginUser.getIdentity())) {
            // 2.如果是医生，那么限制范围是查询的就诊人信息是他的直接患者或者直接患者的亲友 - 医生的登录信息就是bus_doctor表的id
            return allowPatientIdWithDoctor(loginUser, patientFamily);
        }
        // 3..合作渠道 - 不允许查询患者信息
        if (AppRoleEnum.CHANNEL.getCode().equals(loginUser.getIdentity())) {
            log.debug("合作渠道{}没有权限查询患者{}的就诊人信息", loginUser.getUserid(), patientFamily.getPatientId());
            return false;
        }
        // 4.经纪人或者患者
        if (Objects.isNull(loginUser.getIdentity()) || AppRoleEnum.isPatient(loginUser.getIdentity()) || AppRoleEnum.isAgent(loginUser.getIdentity())) {
            if (Objects.equals(loginUser.getUserid(), patientFamily.getPatientId())) {
                // 如果是患者本人 - 放行
                return true;
            }
            BaseChannelPartnerVO channelPartner = channelPartnerCompatible.getChannelPartner();
            // 如果没有拿到信息，那就是普通的患者，只能查自己的身份信息
            if (ObjectUtil.isNull(channelPartner)) {
                patientFamily.setPatientId(loginUser.getUserid());
            }
            return true;
        }
        // 放行，返回原来查询的患者用户id
        return true;
    }

    /**
     * 医生 - 校验检查PatientId是否可以查询
     * @param loginUser 登录用户
     * @param patientFamily 患者信息
     * @return true - 可以查询，false - 不可以查询
     */
    private boolean allowPatientIdWithDoctor(SMSLoginUser loginUser, BusPatientFamily patientFamily) {
        // 新版本
        // 如果是群组进来的查询，医生和患者属于一个群组内，则可以查询到
        if (!Objects.isNull(patientFamily.getGroupId())) {
            ConversationInfoVO groupInfo = busDoctorPatientGroupServiceImpl.getGroupInfo(patientFamily.getGroupId());
            return Objects.equals(groupInfo.getPatientId(), patientFamily.getPatientId()) &&
                    checkDoctorIdInGroupMember(loginUser.getUserid(), Collections.singletonList(groupInfo.getGroupId()));
        }
        // 所以判断条件是查询的patientId必须是医生账号关联的患者的patientId
        // 以下 兼容医生端旧版本 - 3.3.5 之前的版本
        // 2.1 1v1 的关联关系查询
        BusDoctorPatient busDoctorPatient = new BusDoctorPatient();
        busDoctorPatient.setPatientId(patientFamily.getPatientId());
        busDoctorPatient.setDoctorId(loginUser.getUserid());
        busDoctorPatient.setFamilyId(patientFamily.getId());
        List<BusDoctorPatient> doctorPatientInfoList = busDoctorPatientService.getDoctorPatientInfo(busDoctorPatient);
        if (!CollectionUtils.isEmpty(doctorPatientInfoList)) {
            return true;
        }
        // 2.2 服务包的关联关系查询 - 只能兼容到服务包的群组账号不受权限影响
        // 2.2.1 根据查询患者查询对应的群组
        BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
        busDoctorPatientGroup.setPatientId(patientFamily.getPatientId());
        busDoctorPatientGroup.setFamilyId(patientFamily.getId());
        List<BusDoctorPatientGroup> busDoctorPatientGroupList = busDoctorPatientGroupServiceImpl.getBusDoctorPatientGroupList(busDoctorPatientGroup);
        if (CollectionUtils.isEmpty(busDoctorPatientGroupList)) {
            log.debug("医生{}没有权限查询患者id{}的就诊人信息", loginUser.getUserid(), patientFamily.getPatientId());
            return false;
        }
        // 2.2.2 医生id在组内
        return checkDoctorIdInGroupMember(loginUser.getUserid(),
                busDoctorPatientGroupList.stream().map(BusDoctorPatientGroup::getId).collect(Collectors.toList()));
    }

    /**
     * 检查医生id是否在群组成员内
     * @param doctorId 医生id
     * @param groupIdList 群组id列表
     * @return true - 在群组内，false - 不在群组内
     */
    private boolean checkDoctorIdInGroupMember(Long doctorId, List<Long> groupIdList) {
        List<BusImGroupMember> memberList = busImGroupMemberServiceImpl.getGroupMember(doctorId, groupIdList);
        return !CollectionUtils.isEmpty(memberList);
    }

    @Override
    public boolean checkPatientFamilyWithHospital(@NotNull SMSLoginUser loginUser, BusPatientFamily interrogator) {
        if (Objects.isNull(interrogator) || Objects.isNull(interrogator.getPatientId())) {
            return true;
        }
        // 如果是审方药师 - 只能查看本院的患者信息
        if (AppRoleEnum.PHARMACIST.getCode().equals(loginUser.getIdentity())) {
            List<BusDoctorHospital> busDoctorHospitalList = busDoctorHospitalService.selectDoctorHospitalListByDoctorId(loginUser.getUserid());
            if (CollectionUtils.isEmpty(busDoctorHospitalList)) {
                log.debug("未查询到该审方药师{}，没有权限查询患者id{}的就诊人信息", loginUser.getUserid(), interrogator.getPatientId());
                return false;
            }
            //TODO - 患者第一次登录的医院id有了之后去其他医院登录，对应的医院id还是原来的，所以审方药师此处校验不了医院id
//            boolean isExist = busDoctorHospitalList.stream()
//                    .anyMatch(busDoctorHospital -> Objects.equals(busDoctorHospital.getHospitalId(), interrogator.getHospitalId()));
//            if (!isExist) {
//                log.debug("审方药师{}没有权限查询患者id{}的就诊人信息", loginUser.getUserid(), interrogator.getPatientId());
//                return false;
//            }
        }
        return true;
    }


    /**
     * 数据转换
     * @param busPatientFamily 就诊人信息
     * @return 就诊人信息
     */
    private com.puree.hospital.app.api.model.BusPatientFamilyVo dataConvert(BusPatientFamily busPatientFamily) {
        //数据校验，避免空指针异常
        if(busPatientFamily == null){
            return null;
        }

        com.puree.hospital.app.api.model.BusPatientFamilyVo vo = BeanUtil.copyProperties(busPatientFamily, com.puree.hospital.app.api.model.BusPatientFamilyVo.class);
        String sex = busPatientFamily.getSex();
        //性别
        if (CharSequenceUtil.isNotEmpty(sex)) {
            if (sex.equals("1")) {
                vo.setSex("男");
            } else {
                vo.setSex("女");
            }
        }
        //年龄
        Date dateOfBirth = busPatientFamily.getDateOfBirth();
        if (ObjectUtil.isNotNull(dateOfBirth)) {
            vo.setAge(DateUtil.ageOfNow(dateOfBirth));
        }
        //民族
        Long nation = busPatientFamily.getNation();
        if (ObjectUtil.isNotNull(nation)) {
            vo.setNation(busPatientFamilyMapper.selectRelation(nation));
        }
        //婚姻状况
        Integer maritalStatus = busPatientFamily.getMaritalStatus();
        if (ObjectUtil.isNotEmpty(maritalStatus)) {
            if (maritalStatus == 1) {
                vo.setMaritalStatus("已婚");
            } else {
                vo.setMaritalStatus("未婚");
            }
        }
        String detailAddress = busPatientFamily.getDetailAddress();
        if (CharSequenceUtil.isEmpty(detailAddress)) {
            vo.setDetailAddress(null);
        }
        return vo;
    }

    /**
     * 根据身份证号查询就诊人信息
     *
     * @param patientFamily 身份证号码
     * @return 就诊人信息
     */
    @Override
    public BusPatientFamily selectFamilyByNo(BusPatientFamily patientFamily) {
        LambdaQueryWrapper<BusPatientFamily> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusPatientFamily::getIdNumber, patientFamily.getIdNumber());
        lambdaQuery.orderByAsc(BusPatientFamily::getCreateTime);
        lambdaQuery.last(" limit 1");
        return busPatientFamilyMapper.selectOne(lambdaQuery);
    }

    @Override
    public BusPatientFamily selectFamilyById(Long id) {
        return busPatientFamilyMapper.selectById(id);
    }


    /**
     * 获取或注册乐尔受检人ID
     *
     * @param id 就诊人ID
     * @return 格式：leerPatientId|phone|leerVisitorId（如果visitorId不存在则只返回前两个）
     */
    @Override
    public String getOrRegisterLeerPatientId(Long id) {
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(id);
        if (Objects.isNull(busPatientFamily)) {
            throw new ServiceException("就诊人信息不存在");
         }
        // 获取患者信息（仅用于获取正确的手机号）
        BusPatient busPatient = busPatientMapper.selectById(busPatientFamily.getPatientId());
        if (Objects.isNull(busPatient)) {
            throw new ServiceException("患者信息不存在");
        }

        // 尝试获取九合一受检人ID
        String leerPatientId = busPatientFamily.getLeerPatientId();
        // 如果没有九合一受检人ID，注册并保存到数据库
        if (StringUtils.isEmpty(leerPatientId)) {
            JSONObject result = registerLeerPatient(busPatientFamily, busPatient);
            if (!Objects.equals(200, result.getInteger("code"))) {
                throw new ServiceException("注册众爱健康受检者失败：" + result.getString("msg"));
            }
            JSONObject data = result.getJSONObject("data");
            busPatientFamily.setLeerPatientId(data.getString("patientId"));
            busPatientFamily.setLeerVisitorId(data.getString("visitorId"));
            // 保存到数据库
            busPatientFamilyMapper.updateById(busPatientFamily);
            leerPatientId = busPatientFamily.getLeerPatientId();
        }

        // 构建返回格式：leerPatientId|phone|leerVisitorId
        return buildLeerPatientIdFormat(leerPatientId, busPatient.getPhoneNumber(), busPatientFamily.getLeerVisitorId());
    }

    /**
     * 构建乐尔受检人ID返回格式
     *
     * @param leerPatientId 受检人ID
     * @param phone         手机号
     * @param leerVisitorId 匿名受检人ID（可能为空）
     * @return 格式化的字符串：leerPatientId|phone|leerVisitorId 或 leerPatientId|phone
     */
    private String buildLeerPatientIdFormat(String leerPatientId, String phone, String leerVisitorId) {
        StringJoiner joiner = new StringJoiner("|");
        joiner.add(leerPatientId);
        joiner.add(phone);
        if (StringUtils.isNotEmpty(leerVisitorId)) {
            joiner.add(leerVisitorId);
        }
        return joiner.toString();
    }

    /**
     * 向乐尔注册受检人
     *
     * @param busPatientFamily 就诊人信息
     * @param busPatient 患者信息（已查询，避免重复查询）
     * @return 响应结果的jsonObject对象
     */
    private JSONObject registerLeerPatient(BusPatientFamily busPatientFamily, BusPatient busPatient) {
        // 获取签名配置
        R<String> result = remotePlatformSettingApi.getSettingValue(LEER_REGISTER_CONFIG_KEY);
        if (!result.isSuccess()) {
            throw new ServiceException("获取乐尔注册配置失败");
        }
        LeerRegisterConfig config = JSONObject.parseObject(result.getData(), LeerRegisterConfig.class);
        // 组装参数（传入患者信息避免重复查询）
        String jsonBody = buildLeerRegisterJsonBody(busPatientFamily, busPatient);
        // 生成签名
        Map<String, String> headers = LeerRegisterSignatureUtil.generateSignatureHeaders(config.getAppKey(), config.getAppSecret(), jsonBody);
        // 发送请求注册
        String response = HttpUtils.sendPostJson(config.getUrl(), jsonBody, headers);
        // 解析响应
        return JSONObject.parseObject(response);
    }

    /**
     * 组装乐尔注册受检人需要的参数
     *
     * @param busPatientFamily 就诊人信息
     * @param busPatient 患者信息（已查询，避免重复查询）
     * @return json字符串
     */
    private String buildLeerRegisterJsonBody(BusPatientFamily busPatientFamily, BusPatient busPatient) {
        LeerRegisterRequestDTO request = new LeerRegisterRequestDTO();
        // 查询患者关系
        String relation = busPatientFamilyMapper.selectRelation(busPatientFamily.getPatientRelationship());
        if ("本人".equals(relation)) {
            request.setAnonymous(Boolean.FALSE);
        } else {
            // 若不是本人，则设置匿名访问，乐尔会额外返回visitorId
            request.setAnonymous(Boolean.TRUE);
        }
        // 将Date格式化为yyyy-MM-dd字符串格式
        if (ObjectUtil.isNotNull(busPatientFamily.getDateOfBirth())) {
            request.setBirthday(DateUtil.formatDate(busPatientFamily.getDateOfBirth()));
        } else {
            request.setBirthday(null);
        }
        // 使用对应患者账号的手机号，而不是就诊人的
        request.setPhone(busPatient.getPhoneNumber());
        // 继续封装需要的参数
        request.setPatientName(busPatientFamily.getName());
        request.setSex(busPatientFamily.getSex());
        request.setHeight(busPatientFamily.getHeight());
        request.setWeight(busPatientFamily.getWeight());
        return JSONObject.toJSONString(request);
    }
}
