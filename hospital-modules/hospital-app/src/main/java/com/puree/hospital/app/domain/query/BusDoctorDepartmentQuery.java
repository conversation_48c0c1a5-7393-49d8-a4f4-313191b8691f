package com.puree.hospital.app.domain.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * 医生关联科室表
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Data
public class BusDoctorDepartmentQuery extends Entity {
    /** 科室id */
    private Long departmentId;

    /**
     * 部门名称
     * */
    private String departmentName;

    private Collection<Long> doctorIds;

    private Collection<Long> hospitalIds;
}
