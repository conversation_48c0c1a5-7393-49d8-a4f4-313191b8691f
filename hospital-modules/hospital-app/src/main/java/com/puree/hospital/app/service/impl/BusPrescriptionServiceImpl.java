package com.puree.hospital.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.layout.font.FontProvider;
import com.puree.hospital.app.api.model.ApprovePrescriptionDTO;
import com.puree.hospital.app.api.model.BusTcmSyndromeIdDTO;
import com.puree.hospital.app.api.model.event.prescription.BasePrescriptionEvent;
import com.puree.hospital.app.api.model.event.prescription.PrescriptionApprovePassedEvent;
import com.puree.hospital.app.constant.ConsultationOrderStatus;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusCommunicationMessage;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDisease;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusDrugs;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusEnterpriseDrugs;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusHospitalOfficina;
import com.puree.hospital.app.domain.BusHospitalPa;
import com.puree.hospital.app.domain.BusHospitalPreorderDoctor;
import com.puree.hospital.app.domain.BusHospitalTcmEnterprise;
import com.puree.hospital.app.domain.BusOfficinaPharmacist;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusPatient;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPreorderDrugs;
import com.puree.hospital.app.domain.BusPreorderPatient;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDoctorAdviceTemplate;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusPrescriptionDualChannel;
import com.puree.hospital.app.domain.BusPrescriptionFailTemplate;
import com.puree.hospital.app.domain.BusShopCart;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.HospitalBarcodeConfig;
import com.puree.hospital.app.domain.dto.BusDispensingDTO;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.domain.dto.BusPreorderPatientDto;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.PrescriptionGenerateResultDTO;
import com.puree.hospital.app.domain.dto.PrescriptionPharmacistSignDTO;
import com.puree.hospital.app.domain.vo.BusConsultationOrderVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusHospitalVo;
import com.puree.hospital.app.domain.vo.BusPrescriptionVo;
import com.puree.hospital.app.domain.vo.BusSignatureVO;
import com.puree.hospital.app.domain.vo.HospitalVo;
import com.puree.hospital.app.domain.vo.MMDiagnosisVo;
import com.puree.hospital.app.domain.vo.PatientTemplateMsgVo;
import com.puree.hospital.app.domain.vo.PrescriptionStatusTypeVO;
import com.puree.hospital.app.domain.vo.TCMDiagnosisVo;
import com.puree.hospital.app.domain.vo.TCMSyndromeVo;
import com.puree.hospital.app.enums.DispensingQueryTypeEnum;
import com.puree.hospital.app.helper.HospitalWxNameHelper;
import com.puree.hospital.app.helper.SysDictDataHelper;
import com.puree.hospital.app.infrastructure.getui.enums.PageEnum;
import com.puree.hospital.app.infrastructure.getui.model.Notification;
import com.puree.hospital.app.infrastructure.getui.model.Transmission;
import com.puree.hospital.app.mapper.BusCommunicationMessageMapper;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusDiseaseMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDrugsMapper;
import com.puree.hospital.app.mapper.BusDrugsOrderMapper;
import com.puree.hospital.app.mapper.BusEnterpriseDrugsMapper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusHospitalTcmEnterpriseMapper;
import com.puree.hospital.app.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.app.mapper.BusOrderMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPatientMapper;
import com.puree.hospital.app.mapper.BusPreorderDrugsMapper;
import com.puree.hospital.app.mapper.BusPreorderPatientMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDoctorAdviceTemplateMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDualChannelMapper;
import com.puree.hospital.app.mapper.BusPrescriptionFailTemplateMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusSignatureMapper;
import com.puree.hospital.app.prescription.approve.IPrescriptionApproveHandler;
import com.puree.hospital.app.prescription.config.PrescriptionAutomationConfig;
import com.puree.hospital.app.prescription.generator.IPrescriptionGenerator;
import com.puree.hospital.app.prescription.helper.BusPrescriptionDrugStockHelper;
import com.puree.hospital.app.prescription.helper.DualChannelDigitalRxParamHelper;
import com.puree.hospital.app.prescription.helper.DualChannelRxRpcHelper;
import com.puree.hospital.app.prescription.helper.ExaminationFeeHelper;
import com.puree.hospital.app.prescription.helper.PrescriptionHelper;
import com.puree.hospital.app.queue.producer.SmsProducer;
import com.puree.hospital.app.queue.producer.event.prescription.PrescriptionApprovePassedEventProducer;
import com.puree.hospital.app.service.IAsyncJoinFollowUpService;
import com.puree.hospital.app.service.IBusAppMessagePushService;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusConsultationSettingsService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusEnterpriseDrugsService;
import com.puree.hospital.app.service.IBusHospitalOfficinaService;
import com.puree.hospital.app.service.IBusHospitalPreorderDoctorService;
import com.puree.hospital.app.service.IBusOfficinaPharmacistService;
import com.puree.hospital.app.service.IBusPreorderPatientService;
import com.puree.hospital.app.service.IBusPrescriptionDrugsService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.app.service.IBusShopCartService;
import com.puree.hospital.business.api.RemoteHospitalSmsConfigService;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.business.api.model.enums.DispensingStatusEnum;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.core.domain.SmsDTO;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.AuditStatus;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DirectoryTypeEnum;
import com.puree.hospital.common.core.enums.DoctorPracticeEnum;
import com.puree.hospital.common.core.enums.DoctorRoleEnum;
import com.puree.hospital.common.core.enums.EnterpriseEnum;
import com.puree.hospital.common.core.enums.FeeSettleTypeEnum;
import com.puree.hospital.common.core.enums.ImGroupType;
import com.puree.hospital.common.core.enums.PrescriptionStatusEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.ShopCartTypeEnum;
import com.puree.hospital.common.core.enums.SignatureRole;
import com.puree.hospital.common.core.enums.StockEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeCalculationUtil;
import com.puree.hospital.common.core.utils.BarcodeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.DoubleUtils;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.oss.OSSSaveDirectory;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.followup.api.model.BusTcmSyndromeDTO;
import com.puree.hospital.followup.api.model.PatientFollowUpJoinRuleDTO;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.MsgBody;
import com.puree.hospital.im.api.model.MsgContent;
import com.puree.hospital.im.api.model.SendMessageRequest;
import com.puree.hospital.insurance.api.enums.DigitalRxStatusEnum;
import com.puree.hospital.insurance.api.model.BaseDigitalRxStatusDTO;
import com.puree.hospital.insurance.api.model.DigitalRxRevokeDTO;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import com.puree.hospital.setting.api.RemotePlatformSettingApi;
import com.puree.hospital.supplier.api.RemoteDXService;
import com.puree.hospital.supplier.api.model.dx.Drug;
import com.puree.hospital.supplier.api.model.dx.Prescription;
import com.puree.hospital.supplier.api.model.dx.PrescriptionDrugInfo;
import com.puree.hospital.system.api.model.SysDictData;
import com.puree.hospital.system.api.model.constant.SysDictTypeConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPrescriptionServiceImpl extends ServiceImpl<BusPrescriptionMapper, BusPrescription> implements IBusPrescriptionService {

    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final BusSignatureMapper busSignatureMapper;
    private final IBusHospitalOfficinaService busHospitalOfficinaService;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final BusHospitalTcmEnterpriseMapper busHospitalTcmEnterpriseMapper;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final BusEnterpriseDrugsMapper busEnterpriseDrugsMapper;
    private final RemoteDXService remoteDXService;
    private final BusDrugsOrderMapper busDrugsOrderMapper;
    private final BusPreorderPatientMapper busPreorderPatientMapper;
    private final BusPreorderDrugsMapper busPreorderDrugsMapper;
    private final BusPrescriptionDoctorAdviceTemplateMapper busPrescriptionDoctorAdviceTemplateMapper;
    private final BusPrescriptionFailTemplateMapper busPrescriptionFailTemplateMapper;
    private final IBusEnterpriseDrugsService busEnterpriseDrugsService;
    private final BusDiseaseMapper busDiseaseMapper;
    private final BusHospitalPaMapper paMapper;
    private final IBusHospitalPreorderDoctorService iBusHospitalPreorderDoctorService;
    private final BusHospitalMapper busHospitalMapper;
    private final OSSUtil ossUtil;
    private final BusOrderMapper busOrderMapper;
    private final BusDrugsMapper busDrugsMapper;
    private final IBusDoctorPatientGroupService doctorPatientGroupService;
    private final RemoteImService remoteImService;
    private final IAsyncJoinFollowUpService asyncJoinFollowUpService;
    private final BusDoctorMapper busDoctorMapper;
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;
    @Lazy
    @Resource
    private IBusShopCartService busShopCartService;

    @Autowired @Lazy
    private IBusPrescriptionService busPrescriptionService;

    @Autowired @Lazy
    private IBusConsultationOrderService busConsultationOrderService;

    @Autowired @Lazy
    private IBusAppMessagePushService busAppMessagePushService;
    @Autowired @Lazy
    private PureeRabbitProducer pureeRabbitProducer;
    @Autowired @Lazy
    private IBusCommunicationMessageService busCommunicationMessageService;
    @Autowired @Lazy
    private IBusPrescriptionDrugsService busPrescriptionDrugsService;
    @Autowired @Lazy
    private IBusDoctorPatientGroupService busDoctorPatientGroupService;
    @Autowired @Lazy
    private BusConsultationOrderMapper busConsultationOrderMapper;
    @Autowired
    private BusCommunicationMessageMapper busCommunicationMessageMapper;
    @Autowired
    private RemoteHospitalSmsConfigService remoteHospitalSmsConfigService ;
    @Autowired
    private BusPatientMapper patientMapper ;
    @Autowired @Lazy
    private SmsProducer smsProducer ;

    @Resource
    private ExaminationFeeHelper examinationFeeHelper;

    @Resource
    private Map<String, IPrescriptionGenerator> prescriptionCreatorMap;

    @Resource
    private Map<String, IPrescriptionApproveHandler> prescriptionApproveHandlerMap;

    @Resource
    private BusPrescriptionDrugStockHelper busPrescriptionDrugStockHelper;

    @Resource
    private DualChannelDigitalRxParamHelper dualChannelDigitalRxParamHelper;

    @Resource
    private DualChannelRxRpcHelper dualChannelRxRpcHelper;

    @Resource
    private BusPrescriptionDualChannelMapper busPrescriptionDualChannelMapper;

    @Resource
    private SysDictDataHelper sysDictDataHelper;

    @Lazy
    @Resource
    private PrescriptionApprovePassedEventProducer prescriptionApprovePassedEventProducer;

    @Lazy
    @Resource
    private IBusPreorderPatientService busPreorderPatientService;

    @Resource
    private IBusOfficinaPharmacistService busOfficinaPharmacistService;

    @Lazy
    @Resource
    private IBusDoctorService busDoctorService;

    @Lazy
    @Resource
    private PrescriptionHelper prescriptionHelper;

    @Lazy
    @Resource
    private HospitalWxNameHelper hospitalWxNameHelper;

    @Resource
    private RemoteHospitalSettingApi remoteHospitalSettingApi;

    @Resource
    private RemotePlatformSettingApi remotePlatformSettingApi;

    public static final String BARCODE_PLATFORM_TEMPLATE_KEY = "hospital.barcodeStyle";
    public static final String BARCODE_HOSPITAL_TEMPLATE_KEY = "setting.barCode";
    public static final String BARCODE_URL = "barcodeUrl";
    public static final String TITLE = "title";

    @Override
    public Long selectFailQuantity(BusPrescription busPrescription, Integer isThisCourt) {
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getHospitalId, busPrescription.getHospitalId())
                .eq(BusPrescription::getStatus, busPrescription.getStatus());
        // 判断是否为 特聘专家
        if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(isThisCourt)) {
            queryWrapper.eq(Objects.nonNull(busPrescription.getDoctorId()), BusPrescription::getExpertId, busPrescription.getDoctorId());
        }else {
            queryWrapper.eq(Objects.nonNull(busPrescription.getDoctorId()), BusPrescription::getDoctorId, busPrescription.getDoctorId());
        }
        return busPrescriptionMapper.selectCount(queryWrapper);
    }

    @Override
    public List<BusPrescriptionVo> selectNotApprovedList(Long hospitalId, String reviewContent) {
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setStatus(String.valueOf(AuditStatus.NOTAPPROVED.getCode()));
        busPrescription.setHospitalId(hospitalId);
        String[] typeArray = reviewContent.split(",");
        if (typeArray.length == 1) {
            //审方药师审方内容code定义跟处方类型code定义有区别
            if ("0".equals(typeArray[0])) {// 审核西药
                // 西药处方
                busPrescription.setPrescriptionType(PrescriptionTypeEnum.MM.getCode());
            } else if ("1".equals(typeArray[0])) { // 审核中药
                // 中药处方，中药协定方
                busPrescription.setPrescriptionType(PrescriptionTypeEnum.TCM.getCode());
            }
        }
        return busPrescriptionMapper.selectNotApprovedList(busPrescription);
    }

    @Override
    public List<BusPrescriptionVo> selectApprovedList(BusPrescription busPrescription) {
        return busPrescriptionMapper.selectApprovedList(busPrescription);
    }

    @Override
    public List<BusPrescriptionVo> selectApprovedPatientList(BusPrescription busPrescription) {
        return busPrescriptionMapper.selectApprovedPatientList(busPrescription);
    }

    @Override
    public List<BusPrescriptionVo> selectList(BusPrescription busPrescription) {
        // 如果 ExpertId 不为空，则为特聘专家 否则为普通医生
        if (busPrescription.getExpertId() != null){
           busPrescription.setDoctorId(null);
        }
        return busPrescriptionMapper.selectPrescriptionList(busPrescription);
    }

    @Override
    public BusPrescriptionVo select(BusPrescription busPrescription) {
        log.info("处方详情入参={}", JSON.toJSONString(busPrescription));
        BusPrescription bp;
        // 查询处方信息
        if (YesNoEnum.YES.getCode().equals(busPrescription.getPass())) {
            bp = busPrescription;
        } else {
            LambdaQueryWrapper<BusPrescription> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusPrescription::getHospitalId, busPrescription.getHospitalId());
            if (Objects.nonNull(busPrescription.getId()) || StringUtils.isNotBlank(busPrescription.getPrescriptionNumber())) {
                lambdaQuery.eq(Objects.nonNull(busPrescription.getId()), BusPrescription::getId, busPrescription.getId());
                lambdaQuery.eq(StringUtils.isNotBlank(busPrescription.getPrescriptionNumber()), BusPrescription::getPrescriptionNumber, busPrescription.getPrescriptionNumber());
                bp = busPrescriptionMapper.selectOne(lambdaQuery);
            } else {
                bp = null;
            }
        }
        if (Objects.isNull(bp)) {
            return null;
        }
        String barcodeUrl = BarcodeUtil.generateBarcode(bp.getPrescriptionNumber());
        bp.setBarcodeUrl(barcodeUrl);
        BusPrescriptionVo vo = BeanUtil.copyProperties(bp, BusPrescriptionVo.class);
        // 查询医院电话
        BusHospitalVo hospitalVo = busPrescriptionMapper.selectHospitalInfo(busPrescription.getHospitalId());
        vo.setHospitalPhone(DESUtil.decrypt(hospitalVo.getHospitalPhone()));
        vo.setHospitalSeal(hospitalVo.getHospitalSeal());
        vo.setProvince(hospitalVo.getProvince());
        vo.setCity(hospitalVo.getCity());
        vo.setArea(hospitalVo.getArea());
        /*设置中药方剂类型*/
        if (Objects.nonNull(vo.getPaId())) {
            Long paId = Long.valueOf(vo.getPaId());
            BusHospitalPa busHospitalPa = paMapper.selectById(paId);
            vo.setZyfjType(busHospitalPa.getType());
            vo.setZyfjAmount(busHospitalPa.getAmount());
        }
        List<BusPrescriptionDrugs> busPrescriptionDrugsList = busPrescriptionDrugsMapper.selectDrugsList(bp);
        if (CollectionUtil.isNotEmpty(busPrescriptionDrugsList)) {
            Map<Long, SysDictData> packageUnitMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_PACKAGING_UNIT);
            for (BusPrescriptionDrugs pd : busPrescriptionDrugsList) {
                if (DirectoryTypeEnum.isInner(vo.getDrugDirectoryType())) {
                    int stock = 0;
                    // 查询药房库存
                    BusHospitalOfficina hospitalOfficina = busHospitalOfficinaService.selectStock(pd.getHospitalId(), pd.getDrugsId());
                    if (Objects.nonNull(hospitalOfficina)) {
                        if (Objects.nonNull(hospitalOfficina.getStock())) {
                            stock = hospitalOfficina.getStock();
                        }
                        if (Objects.nonNull(pd.getEnterpriseId())) {
                            // 查询配送企业药品库存
                            BusEnterpriseDrugs busEnterpriseDrugs = busEnterpriseDrugsService.selectStock(pd.getHospitalId(), pd.getEnterpriseId(), pd.getDrugsId());
                            if (Objects.nonNull(busEnterpriseDrugs) &&
                                    (EnterpriseEnum.GYJT.getInfo().equals(busEnterpriseDrugs.getIdentifying()) &&
                                            Objects.nonNull(busEnterpriseDrugs.getStock()))) {
                                stock += busEnterpriseDrugs.getStock();
                            }
                        }
                        log.info("处方详情库存数={}", stock);
                    }
                    pd.setStock(stock);
                } else {
                    pd.setStock(9999);
                }
                pd.setReferenceSellingPrice(pd.getSellingPrice() + "");
                if (Objects.nonNull(pd.getDrugsPackagingUnit())) {
                    SysDictData sysDictData = packageUnitMap.get(pd.getDrugsPackagingUnit());
                    if (Objects.nonNull(sysDictData)) {
                        pd.setDrugsPackagingUnitName(sysDictData.getDictLabel());
                    }
                }
            }
        }
        vo.setList(busPrescriptionDrugsList);
        Long doctorId = vo.getDoctorId();
        //设置医生医保编码
        BusDoctor doctor = busDoctorMapper.selectById(doctorId);
        if (Objects.nonNull(doctor)){
            vo.setDoctorMiCode(DESUtil.decrypt(doctor.getNationalDoctorCode()));
        }
        //开方医生签名
        List<BusSignature> busSignatureList = new ArrayList<>();
        LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusSignature::getObjectType, SignatureRole.DOCTOR.getValue());
        lambdaQuery.eq(BusSignature::getObjectId, doctorId);
        lambdaQuery.last(" limit 1");
        BusSignature doctorSignature = busSignatureMapper.selectOne(lambdaQuery);
        if (Objects.nonNull(doctorSignature)) {
            busSignatureList.add(doctorSignature);
        }
        //审方医生签名
        if ((vo.getReviewPharmacistId() != null) && (vo.getReviewPharmacistId() > 0)) {
            LambdaQueryWrapper<BusSignature> lambdaQuery1 = Wrappers.lambdaQuery();
            lambdaQuery1.in(BusSignature::getObjectType, Lists.newArrayList(SignatureRole.PHARMACIST.getValue()));
            lambdaQuery1.eq(BusSignature::getObjectId, vo.getReviewPharmacistId());
            lambdaQuery1.last(" limit 1");
            BusSignature pharmacistSignature = busSignatureMapper.selectOne(lambdaQuery1);
            if (Objects.nonNull(pharmacistSignature)) {
                pharmacistSignature.setPharmacistType(Integer.valueOf(SignatureRole.PHARMACIST.getValue()));
                busSignatureList.add(pharmacistSignature);
            }
        }
        //发货药师签名
        if ((vo.getDeliveryPharmacistId() != null) && (vo.getDeliveryPharmacistId() > 0)) {
            LambdaQueryWrapper<BusSignature> lambdaQuery1 = Wrappers.lambdaQuery();
            lambdaQuery1.inSql(BusSignature::getObjectType, SignatureRole.DELIVERY_PHARMACIST.getValue());
            lambdaQuery1.eq(BusSignature::getObjectId, vo.getDeliveryPharmacistId());
            lambdaQuery1.last(" limit 1");
            BusSignature pharmacistSignature = busSignatureMapper.selectOne(lambdaQuery1);
            if (Objects.nonNull(pharmacistSignature)) {
                pharmacistSignature.setPharmacistType(Integer.valueOf(SignatureRole.DELIVERY_PHARMACIST.getValue()));
                busSignatureList.add(pharmacistSignature);
            }
        }
        //调剂药师签名
        if ((vo.getDispensingPharmacistId() != null) && (vo.getDispensingPharmacistId() > 0)) {
            LambdaQueryWrapper<BusSignature> lambdaQuery1 = Wrappers.lambdaQuery();
            lambdaQuery1.in(BusSignature::getObjectType, SignatureRole.CHEMIST.getValue());
            lambdaQuery1.eq(BusSignature::getObjectId, vo.getDispensingPharmacistId());
            lambdaQuery1.last(" limit 1");
            BusSignature pharmacistSignature = busSignatureMapper.selectOne(lambdaQuery1);
            if (Objects.nonNull(pharmacistSignature)) {
                pharmacistSignature.setPharmacistType(Integer.valueOf(SignatureRole.CHEMIST.getValue()));
                busSignatureList.add(pharmacistSignature);
            }
        }
        vo.setSignatureList(busSignatureList.stream().map(s -> {
            BusSignatureVO busSignatureVO = new BusSignatureVO();
            BeanUtil.copyProperties(s, busSignatureVO);
            return busSignatureVO;
        }).collect(Collectors.toList()));
        BusDrugsOrder drugsOrder = busDrugsOrderMapper.selectOne(new LambdaQueryWrapper<BusDrugsOrder>()
                .eq(BusDrugsOrder::getPrescriptionId, vo.getId())
                .orderByDesc(BusDrugsOrder::getCreateTime).last("limit 1"));
        if (!Objects.isNull(drugsOrder)) {
            BusOrder order = busOrderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                    .eq(BusOrder::getSubOrderType, CodeEnum.NO.getCode())
                    .eq(BusOrder::getSubOrderId, drugsOrder.getId()));
            vo.setOrderNo(order.getOrderNo());
        }
        vo.setUsages(null==vo.getUsages()?"":vo.getUsages());
        BusConsultationSettings query = new BusConsultationSettings();
        query.setHospitalId(vo.getHospitalId());
        BusConsultationSettings settings = busConsultationSettingsService.selectOne(query);
        vo.setEffectiveHours(getPrescriptionEffectiveHours(vo.getReviewTime(), vo.getValidTime(), settings));
        return vo;
    }

    @Override
    public List<MMDiagnosisVo> selectMMDiagnosisList(String key) {
        return busPrescriptionMapper.selectMMDiagnosisList(key);
    }

    @Override
    public List<MMDiagnosisVo> selectMMDiagnosisListByIds(List<Long> ids) {
        return busPrescriptionMapper.selectMMDiagnosisListByIds(ids);
    }

    @Override
    public List<TCMDiagnosisVo> selectTCMDiagnosisList(String key) {
        return busPrescriptionMapper.selectTCMDiagnosisList(key);
    }

    @Override
    public List<TCMDiagnosisVo> selectTCMDiagnosisListByIds(List<BusTcmSyndromeIdDTO> dtos) {
        List<TCMDiagnosisVo> result = new ArrayList<>();
        for (BusTcmSyndromeIdDTO dto : dtos) {
            TCMDiagnosisVo tcmDiagnosis = busPrescriptionMapper.selectTCMById(dto.getId());
            TCMSyndromeVo tcmSyndrome = busPrescriptionMapper.selectTCMSyndromeById(dto.getSyndromeId());
            if(tcmDiagnosis != null && tcmSyndrome != null){
                TCMDiagnosisVo vo = new TCMDiagnosisVo();
                result.add(vo);
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean pharmacistSignature(PrescriptionPharmacistSignDTO signDTO) {
        BusPrescription busPrescription;
        if (Objects.nonNull(signDTO.getPrescriptionId())) {
            busPrescription = busPrescriptionMapper.selectById(signDTO.getPrescriptionId());
        } else {
            LambdaQueryWrapper<BusPrescription> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BusPrescription::getPrescriptionNumber, signDTO.getPrescriptionNumber());
            wrapper.eq(BusPrescription::getHospitalId, signDTO.getHospitalId());
            wrapper.last(" limit 1");
            busPrescription = busPrescriptionMapper.selectOne(wrapper);
        }
        if (Objects.isNull(busPrescription)) {
            throw new ServiceException("处方信息不存在");
        }
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistService.selectPharmacistByIdNumber(signDTO.getPharmacistIdCard());
        //场景：药师签名
        switch (signDTO.getSignType()) {
            case APPROVE:
                BusDoctorDto query = new BusDoctorDto();
                query.setIdCardNo(signDTO.getPharmacistIdCard());
                query.setHospitalId(signDTO.getHospitalId());
                query.setRole(DoctorRoleEnum.PHARMACIST.getCode());
                BusDoctor doctor = busDoctorService.queryByDTO(query);
                if (Objects.isNull(doctor)) {
                    throw new ServiceException(String.format("身份证号：%s关联的药师信息不存在", signDTO.getPharmacistIdCard()));
                }
                if (!PrescriptionStatus.NOT_EXAMINE.equals(busPrescription.getStatus())) {
                    throw new ServiceException("当前处方不是待审核状态,不能做审核签名操作");
                }
                ApprovePrescriptionDTO approveDTO = new ApprovePrescriptionDTO();
                approveDTO.setId(busPrescription.getId());
                approveDTO.setHospitalId(busPrescription.getHospitalId());
                approveDTO.setReviewPharmacistId(doctor.getId());
                approveDTO.setReviewPharmacistName(doctor.getFullName());
                approveDTO.setReviewTime(new Date());
                approveDTO.setStatus(PrescriptionStatus.PASS);
                approveDTO.setPrescriptionType(busPrescription.getPrescriptionType());
                this.approve(approveDTO, busPrescription);
                break;
            case DELIVERY:
                if (Objects.isNull(pharmacist)) {
                    throw new ServiceException(String.format("身份证号：%s关联的药师信息不存在", signDTO.getPharmacistIdCard()));
                }
                if(!PrescriptionStatus.PASS.equals(busPrescription.getStatus())) {
                    throw new ServiceException("当前处方不是通过审核状态,不能做发药签名操作");
                }
                busPrescription.setDeliveryPharmacistId(pharmacist.getId());
                busPrescription.setDeliveryPharmacistName(pharmacist.getPharmacistName());
                busPrescription.setStatus(PrescriptionStatus.USED);
                busPrescription.setUpdateBy(pharmacist.getPharmacistName());
                busPrescriptionMapper.updateById(busPrescription);
                //用药预订单还需要删除购物信息
                BusShopCart shopCart = new BusShopCart();
                shopCart.setHospitalId(busPrescription.getHospitalId());
                shopCart.setPatientId(busPrescription.getPatientId());
                shopCart.setBusinessId(busPrescription.getId());
                shopCart.setType(ShopCartTypeEnum.PRESCRIPTION.getCode());
                busShopCartService.deleteBusinessById(shopCart);
                //生成PDF
                generatePdf(busPrescription);
                break;
            case ADJUSTMENT:
                if (Objects.isNull(pharmacist)) {
                    throw new ServiceException(String.format("身份证号：%s关联的药师信息不存在", signDTO.getPharmacistIdCard()));
                }
                if(!PrescriptionStatus.PASS.equals(busPrescription.getStatus())) {
                    throw new ServiceException("当前处方不是通过审核状态,不能做调剂签名操作");
                }
                // 处方已复核不再允许签名
                if (DispensingStatusEnum.DISPENSING_PENDING.getCode().equals(busPrescription.getDispensingStatus())) {
                    busPrescription.setDispensingPharmacistId(pharmacist.getId());
                    busPrescription.setDispensingPharmacistName(pharmacist.getPharmacistName());
                    busPrescription.setUpdateBy(pharmacist.getPharmacistName());
                    busPrescriptionMapper.updateById(busPrescription);
                }
                generatePdf(busPrescription);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public PatientTemplateMsgVo selectPatient(Long prescriptionId, Long hospitalId) {
        return busPrescriptionMapper.selectPatient(prescriptionId, hospitalId);
    }

    @Override
    public List<PatientTemplateMsgVo> selectPrescriptionDrugs(Long prescriptionId) {
        return busPrescriptionMapper.selectPrescriptionDrugs(prescriptionId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(BusPrescriptionDto dto) {
        log.info("处方修改入参={}", dto);
        BusPrescription busPrescription = OrikaUtils.convert(dto, BusPrescription.class);
        int ret = busPrescriptionMapper.updateById(busPrescription);
        if (CollectionUtils.isNotEmpty(dto.getList())) {
            //先删除处方对应的药品，再批量添加
            QueryWrapper queryWrapper = new QueryWrapper<>()
                    .eq("prescription_id", dto.getId())
                    .eq("hospital_id", dto.getHospitalId())
                    .eq("doctor_id", dto.getDoctorId());
            busPrescriptionDrugsMapper.delete(queryWrapper);
            List<BusPrescriptionDrugs> busPrescriptionDrugsList = dto.getList();
            busPrescriptionDrugsList.stream().map(i -> {
                i.setHospitalId(dto.getHospitalId());
                i.setDoctorId(dto.getDoctorId());
                i.setPrescriptionId(dto.getId());
                return i;
            }).collect(Collectors.toList());
            busPrescriptionDrugsMapper.batchInsert(busPrescriptionDrugsList);
        }
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int discard(BusPrescription busPrescription) {
        BusPrescription update = new BusPrescription();
        update.setId(busPrescription.getId());
        update.setHospitalId(busPrescription.getHospitalId());
        update.setStatus(String.valueOf(AuditStatus.DISCARD.getCode()));
        update.setUpdateTime(DateUtils.getNowDate());
        update.setUpdateBy(busPrescription.getUpdateBy());
        update.setDispensingStatus(busPrescription.getDispensingStatus());
        update.setDispensingTime(busPrescription.getDispensingTime());
        update.setDispensingPharmacistName(busPrescription.getDispensingPharmacistName());
        update.setDispensingPharmacistId(busPrescription.getDispensingPharmacistId());
        int discard = busPrescriptionMapper.updateById(update);
        if (discard < 1) {
            return 0;
        }
        //双通道处方撤销逻辑
        dualChannelRevoke(busPrescription);
        //院内目录
        if (DirectoryTypeEnum.isInner(busPrescription.getDrugDirectoryType())) {
            //删除购物车
            busShopCartService.deletePrescriptionOrOrder(busPrescription.getId(), true);
            //处方的前一个状态为未审核/审核通过，需要库存释放
            if (PrescriptionStatus.PASS.equals(busPrescription.getStatus())
                    || PrescriptionStatus.NOT_EXAMINE.equals(busPrescription.getStatus())) {
                this.increaseDrugsStock(busPrescription.getHospitalId(), busPrescription.getId());
            }
        }
        return discard;
    }

    @Override
    public int invalidOldRx(BusPrescription busPrescription) {
        //双通道处方撤销逻辑
        dualChannelRevoke(busPrescription);
        BusPrescription rx = new BusPrescription();
        rx.setId(busPrescription.getId());
        rx.setUpdateTime(new Date());
        rx.setStatus(AuditStatus.DISCARD.getCode() + "");
        return busPrescriptionMapper.updateById(rx);
    }

    /**
     * 药师审核处方
     * @param dto 审核信息
     * @param prescription 处方信息
     * @return
     */
    @Override
    public AjaxResult approve(ApprovePrescriptionDTO dto, BusPrescription prescription) {
        Date now = new Date();
        dto.setReviewTime(now);
        dto.setUpdateTime(now);
        dto.setPatientId(prescription.getPatientId());
        int ret = busPrescriptionService.approve(dto);
        if (ret <= 0) {
            return AjaxResult.error("审核处方失败！");
        }
        //审批完成直接上报监管
        RegulatoryCollectEvent event = new RegulatoryCollectEvent(dto.getHospitalId(), RegulatoryEventTypeEnum.PRESCRIPTION_APPROVE, dto.getId() + "");
        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);

        //推送信息给医生
        Notification notice = new Notification();
        notice.setTitle("处方审核消息");
        notice.setChannelId(PageEnum.sf.getCode().toString());
        Transmission transmission = new Transmission();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("prescriptionId", dto.getId());
        jsonObject.put("hospitalId", dto.getHospitalId());
        jsonObject.put("type", PageEnum.sf.getCode());
        transmission.setPayload(jsonObject.toJSONString());
        notice.setTransmission(transmission);
        String body = dto.getStatus().equals(AuditStatus.ADOPT.getCode().toString()) ?
                "您提交的" + prescription.getPrescriptionNumber() + "编号处方审核通过" :
                "您提交的" + prescription.getPrescriptionNumber() + "编号处方审核不通过";
        notice.setBody(body);
        notice.setChannelId(PageEnum.sf.getCode().toString());
        Long doctorId = prescription.getDoctorId();
        //判断是否是特聘专家
        if(prescription.getExpertId() != null){
            doctorId = prescription.getExpertId();
        }
        busAppMessagePushService.pushListByCid(doctorId, AppRoleEnum.DOCTOR.getCode(), notice);
        //审核通过： 1、触发加入随访  2、需要推送处方给患者
        if (dto.getStatus().equals(String.valueOf(AuditStatus.ADOPT.getCode()))) {

            busPrescriptionService.joinFollowUp(prescription);
            //双通道外层不再生成pdf
            if (!DirectoryTypeEnum.isDualChannel(prescription.getDrugDirectoryType())) {
                // 将处方保存为pdf文件
                busPrescriptionService.generatePdf(prescription);
            }
            // 修改订单状态
            if (Objects.nonNull(prescription.getConsultationOrderId())) {
                // 查看问诊订单状态
                BusConsultationOrder busConsultationOrder =
                        busConsultationOrderService.selectOrderStatus(dto.getHospitalId(),
                                prescription.getConsultationOrderId());
                if (Objects.nonNull(busConsultationOrder)) {
                    if (ConsultationOrderStatus.VISITING.equals(busConsultationOrder.getStatus()) ||
                            ConsultationOrderStatus.VISITING.equals(busConsultationOrder.getVideoStatus())) {
                        // 修改问诊订单状态为问诊中，已开处方
                        BusConsultationOrder consultationOrder = new BusConsultationOrder();
                        consultationOrder.setId(dto.getConsultationOrderId());
                        consultationOrder.setUpdateTime(now);
                        // 图文问诊
                        if (CodeEnum.NO.getCode().equals(busConsultationOrder.getOrderType())) {
                            consultationOrder.setStatus(ConsultationOrderStatus.PRESCRIBED);
                        } else { // 视频问诊
                            consultationOrder.setVideoStatus(ConsultationOrderStatus.PRESCRIBED);
                        }
                        int result = busConsultationOrderMapper.updateById(consultationOrder);
                        log.info("处方审核通过，问诊订单状态修改是否成功" + result);
                    }
                }
            }


            busPrescriptionService.sendPrescriptionApproveSms(prescription);


            // 线上开处方
            if (CodeEnum.YES.getCode().equals(prescription.getType()) && Objects.isNull(prescription.getPreorderId())) {
                // 校验就诊人是否上传过往病例
                BusPatientFamily patientFamily = new BusPatientFamily();
                patientFamily.setId(prescription.getFamilyId());
                patientFamily.setPatientId(prescription.getPatientId());
            }

            //发送处方审核通过事件
            PrescriptionApprovePassedEvent approvePassedEvent = new PrescriptionApprovePassedEvent();
            approvePassedEvent.setEventType(BasePrescriptionEvent.EventType.APPROVE_PASSED);
            approvePassedEvent.setPrescriptionId(dto.getId());
            prescriptionApprovePassedEventProducer.send(approvePassedEvent);

            //发送聊天信息
            boolean ret2;
            if (Objects.isNull(prescription.getServicePackId())) {
                BusDoctorPatientGroup doctorPatientGroup = new BusDoctorPatientGroup();
                Long groupId = prescription.getGroupId();
                if(ObjectUtil.isNotNull(groupId)){
                    doctorPatientGroup.setId(groupId);
                } else {
                    doctorPatientGroup.setDoctorId(doctorId);
                    doctorPatientGroup.setType(ImGroupType.INQUIRIES.getCode());
                    doctorPatientGroup.setDepartmentId(prescription.getDepartmentId());
                    doctorPatientGroup.setFamilyId(prescription.getFamilyId());
                    doctorPatientGroup.setPatientId(prescription.getPatientId());
                    doctorPatientGroup.setHospitalId(prescription.getHospitalId());
                }
                BusDoctorPatientGroup checkGroup = busDoctorPatientGroupService.checkGroup(doctorPatientGroup);
                if (Objects.nonNull(checkGroup)) {
                    ret2 = sendChatMsgToPatient(dto.getId(), dto.getHospitalId(), dto.getPrescriptionType(),
                            prescription.getServicePackId(),prescription.getGroupId());
                    log.info("im推送状态[" + ret2 + "]");
                    if (!ret2) {
                        return AjaxResult.error("微信推送消息异常，请通知患者在处方列表中查看已开具的处方！");
                    }
                }
            }
            //服务包处方
            if (Objects.nonNull(prescription.getServicePackId())) {
                ret2 = sendChatMsgToPatient(dto.getId(), dto.getHospitalId(), dto.getPrescriptionType(),
                        prescription.getServicePackId(),prescription.getGroupId());
                log.info("im推送状态[" + ret2 + "]");
                if (!ret2) {
                    return AjaxResult.error("微信推送消息异常，请通知患者在处方列表中查看已开具的处方！");
                }
            }
        }
        return AjaxResult.success(ret);
    }

    /**
     * 发送聊天信息给患者
     */
    private boolean sendChatMsgToPatient(Long id, Long hospitalId, String prescriptionType, Long servicePackId,Long groupId) {
        try {
            log.info("sendChatMsgToPatient():id={},hospitalId={},servicePackId={}", id, hospitalId, servicePackId);
            //查询患者openid
            PatientTemplateMsgVo templateMsgVo = busPrescriptionService.selectPatient(id, hospitalId);
            log.info("templateMsgVo请求参数={}", templateMsgVo);
            if (Objects.isNull(templateMsgVo)) {
                log.error("sendChatMsgToPatient()：患者信息不存在");
                return false;
            }
            BusDoctorPatientGroup patientGroup = getBusDoctorPatientGroup(hospitalId, servicePackId, templateMsgVo,groupId);
            if (Objects.isNull(patientGroup)) {
                log.error("查询群组为空或患者不在该群组中");
                return true;
            }
            //查询处方药品
            BusPrescriptionDrugs busPrescriptionDrugs = new BusPrescriptionDrugs();
            busPrescriptionDrugs.setPrescriptionId(id);
            busPrescriptionDrugs.setHospitalId(hospitalId);
            //处方药品
            List<BusPrescriptionDrugs> prescriptionDrugs =
                    busPrescriptionDrugsService.selectPrescriptionDrugsList(busPrescriptionDrugs);
            List<BusPrescriptionDrugs> returnList = new ArrayList<>();
            prescriptionDrugs.forEach(p -> {
                BusPrescriptionDrugs drugs = new BusPrescriptionDrugs();
                drugs.setDrugsName(p.getDrugsName());
                drugs.setWeight(p.getWeight());
                drugs.setStandardCommonName(p.getStandardCommonName());
                drugs.setDrugsDosageForm(p.getDrugsDosageForm());
                drugs.setDrugsSpecification(p.getDrugsSpecification());
                //药品数量
                drugs.setQuantity(p.getQuantity());
                //药品主图
                drugs.setMainImg(p.getMainImg());
                returnList.add(drugs);
            });
            log.info("prescriptionDrugs={}", JSON.toJSONString(prescriptionDrugs));
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            int i = (int) ((Math.random() * 9 + 1) * 100000);
            sendMessageRequest.setRandom(i + "");
            sendMessageRequest.setGroupId(patientGroup.getId() + "");
            //获取特聘专家信息
            Long doctorId = templateMsgVo.getDoctorId();
            String doctorName = templateMsgVo.getDoctorName();
            if (templateMsgVo.getExpertId() != null){
                BusDoctorVo busDoctorVo = busDoctorMapper.selectDoctorInfoById(templateMsgVo.getExpertId());
                doctorId = templateMsgVo.getExpertId();
                doctorName = busDoctorVo.getFullName();
            }
            sendMessageRequest.setFrom_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + doctorId);
            List<MsgBody> msgBodyList = new ArrayList<MsgBody>();
            MsgBody msgBody = new MsgBody();
            msgBody.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            MsgContent msgContent = new MsgContent();
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.PRESCRIPTION);
            data.put("prescriptionType", prescriptionType);

            data.put("doctorName", doctorName);
            data.put("prescription", templateMsgVo);
            data.put("prescriptionDrugs", returnList);
            msgContent.setData(data.toJSONString());
            msgBody.setMsgContent(msgContent);
            msgBodyList.add(msgBody);
            sendMessageRequest.setMsgBody(msgBodyList);
            R<Boolean> groupMsg = remoteImService.sendGroupMsg(sendMessageRequest);
            if (Constants.FAIL.equals(groupMsg.getCode())) {
                log.info("标记1----" + groupMsg.getCode());
                return false;
            }
            //添加消息记录
            JSONObject payload = new JSONObject();
            payload.put("data", data);
            BusCommunicationMessage communicationMessage = new BusCommunicationMessage();
            communicationMessage.setType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            communicationMessage.setPayload(payload.toJSONString());
            communicationMessage.setGroupId(patientGroup.getId() + "");
            communicationMessage.setConversationType("GROUP");
            communicationMessage.setTo(patientGroup.getId() + "");
            communicationMessage.setFrom(TencentyunImConstants.DOCTOR_IM_ACCOUNT + doctorId);
            communicationMessage.setNick(doctorName);
            communicationMessage.setFlow("out");
            communicationMessage.setTime(System.currentTimeMillis());
            communicationMessage.setStatus("success");
            communicationMessage.setPriority("Normal");
            communicationMessage.setCreateTime(DateUtils.getNowDate());
            busCommunicationMessageService.insert(communicationMessage);
            return true;
        } catch (Exception e) {
            log.error("发送im处方报错e={}", e.getMessage());
            return false;
        }
    }

    private BusDoctorPatientGroup getBusDoctorPatientGroup(Long hospitalId, Long servicePackId, PatientTemplateMsgVo templateMsgVo,Long groupId) {
        //查询群组信息
        BusDoctorPatientGroup group = new BusDoctorPatientGroup();
        if (ObjectUtil.isNotNull(groupId)) {
            group.setId(groupId);
        }else {
            group.setPatientId(templateMsgVo.getPatientId());
            if (Objects.nonNull(servicePackId)) {
                group.setType(CodeEnum.YES.getCode());
                group.setServiceId(servicePackId);
            } else {
                group.setType(CodeEnum.NO.getCode());
            }
            group.setDoctorId(templateMsgVo.getDoctorId());
            if (templateMsgVo.getExpertId() != null){
                group.setDoctorId(templateMsgVo.getExpertId());
            }
            group.setDepartmentId(templateMsgVo.getDepartmentId());
            group.setFamilyId(templateMsgVo.getFamilyId());
            group.setHospitalId(hospitalId);
        }

        //就诊人群组
        return busDoctorPatientGroupService.selectOne(group);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int approve(ApprovePrescriptionDTO dto) {
        log.info("审核处方开始，入参：{}", dto);
        BusPrescription prescription = busPrescriptionMapper.selectById(dto.getId());
        if (prescription == null) {
            throw new ServiceException(String.format("处方不存在，id：%s", dto.getId()));
        }
        if (!PrescriptionStatus.NOT_EXAMINE.equals(prescription.getStatus())) {
            throw new ServiceException("当前处方不是待审核状态");
        }
        PrescriptionTypeEnum prescriptionTypeEnum = PrescriptionTypeEnum.getByType(prescription.getPrescriptionType());
        if (Objects.isNull(prescriptionTypeEnum)) {
            throw new ServiceException("当前处方类型不正确");
        }
        DirectoryTypeEnum directoryTypeEnum = DirectoryTypeEnum.getByType(prescription.getDrugDirectoryType());;
        if (Objects.isNull(directoryTypeEnum)) {
            throw new ServiceException("当前处方药品目录类型不正确");
        }
        String approveBeanName = prescriptionTypeEnum.getConstName() + directoryTypeEnum.getConstName() + IPrescriptionApproveHandler.SUFFIX;
        IPrescriptionApproveHandler handler = prescriptionApproveHandlerMap.get(approveBeanName);
        if (handler == null) {
            log.error("未找到生成审核的bean：{}", approveBeanName);
            throw new ServiceException("处方类型与药品目录类型不匹配");
        }
        return handler.approve(dto, prescription);
    }

    /**
     * 查询处方及药品信息
     *
     * @param prescriptionId
     * @return
     */
    @Override
    public BusPrescription selectPrescriptionDrugList(Long prescriptionId) {
        return busPrescriptionMapper.selectPrescriptionDrugList(prescriptionId);
    }

    /**
     * 发送im群组自定义消息
     *
     * @param dto
     * @return
     */
    @Override
    public int sendMsgToIm(BusPrescriptionDto dto) {
        BusDoctorPatientGroup doctorPatientGroup;
        if (StringUtils.isNull(dto.getGroupId())) {
            BusDoctorPatientGroup group = new BusDoctorPatientGroup();
            group.setHospitalId(dto.getHospitalId());
            if (Objects.nonNull(dto.getExpertId())){
                group.setDoctorId(dto.getExpertId());
            }else {
                group.setDoctorId(dto.getDoctorId());
            }
            group.setDepartmentId(dto.getDepartmentId());
            group.setPatientId(dto.getPatientId());
            group.setFamilyId(dto.getFamilyId());
            group.setType(CodeEnum.NO.getCode());
            doctorPatientGroup = doctorPatientGroupService.selectOne(group);
        } else {
            doctorPatientGroup = doctorPatientGroupService.selectGroupInfo(dto.getGroupId());
        }
        if (StringUtils.isNull(doctorPatientGroup)) {
            throw new ServiceException("群组不存在！");
        }
        if (CodeEnum.YES.getCode().equals(doctorPatientGroup.getDelFlag())) {
            Long groupId = doctorPatientGroupService.createGroup(doctorPatientGroup, doctorPatientGroup.getDelFlag());
            if (StringUtils.isNull(groupId)) {
                throw new ServiceException("群组激活失败！");
            }
        }
        JSONObject data = new JSONObject();
        if (YesNoEnum.NO.getCode().equals(dto.getMsgId())) {
            data.put("type", CustomMsgConstants.IMPROVE_PERSONAL_INFORMATION);
        } else {
            data.put("type", CustomMsgConstants.IMPROVED_PERSONAL_INFORMATION);
        }
        SendMessageRequest request = new SendMessageRequest();
        request.setGroupId(doctorPatientGroup.getId() + "");
        request.setFrom_Account(TencentyunImConstants.ADMINISTRATOR);
        request.setContent(data.toJSONString());
        R<Boolean> r = remoteImService.sendCustomMsg(request);
        if (!Constants.SUCCESS.equals(r.getCode())) {
            throw new ServiceException(r.getMsg());
        }
        //添加消息记录
        JSONObject payload = new JSONObject();
        payload.put("data", data);
        //添加消息记录
        BusCommunicationMessage communicationMessage = new BusCommunicationMessage();
        communicationMessage.setType(TencentyunImConstants.TIM_CUSTOM_ELEM);
        communicationMessage.setPayload(payload.toJSONString());
        communicationMessage.setGroupId(doctorPatientGroup.getId() + "");
        communicationMessage.setConversationType("GROUP");
        communicationMessage.setTo(doctorPatientGroup.getId() + "");
        communicationMessage.setFrom(TencentyunImConstants.ADMINISTRATOR);
        communicationMessage.setNick(TencentyunImConstants.ADMINISTRATOR);
        communicationMessage.setFlow("out");
        communicationMessage.setTime(System.currentTimeMillis());
        communicationMessage.setStatus("success");
        communicationMessage.setPriority("Normal");
        communicationMessage.setCreateTime(DateUtils.getNowDate());
        return busCommunicationMessageMapper.insert(communicationMessage);
    }

    /**
     * 开方
     * @param dto dto
     * @return PrescriptionGenerateResultDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrescriptionGenerateResultDTO generate(BusPrescriptionDto dto) {
        log.info("处方创建请求参数,data={}", dto);
        // 1. 基本参数校验
        validateBasicParams(dto);

        // 2. 获取处方生成器（包含处方类型和药品目录类型的校验）
        IPrescriptionGenerator generator = getPrescriptionGenerator(dto);

        // 3. 生成处方
        PrescriptionGenerateResultDTO resultDTO = generator.generate(dto);

        // 4. 后置处理
        postProcessPrescription(dto, resultDTO);

        return resultDTO;
    }

    /**
     * 校验基本参数
     * @param dto dto
     */
    private void validateBasicParams(BusPrescriptionDto dto) {
        if (Objects.isNull(dto)) {
            throw new ServiceException("处方参数缺失");
        }

        // 验证订单状态
        validateConsultationOrder(dto.getConsultationOrderId());
    }

    /**
     * 验证问诊订单状态
     * @param consultationOrderId 问诊订单ID
     */
    private void validateConsultationOrder(Long consultationOrderId) {
        if (Objects.isNull(consultationOrderId)) {
            return; // 无订单ID，无需验证
        }

        BusConsultationOrderVo order = busConsultationOrderMapper.selectOrderById(consultationOrderId);
        if (Objects.isNull(order)) {
            throw new ServiceException("当前处方未找到关联的问诊订单");
        }

        // 检查订单状态和视频状态是否有效
        boolean isValid = isValidConsultationStatus(order.getStatus()) ||
                isValidConsultationStatus(order.getVideoStatus());

        if (!isValid) {
            throw new ServiceException("此订单状态为非问诊中不可开处方");
        }
    }

    /**
     * 检查问诊状态是否允许开处方
     * @param status 问诊状态
     * @return 是否允许开处方
     */
    private boolean isValidConsultationStatus(String status) {
        return Objects.isNull(status) ||
                ConsultationOrderStatus.VISITING.equals(status) ||
                ConsultationOrderStatus.PRESCRIBED.equals(status);
    }

    /**
     * 获取处方生成器
     * @param dto dto
     * @return 处方生成器
     */
    private IPrescriptionGenerator getPrescriptionGenerator(BusPrescriptionDto dto) {
        // 处方类型校验
        PrescriptionTypeEnum prescriptionType = PrescriptionTypeEnum.getByType(dto.getPrescriptionType());
        if (Objects.isNull(prescriptionType)) {
            throw new ServiceException("处方类型不正确");
        }

        // 药品目录类型校验
        DirectoryTypeEnum directoryType = DirectoryTypeEnum.getByType(dto.getDrugDirectoryType());
        if (Objects.isNull(directoryType)) {
            throw new ServiceException("药品目录类型不正确");
        }

        // 根据处方类型和目录类型获取生成器
        String beanName = prescriptionType.getConstName() +
                directoryType.getConstName() +
                IPrescriptionGenerator.SUFFIX;
        IPrescriptionGenerator generator = prescriptionCreatorMap.get(beanName);

        if (Objects.isNull(generator)) {
            log.error("未找到生成处方的bean，beanName={}，prescriptionType={}，directoryType={}", beanName, prescriptionType, directoryType);
            throw new ServiceException("处方类型预选择的药品目录类型不匹配，无法生成处方");
        }

        return generator;
    }

    /**
     * 处方生成后的处理
     */
    private void postProcessPrescription(BusPrescriptionDto dto, PrescriptionGenerateResultDTO resultDTO) {
        //如果处方创建成功，且是用药预订单，则修改预订单状态
        if (Objects.nonNull(resultDTO) &&
                Objects.nonNull(resultDTO.getPrescriptionId()) &&
                YesNoEnum.YES.getCode().equals(dto.getIsPrescribe())) {

            busPreorderPatientService.prescriptionCreated(dto);
        }
    }


    @Override
    public boolean check(BusPrescriptionDto busPrescription) {
        //处方信息
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getHospitalId, busPrescription.getHospitalId())
                .eq(BusPrescription::getFamilyId, busPrescription.getFamilyId())
                .eq(BusPrescription::getPatientId, busPrescription.getPatientId())
                .eq(BusPrescription::getDepartmentId, busPrescription.getDepartmentId());
        queryWrapper.orderByDesc(BusPrescription::getCreateTime);
        //如果 expert_id 不为空，则查询该专家的订单
        if (busPrescription.getExpertId()!= null) {
            queryWrapper.eq(BusPrescription::getExpertId, busPrescription.getExpertId());
        }else{
            queryWrapper.eq(BusPrescription::getDoctorId, busPrescription.getDoctorId());
        }
        BusPrescription prescription = busPrescriptionMapper.selectOne(queryWrapper.last("limit 1"));
        return Objects.nonNull(prescription);
    }

    /**
     * 查询处方药品信息
     * @param hospitalId
     * @param prescriptionId
     * @return
     */
    private List<BusPrescriptionDrugs> selectPdList(Long hospitalId, Long prescriptionId, Long enterpriseId) {
        LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusPrescriptionDrugs::getHospitalId, hospitalId);
        lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
        if (null == enterpriseId) {
            lambdaQuery.isNull(BusPrescriptionDrugs::getEnterpriseId);
        } else {
            lambdaQuery.eq(BusPrescriptionDrugs::getEnterpriseId, enterpriseId);
        }
        List<BusPrescriptionDrugs> drugsList = busPrescriptionDrugsMapper.selectList(lambdaQuery);
        if (CollectionUtils.isEmpty(drugsList)) {
            throw new ServiceException("处方药品缺失");
        }
        return drugsList;
    }

    @Override
    public BusPrescription selectPrescription(Long hospitalId, Long id) {
        LambdaQueryWrapper<BusPrescription> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(Objects.nonNull(hospitalId), BusPrescription::getHospitalId, hospitalId);
        lambdaQuery.eq(BusPrescription::getId, id);
        return busPrescriptionMapper.selectOne(lambdaQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int invalid(Long prescriptionId) {
        BusPrescription busPrescription = busPrescriptionMapper.selectById(prescriptionId);
        if (busPrescription == null) {
            return 0;
        }
        //更新处方状态为已失效
        BusPrescription update = new BusPrescription();
        update.setId(prescriptionId);
        update.setStatus(PrescriptionStatus.INVALID);
        update.setUpdateTime(new Date());
        int count = busPrescriptionMapper.updateById(update);
        if (count < 1) {
            return 0;
        }
        //院内处方，是否释放库存和删除购物车
        if (DirectoryTypeEnum.isInner(busPrescription.getDrugDirectoryType())) {
            // 释放药品库存
            this.increaseDrugsStock(busPrescription.getHospitalId(), busPrescription.getId());
            // 删除购物车
            int i = busShopCartService.deletePrescriptionOrOrder(prescriptionId, true);
            log.info("购物车删除 msg:{}", i == 0 ? "失败" : "成功");
            // 删除诊查费缓存标识
            examinationFeeHelper.removeCache(busPrescription);
        } else {
            //todo 这块代码逻辑待讨论，理论上两变状态不应该耦合
            //双通道处方的状态由医保xxl-job进行状态同步
//            //双通道则直接将状态调整为已过期
//            BusPrescriptionDualChannel dualChannel = new BusPrescriptionDualChannel();
//            dualChannel.setPrescriptionId(prescriptionId);
//            dualChannel.setRxStatus(DigitalRxStatusEnum.EXPIRED.getStatus());
//            dualChannel.setRxStatusName(DigitalRxStatusEnum.EXPIRED.getDesc());
//            dualChannel.setUpdateTime(new Date());
//            busPrescriptionDualChannelMapper.updateById(dualChannel);
        }
        return count;
    }

    @Override
    public List<TCMSyndromeVo> selectTCMSyndromeList(String key) {
        return busPrescriptionMapper.selectTCMSyndromeList(key);
    }

    @Override
    public List<Long> getPrescriptionId(Long id) {
        List<Long> ids = new ArrayList<>();
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusPrescription::getConsultationOrderId, id);
        // 查询处方状态为审核通过、未通过、取消、已使用、已失效的处方
        List<String> newsTypes = Stream.of(
                         PrescriptionStatusEnum.NOT_PASS,
                         PrescriptionStatusEnum.PASS,
                         PrescriptionStatusEnum.CANCELLATION,
                         PrescriptionStatusEnum.USED,
                         PrescriptionStatusEnum.INVALID
                ).map(PrescriptionStatusEnum::getStatus)
                .collect(Collectors.toList());
        queryWrapper.in(BusPrescription::getStatus, newsTypes);
        queryWrapper.isNotNull(BusPrescription::getPatientId);
        queryWrapper.orderByDesc(BusPrescription::getId);
        List<BusPrescription> prescriptions = busPrescriptionMapper.selectList(queryWrapper);
        for (BusPrescription prescription : prescriptions) {
            // 返回已审核通过的处方id
            if (Objects.nonNull(prescription.getReviewPharmacistId())) {
                ids.add(prescription.getId());
            }
        }
        return ids;
    }

    @Override
    public void afferentPrescriptionToDx(BusDrugsOrder drugsOrder, BusPrescription busPrescription) {
        // 查询医院中药默认配送企业
        QueryWrapper<BusHospitalTcmEnterprise> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_id", drugsOrder.getHospitalId());
        BusHospitalTcmEnterprise hospitalTcmEnterprise = busHospitalTcmEnterpriseMapper.selectOne(queryWrapper);
        // 获取药品订单中药品订单id
        QueryWrapper<BusPrescriptionDrugs> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1
                .eq("hospital_id", busPrescription.getHospitalId())
                .eq("prescription_id", busPrescription.getId());
        List<BusPrescriptionDrugs> prescriptionDrugs = busPrescriptionDrugsMapper.selectList(queryWrapper1);
        // 调用大翔传入处方接口
        PrescriptionDrugInfo prescriptionDrugInfo = new PrescriptionDrugInfo();
        List<Prescription> prescriptions = new ArrayList<>();
        Prescription prescription = new Prescription();
        // TODO 弃用方法，重新使用请读取配置文件医院名称
        prescription.setHospital_name("TODO");
        prescription.setPspnum(busPrescription.getPrescriptionNumber());
        prescription.setName(busPrescription.getFamilyName());
        if (busPrescription.getFamilySex().equals(CodeEnum.NO.getCode())) {
            prescription.setSex(2);
        } else {
            prescription.setSex(1);
        }
        prescription.setPhone(busPrescription.getFamilyTel());
        prescription.setAddress(drugsOrder.getDetailedAddress());
        String[] split = busPrescription.getUsages().split(",");
        prescription.setDose(Integer.parseInt(split[1]));
        prescription.setTakenum(Integer.parseInt(split[2]) * Integer.parseInt(split[3]));
        prescription.setGetdrugtime("dhySYSTEM");
        prescription.setDecscheme(3);
        prescription.setOncetime("dhySYSTEM");
        prescription.setTwicetime("dhySYSTEM");
        prescription.setDoctor(busPrescription.getDoctorName());
        // 查询就诊人出生日期
        QueryWrapper<BusPatientFamily> queryWrapper4 = new QueryWrapper<>();
        queryWrapper4
                .eq("patient_id", busPrescription.getPatientId())
                .eq("id", busPrescription.getFamilyId());
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectOne(queryWrapper4);
        int age = AgeCalculationUtil.getAge(busPatientFamily.getDateOfBirth());
        if (age > 16) {
            prescription.setPackagenum(200);
        } else if (age > 12) {
            prescription.setPackagenum(150);
        } else if (age > 3) {
            prescription.setPackagenum(100);
        } else {
            prescription.setPackagenum(50);
        }
        prescription.setAge(String.valueOf(age));
        prescription.setDotime("dhySYSTEM");
        if (CodeEnum.NO.getCode().equals(drugsOrder.getDeliveryType())) { // 自提
            prescription.setDoperson(busPrescription.getHospitalName());
            prescription.setDtbcompany(busPrescription.getHospitalName());
            prescription.setDtbaddress(busPrescription.getHospitalAddress());
            HospitalVo hospitalVo = busDrugsOrderMapper.selectHospitalTel(drugsOrder.getHospitalId());
            prescription.setDtbphone(DESUtil.decrypt(hospitalVo.getHospitalPhone()));
        } else { // 快递
            prescription.setDoperson(busPrescription.getFamilyName());
            prescription.setDtbcompany(busPrescription.getFamilyName());
            prescription.setDtbaddress(drugsOrder.getReceivingAddress());
            prescription.setDtbphone(busPrescription.getFamilyTel());
        }
        prescription.setDtbtype(1);
        prescription.setSoakwater("dhySYSTEM");
        prescription.setSoaktime(30);
        prescription.setOrdertime("dhySYSTEM");
        prescription.setDecmothed("dhySYSTEM");
        prescription.setTakeway("dhySYSTEM");
        prescription.setTakemethod("0".equals(split[0]) ? "内服" : "外用");
        prescription.setDrug_count(prescriptionDrugs.size());
        if ("0".equals(busPrescription.getProcessingMethod())) {
            prescription.setIsDaijian("0"); // 不代煎
        } else if ("3".equals(busPrescription.getProcessingMethod())) {
            prescription.setIsDaijian("5"); // 膏方
        } else {
            prescription.setIsDaijian("dhySYSTEM"); // 代煎
        }
        prescription.setPtype("dhySYSTEM");
        prescriptions.add(prescription);
        prescriptionDrugInfo.setPrescription(prescriptions);
        List<Drug> drugs = new ArrayList<>();
        for (BusPrescriptionDrugs p : prescriptionDrugs) {
            QueryWrapper<BusEnterpriseDrugs> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2
                    .eq("enterprise_id", hospitalTcmEnterprise.getEnterpriseId())
                    .eq("drugs_id", p.getDrugsId());
            BusEnterpriseDrugs busEnterpriseDrugs = busEnterpriseDrugsMapper.selectOne(queryWrapper2);
            Drug drug = new Drug();
            drug.setDrugnum(busEnterpriseDrugs.getEnterpriseDrugsId());
            drug.setDrugname(p.getDrugsName());
            drug.setDrugposition("g");
            drug.setDrugallnum(p.getWeight());
            drug.setTienum(split[1]);
            drug.setDrugweight(DoubleUtils.mul(Double.valueOf(split[1]), Double.valueOf(p.getWeight())).toString());
            drug.setUnit("g");
            drugs.add(drug);
        }
        prescriptionDrugInfo.setDrug(drugs);
        R<Boolean> objectR = remoteDXService.addPrescription(prescriptionDrugInfo);
        //Boolean data = (Boolean) objectR.getData();
        Boolean data = objectR.getData();
        log.info("处方是否推送成功:{}", data);
    }

    @Override
    public BusPrescription getLastDiagnosis(long hospitalId, long familyId) {
        QueryWrapper<BusPrescription> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", hospitalId)
                .eq("family_id", familyId)
                .orderByDesc("create_time");
        List<BusPrescription> results = busPrescriptionMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(results)) {
            return results.get(0);
        }
        return new BusPrescription();
    }

    @Override
    public BusPrescriptionVo queryReviewed(BusPrescriptionDto dto) {
        log.info("入参={}", dto.toString());
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getHospitalId, dto.getHospitalId())
                .eq(BusPrescription::getPatientId, dto.getPatientId())
                .eq(BusPrescription::getFamilyId, dto.getFamilyId())
                .eq(BusPrescription::getStatus, "2")
                .eq(BusPrescription::getIdentity, "1")
                .eq(BusPrescription::getPreorderId, dto.getPreorderId());
        BusPrescription prescription = busPrescriptionMapper.selectOne(queryWrapper);
        if (Objects.nonNull(prescription)) {
            prescription.setPass(1);
            return select(prescription);
        } else {
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int followUpPurchase(BusPreorderPatientDto dto) {
        // 补充预订单信息
        BusPreorderPatient busPreorderPatient = new BusPreorderPatient();
        busPreorderPatient.setPatientId(dto.getPatientId());
        busPreorderPatient.setFamilyId(dto.getFamilyId());
        busPreorderPatient.setHospitalId(dto.getHospitalId());
        busPreorderPatient.setDiagnosis(dto.getDiagnosis());
        busPreorderPatient.setStatus("0");
        busPreorderPatient.setCreateTime(DateUtils.getNowDate());
        int insert = busPreorderPatientMapper.insert(busPreorderPatient);
        // 补充预订单药品信息
        List<BusPreorderDrugs> preorderDrugsList = dto.getList();
        preorderDrugsList.stream().forEach(p -> {
            p.setCreateTime(DateUtils.getNowDate());
            p.setPreorderId(busPreorderPatient.getId());
        });
        busPreorderDrugsMapper.insert(preorderDrugsList);
        // 修改处方状态
        BusPrescription prescription = new BusPrescription();
        prescription.setId(dto.getId());
        prescription.setStatus(PrescriptionStatus.INVALID);
        busPrescriptionMapper.updateById(prescription);
        return insert;
    }

    public void savePath(Long id, String pdfFilePath) {
        BusPrescription prescription = new BusPrescription();
        prescription.setId(id);
        prescription.setPath(pdfFilePath);
        busPrescriptionMapper.updateById(prescription);
    }

    @Override
    public List<BusPrescriptionDoctorAdviceTemplate> selectPrescriptionDoctorAdviceTemplateList() {
        QueryWrapper<BusPrescriptionDoctorAdviceTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", YesNoEnum.YES.getCode());
        queryWrapper.orderByAsc("order_num");
        List<BusPrescriptionDoctorAdviceTemplate> templates =
                busPrescriptionDoctorAdviceTemplateMapper.selectList(queryWrapper);
        return templates;
    }

    @Override
    public List<BusPrescriptionFailTemplate> queryFailTemplateList() {
        QueryWrapper<BusPrescriptionFailTemplate> queryWrapper = new QueryWrapper<>();
        return busPrescriptionFailTemplateMapper.selectList(queryWrapper);
    }

    @Override
    public int doctorSign(BusPrescription busPrescription) {
        busPrescription.setStatus(PrescriptionStatus.NOT_EXAMINE);
        Long doctorId;
        if (busPrescription.getExpertId() != null){
            //获取医院默认配置开方医院的信息
            BusHospitalPreorderDoctor preorderDoctor = iBusHospitalPreorderDoctorService.selectByHospitalId(busPrescription.getHospitalId());
            if (preorderDoctor == null) {
                throw new IllegalArgumentException("开方失败，医院未设置默认开方医生");
            }
            doctorId = preorderDoctor.getDoctorId();
        }else {
            doctorId = busPrescription.getDoctorId();
        }
        // 查询医生名称
        BusDoctor doctor = busDoctorMapper.selectById(doctorId);
        busPrescription.setDoctorName(doctor.getFullName());
        busPrescription.setDoctorId(doctor.getId());
        busPrescription.setUpdateTime(DateUtils.getNowDate());
        return busPrescriptionMapper.updateById(busPrescription);
    }

    @Override
    public List<BusDisease> selectCommonMMDiagnosis(BusPrescriptionDto dto) {
        log.info("常用疾病入参={}", JSON.toJSONString(dto));
        List<BusDisease> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getDrugsIds())) {
            // 查询该药品疾病信息
            LambdaQueryWrapper<BusDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.in(BusDrugs::getId, dto.getDrugsIds());
            List<BusDrugs> drugsList = busDrugsMapper.selectList(lambdaQuery);
            List<String> list1 = drugsList.stream()
                    .filter(d -> StringUtils.isNotEmpty(d.getIcdCode()))
                    .map(BusDrugs::getIcdCode).collect(Collectors.toList());
            List<String> list3 = new ArrayList<>();
            list1.forEach(l -> {
                String[] split = l.split(",");
                list3.addAll(Arrays.asList(split));
            });
            if (StringUtils.isNotEmpty(list3)) {
                List<BusDisease> busDiseases = busDiseaseMapper.selectDiseaseList(list3);
                list.addAll(busDiseases);
            }
        }
        LambdaQueryWrapper<BusDisease> queryWrapper = new LambdaQueryWrapper<BusDisease>()
                .eq(BusDisease::getCommon, YesNoEnum.YES.getCode())
                .eq(BusDisease::getStatus, YesNoEnum.YES.getCode())
                .orderByDesc(BusDisease::getId);
        List<BusDisease> busDiseases = busDiseaseMapper.selectList(queryWrapper);
        list.addAll(busDiseases);
        return list;
    }

    @Override
    public void checkPrescription(BusPreorderPatient busPreorderPatient) {
        if (CodeEnum.YES.getCode().equals(busPreorderPatient.getStatus())) {
            List<BusPrescription> prescriptions = busPrescriptionMapper.selectList(new LambdaQueryWrapper<BusPrescription>()
                    .eq(BusPrescription::getPreorderId, busPreorderPatient.getId()));
            if (StringUtils.isNotEmpty(prescriptions)) {
                prescriptions.forEach(p -> {
                    if (!(PrescriptionStatus.CANCELLATION.equals(p.getStatus()) || PrescriptionStatus.NOT_PASS.equals(p.getStatus()))) {
                        throw new ServiceException("该预订单已生成处方，无需重复开方");
                    }
                });
            }
        } else {
            // 查询预订单药品
            LambdaQueryWrapper<BusPreorderDrugs> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery
                    .select(BusPreorderDrugs::getDrugsId)
                    .eq(BusPreorderDrugs::getPreorderId, busPreorderPatient.getId());
            List<BusPreorderDrugs> preorderDrugsList = busPreorderDrugsMapper.selectList(lambdaQuery);
            List<Long> drugsIds = preorderDrugsList.stream().map(BusPreorderDrugs::getDrugsId).collect(Collectors.toList());
            List<BusHospitalOfficina> hospitalOfficinaList = busHospitalOfficinaService.selectDrugsList(drugsIds,
                    busPreorderPatient.getHospitalId());
            if (drugsIds.size() != hospitalOfficinaList.size()) {
                List<Long> drugsIdList = hospitalOfficinaList.stream().map(BusHospitalOfficina::getDrugsId).collect(Collectors.toList());
                drugsIds.removeAll(drugsIdList);
                // 查询药品名称
                LambdaQueryWrapper<BusDrugs> drugsLambda = Wrappers.lambdaQuery();
                drugsLambda.select(BusDrugs::getDrugsName)
                        .in(CollUtil.isNotEmpty(drugsIds),BusDrugs::getId, drugsIds);
                List<BusDrugs> drugsList = busDrugsMapper.selectList(drugsLambda);
                List<String> drugsNameList = drugsList.stream().map(BusDrugs::getDrugsName).collect(Collectors.toList());
                String drugsName = String.join(",", drugsNameList);
                throw new ServiceException("该用药预订单的" + drugsName + "药品已删除，请联系患者更改药品");
            }
        }
    }

    @Override
    public List<BusPrescription> queryInvalidList(Long idx, Integer pageSize) {
        return busPrescriptionMapper.queryInvalidList(idx, pageSize);
    }

    @Override
    public List<PrescriptionStatusTypeVO> selectPrescriptionStatusCount(Long hospitalId, Long doctorId, Long expertId) {
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getHospitalId, hospitalId);
        List<PrescriptionStatusTypeVO> prescriptionStatusTypeVOS;
        // 如果
        if (expertId!= null){
            prescriptionStatusTypeVOS = busPrescriptionMapper.selectPrescriptionStatusCount(hospitalId, null, expertId);
            queryWrapper.eq(BusPrescription::getExpertId, expertId);
        }else {
            prescriptionStatusTypeVOS = busPrescriptionMapper.selectPrescriptionStatusCount(hospitalId, doctorId,null);
            queryWrapper.eq(StringUtils.isNotNull(doctorId), BusPrescription::getDoctorId, doctorId);
        }
        Long count = busPrescriptionMapper.selectCount(queryWrapper);
        PrescriptionStatusTypeVO all = new PrescriptionStatusTypeVO();
        all.setCount(count);
        all.setName("全部处方");
        all.setHospitalId(hospitalId);
        all.setStatus(-1);
        prescriptionStatusTypeVOS.add(all);
        return prescriptionStatusTypeVOS;
    }

    @Override
    public BusPrescription getById(Long id) {
        return busPrescriptionMapper.selectById(id);
    }

    /**
     * 远程调用查询处方信息集合
     *
     * @param prescriptionIds
     * @return
     */
    @Override
    public List<BusPrescription> selectPrescriptionList(List<Long> prescriptionIds) {
        return busPrescriptionMapper.selectPdList(prescriptionIds);
    }

    /**
     * 远程调用修改处方信息
     *
     * @param prescriptionId
     * @return
     */
    @Override
    public int updatePrescription(Long prescriptionId) {
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setId(prescriptionId);
        busPrescription.setStatus(PrescriptionStatus.USED);
        return busPrescriptionMapper.updateById(busPrescription);
    }

    /**
     * 生成处方pdf文件
     *
     * @param prescription
     * @return
     */
    @Override
    public String generatePdf(BusPrescription prescription) {
        // 获取模板HTML文件流
        StringBuilder htmlSb = new StringBuilder();
        try {
            InputStream inputStream;
            if (PrescriptionTypeEnum.isMm(prescription.getPrescriptionType())) {
                inputStream = new ClassPathResource("wm_template.html").getInputStream();
            } else {
                inputStream = new ClassPathResource("tcm_template.html").getInputStream();
            }
            BufferedReader br = new BufferedReader(new InputStreamReader(
                    inputStream, StandardCharsets.UTF_8));
            while (br.ready()) {
                htmlSb.append(br.readLine());
            }
            br.close();
        } catch (Exception e) {
            log.error("读取模板文件失败", e);
            throw new ServiceException("读取模板文件失败");
        }
        // 获取html字符串
        String htmlStr = htmlSb.toString();
        Document document = Jsoup.parse(htmlStr);
        Elements element = document.getElementsByClass("title");
        element.get(0).child(0).html(prescription.getHospitalName() + "互联网医院");
        //判断是否双通道
        if (DirectoryTypeEnum.isDualChannel(prescription.getDrugDirectoryType())) {
            element.get(0).child(2).html("电子处方笺(双通道)");
        }
        if (CodeEnum.YES.getCode().equals(prescription.getPrintType())) {
            element.get(0).child(2).html("用药指导单");
        }
        // 处方编号条形码
        generateBarcode(prescription, document);
        Elements span = document.getElementsByTag("span");
        if (span.isEmpty()) {
            throw new ServiceException("处方模板文件格式错误");
        }
        span.get(0).html("处方编号：" + prescription.getPrescriptionNumber());
        span.get(1).html("姓名：" + prescription.getFamilyName());
        String familySex = "0".equals(prescription.getFamilySex()) ? "女" : "男";
        span.get(2).html("性别：" + familySex);
        span.get(3).html("年龄：" + prescription.getFamilyAge());
        span.get(4).html("科别：" + prescription.getDepartmentName());
        Date prescriptionTime = prescription.getPrescriptionTime();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String format = simpleDateFormat.format(prescriptionTime);
        span.get(5).html("日期：" + format);
        //费别
        FeeSettleTypeEnum feeSettleTypeEnum = FeeSettleTypeEnum.getByType(prescription.getFeeSettleType());
        String feeSettleTypeDesc = Objects.nonNull(feeSettleTypeEnum) ? feeSettleTypeEnum.getDesc() : "";
        span.get(6).html("医保类型：" + feeSettleTypeDesc);
        //患者信息（电话和地址）
        String familyTel = StringUtils.isNotBlank(prescription.getFamilyTel()) ? prescription.getFamilyTel() : "";
        String familyDetailAddress = StringUtils.isNotBlank(prescription.getFamilyDetailAddress()) ? prescription.getFamilyDetailAddress() : "";
        span.get(7).html("电话：" + familyTel);
        span.get(8).html("地址：" + familyDetailAddress);
        //诊断信息
        Elements elements = document.getElementsByClass("body-item");
        Elements elementsByDrug = document.getElementsByClass("body-prescription");
        String clinicalDiagnosis = prescription.getClinicalDiagnosis();
        JSONArray objects = JSON.parseArray(clinicalDiagnosis);
        elements.get(1).child(1).remove();
        if (CodeEnum.YES.getCode().equals(prescription.getPrescriptionType())) {
            // 西药
            elements.get(1).child(0).appendElement("br");
            for (int i = 0; i < objects.size(); i++) {
                elements.get(1).child(0).appendElement("span").attr("style", "word-break: break-word")
                        .html(i + 1 + "、" + objects.getJSONObject(i).getString("diseaseName"));
                elements.get(1).child(0).appendElement("br");
            }
        } else {
            // 中药
            elements.get(1).child(0).appendElement("br");
            for (int i = 0; i < objects.size(); i++) {
                JSONObject jsonObject = objects.getJSONObject(i);
                elements.get(1).child(0).appendElement("span").appendElement("span").attr("style", "word-break: break-word")
                        .html(jsonObject.getString("tcmDiagnosis") + "（" + jsonObject.getJSONObject("tcmSyndrome").getString("tcmSyndrome") + "）");
                elements.get(1).child(0).appendElement("br");
            }
        }
        // 查询处方药品
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setHospitalId(prescription.getHospitalId());
        busPrescription.setId(prescription.getId());
        BusPrescriptionVo vo = this.select(busPrescription);
        if (Objects.isNull(vo)) {
            throw new ServiceException("处方信息不存在");
        }
        List<BusPrescriptionDrugs> list = vo.getList();
        PrescriptionAutomationConfig automationConfig = prescriptionHelper.getAutomationConfig(vo.getHospitalId());
        if (PrescriptionTypeEnum.isMm(prescription.getPrescriptionType())) {
            for (int i = 0; i < list.size(); i++) {
                BusPrescriptionDrugs drugs = list.get(i);
                String usageDosageHtml = getUsageDosageHtml(drugs, vo, automationConfig);
                if (i == 0) {
                    elementsByDrug.get(0).child(1).child(0).html(
                            "<div style=\"float: left;\">" + drugs.getDrugsName() + "（" + drugs.getDrugsSpecification() + "）" + "</div>" +
                                    "<div style=\"float: right;\"><span class=\"mr-10\">x</span>" + drugs.getQuantity() + "</div>" +
                                    "<div style=\"clear: both\"></div>"
                    );
                    elementsByDrug.get(0).child(1).child(1).html(usageDosageHtml);
                }
                if (i >= 1) {
                    Element divOne = elementsByDrug.get(0).appendElement("div");
                    divOne.appendElement("div").html(
                            "<div style=\"float: left;\">" + drugs.getDrugsName() + "（" + drugs.getDrugsSpecification() + "）" + "</div>" +
                                    "<div style=\"float: right;\"><span class=\"mr-10\">x</span>" + drugs.getQuantity() + "</div>" +
                                    "<div style=\"clear: both\"></div>");
                    Element divTwo = elementsByDrug.get(0).appendElement("div").attr("style", "text-align: right;");
                    divTwo.appendElement("div").html(usageDosageHtml);
                }
                document.getElementsByClass("mr-10").attr("style", "margin-right: 10px");
                document.getElementsByClass("mr-5").attr("style", "margin-left: 5px;");
            }
            String remark = prescription.getRemark();
            if (StringUtils.isNotEmpty(remark)) {
                elements.get(2).child(1).child(0).html(remark);
            }
        } else { // 中药
            String drughtml = "";
            int num = 0;
            for (BusPrescriptionDrugs drugs : list) {
                num++;
                // 最后一个
                if (num == list.size()) {
                    // 偶数
                    if ((num & 1) == 0) {
                        //普通中药
                        if ("g".equals(drugs.getDrugsSpecification())) {
                            drughtml += "<div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + "</span><div class='float-right'>" + "<span>" + drugs.getWeight() + "克" + "</span></div></div></div></div>";
                        } else {// 其他中药
                            drughtml += "<div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + " " + drugs.getDrugsSpecification() + "</span><span class='float-right'>x" + drugs.getWeight() + "</span></div></div></div>";
                        }
                        break;
                    } else {
                        if ("g".equals(drugs.getDrugsSpecification())) {
                            drughtml += "<div><div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + "</span><div class='float-right'>" + "<span>" + drugs.getWeight() + "克" + "</span></div></div></div>";
                        } else {// 其他中药
                            drughtml += "<div><div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + " " + drugs.getDrugsSpecification() + "</span><span class='float-right'>x" + drugs.getWeight() + "</span></div></div>";
                        }
                        // 单数
                        break;
                    }
                }
                // 偶数
                if ((num & 1) == 0) {
                    if ("g".equals(drugs.getDrugsSpecification())) {
                        drughtml += "<div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + "</span><div class='float-right'><span>" + drugs.getWeight() + "克" + "</span></div></div></div></div>";
                    } else {// 其他中药
                        drughtml += "<div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + " " + drugs.getDrugsSpecification() + "</span><span class='float-right'>x" + drugs.getWeight() + "</span></div></div></div>";
                    }
                    continue;
                } else {
                    if ("g".equals(drugs.getDrugsSpecification())) {
                        drughtml += "<div><div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + "</span><div class='float-right'><span>" + drugs.getWeight() + "克" + "</span></div></div>";
                    } else {// 其他中药
                        drughtml += "<div><div class='drugClass'><span class='float-left'>" + drugs.getDrugsName() + " " + drugs.getDrugsSpecification() + "</span><span class='float-right'>x" + drugs.getWeight() + "</span></div>";
                    }
                    // 单数
                    continue;
                }
            }
            elementsByDrug.get(0).html(drughtml);
            if (list.size() > 0) {
                Elements drugEle = document.getElementsByClass("drugClass");
                for (Element el : drugEle) {
                    el.attr("style", " width:45%;float:left;margin-right:20px;");
                }
                Elements drugLeft = document.getElementsByClass("float-left");
                for (Element el : drugLeft) {
                    el.attr("style", "float: left;");
                }
                Elements drugRight = document.getElementsByClass("float-right");
                for (Element el : drugRight) {
                    el.attr("style", "float: right;line-height:20px;");
                }
            }
            String processingMethod = prescription.getProcessingMethod();
            switch (processingMethod) {
                case "0":
                    processingMethod = "自煎";
                    break;
                case "1":
                    processingMethod = "代煎";
                    break;
                case "2":
                    processingMethod = "颗粒";
                    break;
                case "3":
                    processingMethod = "膏方";
                    break;
                case "4":
                    processingMethod = "素丸";
                    break;
                case "5":
                    processingMethod = "水丸";
                    break;
                case "6":
                    processingMethod = "水蜜丸";
                    break;
                case "7":
                    processingMethod = "小蜜丸";
                    break;
                case "8":
                    processingMethod = "浓缩丸";
                    break;
                default:
            }
            String usages = prescription.getUsages();
            String[] split = usages.split(",");
            String s0 = "0".equals(split[0]) ? "内服" : "外用";

            BusPrescriptionDrugs pdVar = list.get(0);
            BusDrugs busDrugsVar = busDrugsMapper.selectById(pdVar.getDrugsId());
            if ( null!=busDrugsVar && ChinaDrugService.PARTICLE_DRUG_CODE.equals(busDrugsVar.getDrugsDosageForm()) ) {
                // 中药颗粒的煎煮方式不显示
                processingMethod = "" ;
            }

            elementsByDrug.get(0).appendElement("div").attr("style", "float:left;").html(s0 + "共" + split[1] + "剂，" + "每日" + split[2] + "剂，" + "1剂分" + split[3] + "次服用。" + processingMethod);
            String remark = prescription.getRemark();
            if (StringUtils.isNotEmpty(remark)) {
                elements.get(2).child(1).child(0).html(remark);
            }
        }
        if (CodeEnum.YES.getCode().equals(prescription.getPrintType())) {
            Element e1 = elements.get(3);
            e1.remove();
            document.getElementById("preDaySty").html("1、本用药指导单限于" + prescription.getHospitalName() +
                    "使用，自行下载配药不具有用药指导单效力。");
            document.getElementById("preHosSty").html("2、按照卫生部、国家中医药管理局卫医发[2002]24号文件规定：为保证患者用药安全，药品一经发出，不得退换。");
            document.getElementById("preSpan1").html("3、请严格按原处方和药品说明书使用，严禁超量超范围使用；如用药过程中出现病情变化或其它不适症状，请立即停药并及时就医。");
        } else {
            List<BusSignatureVO> signatureList = vo.getSignatureList();
            log.info("签名数组：{}", JSON.toJSONString(signatureList));
            for (BusSignatureVO signature : signatureList) {
                if (SignatureRole.DOCTOR.getValue().equals(signature.getObjectType())) {
                    document.getElementsByClass("preDotSty").get(0).attr("style", "line-height: 30px;")
                            .attr("src", ossUtil.getFullPath(signature.getCertSignature()));
                } else if (SignatureRole.PHARMACIST.getValue().equals(signature.getObjectType())) {
                    document.getElementsByClass("prePharSty").get(0).attr("style", "line-height: 30px;")
                            .attr("src", ossUtil.getFullPath(signature.getCertSignature()));
                } else if (SignatureRole.DELIVERY_PHARMACIST.getValue().equals(signature.getObjectType())) {
                    document.getElementsByClass("preDispenSty").get(0).attr("style", "line-height: 30px;")
                            .attr("src", ossUtil.getFullPath(signature.getCertSignature()));
                } else if (SignatureRole.CHEMIST.getValue().equals(signature.getObjectType())) {
                    document.getElementsByClass("preDeliSty").get(0).attr("style", "line-height: 30px;")
                            .attr("src", ossUtil.getFullPath(signature.getCertSignature()));
                }
            }
            // 查询医院公章
            BusHospital hospital = busHospitalMapper.selectById(prescription.getHospitalId());
            document.getElementsByClass("gongzhangSty").get(0).attr("style", "position:absolute;top: 28px;right: 100px;");
            document.getElementsByClass("imgSty").get(0).attr("style", "width:100px;height:100px;position:absolute;top: -10px;right: 100px;")
                    .attr("src", ossUtil.getFullPath(hospital.getHospitalSeal()));

            document.getElementById("preDaySty").html("1、处方有限期为" + vo.getEffectiveHours() + "小时，请及时取药。");
            document.getElementById("preHosSty").html("2、本处方限于" + prescription.getHospitalName() + "使用，自行下载配药不具有处方效力。");
            document.getElementById("preSpan1").html("3、按照卫生部、国家中医药管理局卫医发[2002]24号文件规定：为保证患者用药安全，药品一经发出，不得退换。");
            document.getElementById("preSpan2").html("4、请严格按原处方和药品说明书使用，严禁超量超范围使用；如用药过程中出现病情变化或其它不适症状，请立即停药并及时就医。");
        }
        return getPdfFilePath(prescription, document);
    }

    /**
     * 生成处方编号条形码
     * @param prescription 处方信息
     * @param document html
     */
    private void generateBarcode(BusPrescription prescription, Document document) {
        String setting = remoteHospitalSettingApi.getSettingValue(BARCODE_HOSPITAL_TEMPLATE_KEY, prescription.getHospitalId()).getData();
        JSONArray barcodeObjects = JSONArray.parseArray(setting);
        if (CollectionUtils.isEmpty(barcodeObjects)) {
            return;
        }
        HospitalBarcodeConfig barcodeConfig = JSONObject.toJavaObject(barcodeObjects.getJSONObject(0), HospitalBarcodeConfig.class);
        if (Objects.isNull(barcodeConfig) || !barcodeConfig.isShow()) {
            return;
        }
        String template = remotePlatformSettingApi.getSettingValue(BARCODE_PLATFORM_TEMPLATE_KEY).getData();
        if (template == null) {
            log.info("平台模板处方编号条形码为空，请配置条形码样式！");
        }else {
            // 条形码参数
            assambledValues(prescription, document, barcodeConfig, template);
        }
    }

    /**
     * 条形码参数
     * @param prescription 处方信息
     * @param document html
     * @param barcodeConfig 医院后台条形码配置
     * @param template 条形码样式平台模板
     */
    private void assambledValues(BusPrescription prescription, Document document, HospitalBarcodeConfig barcodeConfig, String template) {
        // title
        String title = " ";
        if (StringUtils.isNotEmpty(barcodeConfig.getTitle())) {
            title = barcodeConfig.getTitle() + "：";
        }
        // barcode
        String barcode = BarcodeUtil.generateBarcode(prescription.getPrescriptionNumber());
        // 替换
        Map<String, String> values = new HashMap<>();
        values.put(BARCODE_URL, barcode);
        values.put(TITLE, title);
        String replacedHtml = replacePlaceholders(template,values);
        Element titleElement = document.getElementsByClass(TITLE).first();
        if (titleElement != null) {
            titleElement.after(replacedHtml);
        }
    }

    /**
     * 替换占位符
     * @param html html
     * @param values 替换值
     * @return 替换好的html
     */
    public static String replacePlaceholders(String html, Map<String, String> values) {
        if (html == null || values == null) {
            return html;
        }
        Pattern pattern = Pattern.compile("\\{\\{(.*?)\\}\\}");
        Matcher matcher = pattern.matcher(html);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1).trim();
            String replacement = values.getOrDefault(key, "");
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 获取处方html的用法用量
     *
     * @param drugs  处方药品
     * @param vo     处方信息
     * @param automationConfig 配置信息
     * @return String
     */
    private String getUsageDosageHtml(BusPrescriptionDrugs drugs, BusPrescriptionVo vo, PrescriptionAutomationConfig automationConfig) {
        String dose = null == drugs.getSingleDose() ? "" : String.valueOf(drugs.getSingleDose());
        //用法用量
        String usageDosageHtml = "<span class=\"mr-10\">用法：</span>" +
                "<span class=\"mr-10\">" + dose + drugs.getUnit() + "</span>" +
                "<span class=\"mr-10\">" + drugs.getMedicationFrequencyRemarks() + "</span>" +
                "<span class=\"mr-5\">" + drugs.getDrugsUsageValue() + "</span>";
        //医生开方 或者 找药开方且配置了展示用药天数需要展示用药天数
        if (Objects.isNull(vo.getPreorderId()) || Boolean.TRUE.equals(automationConfig.getTakeMedicineDay())) {
            usageDosageHtml += "<span class=\"mr-5\">使用" + drugs.getMedicationDays() + "天</span>";
        }
        return usageDosageHtml;
    }

    /**
     * @Param prescription
     * @Param document
     * @Return java.lang.String
     * @Description 生成 Html 文件 并转换为 PDF  然后写入 OSS
     * <AUTHOR>
     * @Date 2023/10/31 14:06
     **/
    private String getPdfFilePath(BusPrescription prescription, Document document) {
        // 生成html文件
        String html = document.outerHtml();
        String uuid = IdUtils.randomUUID();
        //文件类型
        String fileType = ".pdf";
        //文件名称
        String fileName =  uuid + fileType;


        //读取字体库
//        File file = null;
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("font/msyh.ttf");
            FileCopyUtils.copy(inputStream, outputStream);
        } catch (IOException e) {
            log.error("读取 Html 文件失败 e :", e);
            throw new RuntimeException(e);
        }
        byte[] bytes = outputStream.toByteArray();
        //写入字体
        ConverterProperties converterProperties = new ConverterProperties();
        FontProvider dfp = new DefaultFontProvider();
        dfp.addFont(bytes);

        ByteArrayOutputStream out = null;

        try {
            out = new ByteArrayOutputStream();
            converterProperties.setFontProvider(dfp);
            HtmlConverter.convertToPdf(html, out, converterProperties);
        } catch (Exception e) {
            log.error("生成PDF处方文件失败e:", e);
            throw new ServiceException("生成PDF处方文件失败");
        } finally {
            if (out!=null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("输出流关闭失败 e :", e);
                }
            }
        }
        // 上传pdf文件到OSS
        String ossDir =
                OSSSaveDirectory.PRESCRIPTION.getValue() + "/" + prescription.getHospitalId() + "/";
        //判断是否为特聘专家
        if (Objects.nonNull(prescription.getExpertId())) {
            ossDir += prescription.getExpertId();
        }else {
            ossDir += prescription.getDoctorId();
        }


        ByteArrayInputStream inputStream = new ByteArrayInputStream(out.toByteArray());

        String pdfFilePath = localFileToOss(fileName,fileName,fileType,inputStream,ossDir);
        log.info("PDF 文件上传成功，文件路径为：{}", pdfFilePath);
        // 保存地址
        if (StringUtils.isEmpty(prescription.getPrintType())) {
            this.savePath(prescription.getId(), pdfFilePath);
        }
        return pdfFilePath;
    }



    /**
     * @Param destPath
     * @Param ossDir
     * @Return java.lang.String
     * @Description 上传文件到 OSS
     * <AUTHOR>
     * @Date 2023/10/31 14:59
     **/
    private String localFileToOss(String fieldName,String fileNmae,String contentType,InputStream inputStream, String ossDir) {
        try {
            MultipartFile multipartFile = toMultipartFile(fieldName,fileNmae,contentType,inputStream);
            return ossUtil.simpleUpload(multipartFile, ossDir);
        } catch (Exception ex) {
            log.error("localFileToOss上传到OSS异常：" + ex.getMessage());
        }
        return null;
    }

    /**
     * @Param fieldName 名称
     * @Param fileNmae 文件名称
     * @Param contentType 文件类型
     * @Param inputStream 输入流
     * @Return org.springframework.web.multipart.MultipartFile
     * @Description 生成 MultipartFile
     * <AUTHOR>
     * @Date 2023/10/31 15:09
     **/
    private MultipartFile toMultipartFile(String fieldName,String fileNmae,String contentType,InputStream inputStream) throws IOException {
        DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();
        FileItem fileItem = diskFileItemFactory.createItem(fieldName, contentType, false, fileNmae);
        try (
                OutputStream outputStream = fileItem.getOutputStream()
        ) {
            FileCopyUtils.copy(inputStream, outputStream);
        }catch (Exception e){
            log.error("toMultipartFile异常：",e);
        }
        return new CommonsMultipartFile(fileItem);
    }
    @Override
    public List<BusPrescription> getMiRx(List<String> rxNoList, List<Long> idList) {
        return busPrescriptionMapper.getMiRx(rxNoList, idList);
    }

    /**
     * 释放药品库存
     * @param hospitalId 医院ID
     * @param prescriptionId 处方ID
     */
    @Override
    public void increaseDrugsStock(Long hospitalId, Long prescriptionId) {
        LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
        List<BusPrescriptionDrugs> prescriptionDrugs = busPrescriptionDrugsMapper.selectList(lambdaQuery);
        List<BusPrescriptionDrugs> enterpriseDrugsList =
                prescriptionDrugs.stream().filter(item -> item.getEnterpriseId() != null).collect(Collectors.toList());
        List<BusPrescriptionDrugs> hospitalDrugsList = prescriptionDrugs.stream()
                .filter(item -> !enterpriseDrugsList.contains(item)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(enterpriseDrugsList)) {
            Long enterpriseId = enterpriseDrugsList.get(0).getEnterpriseId();
            busPrescriptionDrugStockHelper.changeEnterpriseStock(hospitalId, enterpriseId, StockEnum.INCREASE.getName(), enterpriseDrugsList);
        }
        if (CollectionUtil.isNotEmpty(hospitalDrugsList)) {
            busPrescriptionDrugStockHelper.changeStock(prescriptionId, hospitalId, StockEnum.INCREASE.getName(), hospitalDrugsList);
        }
    }

    @Async
    @Override
    public void sendPrescriptionApproveSms(BusPrescription prescription){
        BusPatient busPatient = patientMapper.selectById(prescription.getPatientId());
        if (null==busPatient) {
            return ;
        }

        R<com.puree.hospital.business.api.model.BusHospitalSmsConfigVo> r = remoteHospitalSmsConfigService.selectOne(prescription.getHospitalId(), null);
        if(null==r.getData()){
            return ;
        }
        String hospitalSign = r.getData().getHospitalSign() ;

        BusHospitalWechatConfig hospitalWechatConfig = hospitalWxNameHelper.getHospitalWechatConfig(prescription.getHospitalId());

        if (Objects.isNull(hospitalWechatConfig)) {
            return ;
        }

        SmsDTO smsDTO = new SmsDTO();
        smsDTO.setPhoneNum(busPatient.getPhoneNumber());
        smsDTO.setHospitalSign(hospitalSign);
        smsDTO.setHospitalId(prescription.getHospitalId());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", "微信公众号" + hospitalWechatConfig.getOfficialAccountName()) ;
        smsDTO.setData(jsonObject);

        smsProducer.send(smsDTO) ;

    }

    /**
     * 患者加入随访
     * @param prescription 处方诊断信息
     */
    @Override
    public void joinFollowUp(BusPrescription prescription) {

        //判断就诊人是否存在
        BusPatientFamily patientFamily = busPatientFamilyMapper.selectById(prescription.getFamilyId());
        if(patientFamily == null || YesNoEnum.NO.getCode().equals(patientFamily.getVisible())){
            return;
        }

        //加入随访（只有中药或西药处方类型可以加入随访，其他类型不做处理）
        if(PrescriptionTypeEnum.isTcm(prescription.getPrescriptionType()) || PrescriptionTypeEnum.isMm(prescription.getPrescriptionType())
                && StringUtils.isNotEmpty(prescription.getClinicalDiagnosis())){

            //记录中医和西医诊断id列表
            List<BusTcmSyndromeDTO> tcmList = new ArrayList<>();
            List<Long> mmIds = new ArrayList<>();
            //获取中医诊断列表
            if(PrescriptionTypeEnum.isTcm(prescription.getPrescriptionType())) {
                JSONArray jsonArray = JSONArray.parseArray(prescription.getClinicalDiagnosis());
                for (int i = 0; i < jsonArray.size(); i++) {
                    Long tcmId = jsonArray.getJSONObject(i).getLong("id");
                    Long syndromeId = jsonArray.getJSONObject(i).getJSONObject("tcmSyndrome").getLong("id");
                    if(tcmId == null || syndromeId == null) {
                        continue;
                    }
                    tcmList.add(new BusTcmSyndromeDTO(tcmId, syndromeId));
                }
            }
            //获取西医诊断列表
            if(PrescriptionTypeEnum.isMm(prescription.getPrescriptionType())) {
                JSONArray jsonArray = JSONArray.parseArray(prescription.getClinicalDiagnosis());
                for (int i = 0; i < jsonArray.size(); i++){
                    mmIds.add(jsonArray.getJSONObject(i).getLong("id"));
                }
            }

            //构建入组条件
            PatientFollowUpJoinRuleDTO joinRuleDTO = new PatientFollowUpJoinRuleDTO();
            joinRuleDTO.setHospitalId(prescription.getHospitalId());
            joinRuleDTO.setUserId(prescription.getPatientId());
            joinRuleDTO.setFamilyId(prescription.getFamilyId());
            joinRuleDTO.setTcmList(tcmList);
            joinRuleDTO.setMmIds(mmIds);
            joinRuleDTO.setGroupId(prescription.getGroupId());

            log.debug("处方诊断信息：{}", joinRuleDTO);
            //患者入组随访(异步处理)
            asyncJoinFollowUpService.joinInFollowUp(joinRuleDTO);
        }
    }

    /**
     * 涮通道电子处方撤销
     *
     * @param busPrescription 电子处方撤销
     */
    private void dualChannelRevoke(BusPrescription busPrescription) {
        //只有双通道处方才进入
        if (Objects.isNull(busPrescription) || !DirectoryTypeEnum.isDualChannel(busPrescription.getDrugDirectoryType())) {
            return;
        }
        BusPrescriptionDualChannel dualChannel = new BusPrescriptionDualChannel();
        dualChannel.setPrescriptionId(busPrescription.getId());
        dualChannel.setRxStatus(DigitalRxStatusEnum.REVOKED.getStatus());
        dualChannel.setRxStatusName(DigitalRxStatusEnum.REVOKED.getDesc());
        dualChannel.setUpdateBy(busPrescription.getDoctorName());
        //如果审核通过，需要调用双通道接口撤销
        if (PrescriptionStatus.PASS.equals(busPrescription.getStatus())) {
            long start = System.currentTimeMillis();
            DigitalRxRevokeDTO revokeDTO = dualChannelDigitalRxParamHelper.buildRevokeDTO(busPrescription);
            R<BaseDigitalRxStatusDTO> result = dualChannelRxRpcHelper.revoke(revokeDTO);
            log.info("处方撤销,请求参数：{}, 结果：{}, 耗时：{}", revokeDTO, result, System.currentTimeMillis() - start);
            if (Objects.isNull(result) || !result.isSuccess()) {
                throw new ServiceException("处方作废失败, " + result.getMsg());
            }
            BaseDigitalRxStatusDTO statusDTO = result.getData();
            if (Objects.nonNull(statusDTO)) {
                dualChannel.setRxStatus(statusDTO.getRxStatus());
                dualChannel.setRxStatusName(statusDTO.getRxStatusName());
            }
        }
        busPrescriptionDualChannelMapper.updateById(dualChannel);
    }

    /**
     * 获取处方的有效时长
     *
     * @param reviewTime 审批时间
     * @param validTime  处方有效时间
     * @param settings   处方设置
     * @return 处方有效时长
     */
    private Integer getPrescriptionEffectiveHours(Date reviewTime, Date validTime, BusConsultationSettings settings) {
        if (Objects.isNull(reviewTime) || Objects.isNull(validTime)) {
            if (Objects.isNull(settings)) {
                return 24;
            }
            return settings.getPrescrptionOrderExpire();
        }
        //计算两个时间的秒数
        long second = TimeUnit.MILLISECONDS.toSeconds(validTime.getTime() - reviewTime.getTime());
        //秒数转小时(向上取整)
        return (int) Math.ceil((double)second / 3600);
    }


    /**
     * 获取复核列表
     *
     * @param dto 查询参数
     * @return 复核列表
     */
    @Override
    public List<BusPrescriptionVo> getList(BusDispensingDTO dto) {
        // 获取未复核列表
        if (DispensingQueryTypeEnum.UN_DISPENSING.ordinal() == dto.getDispensingQueryType()) {
            return getDispensingList(dto);
        }
        // 获取已复核列表
        return getDispensedList(dto);
    }

    /**
     * 查询所有未复核的处方列表
     *
     * @param dto 查询参数
     * @return 处方列表
     */
    public List<BusPrescriptionVo> getDispensingList(BusDispensingDTO dto) {
        // 需求如此 - 搜索条件为空时 返回空
        if (StringUtils.isEmpty(dto.getPatientName()) && StringUtils.isEmpty(dto.getIdCardNo()) && StringUtils.isEmpty(dto.getPhoneNumber())) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getStatus, PrescriptionStatusEnum.PASS.getStatus())
                .eq(BusPrescription::getDispensingStatus, DispensingStatusEnum.DISPENSING_PENDING.getCode())
                .eq(BusPrescription::getHospitalId, dto.getHospitalId())
                .like(StrUtil.isNotEmpty(dto.getPatientName()), BusPrescription::getFamilyName, dto.getPatientName())
                .and(StrUtil.isNotEmpty(dto.getIdCardNo()) || StrUtil.isNotEmpty(dto.getPhoneNumber()), wrapper -> wrapper
                        .like(StrUtil.isNotEmpty(dto.getIdCardNo()), BusPrescription::getFamilyIdcard, dto.getIdCardNo())
                        .or()
                        .like(StrUtil.isNotEmpty(dto.getPhoneNumber()), BusPrescription::getFamilyTel, dto.getPhoneNumber()))
                .orderByAsc(BusPrescription::getReviewTime);
        return getPrescriptionVOS(queryWrapper);
    }

    /**
     * 查询已审核通过的处方列表
     *
     * @param dto 查询参数
     * @return 处方列表
     */
    public List<BusPrescriptionVo> getDispensedList(BusDispensingDTO dto) {
        if (dto.getEndTime() != null) {
            dto.setEndTime(DateUtils.addDays(dto.getEndTime(), 1));
        }
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getHospitalId, dto.getHospitalId())
                .eq(BusPrescription::getDispensingPharmacistId, SecurityUtils.getUserId())
                .eq(BusPrescription::getDispensingStatus, DispensingStatusEnum.DISPENSING_PASS.getCode())
                .like(StrUtil.isNotEmpty(dto.getPatientName()), BusPrescription::getFamilyName, dto.getPatientName())
                .and(StrUtil.isNotEmpty(dto.getIdCardNo()) || StrUtil.isNotEmpty(dto.getPhoneNumber()), wrapper -> wrapper
                        .like(StrUtil.isNotEmpty(dto.getIdCardNo()), BusPrescription::getFamilyIdcard, dto.getIdCardNo())
                        .or()
                        .like(StrUtil.isNotEmpty(dto.getPhoneNumber()), BusPrescription::getFamilyTel, dto.getPhoneNumber()))
                .ge(dto.getStartTime() != null, BusPrescription::getDispensingTime, dto.getStartTime())
                .le(dto.getEndTime() != null, BusPrescription::getDispensingTime, dto.getEndTime())
                .orderByDesc(BusPrescription::getDispensingTime);
        return getPrescriptionVOS(queryWrapper);
    }

    private List<BusPrescriptionVo> getPrescriptionVOS(LambdaQueryWrapper<BusPrescription> queryWrapper) {
        List<BusPrescription> busPrescriptions = busPrescriptionMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(busPrescriptions)) {
            return Collections.emptyList();
        }
        List<BusPrescriptionVo> vos = busPrescriptions.stream().map(p -> {
            BusPrescriptionVo vo = new BusPrescriptionVo();
            BeanUtils.copyProperties(p, vo);
            List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectDrugsList(p);
            vo.setList(busPrescriptionDrugs);
            return vo;
        }).collect(Collectors.toList());
        return PageUtil.buildPage(busPrescriptions, vos);
    }

    /**
     * 复核审核接口
     *
     * @param dto 参数
     * @return 结果
     */
    @Override
    public Integer dispensingPrescription(BusDispensingDTO dto) {
        // 获取复核药师信息
        BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectById(dto.getDispensingPharmacistId());
        // 参数校验
        checkReq(dto, busOfficinaPharmacist);
        BusPrescription update = new BusPrescription();
        // 审核未通过
        if (DispensingStatusEnum.DISPENSING_NOT_PASS.getCode().equals(dto.getDispensingStatus())) {
            // 组装更新处方数据
            assembledData(update, dto, DispensingStatusEnum.DISPENSING_NOT_PASS, busOfficinaPharmacist);
        }
        // 审核通过
        if (DispensingStatusEnum.DISPENSING_PASS.getCode().equals(dto.getDispensingStatus())) {
            // 组装更新处方数据
            assembledData(update, dto, DispensingStatusEnum.DISPENSING_PASS, busOfficinaPharmacist);
        }
        return busPrescriptionMapper.updateById(update);
    }

    private void checkReq(BusDispensingDTO dto, BusOfficinaPharmacist busOfficinaPharmacist) {
        if (Objects.isNull(busOfficinaPharmacist)) {
            throw new ServiceException("请联系管理员！");
        }
        // 获取复核（调剂）药师签名
        BusSignature busSignature = getSignature(busOfficinaPharmacist);
        if (Objects.isNull(busSignature) || StringUtils.isEmpty(busSignature.getCertSignature())) {
            throw new ServiceException("请联系管理员进行签名认证！");
        }
        // 获取处方信息
        BusPrescription busPrescription = busPrescriptionMapper.selectById(dto.getId());
        if (Objects.isNull(busPrescription)) {
            throw new ServiceException("未找到该处方");
        }
        if (!DispensingStatusEnum.DISPENSING_PENDING.getCode().equals(busPrescription.getDispensingStatus())) {
            throw new ServiceException("该处方已审核");
        }
    }

    private BusSignature getSignature(BusOfficinaPharmacist busOfficinaPharmacist) {
        LambdaQueryWrapper<BusSignature> queryWrapper = new LambdaQueryWrapper<BusSignature>()
                .eq(BusSignature::getObjectId, busOfficinaPharmacist.getId())
                .eq(BusSignature::getObjectType, SignatureRole.CHEMIST.getValue())
                .last("limit 1");
        return busSignatureMapper.selectOne(queryWrapper);
    }

    private static void assembledData(BusPrescription update, BusDispensingDTO dto, DispensingStatusEnum reviewStatus, BusOfficinaPharmacist busOfficinaPharmacist) {
        update.setId(dto.getId());
        update.setDispensingStatus(reviewStatus.getCode());
        update.setDispensingTime(new Date());
        update.setDispensingPharmacistId(busOfficinaPharmacist.getId());
        update.setDispensingPharmacistName(busOfficinaPharmacist.getPharmacistName());
    }


}
