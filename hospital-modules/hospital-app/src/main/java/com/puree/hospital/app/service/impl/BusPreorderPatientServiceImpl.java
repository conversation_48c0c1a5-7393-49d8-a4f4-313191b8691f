package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.model.event.order.DrugsPreOrderAutoCancelEvent;
import com.puree.hospital.app.api.model.event.preorder.DrugPreOrderConfirmEvent;
import com.puree.hospital.app.api.model.event.preorder.DrugPreOrderCreateEvent;
import com.puree.hospital.app.consultation.helper.BusQuickConsultationHelper;
import com.puree.hospital.app.domain.BusBizDepartment;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorPatient;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusDrugs;
import com.puree.hospital.app.domain.BusEnterpriseDrugs;
import com.puree.hospital.app.domain.BusHospitalOfficina;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPatientReportPhysician;
import com.puree.hospital.app.domain.BusPreorderDoctor;
import com.puree.hospital.app.domain.BusPreorderDrugs;
import com.puree.hospital.app.domain.BusPreorderPatient;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusQuickConsultation;
import com.puree.hospital.app.domain.BusShopCart;
import com.puree.hospital.app.domain.GuardianConfig;
import com.puree.hospital.app.domain.dto.BusPreorderDto;
import com.puree.hospital.app.domain.dto.BusPreorderPatientDrugsDto;
import com.puree.hospital.app.domain.dto.BusPreorderPatientDto;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.vo.BusContactPatientResultVO;
import com.puree.hospital.app.domain.vo.BusGuardianVO;
import com.puree.hospital.app.domain.vo.BusPreorderPatientVo;
import com.puree.hospital.app.helper.GuardianHelper;
import com.puree.hospital.app.helper.SysDictDataHelper;
import com.puree.hospital.app.mapper.BusBizDepartmentMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDrugsMapper;
import com.puree.hospital.app.mapper.BusEnterpriseDrugsMapper;
import com.puree.hospital.app.mapper.BusHospitalOfficinaMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPreorderDoctorMapper;
import com.puree.hospital.app.mapper.BusPreorderDrugsMapper;
import com.puree.hospital.app.mapper.BusPreorderPatientMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusQuickConsultationMapper;
import com.puree.hospital.app.mapper.BusShopCartMapper;
import com.puree.hospital.app.prescription.config.PrescriptionAutomationConfig;
import com.puree.hospital.app.prescription.helper.PrescriptionHelper;
import com.puree.hospital.app.queue.producer.event.order.DrugsPreOrderAutoCancelEventProducer;
import com.puree.hospital.app.service.IBusConsultationPackageService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusDoctorPatientService;
import com.puree.hospital.app.service.IBusPatientReportPhysicianService;
import com.puree.hospital.app.service.IBusPreorderPatientService;
import com.puree.hospital.app.service.IBusShopCartService;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DoctorRoleEnum;
import com.puree.hospital.common.core.enums.FollowUpStatusEnum;
import com.puree.hospital.common.core.enums.ImGroupType;
import com.puree.hospital.common.core.enums.PreOrderStatusEnum;
import com.puree.hospital.common.core.enums.ShopCartTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.system.api.model.SysDictData;
import com.puree.hospital.system.api.model.constant.SysDictTypeConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用药预订单服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPreorderPatientServiceImpl implements IBusPreorderPatientService {

    private final BusPreorderPatientMapper busPreorderPatientMapper;
    private final BusPreorderDoctorMapper busPreorderDoctorMapper;
    private final BusPreorderDrugsMapper busPreorderDrugsMapper;
    private final BusDrugsMapper busDrugsMapper;
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final IBusConsultationPackageService consultationPackageService;
    private final BusPatientFamilyMapper patientFamilyMapper;
    private final BusDoctorMapper doctorMapper;
    private final BusBizDepartmentMapper bizDepartmentMapper;
    private final BusHospitalOfficinaMapper busHospitalOfficinaMapper;
    private final BusEnterpriseDrugsMapper busEnterpriseDrugsMapper;
    private final IBusPatientReportPhysicianService iBusPatientReportPhysicianService;
    private final IBusDoctorPatientService busDoctorPatientService;
    private final BusQuickConsultationMapper busQuickConsultationMapper;
    private final IBusShopCartService busShopCartService;
    private final ApplicationEventPublisher publisher;
    private final PrescriptionHelper prescriptionHelper;
    private final BusShopCartMapper busShopCartMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final SysDictDataHelper sysDictDataHelper;
    private final GuardianHelper guardianHelper;

    @Lazy
    @Resource
    private DrugsPreOrderAutoCancelEventProducer drugsPreOrderAutoCancelEventProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(BusPreorderPatientDrugsDto dto) {
        log.info("新增预订单参数 data={}",JSON.toJSONString(dto));
        List<BusPreorderDrugs> drugsList = dto.getDrugsList();
        //校验药品信息
        checkPreOrderDrugs(drugsList, dto.getHospitalId());
        //将就诊人的信息从Dto中拿出来
        BusPreorderPatient preorderPatient = new BusPreorderPatient();
        preorderPatient.setHospitalId(dto.getHospitalId());
        preorderPatient.setPatientId(dto.getPatientId());
        preorderPatient.setStatus("0");
        if (StringUtils.isNotEmpty(dto.getPartnersCode())) {
            preorderPatient.setPartnersCode(dto.getPartnersCode());
        }
        preorderPatient.setCreateBy(SecurityUtils.getUsername());
        preorderPatient.setCreateTime(DateUtils.getNowDate());
        //将就诊人信息存入数据库
        busPreorderPatientMapper.insert(preorderPatient);
        for (BusPreorderDrugs item : drugsList) {
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(DateUtils.getNowDate());
            item.setPreorderId(preorderPatient.getId());
        }
        //插入药品
        busPreorderDrugsMapper.insert(drugsList);
        //用药预订单创建事件
        DrugPreOrderCreateEvent event = new DrugPreOrderCreateEvent();
        event.setHospitalId(dto.getHospitalId());
        event.setPreOrderId(preorderPatient.getId());
        publisher.publishEvent(event);
        return preorderPatient.getId();
    }

    @Override
    public List<BusPreorderPatientVo> queryMedicationConsultationList(BusPreorderPatientDto dto) {
        List<BusPreorderPatientVo> busPreorderPatientVos = busPreorderPatientMapper.selectMedicationConsultationList(dto);
        if (CollectionUtil.isNotEmpty(busPreorderPatientVos)) {
            busPreorderPatientVos.forEach(v -> v.setFamilyAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM" +
                    "-dd", v.getDateOfBirth()))));
            busPreorderPatientVos.stream().map(BusPreorderPatientVo::getPreorderDrugs).forEach(b -> b.forEach(p -> {
                if (Objects.nonNull(p.getDrugsId())){
                    log.info("药品ID={}", p.getDrugsId());
                    BusDrugs busDrugs = busDrugsMapper.selectById(p.getDrugsId());
                    if (StringUtils.isNotEmpty(busDrugs.getMainImg()) && StringUtils.isNotEmpty(busDrugs.getMainImg())
                            && StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail()) &&  StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail())){
                        p.setDrugsImg(busDrugs.getMainImg()+","+busDrugs.getDrugsImgDetail());
                    }else if(StringUtils.isNotEmpty(busDrugs.getMainImg()) && StringUtils.isNotEmpty(busDrugs.getMainImg())){
                        p.setDrugsImg(busDrugs.getMainImg());
                    }else if ( StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail()) &&  StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail())){
                        p.setDrugsImg(busDrugs.getDrugsDetails());
                    }
                    p.setMinMakeUnit(busDrugs.getMinMakeUnit());
                    p.setMinPackNum(busDrugs.getMinPackNum());
                    p.setMinPackUnit(busDrugs.getMinPackUnit());
                    if (StringUtils.isNotEmpty(p.getDrugsImg()) && p.getDrugsImg().contains(",")) {
                        String[] split = p.getDrugsImg().split(",");
                        p.setDrugsImg(split[0]);
                    } else {
                        p.setDrugsImg(p.getDrugsImg());
                    }
                }
            }));
        }
        return busPreorderPatientVos;
    }

    @Override
    public BusPreorderPatientVo queryMedicationConsultation(BusPreorderPatientDto dto) {
        BusPreorderPatientVo busPreorderPatientVo = busPreorderPatientMapper.selectMedicationConsultation(dto.getId());
        if (Objects.nonNull(busPreorderPatientVo)) {
            Date dateOfBirth = busPreorderPatientVo.getDateOfBirth();
            busPreorderPatientVo.setFamilyAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd",dateOfBirth)));
            List<BusPreorderDrugs> preorderDrugs = busPreorderPatientVo.getPreorderDrugs();
            Map<Long, SysDictData> packageUnitMap = sysDictDataHelper.getDictDataMap(SysDictTypeConstant.DRUGS_PACKAGING_UNIT);
            preorderDrugs.forEach(p -> {
                if (Objects.nonNull(p.getDrugsId())){
                    BusDrugs busDrugs = busDrugsMapper.selectById(p.getDrugsId());
                    String ancestors = busDrugsMapper.selectDrugsDictClassify(busDrugs.getClassifyId());
                    if (Objects.nonNull(ancestors)){
                        if ("0".equals(ancestors)){
                            p.setAncestorsId(Long.valueOf(ancestors));
                        }else{
                            String[] split = ancestors.split(",");
                            p.setAncestorsId(Long.valueOf(split[1]));
                        }
                    }
                    if (StringUtils.isNotEmpty(busDrugs.getMainImg()) && StringUtils.isNotEmpty(busDrugs.getMainImg())
                            && StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail()) &&  StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail())){
                        p.setDrugsImg(busDrugs.getMainImg()+","+busDrugs.getDrugsImgDetail());
                    }else if(StringUtils.isNotEmpty(busDrugs.getMainImg()) && StringUtils.isNotEmpty(busDrugs.getMainImg())){
                        p.setDrugsImg(busDrugs.getMainImg());
                    }else if ( StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail()) &&  StringUtils.isNotEmpty(busDrugs.getDrugsImgDetail())){
                        p.setDrugsImg(busDrugs.getDrugsDetails());
                    }
                    p.setMinMakeUnit(busDrugs.getMinMakeUnit());
                    p.setMinPackNum(busDrugs.getMinPackNum());
                    p.setMinPackUnit(busDrugs.getMinPackUnit());
                    p.setDrugsPackagingUnit(busDrugs.getDrugsPackagingUnit());
                    if (Objects.nonNull(p.getDrugsPackagingUnit())) {
                        SysDictData sysDictData = packageUnitMap.get(p.getDrugsPackagingUnit());
                        if (Objects.nonNull(sysDictData)) {
                            p.setDrugsPackagingUnitName(sysDictData.getDictLabel());
                        }
                    }
                    if (StringUtils.isNotEmpty(p.getDrugsImg()) && p.getDrugsImg().contains(",")) {
                        String[] split = p.getDrugsImg().split(",");
                        p.setDrugsImg(split[0]);
                    }else{
                        p.setDrugsImg(p.getDrugsImg());
                    }
                }

                //药房或配送企业库存
                Long drugsId = p.getDrugsId();
                Integer totalStock = 0;
                //药房库存
                BusHospitalOfficina hospitalOfficina = busHospitalOfficinaMapper.selectOne(new LambdaQueryWrapper<BusHospitalOfficina>()
                    .eq(BusHospitalOfficina::getHospitalId, busPreorderPatientVo.getHospitalId())
                    .eq(BusHospitalOfficina::getDrugsId, drugsId));
                if (Objects.nonNull(hospitalOfficina)) {
                    totalStock = hospitalOfficina.getStock();
                    Long enterpriseId = hospitalOfficina.getEnterpriseId();
                    if (Objects.nonNull(enterpriseId)) {
                        BusEnterpriseDrugs busEnterpriseDrugs = busEnterpriseDrugsMapper.selectStock(enterpriseId, drugsId, dto.getHospitalId());
                        if (Objects.nonNull(busEnterpriseDrugs) && Objects.nonNull(busEnterpriseDrugs.getStock())) {
                            Integer stock = busEnterpriseDrugs.getStock();
                            totalStock = totalStock + stock;
                        }
                    }
                    log.info("返回库存数量,stock={}", totalStock);
                }
                p.setStock(totalStock);
            });
            // 查询业务科室id
            LambdaQueryWrapper<BusPreorderDoctor> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusPreorderDoctor::getHospitalId, dto.getHospitalId())
                    .eq(BusPreorderDoctor::getDoctorId, dto.getDoctorId())
                    .eq(BusPreorderDoctor::getDoctorRole, DoctorRoleEnum.DOCTOR.getCode());
            BusPreorderDoctor busPreorderDoctor = busPreorderDoctorMapper.selectOne(queryWrapper.last(" limit 1"));
            if (Objects.nonNull(busPreorderDoctor)) {
                busPreorderPatientVo.setDepartmentId(busPreorderDoctor.getBizDepartmentId());
            }

            // 儿童就诊人查询监护人信息
            GuardianConfig guardianConfig = guardianHelper.getGuardianConfig(dto.getHospitalId());
            if (guardianConfig != null && guardianConfig.isEnableChangeAge() && guardianHelper.isChild(dateOfBirth, guardianConfig)) {
                BusGuardianVO busGuardianInfo = guardianHelper.getBusGuardianInfo(busPreorderPatientVo.getFamilyId(), busPreorderPatientVo.getPatientId());
                if (Objects.isNull(busGuardianInfo)) {
                    return busPreorderPatientVo;
                }
                busPreorderPatientVo.setBusGuardianInfo(busGuardianInfo);
            }

        }
        return busPreorderPatientVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusContactPatientResultVO contactPatient(BusDoctorPatientGroup group) {
        log.info("联系患者 req={}", JSON.toJSONString(group));
        group.setType(ImGroupType.INQUIRIES.getCode());
        BusDoctorPatientGroup checkGroup = busDoctorPatientGroupService.checkGroup(group);
        Long groupId;
        BusConsultationOrder consultationOrder = new BusConsultationOrder();
        consultationOrder.setOrderType(CodeEnum.NO.getCode());
        consultationOrder.setHospitalId(group.getHospitalId());
        consultationOrder.setDoctorId(group.getDoctorId());
        consultationOrder.setFamilyId(group.getFamilyId());
        consultationOrder.setPatientId(group.getPatientId());
        consultationOrder.setDepartmentId(group.getDepartmentId());
        consultationOrder.setRound(3);
        BusPatientFamily patientFamily = patientFamilyMapper.selectById(group.getFamilyId());
        if (Objects.isNull(patientFamily)){
            throw new ServiceException("就诊人为空");
        }
        consultationOrder.setFamilyName(patientFamily.getName());
        consultationOrder.setFamilyAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd",patientFamily.getDateOfBirth())));
        consultationOrder.setFamilySex(patientFamily.getSex());
        BusDoctor busDoctor = doctorMapper.selectById(group.getDoctorId());
        if (Objects.isNull(busDoctor)){
            throw new ServiceException("医生为空");
        }
        consultationOrder.setPhoto(busDoctor.getPhoto());
        consultationOrder.setDoctorName(busDoctor.getFullName());
        String title = doctorMapper.selectDoctorTitle(busDoctor.getTitle());
        consultationOrder.setTitle(title);
        BusBizDepartment bizDepartment = bizDepartmentMapper.selectById(group.getDepartmentId());
        if (Objects.isNull(bizDepartment)){
            throw new ServiceException("业务科室为空");
        }
        consultationOrder.setDepartmentName(bizDepartment.getDepartmentName());
        // 更新预定单负责医生
        BusPreorderPatient preorderPatient = new BusPreorderPatient();
        preorderPatient.setId(group.getPreorderPatientId());
        preorderPatient.setDoctorId(group.getDoctorId());
        // 如果预定单已被医生负责，抛出异常
        BusPreorderPatient patient = busPreorderPatientMapper.selectById(group.getPreorderPatientId());
        if (Objects.nonNull(patient.getDoctorId()) && !group.getDoctorId().equals(patient.getDoctorId())){
            throw new ServiceException("该用药预定单已被其他医生负责!");
        }
        //校验是否绑定医生
        BusDoctorPatient doctorPatient = new BusDoctorPatient();
        doctorPatient.setDoctorId(group.getDoctorId());
        doctorPatient.setPatientId(group.getPatientId());
        doctorPatient.setHospitalId(group.getHospitalId());
        doctorPatient.setDepartmentId(group.getDepartmentId());
        doctorPatient.setFamilyId(group.getFamilyId());
        BusDoctorPatient busPatient = busDoctorPatientService.selectInfo(doctorPatient);
        if (busPatient == null) {
            doctorPatient.setCreateTime(DateUtils.getNowDate());
            doctorPatient.setGrade(YesNoEnum.NO.getCode());
            busDoctorPatientService.insert(doctorPatient);
        }

        boolean flatFlow=true;
        if (Objects.isNull(checkGroup)) {
            groupId = busDoctorPatientGroupService.createGroup(group,CodeEnum.NO.getCode());
            flatFlow=false;
        }else if (CodeEnum.YES.getCode().equals(checkGroup.getDelFlag())) {
            //后台逻辑删除im创建
            group.setId(checkGroup.getId());
            busDoctorPatientGroupService.createGroup(group, checkGroup.getDelFlag());
            groupId = checkGroup.getId();
            //校验问诊包是否用完
            boolean b = consultationPackageService.checkOrderState(consultationOrder);
            if (b) {
                busPreorderPatientMapper.updateById(preorderPatient);
                return new BusContactPatientResultVO(groupId, null);
            }
        }else {
            groupId = checkGroup.getId();
            //校验问诊包是否用完
            boolean b = consultationPackageService.checkOrderState(consultationOrder);
            if (b) {
                busPreorderPatientMapper.updateById(preorderPatient);
                return new BusContactPatientResultVO(groupId, null);
            }
        }
        consultationPackageService.give(consultationOrder,flatFlow);
        busPreorderPatientMapper.updateById(preorderPatient);
        return new BusContactPatientResultVO(groupId, consultationOrder.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long reportContactPatient(BusDoctorPatientGroup group) {
        log.info("报告跟进联系患者 req={}", JSON.toJSONString(group));
        group.setType(ImGroupType.INQUIRIES.getCode());
        BusDoctorPatientGroup checkGroup = busDoctorPatientGroupService.checkGroup(group);
        Long groupId;
        BusConsultationOrder consultationOrder = new BusConsultationOrder();
        consultationOrder.setOrderType(CodeEnum.NO.getCode());
        consultationOrder.setHospitalId(group.getHospitalId());
        consultationOrder.setDoctorId(group.getDoctorId());
        consultationOrder.setFamilyId(group.getFamilyId());
        consultationOrder.setPatientId(group.getPatientId());
        consultationOrder.setDepartmentId(group.getDepartmentId());
        consultationOrder.setRound(3);
        BusPatientFamily patientFamily = patientFamilyMapper.selectById(group.getFamilyId());
        if (Objects.isNull(patientFamily)){
            throw new ServiceException("就诊人为空");
        }
        consultationOrder.setFamilyName(patientFamily.getName());
        consultationOrder.setFamilyAge(AgeUtil.getAgeDetail(DateUtils.parseDateToStr("yyyy-MM-dd",patientFamily.getDateOfBirth())));
        consultationOrder.setFamilySex(patientFamily.getSex());
        BusDoctor busDoctor = doctorMapper.selectById(group.getDoctorId());
        if (Objects.isNull(busDoctor)){
            throw new ServiceException("医生为空");
        }
        consultationOrder.setPhoto(busDoctor.getPhoto());
        consultationOrder.setDoctorName(busDoctor.getFullName());
        String title = doctorMapper.selectDoctorTitle(busDoctor.getTitle());
        consultationOrder.setTitle(title);
        BusBizDepartment bizDepartment = bizDepartmentMapper.selectById(group.getDepartmentId());
        if (Objects.isNull(bizDepartment)){
            throw new ServiceException("业务科室为空");
        }
        consultationOrder.setDepartmentName(bizDepartment.getDepartmentName());
        //校验是否绑定医生
        BusDoctorPatient doctorPatient = new BusDoctorPatient();
        doctorPatient.setDoctorId(group.getDoctorId());
        doctorPatient.setPatientId(group.getPatientId());
        doctorPatient.setHospitalId(group.getHospitalId());
        doctorPatient.setDepartmentId(group.getDepartmentId());
        doctorPatient.setFamilyId(group.getFamilyId());
        BusDoctorPatient busPatient = busDoctorPatientService.selectInfo(doctorPatient);
        if (busPatient == null) {
            doctorPatient.setCreateTime(DateUtils.getNowDate());
            doctorPatient.setGrade(YesNoEnum.NO.getCode());
            busDoctorPatientService.insert(doctorPatient);
        }

        //修改报告跟进状态
        BusPatientReportPhysician busPatientReportPhysician = new BusPatientReportPhysician();
        busPatientReportPhysician.setId(group.getPatientReportId());
        //跟进状态
        busPatientReportPhysician.setFollowUpStatus(Integer.parseInt(FollowUpStatusEnum.FOLLOW_UP.getCode()));
        //跟进时间
        busPatientReportPhysician.setFollowUpTimeStart(DateUtils.getNowDate());
        iBusPatientReportPhysicianService .updateStatus(busPatientReportPhysician);
        log.info("报告跟进联系患者,修改报告跟进状态修改完成");
        boolean flatFlow=true;
        log.info("报告跟进联系患者,checkGroup：  "+checkGroup);
        if (Objects.isNull(checkGroup)) {
            groupId = busDoctorPatientGroupService.createGroup(group,CodeEnum.NO.getCode());
            flatFlow=false;
        }else if (CodeEnum.YES.getCode().equals(checkGroup.getDelFlag())) {
            //后台逻辑删除im创建
            group.setId(checkGroup.getId());
            busDoctorPatientGroupService.createGroup(group, checkGroup.getDelFlag());
            groupId = checkGroup.getId();
            //校验问诊包是否用完
            boolean b = consultationPackageService.checkOrderState(consultationOrder);
            if (b) {
                return groupId;
            }
        }else {
            groupId = checkGroup.getId();
            //校验问诊包是否用完
            boolean b = consultationPackageService.checkOrderState(consultationOrder);
            if (b) {
                return groupId;
            }
        }
        consultationPackageService.give(consultationOrder,flatFlow);
        return groupId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer confirm(BusPreorderDto dto) {
        //补充问诊信息
        BusQuickConsultation busQuickConsultation = getBusQuickConsultation(dto);
        BusQuickConsultationHelper.check(busQuickConsultation);
        busQuickConsultationMapper.insert(busQuickConsultation);
        // 补充预订单信息
        BusPreorderPatient busPreorderPatient = new BusPreorderPatient();
        busPreorderPatient.setId(dto.getPreorderPatientId());
        busPreorderPatient.setConsultationId(busQuickConsultation.getId());
        Long familyId = dto.getFamilyId();
        if (familyId == null) {
            throw new ServiceException("请选择就诊人信息");
        }
        BusPatientFamily patientFamily = patientFamilyMapper.selectById(familyId);
        if (patientFamily == null) {
            throw new ServiceException("请选择正确的就诊人信息");
        }
        if (StringUtils.isBlank(patientFamily.getIdNumber())) {
            throw new ServiceException("请完善就诊人信息后再提交订单");
        }
        busPreorderPatient.setFamilyId(familyId);
        busPreorderPatient.setDiagnosis(dto.getDiagnosis());
        List<String> diagnosisArchives = dto.getDiagnosisArchives();
        if (CollUtil.isNotEmpty(diagnosisArchives)) {
            String join = String.join(",", diagnosisArchives);
            busPreorderPatient.setDiagnosisArchives(join);
        }
        busPreorderPatientMapper.updateById(busPreorderPatient);
        // 添加进入购物车
        BusShopCart shopCart = new BusShopCart();
        shopCart.setType(ShopCartTypeEnum.ADVANCE_ORDER.getCode());
        shopCart.setPatientId(dto.getPatientId());
        shopCart.setHospitalId(dto.getHospitalId());
        shopCart.setBusinessId(busPreorderPatient.getId());
        shopCart.setCreateTime(DateUtils.getNowDate());
        busShopCartService.insert(shopCart);
        //发送事件
        DrugPreOrderConfirmEvent event = new DrugPreOrderConfirmEvent();
        event.setHospitalId(dto.getHospitalId());
        event.setPreOrderId(dto.getPreorderPatientId());
        event.setPatientId(dto.getPatientId());
        event.setFamilyId(dto.getFamilyId());
        publisher.publishEvent(event);
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void prescriptionCreated(BusPrescriptionDto prescriptionDto) {
        //1.更新用药预订单状态
        BusPreorderPatient busPreorderPatient = busPreorderPatientMapper.selectById(prescriptionDto.getPreorderId());
        if (busPreorderPatient == null || PreOrderStatusEnum.isPrescribed(busPreorderPatient.getStatus())) {
            return;
        }
        busPreorderPatient.setStatus(PreOrderStatusEnum.PRESCRIBED.getStatus());
        if (Objects.nonNull(prescriptionDto.getExpertId())) {
            busPreorderPatient.setDoctorId(prescriptionDto.getExpertId());
        } else {
            busPreorderPatient.setDoctorId(prescriptionDto.getDoctorId());
        }
        busPreorderPatientMapper.updateById(busPreorderPatient);
        //2.购物车处理
        BusShopCart shopCart = busShopCartMapper.selectOne(new LambdaQueryWrapper<BusShopCart>()
                .eq(BusShopCart::getHospitalId, busPreorderPatient.getHospitalId())
                .eq(BusShopCart::getPatientId, busPreorderPatient.getPatientId())
                .eq(BusShopCart::getType, ShopCartTypeEnum.ADVANCE_ORDER.getCode())
                .eq(BusShopCart::getBusinessId, busPreorderPatient.getId()));
        if (Objects.isNull(shopCart)) {
            BusShopCart cart = new BusShopCart();
            cart.setHospitalId(busPreorderPatient.getHospitalId());
            cart.setPatientId(busPreorderPatient.getPatientId());
            cart.setBusinessId(busPreorderPatient.getId());
            cart.setType(ShopCartTypeEnum.ADVANCE_ORDER.getCode());
            cart.setCreateTime(new Date());
            int i = busShopCartService.insert(cart);
            if (i > 0) {
                for (BusPrescriptionDrugs drug : prescriptionDto.getList()) {
                    BusShopCart drugCart = busShopCartMapper.selectOne(new LambdaQueryWrapper<BusShopCart>()
                            .eq(BusShopCart::getHospitalId, busPreorderPatient.getHospitalId())
                            .eq(BusShopCart::getPatientId, busPreorderPatient.getPatientId())
                            .eq(BusShopCart::getType, ShopCartTypeEnum.DRUGS.getCode())
                            .eq(BusShopCart::getBusinessId, drug.getDrugsId()));
                    if (Objects.nonNull(drugCart)) {
                        // 扣减药品数量大于购物车数量，删除
                        if (drug.getQuantity() >= drugCart.getQuantity()) {
                            busShopCartService.delete(Lists.newArrayList(drugCart.getId()));
                        } else {
                            drugCart.setQuantity(drugCart.getQuantity() - drug.getQuantity());
                            drugCart.setUpdateTime(new Date());
                            busShopCartMapper.updateById(drugCart);
                        }
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void expire(Long preorderId) {
        BusPreorderPatient preorderPatient = busPreorderPatientMapper.selectById(preorderId);
        if (Objects.isNull(preorderPatient) || !PreOrderStatusEnum.isPendingPrescribe(preorderPatient.getStatus())) {
            return;
        }
        int i = busPrescriptionMapper.countPreorderApproved(preorderId);
        if (i > 0) {
            return ;
        }
        // 查询药品集合
        LambdaQueryWrapper<BusPreorderDrugs> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusPreorderDrugs::getPreorderId, preorderId);
        List<BusPreorderDrugs> busPreorderDrugs = busPreorderDrugsMapper.selectList(queryWrapper);
        busPreorderPatientMapper.deleteById(preorderId) ;
        busPreorderDrugsMapper.delByPreorderId(preorderId) ;
        busShopCartMapper.delByBusinessId(preorderId) ;
        //预订单取消事件通知
        DrugsPreOrderAutoCancelEvent preOrderCancelEvent = new DrugsPreOrderAutoCancelEvent();
        preOrderCancelEvent.setJumpHomePage(Boolean.TRUE);
        preOrderCancelEvent.setHospitalId(preorderPatient.getHospitalId());
        preOrderCancelEvent.setPatientId(preorderPatient.getPatientId());
        preOrderCancelEvent.setPartnersCode(preorderPatient.getPartnersCode());
        preOrderCancelEvent.setTotalOrderNo(preorderPatient.getId().toString());
        if (CollUtil.isNotEmpty(busPreorderDrugs)) {
            String productName = busPreorderDrugs.stream().map(BusPreorderDrugs::getDrugsName).collect(Collectors.joining("、"));
            preOrderCancelEvent.setProductName(productName);
        }
        drugsPreOrderAutoCancelEventProducer.send(preOrderCancelEvent);
    }

    /**
     * 校验药品西悉尼
     *
     * @param drugsList 用药预订单药品集合
     */
    private void checkPreOrderDrugs(List<BusPreorderDrugs> drugsList, Long hospitalId) {
        if(CollectionUtil.isNotEmpty(drugsList)){
            drugsList.remove(null);
        }
        if (CollectionUtil.isEmpty(drugsList)) {
            throw new ServiceException("请选择需要购买的药品");
        }
        PrescriptionAutomationConfig automationConfig = prescriptionHelper.getAutomationConfig(hospitalId);
//        //如果开启自动创建, 则需要校验药品是否有配置基本信息
//        if (!Boolean.TRUE.equals(automationConfig.getAutoCreate())) {
//            return;
//        }
///        //查询药品基础信息
///        List<Long> drugIds = drugsList.stream().map(BusPreorderDrugs::getDrugsId).distinct().collect(Collectors.toList());
///        //2.查询药品信息
///        List<BusDrugs> busDrugList = busDrugsService.selectEffectiveByIds(drugIds);
///        Map<Long, BusDrugs> busDrugsMap = new HashMap<>(busDrugList.size());
///        if (CollectionUtil.isNotEmpty(busDrugList)) {
///            busDrugsMap.putAll(busDrugList.stream().collect(Collectors.toMap(BusDrugs::getId, Function.identity())));
///        }
///        drugsList.forEach(drugs -> {
///            String drugName = StringUtils.isNotEmpty(drugs.getStandardCommonName()) ? drugs.getStandardCommonName() : drugs.getDrugsName();
///            BusDrugs busDrugs = busDrugsMap.get(drugs.getDrugsId());
///            if (Objects.isNull(busDrugs)) {
///                throw new ServiceException(String.format("药品%s已下架", drugName));
///            }
///            if (Objects.isNull(busDrugs.getDrugsUsage())) {
///                throw new ServiceException(String.format("药品%s未设置给药途经(药品用法)", drugName));
///            }
///            if (StringUtils.isBlank(busDrugs.getMinMakeUnit())) {
///                throw new ServiceException(String.format("药品%s未设置最用药单位", drugName));
///            }
///            if (Objects.isNull(busDrugs.getBaseDose())) {
///                throw new ServiceException(String.format("药品%s未设置基本剂量", drugName));
///            }
///            if (StringUtils.isBlank(busDrugs.getBaseDoseUnit())) {
///                throw new ServiceException(String.format("药品%s未设置基本剂量单位", drugName));
///            }
///            if (Objects.isNull(busDrugs.getSingleDose())) {
///                throw new ServiceException(String.format("药品%s未配置每次用量", drugName));
///            }
///            if (StringUtils.isBlank(busDrugs.getDefaultFrequency())) {
///                throw new ServiceException(String.format("药品%s未配置默认频次", drugName));
///            }
///            if (Objects.isNull(busDrugs.getRecommendUseDays())) {
///                throw new ServiceException(String.format("药品%s未配置建议使用天数", drugName));
///            }
///        });

    }

    /**
     * 获取问诊信息
     *
     * @param dto 用药预订单确认请求参数
     * @return BusQuickConsultation
     */
    private @NotNull BusQuickConsultation getBusQuickConsultation(BusPreorderDto dto) {
        BusQuickConsultation busQuickConsultation = new BusQuickConsultation();
        busQuickConsultation.setHospitalId(dto.getHospitalId());
        busQuickConsultation.setPatientId(dto.getPatientId());
        busQuickConsultation.setFamilyId(dto.getFamilyId());
        busQuickConsultation.setAllergicHistory(dto.getAllergicHistory());
        busQuickConsultation.setAllergicDrugs(dto.getAllergicDrugs());
        busQuickConsultation.setPreviousIllness(dto.getPreviousIllness());
        busQuickConsultation.setAnamneses(dto.getAnamneses());
        busQuickConsultation.setPresentIllness(dto.getPresentIllness());
        busQuickConsultation.setIllness(dto.getIllness());
        busQuickConsultation.setMedicationHistory(dto.getMedicationHistory());
        busQuickConsultation.setCreateTime(DateUtils.getNowDate());
        busQuickConsultation.setFindMedicine(YesNoEnum.YES.getCode());
        //增加费别设置
        busQuickConsultation.setFeeSettleType(dto.getFeeSettleType());
        busQuickConsultation.setSpecialDiseaseInfo(dto.getSpecialDiseaseInfo());
        return busQuickConsultation;
    }

}
