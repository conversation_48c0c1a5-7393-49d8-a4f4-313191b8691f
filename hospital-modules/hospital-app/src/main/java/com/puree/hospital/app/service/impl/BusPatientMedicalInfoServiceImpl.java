package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.vo.BusPatientMedicalRecordListVO;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.domain.vo.BusExamineReportDetailVO;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.his.IPatientMedicalRecord;
import com.puree.hospital.app.his.constants.HisConstant;
import com.puree.hospital.app.his.domain.HisQueryContext;
import com.puree.hospital.app.his.domain.vo.BusEmrDetailVO;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;
import com.puree.hospital.app.his.helper.HospitalHisHelper;
import com.puree.hospital.app.service.IBusPatientMedicalInfoService;
import com.puree.hospital.common.core.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 患者就诊信息
 * <AUTHOR>
 * @date 2025-02-26 18:38
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPatientMedicalInfoServiceImpl implements IBusPatientMedicalInfoService {

    private final HospitalHisHelper hospitalHisHelper;
    private final List<IPatientMedicalRecord> channelHandlerList;

    /**
     * 获取患者就诊记录列表
     * @param dto 查询参数
     * @return 就诊记录列表
     **/
    @Override
    public List<BusPatientRegRecordVO> regList(BusPatientMedicalRecordDTO dto) {
        // 1.获取HIS渠道处理类
        IPatientMedicalRecord handler = getHisChannelHandler();
        if (handler == null) {
            return Collections.emptyList();
        }
        // 2.封装参数请求数据
        HisQueryContext hisQueryContext = createQueryContext(handler, dto,null);
        return handler.getPatientMedicalRecordList(hisQueryContext);
    }

    @Override
    public List<BusPatientMedicalRecordListVO> recordList(BusPatientMedicalRecordDTO dto) {
        // 1.获取HIS渠道处理类
        IPatientMedicalRecord handler = getHisChannelHandler();
        if (handler == null) {
            return Collections.emptyList();
        }
        // 2.封装参数请求数据
        HisQueryContext hisQueryContext = createQueryContext(handler, dto, null);
        return handler.getElectronicMedicalRecordItemList(hisQueryContext);
    }

    @Override
    public List<BusEmrDetailVO> getEmrDetail(String tranSerialNo, String patientName,String patientId) {
        // 1.获取HIS渠道处理类
        IPatientMedicalRecord handler = getHisChannelHandler();
        if (handler == null) {
            return Collections.emptyList();
        }
        // 2.封装参数请求数据
        BusPatientMedicalRecordDTO dto = new BusPatientMedicalRecordDTO();
        dto.setPatientId(Long.valueOf(patientId));
        HisQueryContext hisQueryContext = createQueryContext(handler, dto, tranSerialNo);
        hisQueryContext.setPatientName(patientName);
        return handler.getEmrDetail(hisQueryContext);
    }

    @Override
    public List<BusInspectReportDetail> inspectDetail(String reportId) {
        // 1.获取HIS渠道处理类
        IPatientMedicalRecord handler = getHisChannelHandler();
        if (handler == null) {
            return Collections.emptyList();
        }
        // 2.封装参数请求数据
        HisQueryContext hisQueryContext = createQueryContext(handler, null, reportId);
        return handler.getInspectDetail(hisQueryContext);
    }

    @Override
    public BusExamineReportDetailVO examineDetail(String reportId, String itemCode, String testOrder) {
        // 1.获取HIS渠道处理类
        IPatientMedicalRecord handler = getHisChannelHandler();
        if (handler == null) {
            return null;
        }
        // 2.封装参数请求数据
        BusPatientMedicalRecordDTO dto = new BusPatientMedicalRecordDTO();
        dto.setItemCode(itemCode);
        HisQueryContext hisQueryContext = createQueryContext(handler, dto, reportId);
        hisQueryContext.setTestOrder(testOrder);
        return handler.getExamineDetail(hisQueryContext);
    }

    @Override
    public BusPrescriptionInfoVO getPrescriptionInfo(BusPatientMedicalRecordDTO dto) {
        // 1.获取HIS渠道处理类
        IPatientMedicalRecord handler = getHisChannelHandler();
        if (handler == null) {
            return null;
        }
        // 2.封装参数请求数据
        HisQueryContext hisQueryContext = createQueryContext(handler, dto, null);
        return handler.getPrescriptionInfo(hisQueryContext);
    }

    /**
     * 获取 HIS 渠道处理类
     * @return IPatientMedicalRecord 或 null
     */
    public IPatientMedicalRecord getHisChannelHandler() {
        Long hospitalId = SecurityUtils.getHospitalId();
        boolean enabled = hospitalHisHelper.getHospitalHisEnableStatus(SecurityUtils.getHospitalId());
        if (!enabled) {
            if (log.isDebugEnabled()) {
                log.debug("医院 {} 没有启用 HIS", hospitalId);
            }
            return null;
        }
        String hisChannel = hospitalHisHelper.getHospitalHisChannel(hospitalId);
        if (StrUtil.isEmpty(hisChannel)) {
            log.warn("医院 {} 没有配置 HIS 渠道", hospitalId);
            return null;
        }
        if (CollectionUtil.isEmpty(channelHandlerList)) {
            return null;
        }
        // 遍历所有 HIS 处理类，找到匹配的
        for (IPatientMedicalRecord channelHandler : channelHandlerList) {
            if (channelHandler.getChannelName().equalsIgnoreCase(hisChannel)) {
                return channelHandler;
            }
        }
        return null;
    }

    /**
     *  创建 HisQueryContext
     * @param handler       处理类
     * @param dto           查询条件
     * @param reportId      报告ID，查详情
     */
    private HisQueryContext createQueryContext(IPatientMedicalRecord handler, BusPatientMedicalRecordDTO dto, String reportId) {
        HisQueryContext context = new HisQueryContext();
        context.setHospitalId(SecurityUtils.getHospitalId());
        // 设置查询参数：需要传递人员信息；此处进行填充
        if (Objects.nonNull(dto)) {
            context.setQueryDto(dto);
            if (dto.getPatientId() != null) {
                hospitalHisHelper.setPatientFamily(context);
            }
        }
        // 设置根据ID查询的参数
        if (Objects.nonNull(reportId)) {
            context.setReportId(reportId);
        }
        // 目前除东软外，其余需要填充His系统凭证信息
        if (!HisConstant.HisChannel.DR.name().equals(handler.getChannelName())) {
            hospitalHisHelper.setHisCredentialConfig(context);
        }
        return context;
    }

}

