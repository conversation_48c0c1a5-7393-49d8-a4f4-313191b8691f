package com.puree.hospital.app.service;

import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.vo.BusPatientMedicalRecordListVO;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.domain.vo.BusExamineReportDetailVO;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.his.domain.vo.BusEmrDetailVO;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;

import java.util.List;

/**
 * 患者就诊信息
 * <AUTHOR>
 * @date 2025-02-26 18:38
 **/
public interface IBusPatientMedicalInfoService {

    /**
     * 获取患者就诊记录列表
     * @param dto 查询参数
     * @return 就诊记录列表
     **/
    List<BusPatientRegRecordVO> regList(BusPatientMedicalRecordDTO dto);

    /**
     * 获取患者医疗记录列表
     * @param dto 查询参数
     * @return 患者医疗记录列表
     **/
    List<BusPatientMedicalRecordListVO> recordList(BusPatientMedicalRecordDTO dto);

    /**
     *  获取患者病历详情
     * @param tranSerialNo  门诊流水号
     * @param patientName   患者姓名
     * @return 病历详情
     */
    List<BusEmrDetailVO> getEmrDetail(String tranSerialNo, String patientName,String patientId);

    /**
     * 获取患者检查记录详情
     * @param reportId 报告ID
     * @return 检查记录详情
     **/
    List<BusInspectReportDetail> inspectDetail(String reportId);

    /**
     * 获取患者检验记录详情
     * @param reportId 报告ID
     * @return 检验记录详情
     **/
    BusExamineReportDetailVO examineDetail(String reportId, String itemCode, String testOrder);

    /**
     * 获取患者处方详情
     * @param dto 查询参数
     * @return 处方详情
     */
    BusPrescriptionInfoVO getPrescriptionInfo(BusPatientMedicalRecordDTO dto);
}
