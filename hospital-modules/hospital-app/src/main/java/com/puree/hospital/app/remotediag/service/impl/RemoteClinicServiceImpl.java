package com.puree.hospital.app.remotediag.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import com.puree.hospital.app.remotediag.domain.DiagnosisInfo;
import com.puree.hospital.app.remotediag.domain.RemoteClinic;
import com.puree.hospital.app.remotediag.domain.RemoteDiagnosis;
import com.puree.hospital.app.remotediag.domain.RemoteDiagnosisDoctorPatient;
import com.puree.hospital.app.remotediag.domain.RemoteDiagnosisInvitedDoctor;
import com.puree.hospital.app.remotediag.domain.RemoteDiagnosisPatient;
import com.puree.hospital.app.remotediag.domain.RemoteDiagnosisVideoInfo;
import com.puree.hospital.app.remotediag.domain.dto.RemoteClinicDTO;
import com.puree.hospital.app.remotediag.domain.dto.RemoteClinicOptDTO;
import com.puree.hospital.app.remotediag.domain.dto.RemoteDiagnosisListDTO;
import com.puree.hospital.app.remotediag.domain.enums.RemoteDiagnosisDoctorActionEnum;
import com.puree.hospital.app.remotediag.domain.enums.RemoteDiagnosisDoctorRoleEnum;
import com.puree.hospital.app.remotediag.domain.enums.RemoteDiagnosisStatusEnum;
import com.puree.hospital.app.remotediag.domain.enums.RemoteDiagnosisTypeEnum;
import com.puree.hospital.app.remotediag.domain.query.RemoteClinicQuery;
import com.puree.hospital.app.remotediag.domain.vo.RemoteClinicListVO;
import com.puree.hospital.app.remotediag.domain.vo.RemoteClinicVO;
import com.puree.hospital.app.remotediag.domain.vo.RemoteDiagnosisPatientVO;
import com.puree.hospital.app.remotediag.mapper.RemoteDiagnosisDoctorPatientMapper;
import com.puree.hospital.app.remotediag.mapper.RemoteDiagnosisInvitedDoctorMapper;
import com.puree.hospital.app.remotediag.mapper.RemoteDiagnosisMapper;
import com.puree.hospital.app.remotediag.mapper.RemoteDiagnosisPatientMapper;
import com.puree.hospital.app.remotediag.service.IRemoteClinicService;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.IdCardNumberUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.im.api.callback.domain.VideoStreamingEvent;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/4 14:54
 * @description 远程门诊 服务实现
 */
@Slf4j
@RefreshScope
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RemoteClinicServiceImpl implements IRemoteClinicService {

    private final RemoteDiagnosisDoctorPatientMapper remoteDiagnosisDoctorPatientMapper;
    private final RemoteDiagnosisInvitedDoctorMapper remoteDiagnosisInvitedDoctorMapper;
    private final RemoteDiagnosisMapper remoteDiagnosisMapper;
    private final RemoteDiagnosisPatientMapper remoteDiagnosisPatientMapper;
    private final IBusCommunicationMessageService busCommunicationMessageService;
    private final PureeRabbitProducer pureeRabbitProducer;
    private static final String DATETIME_FORMAT = "yyyyMMddHHmmssSSS";

    /**
     * 未更新url的视频所持续的最大分钟数
     */
    @Value("${remoteDiagnosis.noUrlVideo.maxMinute}")
    private Long maxMinutes;

    /**
     * 分页查询远程门诊申请记录
     *
     * @param query 查询条件
     * @return 门诊申请列表
     */
    @Override
    public List<RemoteClinicListVO> clinicList(RemoteClinicQuery query) {
        PageUtil.startPage();
        List<RemoteDiagnosisListDTO> result ;
        if(RemoteDiagnosisDoctorRoleEnum.isInitiator(query.getRole())){
            //我发起的门诊申请列表
            result = remoteDiagnosisMapper.getInitiatorList(SecurityUtils.getUserId(), query.getHospitalId());
        }else{
            //我收到的门诊申请列表
            result = remoteDiagnosisMapper.getReceiverList(SecurityUtils.getUserId(), query.getHospitalId());
        }

        // VO返回处理
        List<RemoteClinicListVO> vos = result.stream()
                .map(item -> {
                    RemoteClinicListVO vo = BeanUtil.copyProperties(item, RemoteClinicListVO.class);
                    vo.setDiagnosis(handleDiagnosis(item.getDiagnosis()));
                    vo.setAge(DateUtil.ageOfNow(item.getDateOfBirth()));
                    vo.setInvitedDoctorIds(item.convertDoctorIds());
                    return vo;
                })
                .collect(Collectors.toList());

        return PageUtil.buildPage(result, vos);
    }

    /**
     * 处理中西医诊断，转换为字符串列表
     *
     * @param diagnosis 诊断列表
     * @return 门诊申请列表
     */
    private List<String> handleDiagnosis(List<DiagnosisInfo> diagnosis){
        return diagnosis.stream().map(DiagnosisInfo::getDiagnosisName).collect(Collectors.toList());
    }

    /**
     * 远程门诊申请单提交
     *
     * @param dto 远程门诊申请单
     * @return 记录id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer add(RemoteClinicDTO dto) {
        log.debug("远程门诊申请单提交信息：{}", dto);
        //校验并处理患者信息
        validateAndProcessPatient(dto);
        //添加医生与患者的关联关系
        addDoctorPatientRelation(dto);
        //创建远程诊断记录
        RemoteDiagnosis remoteDiagnosis = addRemoteDiagnosisRecord(dto);
        //添加受邀医生
        addInvitedDoctor(dto, remoteDiagnosis.getId());
        //返回申请记录id
        return remoteDiagnosis.getId();
    }

    /**
     * 校验并处理患者信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void validateAndProcessPatient(RemoteClinicDTO dto) {
        //检查患者是否存在
        if (Objects.nonNull(dto.getPatientId())) {
            RemoteDiagnosisPatient patient = remoteDiagnosisPatientMapper.getById(dto.getPatientId());
            Assert.notNull(patient, "患者不存在");
        } else {
            //验证身份证并创建患者记录
            if (IdCardTypeEnum.ID_CARD.getLabel().equals(dto.getIdType()) && !IdCardNumberUtils.validateIdCard(dto.getIdNumber())) {
                log.error("身份证号：{}", dto.getIdNumber());
                throw new ServiceException("身份证号不合法");
            }
            RemoteDiagnosisPatient patientInfo = createPatientInfo(dto);
            remoteDiagnosisPatientMapper.insert(patientInfo);
            dto.setPatientId(patientInfo.getId());
        }
    }

    /**
     * 创建患者信息
     */
    private RemoteDiagnosisPatient createPatientInfo(RemoteClinicDTO dto) {
        RemoteDiagnosisPatient patientInfo = new RemoteDiagnosisPatient();
        patientInfo.setPatientName(dto.getPatientName());
        patientInfo.setCellPhoneNumber(dto.getCellPhoneNumber());
        patientInfo.setIdNumber(dto.getIdNumber());
        patientInfo.setIdType(IdCardTypeEnum.getByLabel(dto.getIdType()).getCode());
        patientInfo.setSex(dto.getSex());
        patientInfo.setCreateBy(SecurityUtils.getUsername());
        patientInfo.setUpdateBy(SecurityUtils.getUsername());
        patientInfo.setDateOfBirth(dto.getDateOfBirth());
        return patientInfo;
    }

    /**
     * 添加医生与患者的关联关系
     */
    @Transactional(rollbackFor = Exception.class)
    public void addDoctorPatientRelation(RemoteClinicDTO dto) {
        RemoteDiagnosisDoctorPatient doctorPatientRef = new RemoteDiagnosisDoctorPatient();
        doctorPatientRef.setHospitalId(dto.getHospitalId());
        doctorPatientRef.setDoctorId(SecurityUtils.getUserId());
        doctorPatientRef.setPatientId(dto.getPatientId());
        doctorPatientRef.setCreateBy(SecurityUtils.getUsername());
        remoteDiagnosisDoctorPatientMapper.insertOrUpdate(doctorPatientRef);
    }

    /**
     * 创建远程诊断记录
     */
    @Transactional(rollbackFor = Exception.class)
    public RemoteDiagnosis addRemoteDiagnosisRecord(RemoteClinicDTO dto) {
        RemoteDiagnosis rd = BeanUtil.copyProperties(dto,RemoteDiagnosis.class);
        //recordNum生成规则：RCL/RCO_医院id_yyMMdHHmmssSSS_random(4)
        String recordNum = String.format("%s_%s_%s_%s", RemoteDiagnosisTypeEnum.CLINIC.getAbbr(), dto.getHospitalId(),
                DateUtil.format(new Date(), DATETIME_FORMAT), RandomUtil.randomNumbers(4));
        rd.setRecordNum(recordNum);
        rd.setApplyDoctorId(SecurityUtils.getUserId());
        rd.setDiagStatus(RemoteDiagnosisStatusEnum.APPLIED.getName());
        rd.setRemoteType(RemoteDiagnosisTypeEnum.CLINIC.getName());
        rd.setIsReported(false);
        //获取roomId房间号
        rd.setRoomId(busCommunicationMessageService.generateGroupId().intValue());
        rd.setCreateBy(SecurityUtils.getUsername());
        rd.setUpdateBy(SecurityUtils.getUsername());
        log.debug("远程门诊记录入库：{}", rd);

        try {
            remoteDiagnosisMapper.insert(rd);
        }catch (Exception e){
            log.error("远程门诊申请单插入异常！", e);
            throw new ServiceException("操作失败，请重试！");
        }
        return rd;
    }

    /**
     * 添加受邀医生
     */
    @Transactional(rollbackFor = Exception.class)
    public void addInvitedDoctor(RemoteClinicDTO dto, Integer diagId) {
        RemoteDiagnosisInvitedDoctor invitedDoctor = new RemoteDiagnosisInvitedDoctor();
        invitedDoctor.setHospitalId(dto.getInvitedHospitalId());
        invitedDoctor.setDiagId(diagId);
        invitedDoctor.setDoctorId(dto.getInvitedDoctorId());
        invitedDoctor.setDeptId(dto.getInvitedDeptId());
        remoteDiagnosisInvitedDoctorMapper.insert(invitedDoctor);
    }

    /**
     * 医生端：分页获取历史患者列表
     *
     * @param query 查询条件
     * @return 历史患者列表
     */
    @Override
    public List<RemoteDiagnosisPatientVO> patientList(RemoteClinicQuery query) {
        query.setDoctorId(SecurityUtils.getUserId());
        List<RemoteDiagnosisPatient> result = remoteDiagnosisDoctorPatientMapper.getPatientList(query);

        //VO返回处理
        return result.stream()
                .map(item -> {
                    RemoteDiagnosisPatientVO vo = BeanUtil.copyProperties(item, RemoteDiagnosisPatientVO.class);
                    vo.setFamilyAge(String.format("%s岁", DateUtil.ageOfNow(item.getDateOfBirth())));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 医生端：接受/拒绝申请
     *
     * @param dto 操作
     * @return 操作是否成功
     */
    @Override
    public Boolean processClinicApply(com.puree.hospital.app.remotediag.domain.dto.RemoteClinicOptDTO dto) {
        // 从数据库查询申请记录，并做状态校验
        RemoteDiagnosis remoteDiagnosis = remoteDiagnosisMapper.getById(dto.getId());
        Assert.notNull(remoteDiagnosis, "申请记录不存在");
        if(!RemoteDiagnosisStatusEnum.isApplied(remoteDiagnosis.getDiagStatus())){
            log.error("申请记录状态不正确：{}", remoteDiagnosis);
            throw new ServiceException("申请记录状态不正确");
        }

        RemoteClinicOptDTO record = BeanUtil.copyProperties(dto, RemoteClinicOptDTO.class);
        //设置旧状态
        record.setOldStatus(remoteDiagnosis.getDiagStatus());
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setStatus(RemoteDiagnosisDoctorActionEnum.isAccept(dto.getAction()) ? RemoteDiagnosisStatusEnum.AGREED.getName() : RemoteDiagnosisStatusEnum.REFUSED.getName());
        return remoteDiagnosisMapper.updateStatus(record) > 0;
    }

    /**
     * 医生端：申请单结束
     *
     * @param id 申请记录id
     * @return 操作是否成功
     */
    @Override
    public Boolean finish(Integer id) {
        // 获取申请记录，并校验状态是否符合要求
        RemoteDiagnosis remoteDiagnosis = remoteDiagnosisMapper.getById(id);
        Assert.notNull(remoteDiagnosis, "申请记录不存在");
        if(!RemoteDiagnosisStatusEnum.isDiagnosing(remoteDiagnosis.getDiagStatus())){
            log.error("申请记录状态不正确：{}", remoteDiagnosis);
            throw new ServiceException("申请记录状态不正确");
        }
        // 更新状态
        RemoteClinicOptDTO optStatus = new RemoteClinicOptDTO();
        optStatus.setId(id);
        optStatus.setOldStatus(remoteDiagnosis.getDiagStatus());
        optStatus.setFinishTime(new Date());
        optStatus.setUpdateBy(SecurityUtils.getUsername());
        optStatus.setStatus(RemoteDiagnosisStatusEnum.FINISHED.getName());
        int count = remoteDiagnosisMapper.updateStatus(optStatus);

        //视频数据校验：是否都已上传至OSS
        boolean finished = allVideosFinished(remoteDiagnosis.getVideoUrl());
        //所有视频已就绪 && 未上报监管 --> 上报监管
        if(count >0 && finished && !remoteDiagnosis.getIsReported()){
            //监管事件类型：远程门诊
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(remoteDiagnosis.getHospitalId(), RegulatoryEventTypeEnum.REMOTE_VISIT, String.valueOf(remoteDiagnosis.getId()));
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
            log.info("远程门诊-申请单结束操作-上报监管：{}", remoteDiagnosis);
            remoteDiagnosisMapper.updateReportStatus(id, true);
        }
        return count > 0;
    }

    /**
     * 判断所有视频是否都已上传OSS完毕
     *
     * @param videoList 视频列表
     * @return 是否全部都已完成
     */
    @Override
    public boolean allVideosFinished(List<RemoteDiagnosisVideoInfo> videoList){
        //校验所有视频是否都已上传OSS完毕
        return videoList.stream().allMatch(video -> {
            //上传完成或视频状态错误，返回true
            if(VideoStreamingEvent.EventType.isFinished(video.getStatus()) || Objects.isNull(VideoStreamingEvent.getEventTypeByValue(video.getStatus()))){
                return true;
            }
            //如果未更新url的视频超过了设定的最大时间（单位为分钟），返回true，否则返回false
            return DateUtil.between(video.getUpdateTime(), new Date(), DateUnit.MINUTE) >= maxMinutes;
        });
    }

    /**
     * 医生端：获取申请单详情
     *
     * @param id 申请记录id
     * @return 申请单详情
     */
    @Override
    public RemoteClinicVO getById(Integer id) {

        // 获取申请记录
        RemoteDiagnosis remoteDiagnosis = remoteDiagnosisMapper.getById(id);
        Assert.notNull(remoteDiagnosis, "申请记录不存在");
        RemoteClinicVO vo = BeanUtil.copyProperties(remoteDiagnosis,RemoteClinicVO.class);
        vo.setDiagnosis(handleDiagnosis(remoteDiagnosis.getDiagnosis()));

        //获取患者信息
        RemoteDiagnosisPatient patient = remoteDiagnosisPatientMapper.getById(remoteDiagnosis.getPatientId());
        Assert.notNull(patient, "患者不存在");

        // 填充患者信息
        vo.setPatientName(patient.getPatientName());
        vo.setDateOfBirth(patient.getDateOfBirth());
        vo.setSex(patient.getSex());
        vo.setAge(DateUtil.ageOfNow(patient.getDateOfBirth()));
        return vo;
    }

    /**
     * 获取远程门诊详情（远程调用）
     * @param id 记录id
     * @return 详情
     */
    @Override
    public RemoteClinic getInfoById(Integer id) {
        //获取远程门诊记录
        RemoteDiagnosis diagnosis = remoteDiagnosisMapper.getById(id);
        RemoteClinic remoteClinic = BeanUtil.copyProperties(diagnosis,RemoteClinic.class);
        remoteClinic.setDiagnosis(handleDiagnosis(diagnosis.getDiagnosis()));
        //获取患者信息
        RemoteDiagnosisPatient patient = remoteDiagnosisPatientMapper.getById(diagnosis.getPatientId());
        remoteClinic.setPatientInfo(BeanUtil.copyProperties(patient, RemoteClinic.PatientInfo.class));
        remoteClinic.getPatientInfo().setAge(DateUtil.ageOfNow(patient.getDateOfBirth()));
        //获取受邀医生列表
        List<RemoteDiagnosisInvitedDoctor> doctors = remoteDiagnosisInvitedDoctorMapper.getList(new RemoteDiagnosisInvitedDoctor().setDiagId(diagnosis.getId()));
        remoteClinic.setDoctorInfoList(BeanUtil.copyToList(doctors, RemoteClinic.DoctorInfo.class));
        log.info("获取远程门诊详情-结果信息：{}", remoteClinic);
        return remoteClinic;
    }


}
