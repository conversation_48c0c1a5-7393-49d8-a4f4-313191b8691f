package com.puree.hospital.app.third.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.dto.PrescriptionPharmacistSignDTO;
import com.puree.hospital.app.domain.vo.BusPrescriptionVo;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.app.third.domain.ThirdPartyPrescriptionQueryDTO;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.CheckedException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.common.security.annotation.SignatureVerify;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 第三方-处方相关接口
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/27 下午4:35
 */
@RequestMapping("/3rd/prescription")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ThirdPartyPrescriptionController extends BaseThirdPartyController {

    private final IBusPrescriptionService busPrescriptionService;

    private final OSSUtil ossUtil;

    /**
     * 处方列表查询
     * 注意：方法一定要使用@SignatureVerify注解，帮忙做签名校验，注意加了注解
     *
     * @return 处方量列表查询
     */
    @GetMapping("list")
    @SignatureVerify(signParam = SignatureVerify.SignParamEnum.QUERY_STRING)
    public Paging<List<BusPrescriptionVo>> queryList(ThirdPartyPrescriptionQueryDTO queryDTO, HttpServletRequest request) {
        startPage();
        if (queryDTO == null) {
            return new Paging<>();
        }
        BusPrescription busPrescription = new BusPrescription();
        BeanUtil.copyProperties(queryDTO, busPrescription);
        //避免跨医院查询，需要根据appKey进行拦截
        busPrescription.setHospitalId(getHospitalId(request));
        List<BusPrescriptionVo> list = busPrescriptionService.selectList(busPrescription);
        if (CollectionUtil.isNotEmpty(list)) {
            //path增加oss前面的地址
            list.forEach(this::setFullPath);
        }
        return PageUtil.buildPage(list);
    }

    /**
     * 查询处方详情
     *
     * @param prescriptionId     处方id
     * @param prescriptionNumber 处方编号
     * @param request HttpServletRequest
     * @return 处方详情
     */
    @GetMapping("/detail")
    @SignatureVerify(signParam = SignatureVerify.SignParamEnum.QUERY_STRING)
    public R<BusPrescriptionVo> getDetail(@RequestParam(value = "prescriptionNumber", required = false) String prescriptionNumber,
                                          @RequestParam(value = "prescriptionId", required = false) Long prescriptionId,
                                          HttpServletRequest request) {
        if (Objects.isNull(prescriptionId) && StringUtils.isBlank(prescriptionNumber)) {
            return R.fail("处方id和处方号不能同时为空");
        }
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setId(prescriptionId);
        busPrescription.setPrescriptionNumber(prescriptionNumber);
        busPrescription.setHospitalId(getHospitalId(request));
        BusPrescriptionVo prescriptionVo = busPrescriptionService.select(busPrescription);
        setFullPath(prescriptionVo);
        return R.ok(prescriptionVo);
    }

    /**
     * 药师签名
     *
     * @param signDTO 签名信息
     * @return 签名结果
     */
    @PutMapping("/pharmacist-signature")
    @SignatureVerify(signParam = SignatureVerify.SignParamEnum.REQUEST_BODY)
    public R<Boolean> pharmacistSignature(@RequestBody PrescriptionPharmacistSignDTO signDTO, HttpServletRequest request) {
        if (Objects.isNull(signDTO)) {
            throw new CheckedException("处方药师签名参数不能为空");
        }
        if(Objects.isNull(signDTO.getPrescriptionId()) && StringUtils.isBlank(signDTO.getPrescriptionNumber())) {
            throw new CheckedException("待签名处方id或处方编号不能为空");
        }
        if(Objects.isNull(signDTO.getPharmacistIdCard())) {
            throw new CheckedException("处方签名药师的身份证好不能为空");
        }
        if (Objects.isNull(signDTO.getSignType())) {
            throw new CheckedException("处方医师签名类型不能为空");
        }
        signDTO.setHospitalId(getHospitalId(request));
        return R.ok(busPrescriptionService.pharmacistSignature(signDTO));
    }

    /**
     * 设置处方相关地址
     *
     * @param prescriptionVo 处方相应对象
     */
    private void setFullPath(BusPrescriptionVo prescriptionVo) {
        if (Objects.isNull(prescriptionVo)) {
            return;
        }
        if (StringUtils.isNotBlank(prescriptionVo.getPath())) {
            prescriptionVo.setPath(ossUtil.getFullPath(prescriptionVo.getPath()));
        }
        //设置药品图片地址增加oss前面的地址
        if (CollectionUtil.isNotEmpty(prescriptionVo.getList())) {
            prescriptionVo.getList().forEach(d -> {
                if (StringUtils.isNotBlank(d.getMainImg())) {
                    d.setMainImg(ossUtil.getFullPath(d.getMainImg()));
                }
                if (StringUtils.isNotBlank(d.getDrugsImg())) {
                    String[] image = d.getDrugsImg().split(",");
                    for (int i = 0; i < image.length; i++) {
                        image[i] = ossUtil.getFullPath(image[i]);
                    }
                    d.setDrugsImg(String.join(",", image));
                }
            });
        }
        if (CollectionUtil.isNotEmpty(prescriptionVo.getSignatureList())) {
            prescriptionVo.getSignatureList().forEach(s -> {
                if (StringUtils.isNotBlank(s.getCertSignature())) {
                    s.setCertSignature(ossUtil.getFullPath(s.getCertSignature()));
                }
            });
        }
        if (StringUtils.isNotBlank(prescriptionVo.getHospitalSeal())) {
            prescriptionVo.setHospitalSeal(ossUtil.getFullPath(prescriptionVo.getHospitalSeal()));
        }
    }

}
