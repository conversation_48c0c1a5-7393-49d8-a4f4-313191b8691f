package com.puree.hospital.app.prescription.sign;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.puree.hospital.app.api.model.enums.SignTypeEnum;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.dto.PrescriptionSignDTO;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 处方发货签名
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/13 16:46
 */
@Component
public class PrescriptionDeliverySignStrategy extends AbstractPrescriptionSignStrategy {

    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;

    @Override
    public String getOperatorName(PrescriptionSignDTO prescription) {
        return prescription.getDeliveryPharmacistName();
    }

    @Override
    public Long getOperatorId(PrescriptionSignDTO prescription) {
        return prescription.getDeliveryPharmacistId();
    }

    @Override
    protected String getSignFlag() {
        return "{{delivery_pharmacist}}";
    }

    @Override
    public SignTypeEnum getSignType() {
        return SignTypeEnum.DELIVERY;
    }

    @Override
    public int updateOperater(PrescriptionSignDTO prescription) {
        UpdateWrapper<BusPrescription> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", prescription.getPrescriptionId());
        BusPrescription updateEntity = new BusPrescription();
        updateWrapper.isNull("delivery_pharmacist_id")
                .isNull("delivery_pharmacist_name");
        updateEntity.setDeliveryPharmacistId(getOperatorId(prescription));
        updateEntity.setDeliveryPharmacistName(getOperatorName(prescription));
        return busPrescriptionMapper.update(updateEntity, updateWrapper);
    }

    @Override
    public String getDigestSignature(List<String> digestNames) {
        if(digestNames !=null && digestNames.size()>3)
        {
            return digestNames.get(3);
        }
        return null;
    }

    @Override
    public String getObjectType() {
        return "6";
    }
}
