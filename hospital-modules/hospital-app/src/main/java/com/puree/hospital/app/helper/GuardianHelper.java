package com.puree.hospital.app.helper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusGuardianDO;
import com.puree.hospital.app.domain.GuardianConfig;
import com.puree.hospital.app.domain.vo.BusGuardianVO;
import com.puree.hospital.app.mapper.BusGuardianMapper;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.util.Date;
import java.util.Objects;

/**
 * 监护人辅助类
 * <AUTHOR>
 * @date 2025/03/17 19:43
 */
@Component
@Slf4j
public class GuardianHelper {

    @Autowired
    private BusGuardianMapper busGuardianMapper;

    @Autowired
    private RemoteHospitalSettingApi hospitalSettingApi;

    private static final String FIELD_CONFIG_GUARDIAN_CONFIG = "fieldConfig.guardianConfig";

    /**
     * 判断就诊人是否为儿童
     * @param birthDate 出生日期
     * @param guardianConfig 监护人相关配置
     * @return 是否为儿童
     */
    public boolean isChild(Date birthDate, GuardianConfig guardianConfig) {
        if (birthDate == null) {
            return false;
        }
        String date = DateUtils.parseDateToStr(DatePattern.NORM_DATE_PATTERN, birthDate);
        LocalDate dateOfBirth = LocalDate.parse(date);
        LocalDate currentDate = LocalDate.now();
        int age = Period.between(dateOfBirth, currentDate).getYears();
        // 判断就诊人是否为儿童
        return age < guardianConfig.getChildrenAge();
    }

    /**
     * 获取监护人相关配置信息
     *
     * @param hospitalId
     * @return
     */
    public GuardianConfig getGuardianConfig(Long hospitalId) {
        // 获取配置信息
        R<String> settingValue = hospitalSettingApi.getSettingValue(FIELD_CONFIG_GUARDIAN_CONFIG, hospitalId);
        // 检查返回结果是否为空
        if (settingValue == null || settingValue.getData() == null) {
            return null;
        }
        GuardianConfig guardianConfig = JSONUtil.toBean(JSONUtil.parseObj(settingValue.getData()), GuardianConfig.class);
        log.info("获取监护人相关配置信息成功:{}", guardianConfig);
        return guardianConfig;
    }

    /**
     * 获取监护人相关信息
     *
     * @param familyId
     * @param patientId
     * @return
     */
    public BusGuardianVO getBusGuardianInfo(Long familyId, Long patientId) {
        BusGuardianDO busGuardian = busGuardianMapper.selectOne(new LambdaQueryWrapper<BusGuardianDO>()
                .eq(BusGuardianDO::getFamilyId, familyId)
                .eq(BusGuardianDO::getPatientId, patientId));
        if (Objects.isNull(busGuardian) || Objects.isNull(busGuardian.getIdType())) {
            return null;
        }
        BusGuardianVO guardian = BeanUtil.copyProperties(busGuardian, BusGuardianVO.class);
        guardian.setIdType(IdCardTypeEnum.getByCode(guardian.getIdType()).getLabel());
        return guardian;
    }


}
