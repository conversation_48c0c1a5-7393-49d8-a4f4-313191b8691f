package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusDoctorDepartment;
import com.puree.hospital.app.domain.vo.BusDoctorDepartmentVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 医生管理科室mapper
 */
public interface BusDoctorDepartmentMapper extends BaseMapper<BusDoctorDepartment> {
    /**
     * 批量插入
     * @return
     */
    int batchInsert(List<BusDoctorDepartment> list);
    List<BusDoctorDepartmentVo> selectList(BusDoctorDepartment busDoctorDepartment);
    List<BusDoctorDepartment> selectDeptList(@Param("hospitalId") Long hospitalId, @Param("doctorId") Long doctorId);
}
