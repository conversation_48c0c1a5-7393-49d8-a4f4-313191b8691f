package com.puree.hospital.app.prescription.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.app.api.model.event.prescription.PrescriptionCreatedEvent;
import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.PrescriptionGenerateResultDTO;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.helper.AppMessagePushHelper;
import com.puree.hospital.app.his.domain.HisClientConfig;
import com.puree.hospital.app.prescription.config.PrescriptionAutomationConfig;
import com.puree.hospital.app.prescription.config.PrescriptionMedicalDayLimitConfig;
import com.puree.hospital.app.prescription.config.WesternPrescriptionUnitConfig;
import com.puree.hospital.app.queue.producer.event.prescription.PrescriptionCreatedEventProducer;
import com.puree.hospital.app.service.IBusPatientMedicalInfoService;
import com.puree.hospital.app.service.IBusPreAuditPrescriptionService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 处方配置辅助类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/14 15:55
 */
@Slf4j
@Component
public class PrescriptionHelper {

    private static final String PRESCRIPTION_AUTOMATION_CONFIG_KEY = "setting.prescription-automation-config";

    /**
     * 限制开方天数配置key
     */
    private static final String PRESCRIPTION_DAY_LIMIT_CONFIG = "prescriptionSet.prescription-day-limitConfig";


    /**
     * 西药开方单次用量单位
     */
    private static final String PRESCRIPTION_SET_DRUGS_DOSE_UNIT = "prescriptionSet.drugs-dose-unit";

    /**
     * HIS配置
     */
    private static final String HOSPITAL_HIS_CONFIG = "HIS";

    /**
     * 就诊记录查询时长
     */
    private static final String HOSPITAL_MEDICAL_RECORD_KEY = "HIS.howLong.medicalRecord";

    /**
     * 购药开方
     */
    private static final String BUY_DRUGS_PRESCRIPTION_TYPE = "1";


    @Resource
    private RemoteHospitalSettingApi remoteHospitalSettingApi;

    @Resource
    private IBusPrescriptionService busPrescriptionService;

    @Resource
    private IBusPreAuditPrescriptionService busPreAuditPrescriptionService;

    @Lazy
    @Resource
    private PrescriptionCreatedEventProducer prescriptionCreatedEventProducer;

    @Resource
    private AppMessagePushHelper appMessagePushHelper;

    @Resource
    private IBusPatientMedicalInfoService busPatientMedicalInfoService;

    /**
     * 获取医院处方自动化配置
     *
     * @param hospitalId 医院id
     * @return 药品自动化配置
     */
    public PrescriptionAutomationConfig getAutomationConfig(Long hospitalId) {
        R<String> result = remoteHospitalSettingApi.getSettingValue(PRESCRIPTION_AUTOMATION_CONFIG_KEY, hospitalId);
        if(Objects.isNull(result) || !result.isSuccess()) {
            log.info("获取医院处方自动化配置失败 key = " + PRESCRIPTION_AUTOMATION_CONFIG_KEY + " hospitalId = {}", hospitalId);
            return new PrescriptionAutomationConfig();
        }
        List<PrescriptionAutomationConfig> configList = JSON.parseArray(result.getData(), PrescriptionAutomationConfig.class);
        if(Objects.isNull(configList) || configList.isEmpty()) {
            log.info("当前医院未配置处方自动化配置 key = " + PRESCRIPTION_AUTOMATION_CONFIG_KEY + " hospitalId = {}", hospitalId);
            return new PrescriptionAutomationConfig();
        }
        return configList.stream().findFirst().orElse(new PrescriptionAutomationConfig());
    }

    /**
     * 获取开方天数配置
     * @param hospitalId 医院id
     * @return PrescriptionMedicalDayLimitConfig
     */
    public PrescriptionMedicalDayLimitConfig getPrescriptionDayLimitConfig(Long hospitalId){
        R<String> result = remoteHospitalSettingApi.getSettingValue(PRESCRIPTION_DAY_LIMIT_CONFIG, hospitalId);
        if(Objects.isNull(result) || !result.isSuccess() || StringUtils.isBlank(result.getData())) {
            log.warn("获取医院处方自动化配置失败 key = " + PRESCRIPTION_DAY_LIMIT_CONFIG + " hospitalId = {}", hospitalId);
            return null;
        }
        return JSON.parseObject(result.getData(), PrescriptionMedicalDayLimitConfig.class);
    }

    /**
     * 获取西药开方用量单位 配置
     * @param hospitalId 医院id
     * @return WesternPrescriptionUnitConfig
     */
    public WesternPrescriptionUnitConfig getWesternPrescriptionUnitConfig(Long hospitalId){
        R<String> result = remoteHospitalSettingApi.getSettingValue(PRESCRIPTION_SET_DRUGS_DOSE_UNIT, hospitalId);
        if(Objects.isNull(result) || !result.isSuccess() || StringUtils.isBlank(result.getData())) {
            log.warn("获取 西药开方单次用量单位配置 失败 key = {} ,hospitalId = {}" ,PRESCRIPTION_SET_DRUGS_DOSE_UNIT , hospitalId);
            return null;
        }
        return JSON.parseObject(result.getData(), WesternPrescriptionUnitConfig.class);
    }


    /**
     * 获取随机延迟时间（ms）
     *
     * @param randStartTime 随机开始时间
     * @param randEndTime   随机结束时间
     * @return 随机延迟时间（ms）
     */
    public long getRandDelayTime(Long randStartTime, Long randEndTime) {
        log.info("获取随机延迟时间（s） randStartTime = {} randEndTime = {}", randStartTime, randEndTime);
        if (Objects.isNull(randStartTime) || randEndTime <= 0) {
            randStartTime = 5L;
        }
        if (Objects.isNull(randEndTime) || randEndTime <= 0) {
            randEndTime = 30L;
        }

        if (randEndTime <= randStartTime) {
            return randStartTime * 1000L;
        }
        return RandomUtil.randomLong(randStartTime, randEndTime) * 1000L;
    }

    /**
     * 处方创建
     *
     * @param dto 处方信息
     * @return 处方创建结果
     */
    public PrescriptionGenerateResultDTO create(BusPrescriptionDto dto) {
        // 问诊订单医生开处方需要先校验HIS配置
        checkHisClientConfig(dto);
        PrescriptionGenerateResultDTO resultDTO = busPrescriptionService.generate(dto);
        if (Objects.nonNull(resultDTO)) {
            log.info("生成处方返回参数{}", resultDTO);
            Long prescriptionId = resultDTO.getPrescriptionId();
            if (Objects.nonNull(prescriptionId)) {
                PrescriptionCreatedEvent createdEvent = new PrescriptionCreatedEvent();
                createdEvent.setPrescriptionId(prescriptionId);
                createdEvent.setEventType(PrescriptionCreatedEvent.EventType.CREATED);
                prescriptionCreatedEventProducer.send(createdEvent);
                //前置审方处理
                busPreAuditPrescriptionService.preAuditPrescription(prescriptionId, resultDTO, dto.getList());
                //前置审方完成后，且初审审核才进行推送消息
                if (!resultDTO.isRejected() && needSendPharmacistNotification(dto)) {
                    appMessagePushHelper.sendPharmacistNotification(dto.getHospitalId(), prescriptionId, dto.getPrescriptionType());
                }
            }
        }
        return resultDTO;
    }

    /**
     * 校验HIS配置
     */
    private void checkHisClientConfig(BusPrescriptionDto dto) {
        // 是否为问诊开方 1为购药开方
        if (BUY_DRUGS_PRESCRIPTION_TYPE.equals(dto.getIdentity())) {
            return;
        }
        // 互联网咨询 - 开区紧急方案
        Long hospitalId = SecurityUtils.getHospitalId();
        String configValue = remoteHospitalSettingApi.getSettingValue(HOSPITAL_HIS_CONFIG, hospitalId).getData();
        HisClientConfig hisClientConfig = JSON.parseObject(configValue, HisClientConfig.class);
        if (Objects.isNull(hisClientConfig)) {
            return;
        }
        if (hisClientConfig.isConnected() && hisClientConfig.isVisitHisRecord() && hisClientConfig.isWithoutHisRecord() && !hisClientConfig.isPermissionToPrescribe()) {
            // 查询就诊人是否有线下就诊记录
            BusPatientMedicalRecordDTO query = new BusPatientMedicalRecordDTO();
            query.setPatientId(dto.getFamilyId());
            String time = remoteHospitalSettingApi.getSettingValue(HOSPITAL_MEDICAL_RECORD_KEY, hospitalId).getData();
            DateTime beginDate = DateUtil.parse(DateUtil.format(DateUtils.addMonths(new Date(), -Integer.parseInt(time)), DatePattern.NORM_DATETIME_PATTERN));
            query.setBeginDate(beginDate);
            query.setEndDate(new Date());
            List<BusPatientRegRecordVO> busPatientRegRecordVOS = busPatientMedicalInfoService.regList(query);
            if (CollUtil.isEmpty(busPatientRegRecordVOS)) {
                throw new ServiceException("当前患者为互联网咨询患者，不可开处方!");
            }
        }
    }

    /**
     * 是否需要推送药师通知
     *
     * @param dto 处方信息
     * @return 是否需要推送药师通知
     */
    private boolean needSendPharmacistNotification(BusPrescriptionDto dto) {
        PrescriptionAutomationConfig automationConfig = getAutomationConfig(dto.getHospitalId());
        // 1.非用户找药开方  2.患者找药开方,但是没有开启自动审方
        return !Objects.equals(1, dto.getIsPrescribe())
                || (Objects.equals(1, dto.getIsPrescribe()) && !Boolean.TRUE.equals(automationConfig.getAutoApprove()));
    }
}
