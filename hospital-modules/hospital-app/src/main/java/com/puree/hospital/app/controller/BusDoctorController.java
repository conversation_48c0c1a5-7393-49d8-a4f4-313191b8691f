package com.puree.hospital.app.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.model.vo.BusSignatureVO;
import com.puree.hospital.app.constant.ConsultationPriorStatus;
import com.puree.hospital.app.domain.BusBizDepartment;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorAudit;
import com.puree.hospital.app.domain.BusDoctorConsultation;
import com.puree.hospital.app.domain.BusDoctorDepartment;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusDoctorPatient;
import com.puree.hospital.app.domain.BusDoctorTdl;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.BusHospitalWechatConfig;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.bo.BusDoctorBO;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.domain.dto.BusDoctorPatientDto;
import com.puree.hospital.app.domain.dto.CommunicationMessageDTO;
import com.puree.hospital.app.domain.dto.DoctorAndPatientDTO;
import com.puree.hospital.app.domain.dto.DoctorConsultationDto;
import com.puree.hospital.app.domain.dto.DoctorDTO;
import com.puree.hospital.app.domain.dto.ImDualChannelAuthDTO;
import com.puree.hospital.app.domain.dto.ImRecommendedDoctorDTO;
import com.puree.hospital.app.domain.dto.QrCodeDto;
import com.puree.hospital.app.domain.vo.BizDepartmentTreeSelect;
import com.puree.hospital.app.domain.vo.BusBizDepartmentVo;
import com.puree.hospital.app.domain.vo.BusDoctorBasicInfoVo;
import com.puree.hospital.app.domain.vo.BusDoctorPatientVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.BusHospitalSmsConfigVo;
import com.puree.hospital.app.domain.vo.DoctorPatientInfoVo;
import com.puree.hospital.app.helper.DoctorMessageNotificationIntervalHelper;
import com.puree.hospital.app.mapper.BusDoctorDepartmentMapper;
import com.puree.hospital.app.service.IBusBizDepartmentService;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDoctorConsultationService;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.app.service.IBusDoctorPatientService;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusDoctorTdlService;
import com.puree.hospital.app.service.IBusHospitalService;
import com.puree.hospital.app.service.IBusHospitalSmsConfigService;
import com.puree.hospital.app.service.IBusHospitalWechatConfigService;
import com.puree.hospital.app.service.IQrcodeService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationEnum;
import com.puree.hospital.common.core.enums.DoctorPracticeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.security.annotation.InnerAuth;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.MsgBody;
import com.puree.hospital.im.api.model.MsgContent;
import com.puree.hospital.im.api.model.SendMessageRequest;
import com.puree.hospital.im.api.model.event.ImSendMessageEvent;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.tool.api.RemoteSmsNotificationService;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 医生相关控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/doctor")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(BusDoctorController.class);

    private final IQrcodeService qrcodeService;
    private final IBusDoctorConsultationService doctorConsultationService;
    private final IBusDoctorService doctorService;
    private final IBusDoctorPatientService busDoctorPatientService;
    private final IBusConsultationOrderService consultationOrderService;
    private final IBusDoctorTdlService busDoctorTdlService;
    private final IBusBizDepartmentService busBizDepartmentService;
    private final IBusHospitalWechatConfigService busHospitalWechatConfigService;
    private final IBusHospitalSmsConfigService busHospitalSmsConfigService;
    private final IBusHospitalService busHospitalService;
    private final BusDoctorDepartmentMapper busDoctorDepartmentMapper;
    private final RemoteImService remoteImService;
    private final IBusDoctorService iBusDoctorService;
    private final IBusCommunicationMessageService busCommunicationMessageService;
    private final RemoteSmsNotificationService remoteSmsNotificationService;
    private final IBusDoctorHospitalService busDoctorHospitalService;
    private final DoctorMessageNotificationIntervalHelper doctorMessageNotificationIntervalHelper;

    /**
     * 查询所有医生信息
     * @param dto 条件查询参数
     * @return 医生信息列表
     */
    @GetMapping("/list")
    @Log(title = "查询所有医生信息")
    public AjaxResult list(BusDoctorDto dto){
        List<BusDoctorBO> busDoctorBOS = busDoctorHospitalService.list(dto);
        List<BusDoctorVo> list = new ArrayList<>();
        busDoctorBOS.forEach(b -> {
            BusDoctorVo vo = new BusDoctorVo();
            BeanUtils.copyProperties(b, vo);
            list.add(vo);
        });
        return AjaxResult.success(list);
    }


    /**
     * 获取医生名片二维码
     *
     * @param qrCodeDto
     * @return
     */
    @PostMapping("/home/<USER>")
    @Log(title = "获取医生名片二维码", businessType = BusinessType.OTHER)
    public AjaxResult getDoctorCard(@RequestBody QrCodeDto qrCodeDto) {
        String url = "";
        try {
            url = qrcodeService.createTempTicket(qrCodeDto);
        } catch (Exception ex) {

        }
        return AjaxResult.success(url);
    }

    /**
     * 获取医生名片小程序码
     *
     * @param qrCodeDto
     * @return
     */
    @PostMapping("/home/<USER>")
    @Log(title = "获取医生名片小程序码")
    public R<String> getDoctorCardAppletQrCode(@RequestBody QrCodeDto qrCodeDto) {
        if (logger.isDebugEnabled()) {
            logger.debug("获取医生名片小程序码入参：{}", qrCodeDto);
        }
        return R.ok(qrcodeService.createAppletQrCode(qrCodeDto));
    }

    /**
     * 医生关联科室树结构
     *
     * @param busDoctorDepartment
     * @return
     */
    @Log(title = "医生关联科室树结构")
    @GetMapping("/home/<USER>/tree")
    public AjaxResult getDepartmentTree(BusDoctorDepartment busDoctorDepartment) {
        List<BusBizDepartmentVo> busDepartments = doctorService.selectDoctorLeftDepartmentList(busDoctorDepartment);
        List<BizDepartmentTreeSelect> list = doctorService.buildDepartmentsTreeSelect(busDepartments);
        // 根据医生id查询医生信息
        BusDoctorVo doctor = doctorService.selectDoctorInfoById(busDoctorDepartment.getDoctorId());
        list.forEach(d -> {
            d.setFullName(doctor.getFullName());
            d.setTitle(doctor.getTitleValue());
            d.setPhoto(doctor.getPhoto());
        });
        return AjaxResult.success(list);
    }

    /**
     * 问诊设置列表
     *
     * @param dto
     * @return
     */
    @Log(title = "问诊设置列表")
    @GetMapping("/my/consultationSettings/list")
    public AjaxResult consultationList(DoctorConsultationDto dto) {
        List<BusDoctorConsultation> hospitalPrior = new ArrayList<>();
        // 查询医生问诊信息
        List<BusDoctorConsultation> doctorPrior = doctorConsultationService.selectConsultationList(dto);
        // 查询医生是否为多点
        BusDoctorHospital doctorHospital = doctorService.selectBusDoctorHospital(dto);
        // 获取医院统一设置的次数和分钟
        String[] graphicSplit = dto.getGraphic().split(",");
        String[] videoSplit = dto.getVideo().split(",");
        BigDecimal furtherConsultationPrice = dto.getFurtherConsultationPrice();
        List<Integer> collect = doctorPrior.stream().map(BusDoctorConsultation::getType).collect(Collectors.toList());
        if (!collect.contains(ConsultationEnum.GRAPHIC.getCode())) {
            BusDoctorConsultation graphic1 = new BusDoctorConsultation();
            graphic1.setType(ConsultationEnum.GRAPHIC.getCode());
            graphic1.setCost(Double.valueOf(graphicSplit[0]));
            graphic1.setReplySettings(graphicSplit[1]);
            graphic1.setStatus(YesNoEnum.YES.getCode());
            graphic1.setPool(String.valueOf(YesNoEnum.YES.getCode()));
            graphic1.setFreeClinic(false);
            doctorPrior.add(graphic1);
        }
        if (!collect.contains(ConsultationEnum.VIDEO.getCode())) {
            BusDoctorConsultation video1 = new BusDoctorConsultation();
            video1.setType(ConsultationEnum.VIDEO.getCode());
            video1.setCost(Double.valueOf(videoSplit[0]));
            video1.setReplySettings(videoSplit[1]);
            video1.setStatus(YesNoEnum.YES.getCode());
            video1.setPool(String.valueOf(YesNoEnum.YES.getCode()));
            video1.setFreeClinic(false);
            doctorPrior.add(video1);
        }
        if (!collect.contains(ConsultationEnum.FURTHER.getCode())) {
            BusDoctorConsultation further = new BusDoctorConsultation();
            further.setType(ConsultationEnum.FURTHER.getCode());
            if (ObjectUtil.isNotNull(furtherConsultationPrice)) {
                further.setCost(furtherConsultationPrice.doubleValue());
            }
            further.setReplySettings(graphicSplit[1]);
            further.setStatus(YesNoEnum.YES.getCode());
            further.setPool(String.valueOf(YesNoEnum.YES.getCode()));
            further.setFreeClinic(false);
            doctorPrior.add(further);
        }
        if ((ObjectUtil.isNotNull(doctorHospital) &&
                (doctorHospital.getIsThisCourt().equals(DoctorPracticeEnum.SPECIAL_INVITATION.getCode())
                        || doctorHospital.getIsThisCourt().equals(ConsultationPriorStatus.MULTIPOINT)))
                //多点医生或医生优先
                || dto.getPrior().equals(ConsultationPriorStatus.DOCTOR_PRIOR)) {
            doctorPrior.forEach(d -> {
                // 图文
                if (d.getType().equals(ConsultationEnum.GRAPHIC.getCode())) {
                    d.setReplySettings(graphicSplit[1]);
                // 视频
                } else if (d.getType().equals(ConsultationEnum.VIDEO.getCode())) {
                    d.setReplySettings(videoSplit[1]);
                } else {
                    d.setReplySettings(graphicSplit[1]);
                }
            });
            return AjaxResult.success(doctorPrior);
        } else { // 医院优先
            doctorPrior.forEach(d -> {
                if (ConsultationEnum.GRAPHIC.getCode().equals(d.getType())) {
                    BusDoctorConsultation graphic = new BusDoctorConsultation();
                    graphic.setId(d.getId());
                    graphic.setType(ConsultationEnum.GRAPHIC.getCode());
                    graphic.setStatus(d.getStatus());
                    graphic.setCost(Double.valueOf(graphicSplit[0]));
                    graphic.setReplySettings(graphicSplit[1]);
                    graphic.setPool(d.getPool());
                    graphic.setFreeClinic(d.getFreeClinic());
                    hospitalPrior.add(graphic);
                } else if (ConsultationEnum.VIDEO.getCode().equals(d.getType())) {
                    BusDoctorConsultation video = new BusDoctorConsultation();
                    video.setId(d.getId());
                    video.setType(ConsultationEnum.VIDEO.getCode());
                    video.setStatus(d.getStatus());
                    video.setCost(Double.valueOf(videoSplit[0]));
                    video.setReplySettings(videoSplit[1]);
                    video.setPool(d.getPool());
                    video.setFreeClinic(d.getFreeClinic());
                    hospitalPrior.add(video);
                } else {
                    BusDoctorConsultation further = new BusDoctorConsultation();
                    further.setId(d.getId());
                    further.setType(ConsultationEnum.FURTHER.getCode());
                    further.setStatus(d.getStatus());
                    if (ObjectUtil.isNotNull(furtherConsultationPrice)) {
                        further.setCost(furtherConsultationPrice.doubleValue());
                    }
                    further.setReplySettings(graphicSplit[1]);
                    further.setPool(d.getPool());
                    further.setFreeClinic(d.getFreeClinic());
                    hospitalPrior.add(further);
                }
            });
            return AjaxResult.success(hospitalPrior);
        }
    }

    /**
     * 添加问诊设置费用
     *
     * @param doctorConsultationList 医生问诊设置列表
     * @return 添加结果
     */
    @PostMapping("/my/consultationSettings")
    @Log(title = "添加问诊设置费用", businessType = BusinessType.OTHER)
    public AjaxResult consultation(@RequestBody List<BusDoctorConsultation> doctorConsultationList) {
        return AjaxResult.success(doctorConsultationService.addConsultation(doctorConsultationList));
    }


    /**
     * 实名认证
     *
     * @param doctorAudit
     * @return
     */
    @PostMapping("/realName/authentication")
    @Log(title = "实名认证", businessType = BusinessType.OTHER)
    public AjaxResult realNameAuthentication(@RequestBody BusDoctorAudit doctorAudit) {
        if (ObjectUtil.isNull(doctorAudit.getDoctorId())) {
            SMSLoginUser loginUser = doctorService.queryDrInfo();
            doctorAudit.setDoctorId(loginUser.getUserid());
        }
        return AjaxResult.success(doctorService.realNameAuthentication(doctorAudit));
    }

    /**
     * 医生认证
     *
     * @param busDoctorAudit
     * @return
     */
    @PostMapping("/physician/authentication")
    @Log(title = "医生认证", businessType = BusinessType.OTHER)
    public AjaxResult physicianAuthentication(@RequestBody BusDoctorAudit busDoctorAudit) {
        if (ObjectUtil.isNull(busDoctorAudit.getIsThisCourt())) {
            return AjaxResult.error("请选择是否本院医生");
        }
        return AjaxResult.success(doctorService.physicianAuthentication(busDoctorAudit));
    }

    /**
     * 医生认证信息
     *
     * @param hospitalId
     * @param phoneNumber
     * @return
     */
    @PostMapping("/my/authenticationInfo")
    @Log(title = "医生认证信息", businessType = BusinessType.OTHER)
    public AjaxResult authenticationInfo(Long hospitalId, String phoneNumber) {
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        return AjaxResult.success(doctorService.authenticationInfo(loginUser.getUserid(), hospitalId, phoneNumber));
    }

    /**
     * 医生个人资料
     *
     * @param hospitalId
     * @return
     */
    @GetMapping("personalData")
    @Log(title = "医生个人资料", businessType = BusinessType.OTHER)
    public AjaxResult personalData(@RequestParam("hospitalId") Long hospitalId,
                                   @RequestParam("doctorId") Long doctorId) {
        return AjaxResult.success(doctorService.personalData(hospitalId, doctorId));
    }

    /**
     * 查询医生所有医院信息
     *
     * @return
     */
    @GetMapping("getDoctorHospitalInfo")
    @Log(title = "查询医生所有医院信息", businessType = BusinessType.OTHER)
    public AjaxResult getDoctorHospitalInfo(@RequestParam("doctorId") Long doctorId) {
        return AjaxResult.success(doctorService.getDoctorHospitalInfo(doctorId));
    }

    /**
     * 修改医生基本资料
     *
     * @param busDoctor
     * @return
     */
    @PostMapping("updateBasePersonal")
    @Log(title = "修改医生基本资料", businessType = BusinessType.OTHER)
    public AjaxResult updateBasePersonal(@RequestBody BusDoctor busDoctor) {
        return AjaxResult.success(doctorService.updateBasePersonal(busDoctor));
    }

    /**
     * 修改医生证件资料
     *
     * @param busDoctorAudit
     * @return
     */
    @PostMapping("updatePersonal")
    @Log(title = "修改医生证件资料", businessType = BusinessType.OTHER)
    public AjaxResult updatePersonal(@RequestBody BusDoctorAudit busDoctorAudit) {
        return AjaxResult.success(doctorService.updatePersonal(busDoctorAudit));
    }

    /**
     * 医生多次认证操作
     *
     * @param busDoctorAudit
     * @return
     */
    @PostMapping("/manyTimesAuthentication")
    @Log(title = "医生多次认证操作", businessType = BusinessType.OTHER)
    public AjaxResult manyTimesAuthentication(BusDoctorAudit busDoctorAudit) {
        doctorService.manyTimesAuthentication(busDoctorAudit);
        return AjaxResult.success();
    }

    /**
     * 我的患者列表
     *
     * @param busDoctorPatientDto
     * @return
     */
    @Log(title = "我的患者列表")
    @GetMapping("/myPatientList")
    public TableDataInfo myPatientList(BusDoctorPatientDto busDoctorPatientDto) {
        // 新增代码 2022-11-08 权 如果是特邀医生，患者为空
//        Long hospitalId = busDoctorPatientDto.getHospitalId();
//        Long doctorId = busDoctorPatientDto.getDoctorId();
//        BusDoctorHospital doctorHospital = doctorService.getDoctorHospital(hospitalId, doctorId);
//        if (StringUtils.isNotNull(doctorHospital) &&
//                DoctorPracticeMode.SPECIAL_INVITATION.getCode().equals(doctorHospital.getIsThisCourt())){
//            return getDataTable(new ArrayList<>());
//        }
        // 新增结束
        if (StringUtils.isNotEmpty(busDoctorPatientDto.getName())) {
            busDoctorPatientDto.setName(busDoctorPatientDto.getName().trim());
        }
        startPage();
        List<BusDoctorPatientVo> list = busDoctorPatientService.myPatientList(busDoctorPatientDto);
        return getDataTable(list);
    }

    /**
     * 随访手动添加患者-获取所属医生下的所有患者列表 远程调用
     *
     * @param dto 医生和患者信息
     * @return 患者列表
     */
    @Log(title = "随访入组筛选-获取所属医生下的所有患者列表")
    @PostMapping("/belong/patients")
    public R<List<DoctorPatientInfoVo>> getBelongPatients(@RequestBody DoctorAndPatientDTO dto) {
        return R.ok(busDoctorPatientService.getBelongPatients(dto));
    }

    /**
     * 医生详情
     *
     * @return
     */
    @GetMapping("detail")
    @Log(title = "医生详情", businessType = BusinessType.OTHER)
    public AjaxResult detail() {
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        BusDoctorVo busDoctorVo = doctorService.selectDoctorDetailInfoById(loginUser.getUserid());
        return AjaxResult.success(busDoctorVo);
    }

    /**
     * 医生第一执业地点详情
     *
     * @return
     */
    @GetMapping("firstPractice")
    @Log(title = "医生第一执业地点详情", businessType = BusinessType.OTHER)
    public AjaxResult firstPractice() {
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        return AjaxResult.success(doctorService.firstPractice(loginUser.getUserid()));
    }

    /**
     * 生成添加患者二维码
     *
     * @param qrCodeDto
     * @return
     */
    @PostMapping("addPatientQrCode")
    @Log(title = "生成添加患者二维码", businessType = BusinessType.OTHER)
    public AjaxResult addPatientQrCode(@RequestBody QrCodeDto qrCodeDto) {
        String url = qrcodeService.createTempTicket(qrCodeDto);
        return AjaxResult.success(url);
    }

    /**
     * 查询医生列表
     *
     * @param busDoctorDto
     * @return
     */
    @Log(title = "查询医生列表")
    @GetMapping("doctorList")
    public TableDataInfo doctorList(BusDoctorDto busDoctorDto) {
        String partnersCode = SecurityUtils.getPartnerscode();
        if (StringUtils.isNotEmpty(partnersCode)) {
            busDoctorDto.setPartnersCode(partnersCode);
            logger.info("机构code partnersCode={}", partnersCode);
        }
        logger.info("医生请求信息 busDoctorDto={}", busDoctorDto.toString());
        busDoctorDto.setDoctorType(YesNoEnum.NO.getCode());
        startPage();
        busDoctorDto.setWhetherStartPage(true);
        return getDataTable(doctorService.doctorList(busDoctorDto));
    }

    /**
     * 查询医院医生的信息
     *
     * @param hospitalId
     * @return
     */
    @GetMapping("selHospitalDoctirInfo")
    @Log(title = "查询医院医生的信息", businessType = BusinessType.OTHER)
    public AjaxResult selHospitalDoctirInfo(HttpServletRequest httpRequest, @RequestParam("id") Long id,
                                            @RequestParam("hospitalId") String hospitalId, Long patientId) {
        if (StringUtils.isEmpty(hospitalId) || "undefined".equals(hospitalId) || "null".equals(hospitalId)) {
            boolean isHos = false;
            String domainPre = httpRequest.getHeader("domainPre");
            logger.info("selHospitalDoctirInfo接口请求域名请求头 domainPre:{}", domainPre);
            if (StringUtils.isNotEmpty(domainPre)) {
                hospitalId = busHospitalService.getHospitalId(domainPre, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
                if (StringUtils.isNotEmpty(hospitalId)) {
                    isHos = true;
                }
            }
            if (!isHos) {
                return AjaxResult.error("系统繁忙，请稍后重试");
            }
        }
        return AjaxResult.success(doctorService.getHospitalDoctorInfo(id, Long.parseLong(hospitalId), patientId));
    }

    /**
     * 查询医院医生信息
     *
     * @param hospitalId
     * @return
     */
    @GetMapping("selectHospitalDoctorInfo")
    @Log(title = "查询医院医生信息", businessType = BusinessType.OTHER)
    public AjaxResult selectHospitalDoctorInfo(@RequestParam("hospitalId") Long hospitalId,
                                               @RequestParam("doctorId") Long doctorId) {
        return AjaxResult.success(doctorService.selectHospitalDoctorInfo(doctorId, hospitalId));
    }

    /**
     * 设置患者等级
     *
     * @param doctorPatient
     * @return
     */
    @PutMapping("setLevel")
    @Log(title = "设置患者等级", businessType = BusinessType.OTHER)
    public AjaxResult setLevel(BusDoctorPatient doctorPatient) {
        logger.info("设置vip请求参数={}", JSON.toJSON(doctorPatient));
        int i = busDoctorPatientService.updateGrade(doctorPatient);
        if (i > 0) {
            sendChatMsgToPatient(doctorPatient.getGroupId());
        }
        return toAjax(i);
    }

    /**
     * 发送聊天信息给患者
     */
    private boolean sendChatMsgToPatient(String groupId) {
        try {
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            int i = (int) ((Math.random() * 9 + 1) * 100000);
            sendMessageRequest.setRandom(i + "");
            sendMessageRequest.setGroupId(groupId + "");
            sendMessageRequest.setFrom_Account(TencentyunImConstants.ADMINISTRATOR);
            sendMessageRequest.setSendMsgControl(Lists.newArrayList(TencentyunImConstants.NO_UN_READ));
            List<MsgBody> msgBodyList = new ArrayList<MsgBody>();
            MsgBody msgBody = new MsgBody();
            msgBody.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            MsgContent msgContent = new MsgContent();
            JSONObject data = new JSONObject();
            data.put("type", CustomMsgConstants.SETTINGVIP);
            data.put("settingvip", "settingvip");
            msgContent.setData(data.toJSONString());
            msgBody.setMsgContent(msgContent);
            msgBodyList.add(msgBody);
            sendMessageRequest.setMsgBody(msgBodyList);
            R<Boolean> groupMsg = remoteImService.sendGroupMsg(sendMessageRequest);
            if (Constants.FAIL.equals(groupMsg.getCode())) {
                return false;
            }
            return true;
        } catch (Exception e) {
            logger.error("发送设置vip消息报错e={}", e.getMessage());
            return false;
        }
    }


    /**
     * 查询热门医生列表
     *
     * @param busDoctorDto
     * @return
     */
    @Log(title = "查询热门医生列表")
    @GetMapping("/hot/list")
    public AjaxResult queryHotList(BusDoctorDto busDoctorDto) {
        // 传入的新的只有pageNum，pageSize，hospitalId，patientId，departmentName
        String partnersCode = SecurityUtils.getPartnerscode();
        logger.info("机构信息={}", partnersCode);
        if (StringUtils.isNotEmpty(partnersCode)) {
            busDoctorDto.setPartnersCode(partnersCode);
        }
        busDoctorDto.setHot(CodeEnum.YES.getCode());
        busDoctorDto.setDoctorType(YesNoEnum.NO.getCode());
        busDoctorDto.setWhetherStartPage(false);
        // 根据科室划分（非合作机构） / 合作机构的  + 常用医生（如果在之前列表中，则提取出来放到第一个）

        //找到热门医生
        // --初步优化-- 移到service层处理
        List<BusDoctorBasicInfoVo> doctorList = doctorService.queryHostList(busDoctorDto);
        return AjaxResult.success(doctorList);
    }

    /**
     * 全局搜索
     *
     * @param busDoctorDto
     * @return
     */
    @GetMapping("globalSearch")
    @Log(title = "全局搜索", businessType = BusinessType.OTHER)
    public TableDataInfo globalSearch(BusDoctorDto busDoctorDto) {
        String partnersCode = SecurityUtils.getPartnerscode();
        String token = SecurityUtils.getToken();
        if (StringUtils.isNotEmpty(partnersCode)) {
            busDoctorDto.setPartnersCode(partnersCode);
        }
        busDoctorDto.setDoctorType(YesNoEnum.NO.getCode());
        startPage();
        List<BusDoctorVo> list = doctorService.globalSearch(busDoctorDto, StringUtils.isNotEmpty(token) ? true : false);
        return getDataTable(list);
    }

    /**
     * 搜索填充
     *
     * @param busDoctorDto
     * @return
     */
    @GetMapping("fillSearch")
    @Log(title = "搜索填充", businessType = BusinessType.OTHER)
    public AjaxResult fillSearch(BusDoctorDto busDoctorDto) {
        return AjaxResult.success(doctorService.fillSearch(busDoctorDto.getSearchValue(),
                busDoctorDto.getHospitalId()));
    }

    /**
     * 修改医生密码
     *
     * @param busSignature
     * @return
     */
    @PostMapping("changePassWord")
    @Log(title = "修改医生密码", businessType = BusinessType.OTHER)
    public AjaxResult changePassWord(@RequestBody BusSignature busSignature) {
        return AjaxResult.success(doctorService.changePassWord(busSignature));
    }

    /**
     * 修改医生手机号码
     *
     * @param phoneNumber
     * @param oldPhoneNumber
     * @param code
     * @return
     */
    @GetMapping("updatePhoneNumber")
    @Log(title = "修改医生手机号码", businessType = BusinessType.OTHER)
    public AjaxResult updatePhoneNumber(@RequestParam("phoneNumber") String phoneNumber,
                                        @RequestParam("oldPhoneNumber") String oldPhoneNumber,
                                        @RequestParam("code") String code) {
        if (oldPhoneNumber.equals(phoneNumber)) {
            return AjaxResult.error("修改手机号相同");
        }
        AjaxResult result = remoteSmsNotificationService.checkVerifyCode(phoneNumber, code);
        if (!result.isSuccess()) {
            return result;
        }
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        return AjaxResult.success(doctorService.updatePhoneNumber(phoneNumber, oldPhoneNumber, loginUser.getUserid()));
    }

    /**
     * 获取医生诊断信息
     *
     * @param doctorId
     * @return
     */
    @GetMapping("diagnosis")
    @Log(title = "获取医生诊断信息", businessType = BusinessType.OTHER)
    public AjaxResult diagnosis(@RequestParam("hospitalId") Long hospitalId, @RequestParam("doctorId") Long doctorId) {
        return AjaxResult.success(doctorService.diagnosis(hospitalId, doctorId));
    }

    /**
     * 获取医生待办任务列表
     *
     * @param busDoctorTdl
     * @return
     */
    @GetMapping("tdlList")
    @Log(title = "获取医生待办任务列表", businessType = BusinessType.OTHER)
    public TableDataInfo tdlList(BusDoctorTdl busDoctorTdl) {
        if (ObjectUtil.isNull(busDoctorTdl.getDoctorId())) {
            SMSLoginUser loginUser = doctorService.queryDrInfo();
            busDoctorTdl.setDoctorId(loginUser.getUserid());
        }
        startPage();
        List<BusDoctorTdl> doctorTdls = busDoctorTdlService.selectDoctorTdlList(busDoctorTdl);
        return getDataTable(doctorTdls);
    }

    /**
     * 更新待办任务
     *
     * @param busDoctorTdl
     * @return
     */
    @PostMapping("updateTdl")
    @Log(title = "更新待办任务", businessType = BusinessType.OTHER)
    public AjaxResult updateTdl(@RequestBody BusDoctorTdl busDoctorTdl) {
        return toAjax(busDoctorTdlService.update(busDoctorTdl));
    }

    /**
     * 校验身份证号码
     *
     * @param idCardNo
     * @return
     */
    @GetMapping("checkIdCardNo")
    @Log(title = "校验身份证号码", businessType = BusinessType.OTHER)
    public AjaxResult checkIdCardNo(@RequestParam("idCardNo") String idCardNo,
                                    @RequestParam(value = "id", required = false) Long id) {
        return AjaxResult.success(doctorService.checkIdCardNo(idCardNo, id));
    }

    /**
     * 邀请患者
     *
     * @param url
     * @param mobileNumber
     * @param fullName
     * @param doctorName
     * @return
     */
    @GetMapping("invitePatients")
    @Log(title = "邀请患者", businessType = BusinessType.OTHER)
    public AjaxResult invitePatients(@RequestParam("url") String url, @RequestParam("hospitalId") Long hospitalId,
                                     @RequestParam(value = "mobileNumber") String mobileNumber,
                                     @RequestParam(value = "fullName") String fullName,
                                     @RequestParam(value = "doctorName") String doctorName) {
        BusHospitalWechatConfig wechatConfig = busHospitalWechatConfigService.selectByHospitalId(hospitalId, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
        if (ObjectUtil.isNull(wechatConfig)) {
            return AjaxResult.error("微信公众号未配置");
        }
        BusHospitalSmsConfigVo smsConfigVo = busHospitalSmsConfigService.selectOneItem(hospitalId);
        if (ObjectUtil.isNull(smsConfigVo)) {
            return AjaxResult.error("医院短信未配置");
        }
//        String link="https://show.aidmed.net/v/"+"?"+url;
        String link = smsConfigVo.getHospitalDepartmentUrl() + "?" + url;
        String msg = "您好" + fullName + "，点击：" + link + " ，进入" + doctorName + "医生诊室";
        // 组装参数
        SmsNotificationDTO baseSmsNotificationDTO = new SmsNotificationDTO();
        baseSmsNotificationDTO.setPhoneNumber(mobileNumber);
        baseSmsNotificationDTO.setHospitalId(hospitalId);
        baseSmsNotificationDTO.setMessageContent(msg);
        baseSmsNotificationDTO.setChannel(SmsNotificationDTO.ChannelType.HUYI);
        return remoteSmsNotificationService.templateMessage(baseSmsNotificationDTO);
    }

    @GetMapping("invitePatientsHtmlInfo")
    @Log(title = "邀请患者", businessType = BusinessType.OTHER)
    public AjaxResult invitePatientsHtmlInfo(@RequestParam("did") Long did, @RequestParam(value = "hid") Long hid,
                                             @RequestParam(value = "deid") Long deid,
                                             @RequestParam(value = "status") Integer status,
                                             @RequestParam(value = "uuid") Long uuid) {
        Map<String, Object> map = new HashMap<>();
        QrCodeDto qrCodeDto = new QrCodeDto();
        qrCodeDto.setDoctorId(did);
        qrCodeDto.setHospitalId(hid);
        qrCodeDto.setUuid(uuid);
        qrCodeDto.setStatus(status);
        qrCodeDto.setDepartmentId(deid);
        String url = qrcodeService.createTempTicket(qrCodeDto);
        BusDoctorVo busDoctorVo = doctorService.personalData(hid, did);
        BusBizDepartment busBizDepartment = busBizDepartmentService.selectById(deid);
        BusBizDepartment parentBizDepartment =
                busBizDepartmentService.selectById(busBizDepartment.getParentId());
        BusHospital busHospital = new BusHospital();
        busHospital.setId(hid);
        BusHospital hospital = busHospitalService.select(busHospital);
        map.put("url", url);
        map.put("title", busDoctorVo.getTitleValue());
        map.put("doctorName", busDoctorVo.getFullName());
        map.put("firstDepart", parentBizDepartment.getDepartmentName());
        map.put("secondDepart", busBizDepartment.getDepartmentName());
        map.put("hospitalName", hospital.getHospitalName());
        return AjaxResult.success(map);
    }

    /**
     * 校验医生审核
     *
     * @param doctorId 医生ID
     * @return
     */
    @GetMapping("checkDoctorAudit")
    @Log(title = "校验医生审核", businessType = BusinessType.OTHER)
    public AjaxResult checkDoctorAudit(@RequestParam("doctorId") Long doctorId,
                                       @RequestParam("hospitalId") Long hospiatalId) {
        return AjaxResult.success(doctorService.checkDoctorAudit(doctorId, hospiatalId));
    }

    /**
     * 查询热门医生下所有科室
     *
     * @return
     */
    @Log(title = "查询热门医生下所有科室")
    @GetMapping("/query/dept")
    public AjaxResult queryDept(BusDoctorDto busDoctorDto) {
        // 近期看过的医生
        Long doctorId = consultationOrderService.selectDoctor(busDoctorDto);
        List<BusBizDepartmentVo> busBizDepartmentVos = doctorService.queryDept(busDoctorDto.getHospitalId());
        List<Long> doctorIds =
                busBizDepartmentVos.stream().map(BusBizDepartmentVo::getDoctorId).collect(Collectors.toList());
        if (ObjectUtil.isNotNull(doctorId) && !doctorIds.contains(doctorId)) {
            // 查询医生所在业务科室
            List<BusDoctorDepartment> list = busDoctorDepartmentMapper.selectDeptList(busDoctorDto.getHospitalId(),
                    doctorId);
            list.forEach(l -> {
                BusBizDepartmentVo vo = new BusBizDepartmentVo();
                vo.setId(l.getId());
                vo.setDepartmentName(l.getDepartmentName());
                busBizDepartmentVos.add(vo);
            });
        }
        // 去重
        List<BusBizDepartmentVo> list =
                busBizDepartmentVos.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(BusBizDepartmentVo::getId))), ArrayList::new));
        return AjaxResult.success(list);
    }

    /**
     * 校验医生是否禁用
     *
     * @param hospitalId 医院ID
     * @return
     */
    @GetMapping("checkDisable")
    @Log(title = "校验医生是否禁用", businessType = BusinessType.OTHER)
    public AjaxResult checkDisable(@RequestParam("hospitalId") Long hospitalId) {
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        return AjaxResult.success(doctorService.checkDisable(hospitalId, loginUser.getUserid()));
    }

    /**
     * 注销医生
     *
     * @param hospitalIds 医院ID集合
     * @return
     */
    @PutMapping("/logOut")
    @Log(title = "注销医生", businessType = BusinessType.OTHER)
    public AjaxResult logOut(@RequestParam("hospitalIds") List<Long> hospitalIds) {
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        return toAjax(doctorService.logOut(loginUser.getUserid(), hospitalIds));
    }

    /**
     * 查询医助管理医生关联信息
     *
     * @param hospitalId
     * @return
     */
    @Log(title = "查询医助管理医生关联信息")
    @GetMapping("/manage/dr/list")
    public TableDataInfo queryManageDrList(@RequestParam("hospitalId") Long hospitalId) {
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        startPage();
        return getDataTable(doctorService.queryManageDrList(hospitalId, loginUser.getUserid()));
    }

    /**
     * five远程调用获取医生信息
     *
     * @return
     */
    @InnerAuth
    @GetMapping("/query/dr")
    public R<SMSLoginUser> queryDrInfo() {
        return R.ok(doctorService.queryDrInfo());
    }

    /**
     * 更新医师手机号
     *
     * @param phoneNumber 医生id
     * @param code        医院id
     * @return 医院医生信息
     * <AUTHOR>
     */
    @PostMapping("update/phoneNumber")
    @Log(title = "更新医师手机号", businessType = BusinessType.UPDATE)
    public AjaxResult updateDoctorNumber(String phoneNumber, String code) {
        AjaxResult checkResult = remoteSmsNotificationService.checkVerifyCode(phoneNumber, code);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        int result = iBusDoctorService.updateDoctorNumber(DESUtil.encrypt(phoneNumber));
        return toAjax(result);
    }

    /**
     * im推荐医生
     *
     * @param dto
     * @return
     */
    @Log(title = "im推荐医生")
    @PostMapping("/recommended/doctor")
    public AjaxResult recommendedDoctor(@RequestBody ImRecommendedDoctorDTO dto) {
        CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
        messageDTO.setGroupId(dto.getGroupId());
        messageDTO.setFromAccount(TencentyunImConstants.DOCTOR_IM_ACCOUNT + dto.getReferenceId());
        messageDTO.setNickName(dto.getReferenceName());
        messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
        messageDTO.setSendNotification(true);
        messageDTO.setSendNotificationIntervalLimit(true);
        messageDTO.setSendGroupLeaderDoctorName(true);
        messageDTO.setEventType(ImSendMessageEvent.EventType.SEND_RECOMMEND_DOCTOR);
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("type", CustomMsgConstants.RECOMMENDED_DOCTOR);
        DoctorDTO doctor = dto.getDoctor();
        dataMap.put("id", doctor.getMemberId());
        dataMap.put("photo", doctor.getMemberPortrait());
        dataMap.put("doctorName", doctor.getMemberName());
        dataMap.put("titleValue", doctor.getMemberTitle());
        dataMap.put("hospitalId", doctor.getHospitalId());
        dataMap.put("hospitalName", doctor.getHospitalName());
        dataMap.put("departmentId", doctor.getDepartmentId());
        dataMap.put("departmentName", doctor.getDepartmentName());
        dataMap.put("beGoodAt", doctor.getBeGoodAt());
        dataMap.put("introduce", doctor.getIntroduce());
        messageDTO.setMap(dataMap);
        busCommunicationMessageService.sendCustomImMsg(messageDTO);
        return AjaxResult.success();
    }

    /**
     * 根据医生id，获取医生详情
     *
     * @param id 医生id
     * @return 医生信息详情
     */
    @GetMapping("/feign/{id}")
    public AjaxResult getById(@PathVariable("id") Long id) {
        return AjaxResult.success(doctorService.getById(id));
    }

    /**
     * 医生发送双通道医保授权码
     *
     * @param dto dto
     * @return 发送授权卡片结果
     */
    @PostMapping("/im/dual-channel/send-auth-card")
    public AjaxResult sendDualChannelAuthCard(@RequestBody ImDualChannelAuthDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getGroupId()) || StringUtils.isBlank(dto.getNickName())) {
            throw new ServiceException("参数错误");
        }
        SMSLoginUser loginUser = doctorService.queryDrInfo();
        if (Objects.isNull(loginUser) || !(Objects.equals("0", loginUser.getIdentity()) || Objects.equals("1", loginUser.getIdentity()))) {
            throw new ServiceException("请求非法");
        }
        CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
        messageDTO.setGroupId(dto.getGroupId());
        messageDTO.setFromAccount(TencentyunImConstants.DOCTOR_IM_ACCOUNT + loginUser.getUserid());
        messageDTO.setNickName(dto.getNickName());
        messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
        Map<String, Object> map = new HashMap<>();
        map.put("type", CustomMsgConstants.DUAL_CHANNEL_AUTH_CARD);
        messageDTO.setMap(map);
        messageDTO.setSendNotification(true);
        messageDTO.setEventType(ImSendMessageEvent.EventType.SEND_DUAL_CHANNEL_AUTH);
        busCommunicationMessageService.sendCustomImMsg(messageDTO);
        return AjaxResult.success();
    }

    /**
     * 获取消息发送间隔是否超限
     * @param hospitalId    医院ID
     * @param groupId       群组ID
     * @return  是否可以发消息
     */
    @GetMapping("/notification/message-interval")
    public R<Boolean> messageNotificationIntervalIsOver(@RequestParam("hospitalId") Long hospitalId, @RequestParam("groupId") String groupId) {
        return R.ok(doctorMessageNotificationIntervalHelper.isOverInterval(hospitalId, groupId));
    }

    /**
     *  获取医院医生签名
     * @param hospitalId    医院ID
     * @param doctorId      医生ID
     * @return  医生签名，如果是特聘专家，会返回设置的开方医生签名
     */
    @GetMapping("/signature")
    public R<BusSignatureVO> getDoctorSignature(@RequestParam("hospitalId") Long hospitalId, @RequestParam("doctorId") Long doctorId) {
        return R.ok(doctorService.getDoctorSignature(hospitalId, doctorId));
    }


}