package com.puree.hospital.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.puree.hospital.app.api.model.BusTcmSyndromeIdDTO;
import com.puree.hospital.app.domain.*;
import com.puree.hospital.app.api.model.ApprovePrescriptionDTO;
import com.puree.hospital.app.domain.dto.BusDispensingDTO;
import com.puree.hospital.app.domain.dto.BusPreorderPatientDto;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.PrescriptionGenerateResultDTO;
import com.puree.hospital.app.domain.dto.PrescriptionPharmacistSignDTO;
import com.puree.hospital.app.domain.vo.*;
import com.puree.hospital.common.api.domain.AjaxResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBusPrescriptionService extends IService<BusPrescription> {
    /**
     * 获取医生处方未通过的数量
     *
     * @param busPrescription 处方
     * @param isThisCourt     医生在本医院的执业方式  0多点 1本院 2特约专家
     * @return {@link Long }
     */
    Long selectFailQuantity(BusPrescription busPrescription, Integer isThisCourt);

    List<BusPrescriptionVo> selectNotApprovedList(Long hospitalId, String reviewContent);

    List<BusPrescriptionVo> selectApprovedList(BusPrescription busPrescription);

    /**
     * 查询患者端的处方列表
     * @param busPrescription 处方信息
     * @return 处方列表
     */
    List<BusPrescriptionVo> selectApprovedPatientList(BusPrescription busPrescription);

    List<BusPrescriptionVo> selectList(BusPrescription busPrescription);

    BusPrescriptionVo select(BusPrescription busPrescription);

    List<MMDiagnosisVo> selectMMDiagnosisList(String key);

    List<TCMDiagnosisVo> selectTCMDiagnosisList(String key);

    PatientTemplateMsgVo selectPatient(Long prescriptionId, Long hospitalId);

    List<PatientTemplateMsgVo> selectPrescriptionDrugs(Long prescriptionId);

    int update(BusPrescriptionDto dto);

    int discard(BusPrescription busPrescription);

    AjaxResult approve(ApprovePrescriptionDTO dto, BusPrescription prescription);
    int approve(ApprovePrescriptionDTO dto);

    PrescriptionGenerateResultDTO generate(BusPrescriptionDto dto);

    boolean check(BusPrescriptionDto busPrescription);

    BusPrescription selectPrescription(Long hospitalId, Long id);

    int invalid(Long prescriptionId);

    List<TCMSyndromeVo> selectTCMSyndromeList(String key);

    List<Long> getPrescriptionId(Long id);

    void afferentPrescriptionToDx(BusDrugsOrder drugsOrder, BusPrescription prescription);

    BusPrescription getLastDiagnosis(long hospitalId, long familyId);

    BusPrescriptionVo queryReviewed(BusPrescriptionDto dto);

    int followUpPurchase(BusPreorderPatientDto dto);

    List<BusPrescriptionDoctorAdviceTemplate> selectPrescriptionDoctorAdviceTemplateList();

    List<BusPrescriptionFailTemplate> queryFailTemplateList();

    int doctorSign(BusPrescription busPrescription);

    List<BusDisease> selectCommonMMDiagnosis(BusPrescriptionDto dto);

    void checkPrescription(BusPreorderPatient busPreorderPatient);

    /**
     * 查询无效处方列表 (审批已通过，但超时未使用)
     *
     * @param idx      索引
     * @param pageSize 页大小
     * @return 处方无效列表
     */
    List<BusPrescription> queryInvalidList(Long idx, Integer pageSize);

    /**
     * 查询处方各个分类的数量
     */
    List<PrescriptionStatusTypeVO> selectPrescriptionStatusCount(Long hospitalId, Long doctorId, Long expertId);

    BusPrescription getById(Long id);

    /**
     * 远程调用查询处方信息集合
     *
     * @param prescriptionIds
     * @return
     */
    List<BusPrescription> selectPrescriptionList(List<Long> prescriptionIds);

    /**
     * 远程调用修改处方信息
     *
     * @param prescriptionId
     * @return
     */
    int updatePrescription(Long prescriptionId);

    /**
     * 生成处方pdf文件
     *
     * @param busPrescription
     * @return
     */
    String generatePdf(BusPrescription busPrescription);

    /**
     * 作废旧处方
     *
     * @param busPrescription 处方信息
     * @return 作废结果
     */
    int invalidOldRx(BusPrescription busPrescription);

    /**
     * 查询处方及药品信息
     *
     * @param prescriptionId
     * @return
     */
    BusPrescription selectPrescriptionDrugList(Long prescriptionId);

    /**
     * 发送im群组自定义消息
     *
     * @param dto
     * @return
     */
    int sendMsgToIm(BusPrescriptionDto dto);

    /**
     * 查询医保的处方详情
     *
     * @param rxNoList 处方号列表
     * @param idList   处方ID列表
     * @return 处方详情
     */
    List<BusPrescription> getMiRx(List<String> rxNoList, List<Long> idList);

    /**
     * 释放药品库存
     *
     * @param hospitalId     医院ID
     * @param prescriptionId 处方ID
     */
    void increaseDrugsStock(Long hospitalId, Long prescriptionId);

    void sendPrescriptionApproveSms(BusPrescription prescription);

    void joinFollowUp(BusPrescription prescription);

    List<MMDiagnosisVo> selectMMDiagnosisListByIds(List<Long> ids);

    List<TCMDiagnosisVo> selectTCMDiagnosisListByIds(List<BusTcmSyndromeIdDTO> dtos);

    /**
     * 处方药师签名
     *
     * @param signDTO 签名信息
     * @return 处方药师签名结果
     */
    Boolean pharmacistSignature(PrescriptionPharmacistSignDTO signDTO);

    /**
     * 复核审核接口
     *
     * @param dto 参数
     * @return 结果
     */
    Integer dispensingPrescription(BusDispensingDTO dto);

    /**
     * 获取复核列表
     *
     * @param dto 查询参数
     * @return 复核列表
     */
    List<BusPrescriptionVo> getList(BusDispensingDTO dto);
}
