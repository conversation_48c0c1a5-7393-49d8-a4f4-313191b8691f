package com.puree.hospital.app.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ToString
public class BusPrescriptionDto extends Entity {

    /** 过敏史 */
    private String allergicHistory;
    /** 临床诊断 */
    private String clinicalDiagnosis;
    /** 1:代煎；0：不代煎。 */
    private String decoct;
    /** 科室ID */
    private Long departmentId;
    /** 科室 */
    private String departmentName;
    /** 患者ID */
    private Long patientId;
    private Long familyId;
    private String familyAge;
    private String familyName;
    private String familySex;
    private String familyTel;
    private String familyIdcard;
    /** 初步诊断 */
    private String preliminaryDiagnosis;
    /** 处方金额 */
    private BigDecimal prescriptionAmount;
    private BigDecimal prescriptionRealAmount;
    /** 开方医生ID */
    private Long doctorId;
    /** 开方医生 */
    private String doctorName;
    /** 处方编号 */
    private String prescriptionNumber;
    /** 开方时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date prescriptionTime;
    /** 0:中药；1：西药。2协定方 */
    private String prescriptionType;
    /** 审方药师ID */
    private Long reviewPharmacistId;
    /** 审核药师 */
    private String reviewPharmacistName;
    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;
    /** 医院ID */
    private Long hospitalId;
    /** 医院名称 */
    private String hospitalName;
    /** 医院电话 */
    private String hospitalPhone;
    private String hospitalAddress;
    /** 处方状态 */
    private String status;
    private String notApprovedReason;
    /** 补充说明 */
    private String remark;

    /** 前置审方id */
    private Long preAuditId;
    /** 前置审方评分： null表示待审方，90禁用，80不适用，60慎用，40密切关注，20关注，0正常 */
    private Integer preRiskScore;

    private String drugDirectoryType;
    /** 处方中药品列表 */
    private List<BusPrescriptionDrugs> list;
    /**
     * 问诊订单ID
     */
    private Long consultationOrderId;
    /**
     * 加工方法
     */
    private String processingMethod;
    /**
     * 用法
     */
    private String usages;
    /**
     * 药品金额
     */
    private Double drugsAmount;
    /**
     * 药品重量
     */
    private Double drugsWeight;

    /**
     * 协定方名称
     */
    private String paName;

    /**
     * 配送方名称
     */
    private String enterpriseName;
    /**
     * 协定方id
     */
    private String paId;
    /**
     * 配送方id
     */
    private Long enterpriseId;
    /**
     * 开方类型（0线下 1线上）
     */
    private String type;
    /**
     * 机构code
     */
    private String partnersCode;
    /**
     * 开方来源  1表示患者购药 0医生开方
     */
    private Integer patient;
    /**
     *
     * 1 表示患者找药已开处方
     */
    private Integer isPrescribe;
    /**
     * 预订单ID
     */
    private Long preorderId;

    /** 服务包id*/
    private Long servicePackId;
    /**
     * 处方标识（0普通开方 1找药开方）
     */
    private String identity;
    /**
     * 专家ID
     */
    private Long expertId;
    /**
     * 药品id集合
     */
    private List<Long> drugsIds;
    /**
     * 群组ID
     */
    private Long groupId;
    /**
     * 消息标识（0未完善 1已完善）
     */
    private Integer msgId;

    /**
     * 诊查费
     */
    private BigDecimal examinationFee;

    /**
     * 诊查费项目名称
     */
    private String examinationName;

    /**
     * 费用类别：SELF:自费，MI_GENERAL:医保(普通门诊)，MI_SPECIAL:医保（门特门慢）
     */
    private String feeSettleType;

    /**
     * 门特门慢病种信息，json信息{"opspDiseCode":"M03900","opspDiseName":"高血压(慢病)"}
     */
    private String specialDiseaseInfo;

     /**
     * 患者详细住址
     */
    private String familyDetailAddress;

    /**
     * 推荐人id
     */
    private Long referrer;

    /**
     * 过期时间
     */
    private Date validTime;

    /**
     * 处方编号条形码(base64)
     */
    private String barcodeUrl;
}
