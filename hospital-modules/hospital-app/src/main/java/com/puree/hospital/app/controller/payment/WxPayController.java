package com.puree.hospital.app.controller.payment;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusDrugsOrder;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusOrderAfterSales;
import com.puree.hospital.app.domain.dto.PayDTO;
import com.puree.hospital.app.domain.dto.RefundDTO;
import com.puree.hospital.app.service.IBusAfterSaleService;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusOrderAfterSalesService;
import com.puree.hospital.app.service.IBusOrderService;
import com.puree.hospital.app.service.IWxPayService;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AfterSaleStatusEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.five.api.model.ServicePackOrderResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 微信支付相关控制器
 */
@Slf4j
@RestController
@RequestMapping("wxPay")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WxPayController {
    private final IWxPayService wxPayService;
    private final IBusConsultationOrderService consultationOrderService;
    private final IBusDrugsOrderService busDrugsOrderService;
    private final RemoteServicePackOrderService remoteServicePackOrderService;
    private final IBusAfterSaleService busAfterSaleService;
    private final IBusOrderService busOrderService;
    private final IBusOrderAfterSalesService busOrderAfterSalesService;

    /**
     * 问诊/药品支付
     *
     * @return - 预支付信息
     */
    @PostMapping("payment")
    @Log(title = "微信支付", businessType = BusinessType.OTHER)
    public AjaxResult pay(PayDTO dto) {
        log.info("微信支付入参信息={}", JSONObject.toJSONString(dto));
        HashMap<String, String> map = new HashMap<>();
        map.put("openid", dto.getOpenid());
        // 设置支付应用类型-小程序或公众号
        map.put("appType", dto.getAppType());
        map.put("hospitalId", dto.getHospitalId() + "");
        if (dto.getOrderType().equals(OrderTypeEnum.CONSULTATION.getCode())) {
            wxPayService.consultationOrder(map, dto.getSubOrderId(), dto.getPCode());
        } else if (dto.getOrderType().equals(OrderTypeEnum.DRUGS.getCode())) {
            // 查询总订单编号
            BusOrder busOrder = busOrderService.selectOrederInfo(CodeEnum.NO.getCode(), dto.getSubOrderId());
            wxPayService.drugsOrder(map, busOrder, dto.getPCode());
        } else {
            wxPayService.goodsOrder(map, dto);
        }
        // 获取订单金额
        if (ObjectUtil.isNull(map.get("amount"))) {
            return AjaxResult.error("实际支付金额不能为空！");
        }
        BigDecimal amount = new BigDecimal(map.get("amount"));
        // 校验订单金额
        if (amount.compareTo(new BigDecimal(0)) == 0) {
            return AjaxResult.error("订单实际支付金额必须大于0！");
        }
        try {
            JSONObject result = wxPayService.wxPay(map);
            Integer status = result.getInteger("status");
            if (status != null && status == 0) {
                // 支付失败不返回前端拉起微信支付参数
                return AjaxResult.error("支付失败，请稍后再试");
            }
            return AjaxResult.success(result);
        } catch (ServiceException e){
            log.error("该医院没有关联支付信息hospitalId={}", dto.getHospitalId());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("支付失败,e", e);
            return AjaxResult.error("支付失败，请稍后再试");
        }
    }

    /**
     * 问诊/药品退款
     *
     * @return - 退款结果
     */
    @PostMapping("refund")
    @Log(title = "微信退款", businessType = BusinessType.OTHER)
    public AjaxResult refundOrder(RefundDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("outTradeNo", dto.getOutTradeNo());
        map.put("appType", dto.getAppType());
        map.put("mchType", dto.getMchType());
        if (StringUtils.isNotEmpty(dto.getAutomaticRefund())) {
            map.put("automaticRefund", dto.getAutomaticRefund());
        }
        if (dto.getOrderType().equals(OrderTypeEnum.CONSULTATION.getCode())) {
            BusConsultationOrder consultationOrder = consultationOrderService.queryConsultationOrder(dto.getOutTradeNo());
            if (ObjectUtil.isNull(consultationOrder)) {
                return AjaxResult.error("问诊订单不存在！");
            }
            if (dto.getRefundAmount().compareTo(new BigDecimal(0)) == 0) {
                // 修改订单为已取消
                BusConsultationOrder order = new BusConsultationOrder();
                if (CodeEnum.NO.getCode().equals(consultationOrder.getOrderType())) {
                    order.setStatus(ConsultationOrderStatusEnum.CANCEL.getCode());
                } else {
                    order.setVideoStatus(ConsultationOrderStatusEnum.CANCEL.getCode());
                }
                order.setOrderNo(dto.getOutTradeNo());
                consultationOrderService.updateStatus(order);
                consultationOrderService.endTdl(order.getId(),order.getDoctorId());
                return AjaxResult.success();
            }
            if (StringUtils.isNotEmpty(consultationOrder.getPartnersCode())) {
                map.put("partnersCode", consultationOrder.getPartnersCode());
            }
            map.put("refundAmount", new BigDecimal(consultationOrder.getAmount()));
            map.put("consultationOrder", consultationOrder);
            map.put("amount", new BigDecimal(consultationOrder.getAmount()));
            map.put("hospitalId", consultationOrder.getHospitalId());
        } else if (dto.getOrderType().equals(OrderTypeEnum.DRUGS.getCode())) {
            // 查询药品订单金额
            BusDrugsOrder drugsOrder = busDrugsOrderService.queryDrugsOrder(dto.getOutTradeNo());
            if (ObjectUtil.isNull(drugsOrder)) {
                return AjaxResult.error("药品订单不存在！");
            }
            if (StringUtils.isNotEmpty(drugsOrder.getPartnersCode())) {
                map.put("partnersCode", drugsOrder.getPartnersCode());
            }
            map.put("refundAmount", new BigDecimal(drugsOrder.getAmount()));
            map.put("amount", new BigDecimal(drugsOrder.getAmount()));
            map.put("hospitalId", drugsOrder.getHospitalId());
        } else {
            // 查询订单信息
            List<BusOrder> busOrders = busOrderService.selectOrederByNo(dto.getOutTradeNo());
            if (ObjectUtil.isNotEmpty(busOrders)) {
                BusOrder busOrder = busOrders.get(0);
                if (ObjectUtil.isNotNull(dto.getOrderAfterSaleId())) {
                    BusOrderAfterSales afterSales = busOrderAfterSalesService.selectAfterSales(dto.getOrderAfterSaleId());
                    map.put("refundAmount", afterSales.getRefundAmount());
                    map.put("orderAfterSaleId", dto.getOrderAfterSaleId());
                    map.put("hospitalId", afterSales.getHospitalId());
                } else {
                    map.put("refundAmount", dto.getRefundAmount());
                    map.put("hospitalId", busOrder.getHospitalId());
                }
                // 医保支付且运费不为0
                if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay()) &&
                        Double.compare(busOrder.getFreight(), 0.0) != 0) {
                    map.put("amount", BigDecimal.valueOf(busOrder.getFreight()));
                    map.put("refundAmount", BigDecimal.valueOf(busOrder.getFreight()));
                } else {
                    map.put("amount", busOrder.getRelPrice());
                }
                map.put("transactionId", busOrder.getTonglianTrxid());
            }
        }
        BigDecimal orderAmount = (BigDecimal) map.get("amount");
        orderAmount = orderAmount.setScale(2, RoundingMode.HALF_UP);
        if (dto.getRefundAmount().compareTo(orderAmount) > 0) {
            return AjaxResult.error("退款金额超过订单金额！");
        }
        map.put("orderAmount", orderAmount);
        try {
            JSONObject result = wxPayService.refund(map);
            log.info("微信退款出参 result={}", result.toJSONString());
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("微信退款失败原因={}", e.getMessage());
            return AjaxResult.error("退款失败，请联系医院");
        }
    }


    /**
     * 服务包支付
     *
     * @param openid     用户唯一标识
     * @param orderType  订单类型
     * @param subOrderId 订单ID
     * @param hospitalId 医院ID
     * @param appType    应用类型
     * @param pCode      支付方式
     * @return - 预支付信息
     */
    @PostMapping("service/pack/pay")
    @Log(title = "微信支付服务包", businessType = BusinessType.OTHER)
    public AjaxResult servicePackPay(String openid, String orderType, Long subOrderId, Long hospitalId,
                                     String appType, Integer pCode) {
        log.info("订单id={}，pCode={}", subOrderId, pCode);
        HashMap<String, String> map = new HashMap<>();
        map.put("openid", openid);
        map.put("appType", appType);
        map.put("hospitalId", hospitalId + "");
        if (orderType.equals(OrderTypeEnum.SERVICE_PACK.getCode())) {
            R<ServicePackOrderResponse> r = remoteServicePackOrderService.queryInfo(subOrderId, "");
            log.info("远程调用服务包订单查询结果：{}，订单信息={}", JSON.toJSONString(r), r.getData());
            ServicePackOrderResponse data = r.getData();
            Long hospitalId1 = data.getHospitalId();
            Long patientId = data.getPatientId();
            wxPayService.checkAppidAndOpenid(map,hospitalId1,data.getPartnersCode(),patientId,pCode);
            // 该服务包对应的患者id -- 对应原来的FamilyId
            map.put("patientId", String.valueOf(data.getFamilyId()));
            // 下单用户对应的id -- 对应原来的PatientId
            map.put("userId", String.valueOf(data.getPatientId()));
            map.put("orderNo", data.getOrderNo());
            map.put("amount", data.getAmount());
            map.put("content", "服务包订单-订单编号" + data.getOrderNo());
            map.put("autoActivate", data.getAutoActivate());
        }
        // 获取订单金额
        double amount = Double.parseDouble(map.get("amount"));
        // 校验订单金额
        if (amount <= 0.0) {
            return AjaxResult.error("订单实际支付金额必须大于0！");
        }
        try {
            JSONObject result = wxPayService.wxPay(map);
            Integer status = result.getInteger("status");
            if (status != null && status == 0) {
                // 支付失败不返回前端拉起微信支付参数
                return AjaxResult.error("支付失败，请稍后再试");
            }
            return AjaxResult.success(result);
        } catch (ServiceException e){
            log.warn(e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage());
            return AjaxResult.error("支付失败，请稍后再试");
        }
    }

    /**
     * 服务包退款
     *
     * @param orderId  订单ID
     * @param orderType  订单类型
     * @param refundAmount  退款金额
     * @param appType  应用类型
     * @return
     */
    @PostMapping("service/pack/refund")
    @Log(title = "微信退款服务包", businessType = BusinessType.OTHER)
    public AjaxResult servicePackRefund(Long orderId, String orderType, Double refundAmount, String appType, Integer pCode) {
        log.debug("服务包退款接口入orderId={}，orderId={}，refundAmount={}，appType={}，pCode={}", orderId, orderType, refundAmount, appType, pCode);
        Map<String, Object> map = new HashMap<>();
        map.put("refundAmount", refundAmount);
        map.put("appType", appType);
        if (orderType.equals(OrderTypeEnum.SERVICE_PACK.getCode())) {
            R<ServicePackOrderResponse> r = remoteServicePackOrderService.queryInfo(orderId, "");
            log.info("远程调用服务包订单查询结果：{}", JSON.toJSONString(r));
            ServicePackOrderResponse data = r.getData();
            if (StringUtils.isNotEmpty(data.getPartnersCode())) {
                map.put("partnersCode", data.getPartnersCode());
            }
            map.put("outTradeNo", data.getOrderNo());
            map.put("amount", data.getAmount());
            map.put("hospitalId", data.getHospitalId());
        }

        double orderAmount = Double.parseDouble((String) map.get("amount"));
        int res = BigDecimal.valueOf(orderAmount).compareTo(BigDecimal.valueOf(refundAmount));
        if (res < 0) {
            return AjaxResult.error("退款金额与订单金额不符！");
        }
        map.put("orderAmount", orderAmount);
        JSONObject result = wxPayService.refund(map);
        log.info("服务包微信退款出参 result={}", result.toJSONString());
        return AjaxResult.success(result);
    }

    @GetMapping("/query")
    @Log(title = "查询微信支付订单状态", businessType = BusinessType.QUERY)
    public R<?> queryOrder(Long hospitalId, String appType, String orderNo) {
        log.info("查询订单支付状态，hospitalId={}，appType={}，orderNo={}", hospitalId, appType, orderNo);
        JSONObject jsonObject = wxPayService.queryOrder(hospitalId, appType,null, orderNo);
        log.info("微信返回jsonObject={}", jsonObject.toJSONString());
        String code = jsonObject.getString("code");
        if ("ORDER_NOT_EXIST".equals(code)) {
            return R.fail("该订单无须补单！");
        }
        String tradeState = jsonObject.getString("trade_state");
        if ("NOTPAY".equals(tradeState)) {
            return R.fail("该订单无须补单！");
        }
        // 查询售后信息
        String status = busAfterSaleService.queryAfterSale(orderNo);
        String outTradeNo = jsonObject.getString("out_trade_no");
        String flag = outTradeNo.substring(0, 2);
        // 支付成功
        if ("SUCCESS".equals(tradeState)) {
            if (StringUtils.isEmpty(status)) {
                switch (flag) {
                    case OrderTypeConstant.DRUGS_ORDER:
                        // 修改药品订单状态
                        wxPayService.payUpdateDrugsOrderStatus(orderNo);
                        break;
                    case OrderTypeConstant.CONSULATION_ORDER:
                        // 修改问诊订单状态
                        wxPayService.payUpdateConsultationOrderStatus(orderNo);
                        break;
                    case OrderTypeConstant.SERVICE_PACK_ORDER:
                        // 修改服务包订单状态
                        R<ServicePackOrderResponse> responseR = remoteServicePackOrderService.queryInfo(null, outTradeNo);
                        ServicePackOrderResponse data = responseR.getData();
                        log.info("远程调用查询服务包code={}，查询结果={}", responseR.getCode(), data);
                        String autoActivate = data.getAutoActivate();
                        wxPayService.payUpdateServicePackOrderStatus(orderNo, autoActivate);
                        break;
                    default:
                }
                return R.ok("操作成功！");
            }
        }
        // 转入退款
        if ("REFUND".equals(tradeState)) {
            if (StringUtils.isEmpty(status) || !AfterSaleStatusEnum.DISAPPROVAL.getCode().equals(status)) {
                switch (flag) {
                    case OrderTypeConstant.DRUGS_ORDER:
                        wxPayService.refundUpdateDrugsOrderStatus(orderNo);
                        break;
                    case OrderTypeConstant.CONSULATION_ORDER:
                        wxPayService.refundConsultationOrderStatus(orderNo);
                        break;
                    case OrderTypeConstant.SERVICE_PACK_ORDER:
                        wxPayService.refundServicePackOrderStatus(orderNo);
                        break;
                    default:
                }
                return R.ok("操作成功！");
            }
        }
        return R.fail("该订单无须补单！");
    }

    /**
     * 远程调用修改药品订单状态
     *
     * @param orderNo - 订单编号
     * @return 成功
     */
    @GetMapping("/feign/update/status")
    public R<Objects> updateOrderStatus(@RequestParam("orderNo") String orderNo) {
        log.info("订单编号={}", orderNo);
        BusOrder order = new BusOrder();
        order.setOrderNo(orderNo);
        wxPayService.payUpdateDrugsAndGoodsOrderStatus(order);
        return R.ok();
    }

    /**
     * 推送药品配送企业订单
     *
     * @param orderId 子订单ID
     * @return 成功
     */
    @PostMapping("/feign/push/drugOrder")
    public R<?> pushDrugOrderToEnterprise(@RequestParam("orderId") Long orderId) {
        BusOrder order = busOrderService.selectOreder(CodeEnum.NO.getCode(), orderId);
        wxPayService.pushDrugOrderToEnterprise(order);
        return R.ok();
    }

    /**
     * 推送商品配送企业订单
     *
     * @param orderId 子订单ID
     * @return 成功
     */
    @PostMapping("/feign/push/shopOrder")
    public R<?> pushShopOrderToEnterprise(@RequestParam("orderId") Long orderId) {
        BusOrder order = busOrderService.selectOreder(CodeEnum.YES.getCode(), orderId);
        wxPayService.pushShopOrderToEnterprise(order);
        return R.ok();
    }

    /**
     * 医保退款成功流转售后订单状态
     *
     * @param orderAfterSaleId 售后订单ID
     * @param orderNo          总订单编号
     * @return 成功
     */
    @GetMapping("/feign/update/orderStatus")
    public R<?> updateOrderStatus(@RequestParam("orderAfterSaleId") Long orderAfterSaleId,
                               @RequestParam("orderNo") String orderNo) {
        wxPayService.refundTotalOrderStatus(orderAfterSaleId, orderNo);
        return R.ok();
    }

    /**
     * 医保订单退款成功批量更新售后订单状态
     * @param orderNo - 总订单编号
     * @return - 是否成功
     */
    @GetMapping("/feign/update-batch/after-sales-order-status")
    public R<Boolean> updateBatchAfterSalesOrderStatus(@RequestParam("orderNo") String orderNo) {
        wxPayService.updateInsuranceRefundAfterSaleOrderStatus(orderNo);
        return R.ok(true);
    }
    /**
     * 截取交易订单编号
     *
     * @param orderNo - 订单编号
     * @return - 截取后的订单编号
     */
    private String interceptOrderNo(String orderNo) {
        return orderNo.split("_")[0];
    }

    @PostMapping("/active-service-pack")
    public R<Boolean> payUpdateServicePackOrderStatus(@RequestParam("orderNo") String orderNo, @RequestParam("autoActivate") String autoActivate) {
        wxPayService.payUpdateServicePackOrderStatus(orderNo, autoActivate);
        return R.ok(true);
    }

    /**
     * 查询微信支付订单详情
     *
     * @param hospitalId 医院id
     * @param appType    应用类型//TODO-看能不能去掉这个参数
     * @param orderNo    订单编号
     * @return 返回微信支付订单详情
     */
    @GetMapping("/query-wx-pay-detail")
    public R<JSONObject> queryWxPayDetail(@RequestParam("hospitalId") Long hospitalId,
                                          @RequestParam("appType") String appType,
                                          @RequestParam("orderNo") String orderNo) {
        return R.ok(wxPayService.queryOrder(hospitalId, appType, null, orderNo));
    }
}
