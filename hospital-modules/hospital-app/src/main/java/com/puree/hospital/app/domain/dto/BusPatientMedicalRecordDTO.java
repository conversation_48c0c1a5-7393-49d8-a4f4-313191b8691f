package com.puree.hospital.app.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.his.constants.HisConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Objects;

/**
 * 患者就诊记录 DTO
 *
 * <AUTHOR>
 * @date 2025/2/26 18:27
 */
@Data
@NoArgsConstructor
public class BusPatientMedicalRecordDTO {

    /**
     * 查询开始时间
     */
    @NotNull(message = "请填写开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 查询结束时间
     */
    @NotNull(message = "请填写结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 就诊人id
     */
    @NotNull(message = "就诊人不能为空")
    private Long patientId;

    /**
     * 医疗记录类型：1.门诊病历 2.检查报告 3.检验报告 4.处方信息
     */
    private Integer recordType;

    /**
     * 报告ID
     */
    private String reportId;

    /**
     * 医嘱项目编码
     */
    private String itemCode;

    /**
     * 门诊流水号
     */
    private String tranSerialNo;

    /**
     * 处方号
     */
    private String hisOrderRecId;

    /**
     * 处方类型
     */
    private Integer prescriptionTypeName;

    @Getter
    @AllArgsConstructor
    public enum RecordTypeEnum {
        // 医疗记录类型
//        RECORD_0(0, "全部"),
        RECORD_1(1, HisConstant.Item.EMR.name()),
        RECORD_2(2, HisConstant.Item.INSPECT_REPORT.name()),
        RECORD_3(3, HisConstant.Item.EXAMINE_REPORT.name()),
        RECORD_4(4, HisConstant.Item.PRESCRIPTION.name());

        /**
         * 医疗记录类型编码
         */
        private final Integer code;

        /**
         * 医疗记录类型名称
         */
        private final String name;
    }

    /**
     * 校验医疗记录类型
     * @return 是否存在
     */
    public boolean checkRecordType(){
        if(this.recordType == null){
            return false;
        }
        for (RecordTypeEnum value : RecordTypeEnum.values()) {
            if(this.recordType.equals(value.getCode())){
                return true;
            }
        }
        return false;
    }

    /**
     * 校验医疗记录类型
     * @return 是否存在
     */
    public String getRecordNameByCode(){
        for (RecordTypeEnum value : RecordTypeEnum.values()) {
            if (Objects.equals(value.getCode(), recordType)) {
                return value.getName();
            }
        }
        return null;
    }

}
