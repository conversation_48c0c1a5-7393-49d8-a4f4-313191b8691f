package com.puree.hospital.app.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 处方药师签名
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/27 下午6:51
 */
@Data
public class PrescriptionPharmacistSignDTO implements Serializable {

    private static final long serialVersionUID = 6690838721673854338L;

    /**
     * 处方编号
     */
    private String prescriptionNumber;

    /**
     * 处方id
     */
    private Long prescriptionId;

    /**
     * 药师身份证号码
     */
    private String pharmacistIdCard;

    /**
     * 处方签名类型：审核，发货， 调剂
     */
    private SignTypeEnum signType;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 签名类型枚举
     */
   public enum SignTypeEnum {

        /**
         * 审核
         */
        APPROVE,

        /**
         * 发货
         */
        DELIVERY,

        /**
         * 调剂
         */
        ADJUSTMENT;
    }
}
