package com.puree.hospital.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusHospital;
import com.puree.hospital.app.domain.bo.BusDoctorBO;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.mapper.BusDoctorHospitalMapper;
import com.puree.hospital.app.mapper.BusHospitalMapper;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.app.service.IBusHospitalService;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.system.api.RemoteSysDictDataService;
import com.puree.hospital.system.api.model.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/29 17:10
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorHospitalServiceImpl implements IBusDoctorHospitalService {
    private final BusDoctorHospitalMapper busDoctorHospitalMapper;
    private final RemoteSysDictDataService remoteSysDictDataService;
    private final BusHospitalMapper busHospitalMapper;

    @Override
    public BusDoctorHospital selectDoctorHospitalInfo(Long hospitalId, Long doctorId) {
        LambdaQueryWrapper<BusDoctorHospital> queryWrapper = new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, hospitalId)
                .eq(BusDoctorHospital::getDoctorId, doctorId);
        return busDoctorHospitalMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BusDoctorBO> list(BusDoctorDto dto) {
        BusDoctorBO busDoctorBO = new BusDoctorBO();
        BeanUtils.copyProperties(dto, busDoctorBO);
        LambdaQueryWrapper<BusHospital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusHospital::getIsTest, 1);
        List<BusHospital> busHospitals = busHospitalMapper.selectList(queryWrapper);

        busDoctorBO.setExcludeHospitalIdList(busHospitals.stream().map(BusHospital::getId).collect(Collectors.toList()));
        //查询医生信息
        PageUtil.startPage();
        List<BusDoctorBO> busDoctorBOS = busDoctorHospitalMapper.selectDoctorList(busDoctorBO);

        //查询医生职称字典数据
        SysDictData dictDataQuery = new SysDictData();
        dictDataQuery.setDictType("bus_doctor_title");
        R<List<SysDictData>> busDoctorTitleList = remoteSysDictDataService.getDictList(dictDataQuery, SecurityConstants.INNER);

        Map<Long, String> titelMap = new HashMap<>();
        List<SysDictData> data = busDoctorTitleList.getData();
        data.forEach(e -> titelMap.put(e.getDictCode(), e.getDictLabel()));
        //设置医生职称
        busDoctorBOS.forEach(busDoctorVo -> {
           busDoctorVo.setTitleValue(titelMap.get(busDoctorVo.getTitle()));
        });

        return busDoctorBOS;
    }

    @Override
    public List<BusDoctorHospital> selectDoctorHospitalListByDoctorId(Long doctorId) {
        if (doctorId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BusDoctorHospital> queryWrapper = new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getDoctorId, doctorId);
        return busDoctorHospitalMapper.selectList(queryWrapper);
    }
}
