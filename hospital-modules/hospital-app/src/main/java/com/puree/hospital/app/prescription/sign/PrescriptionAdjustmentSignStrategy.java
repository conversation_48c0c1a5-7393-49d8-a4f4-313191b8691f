package com.puree.hospital.app.prescription.sign;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.puree.hospital.app.api.model.enums.SignTypeEnum;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.dto.PrescriptionSignDTO;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 处方调剂、复核签名策略
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/13 16:45
 */
@Component
public class PrescriptionAdjustmentSignStrategy extends AbstractPrescriptionSignStrategy {
    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;
    @Override
    public String getOperatorName(PrescriptionSignDTO prescription) {
        return prescription.getDispensingPharmacistName();
    }
    @Override
    public Long getOperatorId(PrescriptionSignDTO prescription) {
        return prescription.getDispensingPharmacistId();
    }

    @Override
    protected String getSignFlag() {
        return "{{adjustment_pharmacist}}";
    }

    @Override
    public SignTypeEnum getSignType() {
        return SignTypeEnum.ADJUSTMENT;
    }

    @Override
    public int updateOperater(PrescriptionSignDTO prescription) {
        UpdateWrapper<BusPrescription> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", prescription.getPrescriptionId());
        BusPrescription updateEntity = new BusPrescription();
        updateWrapper.isNull("dispensing_pharmacist_id")
                .isNull("dispensing_pharmacist_name");
        updateEntity.setDispensingPharmacistId(getOperatorId(prescription));
        updateEntity.setDispensingPharmacistName(getOperatorName(prescription));
        return busPrescriptionMapper.update(updateEntity, updateWrapper);
    }

    @Override
    public String getDigestSignature(List<String> digestNames) {
        if(digestNames !=null && digestNames.size()>2)
        {
            return digestNames.get(2);
        }
        return null;
    }

    @Override
    public String getObjectType() {
        return "7";
    }

}
