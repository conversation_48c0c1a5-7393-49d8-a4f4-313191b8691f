package com.puree.hospital.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 医生患者VO
 */
@Data
public class BusDoctorPatientVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /** id*/
    private Long id;
    /** 患者姓名*/
    private String name;
    /** 医生ID */
    private Long doctorId;
    /** 患者ID */
    private Long patientId;
    /** 医院 ID */
    private Long hospitalId;
    /** 患者来源*/
    private Integer patientSource;
    /**等级*/
    private Integer grade;
    /** 科室id */
    private Long departmentId;
    /** 科室名称*/
    private Long departmentName;

    /** 手机号码*/
    private String cellPhoneNumber;
    /** 性别*/
    private Integer sex;
    /** 出生年月日*/
    private String dateOfBirth;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    private Long dpgId;

    private Long familyId;

    private Long gId;

    private Integer nation;

    private String maritalStatus;

    private List<BusPrescription> prescriptionList;

    private List<BusDoctorPatientGroup> patientGroupList;

    private String  familyAge;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 证件类型  01：居民身份证  03：护照  06：港澳居民来往内地通行证  07：台湾居民来往内地通行证
     */
    private String idType;

    public String getIdType(){
        IdCardTypeEnum idCardType = IdCardTypeEnum.getByCode(idType);
        return Objects.isNull(idCardType) ? null : idCardType.getLabel();
    }
}
