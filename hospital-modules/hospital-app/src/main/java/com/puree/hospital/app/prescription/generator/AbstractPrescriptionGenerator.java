package com.puree.hospital.app.prescription.generator;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusConsultationOrder;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusEnterpriseDrugsPrice;
import com.puree.hospital.app.domain.BusHospitalOfficina;
import com.puree.hospital.app.domain.BusHospitalPreorderDoctor;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPreorderPatient;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionCd;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.BusQuickConsultation;
import com.puree.hospital.app.domain.bo.PrescriptionCreateContext;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.HospitalOfficinaDrugsDTO;
import com.puree.hospital.app.domain.dto.PrescriptionGenerateResultDTO;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.prescription.helper.BusPrescriptionDrugStockHelper;
import com.puree.hospital.app.infrastructure.supplier.SupplierFactory;
import com.puree.hospital.app.mapper.BusConsultationOrderMapper;
import com.puree.hospital.app.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusEnterpriseDrugsPriceMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPreorderPatientMapper;
import com.puree.hospital.app.mapper.BusPrescriptionCdMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusQuickConsultationMapper;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusHospitalOfficinaService;
import com.puree.hospital.app.service.IBusHospitalPreorderDoctorService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.business.api.RemoteBusDiagnosisService;
import com.puree.hospital.business.api.model.DiagnosisVO;
import com.puree.hospital.business.api.model.dto.DiagnosisDTO;
import com.puree.hospital.common.core.calculator.price.IPriceCalculator;
import com.puree.hospital.common.core.calculator.price.drug.MmDrugPriceCalculator;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.*;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.five.api.RemoteServicePackService;
import com.puree.hospital.five.api.model.BusFiveGroupMember;
import com.puree.hospital.five.api.model.BusFiveServicePack;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import com.puree.hospital.five.api.model.ServicePackOrderRequest;
import com.puree.hospital.five.api.model.ServicePackOrderResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 抽象的处方生类
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14 9:46
 */
@Slf4j
public abstract class AbstractPrescriptionGenerator implements IPrescriptionGenerator {

    /**
     * 处方号生成规则
     */
    protected final DateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    protected final IPriceCalculator<BusPrescriptionDrugs> wmDrugPriceCalculator = new MmDrugPriceCalculator<>();

    @Resource
    private BusConsultationSettingsMapper consultationSettingsMapper;

    @Resource
    private BusPreorderPatientMapper busPreorderPatientMapper;

    @Lazy
    @Resource
    private IBusPrescriptionService busPrescriptionService;

    @Lazy
    @Resource
    private IBusConsultationOrderService busConsultationOrderService;

    @Resource
    private RemoteServicePackOrderService remoteServicePackOrderService;

    @Resource
    private IBusDoctorPatientGroupService busDoctorPatientGroupService;

    @Resource
    private RemoteServicePackService remoteServicePackService;

    @Resource
    private IBusHospitalOfficinaService busHospitalOfficinaService;

    @Resource
    protected SupplierFactory supplierFactory;

    @Resource
    private BusDoctorMapper busDoctorMapper;

    @Resource
    private IBusDoctorHospitalService busDoctorHospitalService;

    @Resource
    private IBusHospitalPreorderDoctorService busHospitalPreorderDoctorService;

    @Resource
    private BusConsultationOrderMapper busConsultationOrderMapper;

    @Resource
    private BusQuickConsultationMapper busQuickConsultationMapper;

    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;

    @Resource
    protected BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;

    @Resource
    protected BusEnterpriseDrugsPriceMapper busEnterpriseDrugsPriceMapper;

    @Resource
    protected BusPrescriptionCdMapper busPrescriptionCdMapper;

    @Resource
    private BusPatientFamilyMapper busPatientFamilyMapper;

    @Resource
    private BusPrescriptionDrugStockHelper busPrescriptionDrugStockHelper;

    @Resource
    private RemoteBusDiagnosisService remoteBusDiagnosisService;


    @Override
    public PrescriptionGenerateResultDTO generate(BusPrescriptionDto prescriptionDto) {
        //校验参数
        if (Objects.isNull(prescriptionDto)) {
            throw new ServiceException("处方参数缺失");
        }
        List<BusPrescriptionDrugs> prescriptionDrugs = prescriptionDto.getList();
        if (CollectionUtil.isEmpty(prescriptionDrugs)) {
            throw new ServiceException("当前处方还未添加药品！");
        }
        //处方药品设置用法
        prescriptionDrugs.forEach(e -> {
            if (StringUtils.isBlank(e.getUsages())) {
                e.setUsages(prescriptionDto.getUsages());
            }
        });
        //查询医院设置
        BusConsultationSettings setting = consultationSettingsMapper.selectOne(new LambdaQueryWrapper<BusConsultationSettings>()
                .eq(BusConsultationSettings::getHospitalId, prescriptionDto.getHospitalId()));
        if (Objects.isNull(setting)) {
            throw new ServiceException("医院配置信息缺失");
        }
        //组装参数信息
        assemblePrescription(prescriptionDto, setting);
        //校验并更新处方信息
        PrescriptionCreateContext context = new PrescriptionCreateContext();
        context.setBusConsultationSettings(setting);
        checkAndUpdate(prescriptionDto, context);
        PrescriptionGenerateResultDTO dto = new PrescriptionGenerateResultDTO();
        //药品下架或库存不足则直接返回
        if (CollectionUtils.isNotEmpty(context.getOutOfStockDrugIds())) {
            dto.setErrorDrugsId(context.getOutOfStockDrugIds());
            dto.setDrugStatus(context.getDrugStatus());
            return dto;
        }
        //处方信息拷贝
        BusPrescription busPrescription = OrikaUtils.convert(prescriptionDto, BusPrescription.class);
        //设置处方总金额
        busPrescription.setPrescriptionAmount(calculatePrice(prescriptionDto, busPrescription, context));
        //设置处方的费用结算类别
        assembleFeeSettleTypeInfo(busPrescription);
        //校验处方药品用药天数
        checkPrescriptionDayLimit(busPrescription, prescriptionDto.getList());
        //执行双通道
        Object extraData = dualChannelCheck(busPrescription, prescriptionDto.getList());
        //保存处方信息
        savePrescription(busPrescription, prescriptionDto.getList(), extraData);
        //订单推送成功
        dto.setPrescriptionId(busPrescription.getId());
        return dto;
    }



    /**
     * 校验和更新处方信息
     *
     * @param dto     处方参数
     * @param context 上下文信息
     */
    protected void checkAndUpdate(BusPrescriptionDto dto, PrescriptionCreateContext context) {
        // 基础校验
        List<BusPrescriptionDrugs> drugsList = Optional.ofNullable(dto)
                .map(BusPrescriptionDto::getList)
                .orElse(Collections.emptyList());
        if (drugsList.isEmpty()) {
            throw new ServiceException("处方药品不能为空");
        }
        //提取并去重药品ID
        Set<Long> drugsIds = drugsList.stream()
                .map(BusPrescriptionDrugs::getDrugsId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (drugsIds.isEmpty()) {
            throw new ServiceException("处方药品ID非法");
        }
        // 查询药房药品信息
        HospitalOfficinaDrugsDTO queryDto = new HospitalOfficinaDrugsDTO();
        queryDto.setDrugsIds(new ArrayList<>(drugsIds));
        queryDto.setHospitalId(dto.getHospitalId());
        List<BusHospitalOfficina> officinaList = busHospitalOfficinaService.selectOfficinaList(queryDto);
        // 转Map
        Map<Long, BusHospitalOfficina> officinaMap = officinaList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(BusHospitalOfficina::getDrugsId, Function.identity(), (a, b) -> a));
        //未找到药房药品
        List<String> notFoundNames = new ArrayList<>();
        //缺货
        List<Long> outOfStockIds = new ArrayList<>();
        // 下架+未找到药房药品
        for (BusPrescriptionDrugs drug : drugsList) {
            //药品不存在
            BusHospitalOfficina officina = officinaMap.get(drug.getDrugsId());
            if (officina == null) {
                String name = StringUtils.isBlank(drug.getStandardCommonName())
                        ? drug.getDrugsName() : drug.getStandardCommonName();
                notFoundNames.add(name);
                continue;
            }
            //下架药品
            if (EnableStatusEnum.isDisabled(officina.getStatus())) {
                outOfStockIds.add(drug.getDrugsId());
            }
            // 设置价格
            drug.setSellingPrice(officina.getSellingPrice());
        }
        // 处理药品不存在
        if (!notFoundNames.isEmpty()) {
            throw new ServiceException(String.format("药品%s不存在,请删除更换其他药品.", StringUtils.join(notFoundNames, ",")));
        }
        //处理缺货
        if (!outOfStockIds.isEmpty()) {
            context.setDrugStatus(EnableStatusEnum.DISABLED.getStatus());
            context.getOutOfStockDrugIds().addAll(outOfStockIds);
            return;
        }
        context.setHospitalOfficinaMap(officinaMap);
        checkAndUpdateDetail(dto, context);
    }


    /**
     * 校验和更新明细
     *
     * @param dto      处方参数
     * @param context  上下文
     */
    protected abstract void checkAndUpdateDetail(BusPrescriptionDto dto, PrescriptionCreateContext context);

    /**
     * 计算计算处方价格
     *
     * @param dto 处方参数
     * @return 处方总金额
     */
    protected abstract BigDecimal calculatePrice(BusPrescriptionDto dto, BusPrescription prescription, PrescriptionCreateContext context);

    /**
     * 组装信息
     *
     * @param prescriptionDto 处方参数
     */
    private void assemblePrescription(BusPrescriptionDto prescriptionDto, BusConsultationSettings setting) {

        if (Objects.isNull(prescriptionDto) || Objects.isNull(prescriptionDto.getFamilyId())) {
            throw new ServiceException("就诊人id不能为空");
        }

        Date currentDate = DateUtils.getNowDate();

        //设置处方合作机构编码信息
        prescriptionDto.setPartnersCode(getPartnerCode(prescriptionDto));
        prescriptionDto.setPrescriptionNumber(getPrescriptionNumber());
        prescriptionDto.setStatus(PrescriptionStatus.NOT_EXAMINE);
        prescriptionDto.setPrescriptionTime(currentDate);
        prescriptionDto.setCreateBy(SecurityUtils.getUsername());
        prescriptionDto.setCreateTime(currentDate);

        // 验证并获取医生信息
        BusDoctorHospital doctorHospital = busDoctorHospitalService.selectDoctorHospitalInfo(prescriptionDto.getHospitalId(), prescriptionDto.getDoctorId());
        if (Objects.isNull(doctorHospital)) {
            log.warn("医生不存在 ！hospitalId={},doctorId={}", prescriptionDto.getHospitalId(), prescriptionDto.getDoctorId());
            throw new ServiceException("医生不存在 ！");
        }
        //判断是否是特邀
        if (!DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(doctorHospital.getIsThisCourt())) {
            prescriptionDto.setExpertId(null);
        } else {
            //特邀医生开的处方 处方的医生信息设置为医院默认设置的开方医生
            BusHospitalPreorderDoctor busHospitalPreorderDoctor = busHospitalPreorderDoctorService.selectByHospitalId(prescriptionDto.getHospitalId());
            if (busHospitalPreorderDoctor == null) {
                throw new IllegalArgumentException("开方失败，医院未设置默认开方医生");
            }
            Long doctorId = busHospitalPreorderDoctor.getDoctorId();
            BusDoctorVo busDoctorVo = busDoctorMapper.selectDoctorInfoById(doctorId);
            prescriptionDto.setDoctorId(doctorId);
            prescriptionDto.setDoctorName(busDoctorVo.getFullName());
        }
        // 验证就诊人信息
        BusPatientFamily patientFamily = busPatientFamilyMapper.selectById(prescriptionDto.getFamilyId());
        if (Objects.isNull(patientFamily)) {
            throw new ServiceException("就诊人信息不存在");
        }

        //设置就诊人相关信息
        prescriptionDto.setFamilyName(patientFamily.getName());
        prescriptionDto.setFamilySex(patientFamily.getSex());
        prescriptionDto.setFamilyTel(patientFamily.getCellPhoneNumber());
        prescriptionDto.setFamilyIdcard(patientFamily.getIdNumber());

        StringBuilder addressBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(patientFamily.getLocation())) {
            addressBuilder.append(patientFamily.getLocation());
        }
        if (StringUtils.isNotBlank(patientFamily.getDetailAddress())) {
            addressBuilder.append(patientFamily.getDetailAddress());
        }
        prescriptionDto.setFamilyDetailAddress(addressBuilder.toString());


        //设置处方失效时间
        prescriptionDto.setValidTime(getExpireTime(prescriptionDto.getPrescriptionTime(), setting));
        //修复重新生成处方bug
        prescriptionDto.setId(null);
        prescriptionDto.setReviewTime(null);
        prescriptionDto.setReviewPharmacistId(null);
        prescriptionDto.setReviewPharmacistName(null);
        prescriptionDto.setNotApprovedReason(null);
    }

    /**
     * 判断获取处方关联的合作机构编号
     *
     * @param dto 处方参数
     * @return 合作机构编号
     */
    private String getPartnerCode(BusPrescriptionDto dto) {
        String partnersCode = null;
        //只有线上开方才有机构编码
        if (CodeEnum.YES.getCode().equals(dto.getType())) {
            if (Objects.nonNull(dto.getPreorderId())) {
                partnersCode = getPartnerCodeByDrugPreOrder(dto);
            } else {
                partnersCode = getPartnerCodeByConsultationOrder(dto);
            }
            if (Objects.nonNull(dto.getServicePackId())) {
                partnersCode = getPartnerCodeByServicePackOrder(dto);
            }

        }
        return partnersCode;
    }

    /**
     * 获取用药预订单机构编号
     *
     * @param dto 处方参数
     * @return 渠道编号
     */
    private String getPartnerCodeByDrugPreOrder(BusPrescriptionDto dto) {
        BusPreorderPatient busPreorderPatient = busPreorderPatientMapper.selectById(dto.getPreorderId());
        if (Objects.isNull(busPreorderPatient)) {
            throw new ServiceException("患者发起用药订单已经取消或者不存在");
        }
        if (Objects.nonNull(busPreorderPatient.getDoctorId())
                && !busPreorderPatient.getDoctorId().equals(dto.getDoctorId())) {
            throw new ServiceException("该用药预定单已被其他医生负责!");
        }
        // 是否有处方在审核中
        busPrescriptionService.checkPrescription(busPreorderPatient);
        // 查询预订单，获取机构code
        return busPreorderPatient.getPartnersCode();
    }

    private String getPartnerCodeByConsultationOrder(BusPrescriptionDto dto) {
        BusConsultationOrder busConsultationOrder = busConsultationOrderService.selectNewestConsultationOrder(dto);
        if (Objects.nonNull(busConsultationOrder)) {
            return busConsultationOrder.getPartnersCode();
        }
        return null;
    }

    private String getPartnerCodeByServicePackOrder(BusPrescriptionDto dto) {
        //判断开方医生是否存在该服务包 存在则设置 groupId
        BusDoctorPatientGroup busDoctorPatientGroup = new BusDoctorPatientGroup();
        busDoctorPatientGroup.setServiceId(dto.getServicePackId());
        busDoctorPatientGroup.setFamilyId(dto.getFamilyId());
        busDoctorPatientGroup.setPatientId(dto.getPatientId());
        busDoctorPatientGroup.setHospitalId(dto.getHospitalId());
        busDoctorPatientGroup.setType(CodeEnum.YES.getCode());
        BusDoctorPatientGroup groupInfo = busDoctorPatientGroupService.selectOne(busDoctorPatientGroup);
        if (Objects.isNull(groupInfo)) {
            throw new ServiceException("未找到服务包未关联聊天群组");
        }
        //此处存在特聘庄家服务包场景开方 -> 后台配置的医生开方作废，重新开方，如果当前的医生不在服务包成员中，则处方不能走组发送
        BusFiveServicePack busFiveServicePack = new BusFiveServicePack();
        busFiveServicePack.setHospitalId(dto.getHospitalId());
        busFiveServicePack.setId(dto.getServicePackId());
        R<BusFiveServicePackVO> fiveServicePackResult = remoteServicePackService.queryServicePack(busFiveServicePack);
        if (Objects.isNull(fiveServicePackResult)
                || !fiveServicePackResult.isSuccess()
                || Objects.isNull(fiveServicePackResult.getData())) {
            throw new ServiceException("未找到服务包关联的成员信息");
        }
        BusFiveServicePackVO servicePack = fiveServicePackResult.getData();
        //服务包的成员id
        List<Long> memberIds = Lists.newArrayList(servicePack.getLeaderId());
        // 服务包成员
        List<BusFiveGroupMember> groupMembers = servicePack.getWorkGroupVo().getGroupMembers();
        if (CollectionUtil.isNotEmpty(groupMembers)) {
            memberIds.addAll(groupMembers.stream().map(BusFiveGroupMember::getMemberId).distinct().collect(Collectors.toList()));
        }
        if (memberIds.contains(dto.getDoctorId())) {
            dto.setGroupId(groupInfo.getId());
        } else {
            dto.setGroupId(null);
            dto.setServicePackId(null);
            return null;
        }
        // 查询最新的服务包订单
        ServicePackOrderRequest request = new ServicePackOrderRequest();
        request.setHospitalId(dto.getHospitalId());
        // 如果医生id为null，说明是特聘专家开药品清单
        if (Objects.nonNull(dto.getDoctorId())) {
            request.setDoctorId(dto.getDoctorId());
        }
        request.setPatientId(dto.getPatientId());
        request.setFamilyId(dto.getFamilyId());
        request.setServicePackId(dto.getServicePackId());
        R<ServicePackOrderResponse> responseR = remoteServicePackOrderService.queryLatestOrder(request, SecurityConstants.INNER);
        ServicePackOrderResponse data = responseR.getData();
        log.info("远程调用最新服务包订单code={},查询结果={}", responseR.getCode(), data);
        if (Objects.nonNull(data)) {
            return data.getPartnersCode();
        }
        return null;
    }

    /**
     * 获取处方号
     *
     * @return 处方号
     */
    private String getPrescriptionNumber() {
        return DATE_FORMAT.format(new Date());
    }

    protected Integer nullToZero(Integer integer) {
        return Objects.isNull(integer) ? 0 : integer;
    }

    /**
     * 组装结算类型信息
     *
     * @param busPrescription 处方信息
     */
    private void assembleFeeSettleTypeInfo(BusPrescription busPrescription) {
        BusQuickConsultation busQuickConsultation = null;
        String specialDiseaseInfo = null;
        String miAuthNo = null;
        //用药开方和问诊开方需要区分开
        if ("1".equals(busPrescription.getIdentity())) {
            if (Objects.nonNull(busPrescription.getPreorderId())) {
                BusPreorderPatient busPreorderPatient = busPreorderPatientMapper.selectById(busPrescription.getPreorderId());
                if (Objects.nonNull(busPreorderPatient)) {
                    busQuickConsultation = busQuickConsultationMapper.selectById(busPreorderPatient.getConsultationId());
                }
            }
        } else {
            if (Objects.nonNull(busPrescription.getConsultationOrderId())) {
                BusConsultationOrder consultationOrder = busConsultationOrderMapper.selectById(busPrescription.getConsultationOrderId());
                if (Objects.nonNull(consultationOrder)) {
                    busQuickConsultation = busQuickConsultationMapper.selectById(consultationOrder.getConsultationId());
                }
            }
        }
        String feeSettleType = FeeSettleTypeEnum.SELF.getType();
        if (Objects.nonNull(busQuickConsultation) && StringUtils.isNotBlank(busQuickConsultation.getFeeSettleType())) {
            feeSettleType = busQuickConsultation.getFeeSettleType();
            specialDiseaseInfo = busQuickConsultation.getSpecialDiseaseInfo();
            miAuthNo = busQuickConsultation.getMiAuthNo();
        }
        if (FeeSettleTypeEnum.isMiSpecialsSettle(feeSettleType)) {
            checkSpecialDisease(specialDiseaseInfo, busPrescription);
        }
        busPrescription.setFeeSettleType(feeSettleType);
        busPrescription.setSpecialDiseaseInfo(specialDiseaseInfo);
        busPrescription.setMiAuthNo(miAuthNo);
    }

    /**
     *  校验是否有诊断限定不支持医保，如果有只能走自费
     *  <p>
     *     进过沟通，不进行自动更改，医生去处理。后续不用可以删除
     *  </p>
     *
     * @param busPrescription   处方单入参
     * @return  false-有限定不支持医保，true-支持医保
     */
    @SuppressWarnings("unused")
    private boolean checkDiagnosisSupportMiPayment(BusPrescription busPrescription) {
        DiagnosisDTO diagnosisListDTO = new DiagnosisDTO();
        diagnosisListDTO.setType(0);
        diagnosisListDTO.setPrescriptionType(busPrescription.getPrescriptionType());
        String clinicalDiagnosis = busPrescription.getClinicalDiagnosis();
        if (StringUtils.isBlank(clinicalDiagnosis)) {
            throw new ServiceException("诊断信息不能为空");
        }
        ArrayList<Long> diagnosisIds = new ArrayList<>();
        try {
            JSONArray jsonArray = JSON.parseArray(clinicalDiagnosis);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                diagnosisIds.add(jsonObject.getLongValue("id"));
            }
        } catch (Exception e) {
            throw new ServiceException("诊断信息格式错误");
        }
        if (CollectionUtil.isEmpty(diagnosisIds)) {
            throw new ServiceException("诊断信息不能为空");
        }
        diagnosisListDTO.setDiagnosisIds(diagnosisIds);
        R<List<DiagnosisVO>> result = remoteBusDiagnosisService.getListByIds(diagnosisListDTO);
        // 如果没有返回的数据
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            return false;
        }
        return Boolean.TRUE;
    }

    /**
     * 校验处方诊断是否包含门特门慢
     *
     * @param specialDiseaseInfo 诊断信息
     * @param busPrescription    处方信息
     */
    protected abstract void checkSpecialDisease(String specialDiseaseInfo, BusPrescription busPrescription);

    /**
     * 保存处方
     *
     * @param busPrescription 处方信息
     * @param drugsList       处方药品
     * @param extraData       附件信息
     */
    private void savePrescription(BusPrescription busPrescription,
                                  List<BusPrescriptionDrugs> drugsList,
                                  Object extraData) {
        // 新增处方
        int insert = busPrescriptionMapper.insert(busPrescription);
        if (insert > 0 && CollectionUtils.isNotEmpty(drugsList)) {
            // 扣减药品库存
            this.reduceDrugsStock(busPrescription.getHospitalId(), busPrescription.getId(), drugsList);
            this.batchSaveDrugs(busPrescription.getId(), busPrescription.getHospitalId(), drugsList);
        }
        //插入诊断详情
        String clinicalDiagnosis = busPrescription.getClinicalDiagnosis();
        if (StringUtils.isEmpty(clinicalDiagnosis)) {
            throw new ServiceException("请填写临床诊断");
        }
        JSONArray array = JSON.parseArray(clinicalDiagnosis);
        if (CollectionUtil.isNotEmpty(array)) {
            List<BusPrescriptionCd> prescriptionCdList = Lists.newArrayList();
            for (int i = 0; i < array.size(); i++) {
                BusPrescriptionCd prescriptionCd = getPrescriptionCd(busPrescription.getId(), array.getJSONObject(i));
                if (prescriptionCd != null) {
                    prescriptionCdList.add(prescriptionCd);
                }
            }
            if (CollectionUtil.isNotEmpty(prescriptionCdList)) {
                busPrescriptionCdMapper.batchInsert(prescriptionCdList);
            }
        }
        saveExtraData(busPrescription, extraData);
    }

    /**
     * 转 map
     * @param drugsList 处方药品
     * @return Map<enterpriseId,set<drugsId>>
     */
    protected Map<Long, Set<Long>> getEnterpriseDrugMap (List<BusPrescriptionDrugs> drugsList){
        return  drugsList.stream()
                .filter(d -> Objects.nonNull(d.getEnterpriseId()))
                .collect(Collectors.groupingBy(
                        BusPrescriptionDrugs::getEnterpriseId,
                        Collectors.mapping(BusPrescriptionDrugs::getDrugsId, Collectors.toSet())));
    }

    /**
     * 获取企业供货价
     * @param drugsList
     * @param hospitalId
     * @return
     */
    protected Map<String, BigDecimal> getEnterpriseSupplyPrice (List<BusPrescriptionDrugs> drugsList, Long hospitalId){
        Map<Long, Set<Long>> enterpriseDrugMap = getEnterpriseDrugMap(drugsList);
        // 一次性批量查询企业供货价
        Map<String, BigDecimal> priceMap = Collections.emptyMap();
        if (!enterpriseDrugMap.isEmpty()) {
            List<BusEnterpriseDrugsPrice> priceList =
                    busEnterpriseDrugsPriceMapper.findByHospitalAndEnterpriseAndDrugIds(
                            hospitalId, enterpriseDrugMap);

            priceMap = priceList.stream()
                    .collect(Collectors.toMap(
                            p -> buildKey(p.getEnterpriseId(), p.getDrugsId()),
                            BusEnterpriseDrugsPrice::getReferencePurchasePrice));
        }

        return priceMap;
    }

    /**
     * 组合 map 的 key
     * @param enterpriseId 配送企业 ID
     * @param drugsId 药品 ID
     * @return 组合后的 key
     */
    protected static String buildKey(Long enterpriseId, Long drugsId) {
        return enterpriseId + "#" + drugsId;
    }

    /**
     * 扣减药品库存
     *
     * @param hospitalId 医院ID
     * @param prescriptionId 处方ID
     * @param prescriptionDrugs 药品信息
     */
    protected void reduceDrugsStock(Long hospitalId, Long prescriptionId, List<BusPrescriptionDrugs> prescriptionDrugs) {
        List<BusPrescriptionDrugs> enterpriseDrugsList =
                prescriptionDrugs.stream().filter(item -> item.getEnterpriseId() != null).collect(Collectors.toList());
        List<BusPrescriptionDrugs> hospitalDrugsList =
                prescriptionDrugs.stream().filter(item -> !enterpriseDrugsList.contains(item)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(hospitalDrugsList)) {
            busPrescriptionDrugStockHelper.changeStock(prescriptionId, hospitalId, StockEnum.REDUCE.getName(), hospitalDrugsList);
        }
        if (CollectionUtil.isNotEmpty(enterpriseDrugsList)) {
            busPrescriptionDrugStockHelper.changeEnterpriseStock(hospitalId,
                    enterpriseDrugsList.get(0).getEnterpriseId(),
                    StockEnum.REDUCE.getName(),
                    enterpriseDrugsList);
        }
    }

    /**
     * 配送企业价格处理
     * @param drugs 药品
     * @param enterpriseSupplyPrice 配送企业价格
     */
    protected void enterpriseSupplyPriceHandler(BusPrescriptionDrugs drugs,Map<String, BigDecimal> enterpriseSupplyPrice){
        if (enterpriseSupplyPrice == null) {
            log.error("enterpriseSupplyPrice is null, drugs: {}", drugs);
            throw new ServiceException("配送企业价格数据缺失");
        }
        if (Objects.nonNull(drugs.getEnterpriseId())) {
            BigDecimal refPrice =
                    enterpriseSupplyPrice.get(buildKey(drugs.getEnterpriseId(), drugs.getDrugsId()));
            if (refPrice == null) {
                log.error("配送企业药品缺失：{}", drugs);
                throw new ServiceException("配送企业药品缺失");
            }
            drugs.setReferencePurchasePrice(refPrice.doubleValue());
        }
    }


    /**
     * 批量保存处方药品信息
     *
     * @param prescriptionId 处方id
     * @param hospitalId     医院id
     * @param drugsList      处方药品列表
     */
    protected abstract void batchSaveDrugs(Long prescriptionId, Long hospitalId, List<BusPrescriptionDrugs> drugsList);

    /**
     * 获取诊断明细
     *
     * @param prescriptionId 处方id
     * @param jsonObject     json对象
     * @return 诊断明细
     */
    protected abstract BusPrescriptionCd getPrescriptionCd(Long prescriptionId, JSONObject jsonObject);

    /**
     * 校验处方药品的用药天数
     *
     * @param busPrescription 处方信息
     * @param list            处方明细
     */
    protected void checkPrescriptionDayLimit(BusPrescription busPrescription, List<BusPrescriptionDrugs> list){
    }

    /**
     * 双通道处方校验
     *
     * @param busPrescription 处方信息
     * @param drugsList       处方药品信息
     * @return 处理扩展信息
     */
    protected Object dualChannelCheck(BusPrescription busPrescription,
                                      List<BusPrescriptionDrugs> drugsList) {
        return null;
    }

    /**
     * 保存扩展信息
     *
     * @param busPrescription 处方信息
     * @param extraData       扩展信息
     */
    protected void saveExtraData(BusPrescription busPrescription, Object extraData) {

    }

    /**
     * 处方失效时间
     *
     * @param prescriptionTime 开方时间
     * @param setting          运营配置
     * @return 处方失效时间
     */
    protected Date getExpireTime(Date prescriptionTime, BusConsultationSettings setting) {
        //设置处方有效期（小时）
        int prescriptionOrderExpire = Objects.nonNull(setting.getPrescrptionOrderExpire()) ? setting.getPrescrptionOrderExpire() : 24;
        return DateUtils.addHours(prescriptionTime, prescriptionOrderExpire);
    }
}
