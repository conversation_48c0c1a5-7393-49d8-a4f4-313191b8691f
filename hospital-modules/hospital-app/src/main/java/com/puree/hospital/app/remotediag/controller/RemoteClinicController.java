package com.puree.hospital.app.remotediag.controller;

import cn.hutool.core.lang.Assert;
import com.puree.hospital.app.remotediag.domain.RemoteClinic;
import com.puree.hospital.app.remotediag.domain.dto.RemoteClinicDTO;
import com.puree.hospital.app.remotediag.domain.dto.RemoteClinicOptDTO;
import com.puree.hospital.app.remotediag.domain.enums.RemoteDiagnosisDoctorActionEnum;
import com.puree.hospital.app.remotediag.domain.enums.RemoteDiagnosisDoctorRoleEnum;
import com.puree.hospital.app.remotediag.domain.query.RemoteClinicQuery;
import com.puree.hospital.app.remotediag.domain.vo.RemoteClinicListVO;
import com.puree.hospital.app.remotediag.domain.vo.RemoteClinicVO;
import com.puree.hospital.app.remotediag.service.IRemoteClinicService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/4 11:10
 * @description 医生端： 远程门诊 控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/remote-clinic")
public class RemoteClinicController extends BaseController {

    private final IRemoteClinicService remoteClinicService;

    /**
     * 医生端：分页查询远程门诊申请记录
     *
     * @param query 查询条件
     * @return 门诊申请列表
     */
    @GetMapping
    @Log(title = "医生端-分页查询远程门诊申请记录", businessType = BusinessType.QUERY)
    public Paging<List<RemoteClinicListVO>> clinicList(@Valid RemoteClinicQuery query) {
        //数据校验
        Assert.notNull(RemoteDiagnosisDoctorRoleEnum.getByName(query.getRole()), "角色类型错误");
        return PageUtil.buildPage(remoteClinicService.clinicList(query));
    }

    /**
     * 医生端：远程门诊申请单提交
     *
     * @param dto 远程门诊申请单
     * @return 记录id
     */
    @Log(title = "医生端-远程门诊申请单提交", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@Valid @RequestBody RemoteClinicDTO dto) {
        if(new Date().compareTo(dto.getExpectedTime()) >= 0){
            throw new ServiceException("期望时间不能晚于当前时间！");
        }
        if(Objects.isNull(IdCardTypeEnum.getByLabel(dto.getIdType()))){
           throw new ServiceException("证件类型不正确");
        }
        return AjaxResult.success(remoteClinicService.add(dto));
    }

    /**
     * 医生端：分页获取历史患者列表
     *
     * @param query 查询条件
     * @return 历史患者列表
     */
    @Log(title = "医生端-分页获取历史患者列表", businessType = BusinessType.QUERY)
    @GetMapping("/patient-list")
    public TableDataInfo patientList(RemoteClinicQuery query) {
        log.debug("取历史患者列表查询参数：{}", query);
        //数据校验
        Assert.notNull(query.getHospitalId(), "医院不存在");
        startPage();
        return getDataTable(remoteClinicService.patientList(query));
    }

    /**
     * 医生端：接受/拒绝申请
     *
     * @param id 申请记录id
     * @param dto 操作
     * @return 操作是否成功
     */
    @Log(title = "医生端-接受/拒绝申请", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public AjaxResult<Boolean> processClinicApply(@PathVariable("id") Integer id, @Valid @RequestBody RemoteClinicOptDTO dto) {
        //数据校验
        Assert.notNull(RemoteDiagnosisDoctorActionEnum.getByName(dto.getAction()), "数据操作类型不正确");
        if(RemoteDiagnosisDoctorActionEnum.isRefuse(dto.getAction())){
            if(StringUtils.isEmpty(dto.getRefuseReason())){
                throw new ServiceException("请填写拒绝原因！");
            }
            if(dto.getRefuseReason().length() > 300){
                throw new ServiceException("拒绝原因字数超限！");
            }
        }
        if(RemoteDiagnosisDoctorActionEnum.isAccept(dto.getAction())){
            if(Objects.isNull(dto.getDiagTime())){
                throw new ServiceException("请填写会诊时间！");
            }
            if(new Date().compareTo(dto.getDiagTime()) >= 0){
                throw new ServiceException("会诊时间不能晚于当前时间！");
            }
        }
        dto.setId(id);
        return AjaxResult.success(remoteClinicService.processClinicApply(dto));
    }

    /**
     * 医生端：申请单结束
     *
     * @param id 申请记录id
     * @return 操作是否成功
     */
    @Log(title = "医生端-申请单结束", businessType = BusinessType.UPDATE)
    @PutMapping("/finish/{id}")
    public AjaxResult<Boolean> finish(@PathVariable("id") Integer id) {
        //数据校验
        return AjaxResult.success(remoteClinicService.finish(id));
    }

    /**
     * 医生端：获取申请单详情
     *
     * @param id 申请记录id
     * @return 申请单详情
     */
    @Log(title = "医生端-获取申请单详情", businessType = BusinessType.QUERY)
    @GetMapping("/info/{id}")
    public AjaxResult<RemoteClinicVO> getById(@PathVariable("id") Integer id) {
        return AjaxResult.success(remoteClinicService.getById(id));
    }

    /**
     * 获取远程门诊详情（远程调用）
     * @param id 记录id
     * @return 详情
     */
    @GetMapping("/feign/info/{id}")
    AjaxResult<RemoteClinic> getInfoById(@PathVariable("id") Integer id){
        return AjaxResult.success(remoteClinicService.getInfoById(id));
    }


}