package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.bo.BusDoctorBO;
import com.puree.hospital.app.domain.dto.BusDoctorDto;
import com.puree.hospital.app.domain.vo.BusDoctorHospitalVo;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 医生关联医院
 */
public interface BusDoctorHospitalMapper extends BaseMapper<BusDoctorHospital> {
    /**
     * doctor第一执业医院
     * @param doctorId
     * @return
     */
    BusDoctorHospitalVo firstPractice(Long doctorId);
    List<BusDoctorHospitalVo> selectDoctorHospitalList(@Param("doctorId") Long doctorId, @Param("hospitalId") Long hospitalId);

    String selectRole(@Param("doctorId") Long doctorId, @Param("hospitalId") Long hospitalId);

    /**
     * 医生列表
     * @param bo
     * @return
     */
    List<BusDoctorBO> selectDoctorList(BusDoctorBO bo);
}
