package com.puree.hospital.app.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.app.api.model.event.prescription.PatientFamilyImprovedInfoEvent;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusDoctor;
import com.puree.hospital.app.domain.BusDoctorPatientGroup;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.BusPatientFamilyDto;
import com.puree.hospital.app.domain.dto.BusPreorderDto;
import com.puree.hospital.app.domain.dto.CommunicationMessageDTO;
import com.puree.hospital.app.domain.vo.BusPrescriptionVo;
import com.puree.hospital.app.domain.vo.PatientTemplateMsgVo;
import com.puree.hospital.app.mapper.BusDoctorMapper;
import com.puree.hospital.app.mapper.BusDoctorPatientGroupMapper;
import com.puree.hospital.app.queue.producer.event.im.ImSendMessageEventProducer;
import com.puree.hospital.app.queue.producer.event.prescription.PatientFamilyImprovedInfoEventProducer;
import com.puree.hospital.app.service.IBusCommunicationMessageService;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusPatientFamilyService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusPreorderPatientService;
import com.puree.hospital.app.service.IBusPrescriptionDrugsService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.constant.CustomMsgConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.core.web.domain.MapResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.im.api.model.event.ImSendMessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 就诊人信息相关控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/interrogator")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPatientFamilyController extends BaseController {

    private final IBusPatientFamilyService busPatientFamilyService;
    private final IBusCommunicationMessageService busCommunicationMessageService;
    private final IBusPrescriptionService busPrescriptionService;
    private final IBusDoctorPatientGroupService busDoctorPatientGroupService;
    private final IBusPrescriptionDrugsService busPrescriptionDrugsService;
    private final BusDoctorPatientGroupMapper busDoctorPatientGroupMapper;
    private final BusDoctorMapper busDoctorMapper;
    private final IBusPatientService busPatientService;
    private final IBusPreorderPatientService busPreorderPatientService;

    @Lazy
    @Resource
    private PatientFamilyImprovedInfoEventProducer patientFamilyImprovedInfoEventProducer;

    @Lazy
    @Resource
    private ImSendMessageEventProducer imSendMessageEventProducer;

    /**
     * 新增就诊人信息
     *
     * @param patientFamily
     * @return
     */
    @PostMapping("/add")
    @Log(title = "新增就诊人信息", businessType = BusinessType.OTHER)
    public AjaxResult add(@RequestBody BusPatientFamily patientFamily) {
        if (StringUtils.isEmpty(patientFamily.getIdNumber())) {
            return AjaxResult.error("身份证号码为空");
        }
        if (ObjectUtil.isNull(patientFamily.getHospitalId())) {
            SMSLoginUser loginUserInfo = busPatientService.queryPatientInfo();
            Long hospitalId = loginUserInfo.getHospitalId();
            patientFamily.setHospitalId(hospitalId);
        }
        logger.info("添加就诊人入参={}", patientFamily);
        return AjaxResult.success(busPatientFamilyService.addInterrogator(patientFamily));
    }

    /**
     * 获取就诊人详细信息
     *
     * @param patientFamily
     * @return
     */
    @GetMapping("/getInfo")
    @Log(title = "获取就诊人详细信息", businessType = BusinessType.OTHER)
    public AjaxResult getInfo(BusPatientFamily patientFamily) {
        logger.info("患者信息={}", patientFamily);
        // 查找登录用户信息，然后根据用户登录的身份来判断查询的范围
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        if (ObjectUtil.isNull(patientFamily.getId()) || ObjectUtil.isNull(patientFamily.getPatientId())) {
            return AjaxResult.success("患者id不能为空");
        }
        boolean allowPre = busPatientFamilyService.allowPatientIdByAppRole(loginUser, patientFamily);
        if (Boolean.FALSE.equals(allowPre)) {
            // 不允许的查询直接返回空
            return AjaxResult.error("没有权限查看该患者信息");
        }
        BusPatientFamily interrogator = busPatientFamilyService.getInterrogator(patientFamily);
        // 审方药师只能查询本院的患者本人信息 - 走校验方法 - 校验是否是查询医院下的
        boolean allow = busPatientFamilyService.checkPatientFamilyWithHospital(loginUser,interrogator);
        if (!allow) {
            return AjaxResult.error("没有权限查看该患者信息");
        }
        return AjaxResult.success(interrogator);
    }

    /**
     * @Param patientFamily
     * @Return com.puree.hospital.common.core.web.domain.AjaxResult
     * @Description 根据身份证号码获取患者信息
     * <AUTHOR>
     * @Date 2024/4/29 14:48
     **/
    @GetMapping("/info-idNumber")
    public AjaxResult getInfoByIdNumber(BusPatientFamily patientFamily) {
        return AjaxResult.success(busPatientFamilyService.getInfoByIdNumber(patientFamily));
    }

    /**
     * @Param patientFamily
     * @Return AjaxResult
     * @Description 批量获取
     * <AUTHOR>
     * @Date 2024/7/17 17:37
     **/
    @GetMapping("/list-idNumber")
    public AjaxResult getListByIdNumber(BusPatientFamily patientFamily) {
        return AjaxResult.success(busPatientFamilyService.getList(patientFamily));
    }

    /**
     * 查询就诊人信息
     *
     * @param userId 账户id
     * @return
     */
    @GetMapping("/info-userid")
    public AjaxResult queryInfoByUserId(@RequestParam("userId") Long userId, @RequestParam("hospitalId") Long hospitalId) {
        return AjaxResult.success(busPatientFamilyService.queryInfoByUserId(userId, hospitalId));
    }

    /**
     * 修改就诊人信息
     *
     * @param patientFamily
     * @return
     */
    @PutMapping("/edit")
    @Log(title = "修改就诊人信息", businessType = BusinessType.OTHER)
    public AjaxResult edit(@RequestBody BusPatientFamily patientFamily) {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        patientFamily.setPatientId(loginUser.getUserid());
        IdCardTypeEnum idCardTypeEnum = IdCardTypeEnum.getByLabel(patientFamily.getIdType());
        if(idCardTypeEnum != null){
            patientFamily.setIdType(idCardTypeEnum.getCode());
        }
        return toAjax(busPatientFamilyService.edit(patientFamily));
    }

    /**
     * 就诊人列表
     *
     * @param patientFamily
     * @return
     */
    @GetMapping("/list")
    @Log(title = "就诊人列表", businessType = BusinessType.OTHER)
    public TableDataInfo list(BusPatientFamily patientFamily) {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        patientFamily.setPatientId(loginUser.getUserid());
        startPage();
        return getDataTable(busPatientFamilyService.interrogatorList(patientFamily));
    }

    /**
     * 就诊人列表 - 优先根据前端传入的patientId查询
     * @param patientFamily
     * @return
     */
    @GetMapping("/patient")
    @Log(title = "就诊人列表", businessType = BusinessType.OTHER)
    public TableDataInfo familyList(BusPatientFamily patientFamily) {
        if (Objects.isNull(patientFamily.getPatientId())) {
            SMSLoginUser loginUser = busPatientService.queryPatientInfo();
            patientFamily.setPatientId(loginUser.getUserid());
        }
        startPage();
        return getDataTable(busPatientFamilyService.interrogatorList(patientFamily));
    }

    /**
     * 删除就诊人
     *
     * @param patientFamily
     * @return
     */
    @DeleteMapping("/delete")
    @Log(title = "删除就诊人", businessType = BusinessType.OTHER)
    public AjaxResult delete(BusPatientFamily patientFamily) {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        patientFamily.setPatientId(loginUser.getUserid());
        return toAjax(busPatientFamilyService.deleteInterrogator(patientFamily));
    }

    /**
     * 查询诊断人信息
     *
     * @param
     * @return
     */
    @GetMapping("/query")
    @Log(title = "查询诊断人信息", businessType = BusinessType.OTHER)
    public AjaxResult query(@RequestParam("id") Long id, @RequestParam("hospitalId") Long hospitalId) {
        return AjaxResult.success(busPatientFamilyService.selectPatientFamilyById(id, hospitalId));
    }

    /**
     * 完善就诊人档案
     *
     * @param dto
     * @return
     */
    @PutMapping("/perfect")
    @Log(title = "完善就诊人档案", businessType = BusinessType.OTHER)
    public AjaxResult perfect(@RequestBody BusPatientFamilyDto dto) {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        if (ObjectUtil.isNull(dto.getPatientId())) {
            dto.setPatientId(loginUser.getUserid());
        }
        BusPatientFamily patientFamily = OrikaUtils.convert(dto, BusPatientFamily.class);
        int i = busPatientFamilyService.updateInterrogator(patientFamily);
        if (i > 0) {
            CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
            messageDTO.setGroupId(Long.valueOf(dto.getGroupId()));
            messageDTO.setFromAccount(TencentyunImConstants.ADMINISTRATOR);
            Map<String, Object> data = new HashMap<>(2);
            data.put("type", CustomMsgConstants.DIAGNOSIS_ARCHIVES);
            data.put("diagnosisArchives", patientFamily.getDiagnosisArchives());
            messageDTO.setMap(data);
            messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            busCommunicationMessageService.sendCustomImMsg(messageDTO);
        }
        return toAjax(i);
    }

    /**
     * 完善信息发送处方
     *
     * @param
     * @return
     */
    @GetMapping("/sendPrescription")
    @Log(title = "完善信息发送处方", businessType = BusinessType.OTHER)
    public AjaxResult sendPrescription(@RequestParam("id") Long id, @RequestParam("hospitalId") Long hospitalId) {
        BusPatientFamily family = busPatientFamilyService.getById(id);
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setFamilyId(family.getId());
        busPrescription.setPatientId(family.getPatientId());
        busPrescription.setStatus(PrescriptionStatus.PASS);
        busPrescription.setType(CodeEnum.YES.getCode());
        busPrescription.setHospitalId(hospitalId);
        busPrescription.setNotPreorder(true);
        List<BusPrescriptionVo> busPrescriptionVos = busPrescriptionService.selectList(busPrescription);
        for (BusPrescriptionVo p : busPrescriptionVos) {
            PatientFamilyImprovedInfoEvent event = new PatientFamilyImprovedInfoEvent();
            event.setPrescriptionId(p.getId());
            event.setEventType(PatientFamilyImprovedInfoEvent.EventType.IMPROVED_INFO);
            patientFamilyImprovedInfoEventProducer.send(event);
            //todo im消息后面再加监听事件
            boolean ret2 = sendChatMsgToPatient(p.getId(), p.getHospitalId(), p.getPrescriptionType(), p.getServicePackId());
            if (!ret2) {
                return AjaxResult.error("微信推送消息异常，请通知患者在处方列表中查看已开具的处方！");
            }
        }
        return AjaxResult.success();
    }

    /**
     *
     * @param patientFamily
     * @return
     */
    @GetMapping("/list-phoneNumber")
    public AjaxResult listByPhoneNumber(BusPatientFamily patientFamily) {
        return AjaxResult.success(busPatientFamilyService.getList(patientFamily));
    }

    /**
     * 发送聊天信息给患者
     */
    private boolean sendChatMsgToPatient(Long id, Long hospitalId, String prescriptionType, Long servicePackId) {
        try {
            logger.info("sendChatMsgToPatient():id={},hospitalId={},servicePackId={}", id, hospitalId, servicePackId);
            //查询患者信息
            PatientTemplateMsgVo templateMsgVo = busPrescriptionService.selectPatient(id, hospitalId);
            if (ObjectUtil.isNull(templateMsgVo)) {
                logger.error("sendWXTemplateMsgToPatient()：患者信息不存在");
                return false;
            }
            //查询群组信息
            BusDoctorPatientGroup group = new BusDoctorPatientGroup();
            if (ObjectUtil.isNotNull(servicePackId)) {
                group.setType(CodeEnum.YES.getCode());
                group.setServiceId(servicePackId);
            } else {
                group.setType(CodeEnum.NO.getCode());
            }
            group.setDepartmentId(templateMsgVo.getDepartmentId());
            group.setDoctorId(templateMsgVo.getDoctorId());
            group.setHospitalId(templateMsgVo.getHospitalId());
            group.setPatientId(templateMsgVo.getPatientId());
            group.setFamilyId(templateMsgVo.getFamilyId());
            //就诊人群组
            BusDoctorPatientGroup patientGroup = busDoctorPatientGroupService.selectOne(group);
            if (ObjectUtil.isNull(patientGroup)) {
                logger.error("查询群组为空");
                return false;
            }
            //查询处方药品
            BusPrescriptionDrugs busPrescriptionDrugs = new BusPrescriptionDrugs();
            busPrescriptionDrugs.setPrescriptionId(id);
            busPrescriptionDrugs.setHospitalId(hospitalId);
            //处方药品
            List<BusPrescriptionDrugs> prescriptionDrugs =
                    busPrescriptionDrugsService.selectPrescriptionDrugsList(busPrescriptionDrugs);
            List<BusPrescriptionDrugs> returnList = new ArrayList<>();
            prescriptionDrugs.forEach(p -> {
                BusPrescriptionDrugs prescription = new BusPrescriptionDrugs();
                prescription.setDrugsName(p.getDrugsName());
                prescription.setWeight(p.getWeight());
                prescription.setStandardCommonName(p.getStandardCommonName());
                prescription.setDrugsDosageForm(p.getDrugsDosageForm());
                prescription.setDrugsSpecification(p.getDrugsSpecification());
                returnList.add(prescription);
            });
            CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
            messageDTO.setDoctorPatientGroup(group);
            messageDTO.setFromAccount(TencentyunImConstants.DOCTOR_IM_ACCOUNT + templateMsgVo.getDoctorId());
            messageDTO.setNickName(templateMsgVo.getDoctorName());
            Map<String, Object> data = new HashMap<>(4);
            data.put("type", CustomMsgConstants.PRESCRIPTION);
            data.put("prescriptionType", prescriptionType);
            data.put("doctorName", templateMsgVo.getDoctorName());
            data.put("prescription", templateMsgVo);
            data.put("prescriptionDrugs", returnList);
            messageDTO.setMap(data);
            messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
            busCommunicationMessageService.sendCustomImMsg(messageDTO);
            return true;
        } catch (Exception e) {
            logger.error("发送im处方报错e={}", e.getMessage());
            return false;
        }
    }

    /**
     * 校验诊断人是否完善诊疗档案
     *
     * @param
     * @return
     */
    @GetMapping("/check")
    @Log(title = "校验诊断人是否完善诊疗档案", businessType = BusinessType.OTHER)
    public AjaxResult check(@RequestParam("id") Long id,
                            @RequestParam("groupId") String groupId,
                            @RequestParam("role") String role,
                            @RequestParam("hospitalId") Long hospitalId) {
        boolean check = busPatientFamilyService.check(id, hospitalId, Long.parseLong(groupId));
        logger.info("role={},groupId={}", role, groupId);
        if (check) {
            //0 患者 1 医生
            if (CodeEnum.YES.getCode().equals(role)) {
                CommunicationMessageDTO messageDTO = new CommunicationMessageDTO();
                messageDTO.setGroupId(Long.valueOf(groupId));
                messageDTO.setFromAccount(TencentyunImConstants.ADMINISTRATOR);
                Map<String, Object> data = new HashMap<>();
                data.put("type", CustomMsgConstants.REMIND_DIAGNOSIS_ARCHIVES);
                messageDTO.setMap(data);
                messageDTO.setMsgType(TencentyunImConstants.TIM_CUSTOM_ELEM);
                busCommunicationMessageService.sendCustomImMsg(messageDTO);
                //后续改造
                this.sendImMessageEvent(Long.valueOf(groupId));
            }
            return AjaxResult.success(false);
        }
        return AjaxResult.success(true);
    }


    /**
     * 发送微信模板消息给患者
     *
     * @param groupId 聊天群组id
     */
    private void sendImMessageEvent(Long groupId) {
        BusDoctorPatientGroup patientGroup = busDoctorPatientGroupMapper.selectById(groupId);
        BusDoctor busDoctor = busDoctorMapper.selectById(patientGroup.getDoctorId());
        if (busDoctor == null) {
            logger.error("sendWXTemplateMsgToPatient()：医生信息不存在");
            return;
        }
        ImSendMessageEvent event = new ImSendMessageEvent();
        event.setEventType(ImSendMessageEvent.EventType.SEND_PENDING_UPLOAD_MEDICAL);
        event.setGroupId(groupId);
        event.setDoctorName(busDoctor.getFullName());
        imSendMessageEventProducer.send(event);
    }

    /**
     * 补充就诊人信息
     *
     * @param dto 用药预订单提交 参数
     * @return 提交结果
     */
    @PostMapping("/edit/info")
    @Log(title = "用药预订单提交", businessType = BusinessType.OTHER)
    public AjaxResult editInfo(@Validated @RequestBody BusPreorderDto dto) {
        return toAjax(busPreorderPatientService.confirm(dto));
    }

    /**
     * 校验身份证是否绑定过手机号
     *
     * @param patientFamily
     * @return
     */
    @Log(title = "校验身份证是否绑定过手机号")
    @GetMapping("/check/idCardNo")
    public MapResult checkIdCardNo(BusPatientFamily patientFamily) {
        log.info("校验就诊人身份证入参={}", JSON.toJSONString(patientFamily));
        MapResult ajaxResult = new MapResult(Constants.SUCCESS, "操作成功");
        boolean flag = false;
        // 编辑就诊人校验
        if (ObjectUtil.isNotNull(patientFamily.getId())) {
            BusPatientFamily busPatientFamily = busPatientFamilyService.selectFamilyById(patientFamily.getId());
            if (patientFamily.getIdNumber().equals(busPatientFamily.getIdNumber())) {
                ajaxResult.put("flag", false);
            } else {
                flag = true;
            }
        } else { // 新增就诊人校验
            flag = true;
        }
        if (flag) {
            BusPatientFamily family = busPatientFamilyService.selectFamilyByNo(patientFamily);
            if (ObjectUtil.isNotNull(family) && !family.getPatientId().equals(patientFamily.getPatientId())) {
                ajaxResult.put("flag", true);
                ajaxResult.put("phone", family.getCellPhoneNumber());
            } else {
                ajaxResult.put("flag", false);
            }
        }
        return ajaxResult;
    }


    /**
     * 获取九合一设备(乐尔)的patientId
     * @param id 患者ID
     * @return 乐尔需要的数据格式：leerPatientId|phone|leerVisitorId 或 leerPatientId|phone
     */
    @GetMapping("/leer-patient-id")
    public R<String> getOrRegisterLeerPatientId(@RequestParam("id") Long id) {
        return R.ok(busPatientFamilyService.getOrRegisterLeerPatientId(id));
    }

}
