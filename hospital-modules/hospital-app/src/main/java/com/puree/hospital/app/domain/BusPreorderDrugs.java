package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 预订单药品表
 */
@Data
public class BusPreorderDrugs extends Entity {

	/** 药品ID */
	private Long drugsId;
	/** 药品制造商 */
	private String drugsManufacturer;
	/** 标准通用名 */
	private String standardCommonName;
	/** 药品名称 */
	private String drugsName;
	/** 药品图片地址 */
	private String drugsImg;
	/** 药品规格 */
	private String drugsSpecification;
	/** 销售价 */
	private BigDecimal sellingPrice;
	/** 用药频次 */
	private String medicationFrequency;
	/** 单次剂量 */
	private Integer singleDose;
	/** 单位 */
	private String unit;
	/** 给药途径 */
	private String drugsUsageValue;
	/** 重量（克） */
	private String weight;
	/** 数量 */
	private Integer quantity;
	/** 用药天数 */
	private Integer medicationDays;
	/** 预订单药品ID */
	private Long preorderId;
	/**
	 * 配送企业ID
	 */
	private Long enterpriseId;
	/**
	 * 目录类型（1院内 2院外 3中药）
	 */
	private Integer directoryType;

	/**
	 * 推荐人id
	 */
	private Long referrer;

	/**
	 * 最小包装数量
	 * */
	@TableField(exist = false)
	private Integer  minPackNum;
	/**
	 * 最小制剂单位
	 * */
	@TableField(exist = false)
	private String   minMakeUnit;
	/**
	 * 最小包装单位
	 * */
	@TableField(exist = false)
	private String minPackUnit;
	@TableField(exist = false)
	/**药品分类祖籍id*/
	private Long ancestorsId;
	/**
	 * 库存
	 */
	@TableField(exist = false)
	private Integer stock;
	@TableField(exist = false)
	private String detailedSpecifications;

	/**
	 * 医保编码
	 */
	@TableField(exist = false)
	private String nationalDrugCode;

	/**
	 * 医保类型
	 */
	@TableField(exist = false)
	private Long medicalInsuranceType;

	/**
	 * 是否处方 391 392
	 */
	@TableField(exist = false)
	private Long prescriptionIdentification;

	/**
	 * 药品包装单位(字典表关联)
	 */
	@TableField(exist = false)
	private Long drugsPackagingUnit;

	/**
	 * 药品包装单位(字典表关联)
	 */
	@TableField(exist = false)
	private String drugsPackagingUnitName;

	/**
	 * 基本剂量
	 */
	@TableField(exist = false)
	private Double baseDose;
	/**
	 * 基本剂量单位
	 */
	@TableField(exist = false)
	private String  baseDoseUnit;

}
