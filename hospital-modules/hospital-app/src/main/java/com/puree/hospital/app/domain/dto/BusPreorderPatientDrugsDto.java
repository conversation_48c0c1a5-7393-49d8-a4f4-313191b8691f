package com.puree.hospital.app.domain.dto;

import com.puree.hospital.app.domain.BusPreorderDrugs;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.List;

/**
 * 药品预订单表
 */
@Data
public class BusPreorderPatientDrugsDto extends Entity {
	/** 急速问诊ID */
	private Long consultationId;
	/** 患者ID */
	private Long patientId;
	/** 就诊人ID */
	private Long familyId;
	/** 就诊人姓名 */
	private String familyName;
	/** 药品制造商 */
	private String drugsManufacturer;
	/** 标准通用名 */
	private String standardCommonName;
	/** 药品名称 */
	private String drugsName;
	/** 药品图片地址 */
	private String drugsImg;
	/** 药品规格 */
	private String drugsSpecification;
	/** 销售价 */
	private Double sellingPrice;
	/** 用药频次 */
	private String medicationFrequency;
	/** 单次剂量 */
	private Integer singleDose;
	/** 单位 */
	private String unit;
	/** 给药途径 */
	private String drugsUsageValue;
	/** 重量（克） */
	private String weight;
	/** 数量 */
	private Integer quantity;
	/** 用药天数 */
	private Integer medicationDays;
	/** 医院ID */
	private Long hospitalId;
	/** 是否已开处方（0否 1是） */
	private String status;
	/**
	 * 医生id
	 */
	private Long doctorId;
	/**
	 * 用药订单默认开方医生
	 */
	private List<Long> doctorIds;


	/** 药品集合 */
	List<BusPreorderDrugs> drugsList;
	/**
	 * 机构code
	 */
	private String partnersCode;

}