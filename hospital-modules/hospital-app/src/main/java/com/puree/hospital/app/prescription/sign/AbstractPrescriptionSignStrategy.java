package com.puree.hospital.app.prescription.sign;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.app.api.model.enums.SignTypeEnum;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionSignatureRecord;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.dto.PrescriptionSignDTO;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.mapper.BusPrescriptionSignatureRecordMapper;
import com.puree.hospital.app.mapper.BusSignatureMapper;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.tool.api.model.dto.CaSignReturnDTO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/12 16:06
 */
@Component
public abstract class AbstractPrescriptionSignStrategy implements IPrescriptionSignStrategy {

    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;

    @Resource
    private BusSignatureMapper busSignatureMapper;

    @Lazy
    @Resource
    private IBusPrescriptionService busPrescriptionService;

    @Resource
    private BusPrescriptionSignatureRecordMapper busPrescriptionSignatureRecordMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String pdfSign(PrescriptionSignDTO prescription) {
        // 1.执行更新操作
        int ret = updateOperater(prescription);
        if (ret > 0) {
            // 2. 通过策略动态获取ID和name
            Long doctorId = getOperatorId(prescription);
            String doctorName = getOperatorName(prescription);
            String pdfPath = prescription.getPath();
            BusSignature busSignature = querySignatureInfo(doctorId);
            // 3. 执行PDF的CA签署
            try {
                CaSignReturnDTO signResult = executePdfSign(busSignature, pdfPath);
                // 4. 更新处方表
                updatePrescriptionTable(prescription, signResult.getPdfPath());
                // 5. 插入签名操作记录
                insertSignatureRecord(prescription, doctorName, busSignature, signResult);
                return signResult.getPdfPath();
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
        } else {
            throw new ServiceException("该处方已签署过！不可重复签署");
        }
    }


    public BusPrescriptionSignatureRecord buildSignatureRecord(
            Long hospitalId,
            Long prescriptionId,
            String operatorName,
            String certId,
            String certSignature,
            CaSignReturnDTO signResult) {
        BusPrescriptionSignatureRecord record = new BusPrescriptionSignatureRecord();
        record.setHospitalId(hospitalId);
        record.setPrescriptionId(prescriptionId);
        record.setDoctorName(operatorName);
        record.setCertId(certId);
        record.setSignatureImg(certSignature);
        record.setSignedPdfPath(signResult.getPdfPath());
        record.setSignType(getSignType());
        record.setCreateBy(operatorName);
        record.setDigest(getDigestSignature(signResult.getDigestSignature()));
        return record;
    }


    protected abstract String getObjectType();

    protected BusSignature querySignatureInfo(Long doctorId){
        QueryWrapper<BusSignature> wrapper = new QueryWrapper<>();
        wrapper.select("cert_id", "cert_signature")
                .eq("object_id", doctorId)
                .eq("object_type", getObjectType());
        return busSignatureMapper.selectOne(wrapper);
    }

    protected CaSignReturnDTO executePdfSign(BusSignature busSignature, String pdfPath) throws Exception {
        String certId = busSignature.getCertId();
        String certSignature = busSignature.getCertSignature();
        String flag = getSignFlag();
        return busPrescriptionService.PdfSign(certId, pdfPath, flag, certSignature);
    }

    protected void updatePrescriptionTable(PrescriptionSignDTO prescription, String signedPdfPath){
        BusPrescription updatePrescription = new BusPrescription();
        updatePrescription.setId(prescription.getPrescriptionId());
        updatePrescription.setPath(signedPdfPath);
        updatePrescription.setDispensingStatus(prescription.getDispensingStatus());
        updatePrescription.setDispensingTime(prescription.getDispensingTime());
        busPrescriptionMapper.updateById(updatePrescription);
    }

    protected void insertSignatureRecord(PrescriptionSignDTO prescription, String doctorName,
                                         BusSignature busSignature, CaSignReturnDTO signResult){
        BusPrescriptionSignatureRecord record = this.buildSignatureRecord(
                prescription.getHospitalId(),
                prescription.getPrescriptionId(),
                doctorName,
                busSignature.getCertId(),
                busSignature.getCertSignature(),
                signResult
        );
        busPrescriptionSignatureRecordMapper.insert(record);
    }

    protected abstract String getDigestSignature(List<String> digestNames);

    protected abstract int updateOperater(PrescriptionSignDTO prescription);
    /**
     * 获取操作者ID
     * @param prescription
     * @return
     */
    protected abstract Long getOperatorId(PrescriptionSignDTO prescription);
    /**
     * 获取操作者名称
     * @param prescription
     * @return
     */
    protected abstract String getOperatorName(PrescriptionSignDTO prescription);
    /**
     * 获取签名标志位
     * @return
     */
    protected abstract String getSignFlag();
    /**
     * 获取签名类型
     * @return
     */
    @Override
    public abstract SignTypeEnum getSignType();


}
