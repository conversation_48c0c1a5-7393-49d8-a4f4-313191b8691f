package com.puree.hospital.app.domain.dto;

import lombok.Data;

/**
 * <p>
 * 乐尔设备注册受检人请求参数DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/26
 */
@Data
public class LeerRegisterRequestDTO {

    /**
     * 是否匿名
     */
    private Boolean anonymous;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别（0女 1男）
     */
    private String sex;

    /**
     * 生日，格式：yyyy-MM-dd
     */
    private String birthday;

    /**
     * 身高
     */
    private Integer height;

    /**
     * 体重
     */
    private Integer weight;
}