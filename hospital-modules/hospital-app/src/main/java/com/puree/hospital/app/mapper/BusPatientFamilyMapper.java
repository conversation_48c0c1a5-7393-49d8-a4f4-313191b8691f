package com.puree.hospital.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.vo.BusPatientFamilyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusPatientFamilyMapper extends BaseMapper<BusPatientFamily> {
    List<BusPatientFamilyVo> selectInterrogatorList(BusPatientFamily interrogator);
    BusPatientFamilyVo selectPatientFamilyById(@Param("id") Long id, @Param("hospitalId") Long hospitalId);
    String selectRelation(Long patientRelationship);
    Long selectRelationShip(@Param("relation") String relation);

    /**
     * 查询就诊人信息
     * @param familyId
     * @return
     */
    BusPatientFamilyVo queryFamilyInfo(@Param("familyId") Long familyId);

    BusPatientFamily getOne(BusPatientFamily query);
}
