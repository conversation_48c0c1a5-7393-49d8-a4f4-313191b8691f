package com.puree.hospital.app.prescription.sign;


import com.puree.hospital.app.api.model.enums.SignTypeEnum;
import com.puree.hospital.app.domain.dto.PrescriptionSignDTO;
import org.springframework.transaction.annotation.Transactional;


/**
 * <p>
 * 处方签名策略接口
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/12 16:01
 */
public interface IPrescriptionSignStrategy {

    /**
     * 签名
     *
     * @param signDTO 处方签名参数
     * @throws Exception 异常信息
     */

    String pdfSign(PrescriptionSignDTO signDTO) throws Exception;

    /**
     * 获取签名角色
     *
     * @return 签名角色
     */
    SignTypeEnum getSignType();

}
