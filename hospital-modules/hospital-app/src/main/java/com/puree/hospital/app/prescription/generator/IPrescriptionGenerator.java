package com.puree.hospital.app.prescription.generator;

import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.PrescriptionGenerateResultDTO;

/**
 * <p>
 * 处方生成接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14 9:30
 */
public interface IPrescriptionGenerator {

    /**
     * 处方创建接口后缀
     */
    String SUFFIX = "PrescriptionGenerator";

    /**
     * 创建处方接口
     *
     * @param prescriptionDto 处方创建参数信息
     * @return 处方创建结果
     */
    PrescriptionGenerateResultDTO generate(BusPrescriptionDto prescriptionDto);
}
