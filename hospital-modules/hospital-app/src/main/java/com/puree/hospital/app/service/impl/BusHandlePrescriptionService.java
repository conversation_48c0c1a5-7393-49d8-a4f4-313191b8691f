package com.puree.hospital.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.domain.BusHospitalPa;
import com.puree.hospital.app.domain.BusHospitalPaDrugs;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.vo.BusHospitalPaDrugsVo;
import com.puree.hospital.app.domain.vo.DrugsVo;
import com.puree.hospital.app.mapper.BusHospitalPaDrugsMapper;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.ProcessingMethodEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.ehr.api.model.BusEhrCaseDiagnosisHistoryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: BusHandlePrescriptionService
 * @Date 2024/3/26 10:42
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Slf4j
@Service
public class BusHandlePrescriptionService {

    @Autowired
    private BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    @Autowired
    private BusHospitalPaDrugsMapper busHospitalPaDrugsMapper;
    @Autowired
    private BusHospitalPaMapper busHospitalPaMapper;



    public List<DrugsVo> findPrescriptionDrugInfo(Long hospitalId, BusPrescription busPrescription) {

        //获取处方药品
        List<DrugsVo> drugsVos;
        if (!PrescriptionTypeEnum.isZyxdf(busPrescription.getPrescriptionType())){
            drugsVos = busPrescriptionDrugsMapper.selectTemplateDrugsInfo(hospitalId , busPrescription.getId());
        } else {
            BusHospitalPaDrugs busHospitalPaDrugs = new BusHospitalPaDrugs();
            busHospitalPaDrugs.setHospitalId(busPrescription.getHospitalId());
            busHospitalPaDrugs.setPaId(busPrescription.getPaId());
            List<BusHospitalPaDrugsVo> busHospitalPaDrugsVos = busHospitalPaDrugsMapper.selectPaDrugsList(busHospitalPaDrugs);
            drugsVos = new ArrayList<>();
            busHospitalPaDrugsVos.forEach(item ->{
                DrugsVo drugsVo = new DrugsVo();
                drugsVo.setDrugsName(item.getDrugsName());
                drugsVo.setDrugsSpecification(item.getDrugsSpecification());
                drugsVo.setWeight(item.getWeight());
                drugsVo.setSellingPrice(String.valueOf(item.getReferenceSellingPrice()));
                drugsVo.setStandardCommonName(item.getStandardCommonName());
                drugsVos.add(drugsVo);
            });
        }
        return drugsVos ;

    }

    public void handleChinaDrug(BusPrescription busPrescription,  List<DrugsVo> drugsVos , List<String> managementList, List<String> tcmDiagnosisList) {

        StringBuilder stringBuilder = new StringBuilder() ;

        /*中药方剂类型0 协定方 1经典方剂*/
        Integer zyfjType = null;
        if (PrescriptionTypeEnum.isZyxdf(busPrescription.getPrescriptionType())){
            BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(busPrescription.getPaId());

            /*如果是协定方*/
            if (busHospitalPa.getType().equals(YesNoEnum.NO.getCode())){
                stringBuilder.append(busPrescription.getPaName()+"\n");
            }else {
                /*如果是经典方*/
                zyfjType = busHospitalPa.getType();
            }
        }

        if (null != zyfjType || PrescriptionTypeEnum.isTcm(busPrescription.getPrescriptionType())){
            for (int i = 0; i < drugsVos.size(); i++) {
                DrugsVo vo = drugsVos.get(i);
                if ("g".equals(vo.getDrugsSpecification())){
                    if (i  == drugsVos.size()-1){
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getWeight()+vo.getDrugsSpecification()+"。\n");
                    }else{
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getWeight()+vo.getDrugsSpecification()+"，\n");
                    }
                }else{
                    if (i  == drugsVos.size()-1){
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getDrugsSpecification()+"×"+vo.getWeight()+"。\n");
                    }else{
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getDrugsSpecification()+"×"+vo.getWeight()+"，\n");
                    }
                }
            }
        }

        //用法（0内服 1外用）逗号分隔（共几贴，每日几贴，每贴分几次服用）',
        String usages = busPrescription.getUsages();
        if (StrUtil.isNotBlank(usages)) {
            String[] split = usages.split(",");
            String use = "";
            String sumSize = "";
            String daySize = "";
            String timeSize = "";
            for (int i = 0; i < split.length; i++) {
                if ("0".equals(split[i]) && i == 0) {
                    use = "内服";
                } else if ("1".equals(split[i]) && i == 0) {
                    use = "外用";
                } else if (i == 1) {
                    sumSize = "共" + split[i] + "剂";
                } else if (i == 2) {
                    daySize = "每日" + split[i] + "剂";
                } else if (i == 3) {
                    timeSize = "每剂分" + split[i] + "次服用";
                }
            }

            busPrescription.setProcessingMethod(ProcessingMethodEnum.getInfo(busPrescription.getProcessingMethod())) ;

            stringBuilder.append("用法与加工方法："+use + "，" + sumSize + "，" + daySize + "，" + timeSize  + busPrescription.getProcessingMethod()+"。\n");
        }

        /*处置*/
        managementList.add(stringBuilder.toString()) ;


        String tcmSyndrome = "";
        String tcmDiagnosis;
        String diagnosis ="";

        String clinicalDiagnosis = busPrescription.getClinicalDiagnosis();
        JSONArray objects = JSONObject.parseArray(clinicalDiagnosis);

        for (int i = 0; i < objects.size(); i++){
            JSONObject jsonObject = objects.getJSONObject(i);
            tcmDiagnosis = jsonObject.getString("tcmDiagnosis");
            JSONObject obj = jsonObject.getJSONObject("tcmSyndrome");
            if (StringUtils.isNotNull(obj)) {
                tcmSyndrome = "("+obj.getString("tcmSyndrome")+ ")";
            }
            if(i == objects.size()-1){
                diagnosis = diagnosis+ tcmDiagnosis + tcmSyndrome;
            }else{
                diagnosis = diagnosis + tcmDiagnosis + tcmSyndrome+ "\n";
            }
        }

        // 诊断
        if (StrUtil.isNotBlank(diagnosis)) {
            tcmDiagnosisList.add(diagnosis) ;
        }

    }

    public void handlePrescriptionInfo(Long hospitalId, List<BusPrescription> prescriptionList, BusEhrCaseDiagnosisHistoryRequest request) {

        if ( null==prescriptionList || prescriptionList.isEmpty() ) {
            return ;
        }

        // 医嘱, 包含所有处方
        List<String> adviceList = new ArrayList<>() ;

        // 处置， 里面是所有处方的药品信息
        List<String> managementList = new ArrayList<>() ;

        // 诊断, 包含所有西药处方
        List<String> diagnosisList = new ArrayList<>() ;

        // 诊断, 包含所有中药处方
        List<String> tcmDiagnosisList = new ArrayList<>() ;

        for (BusPrescription busPrescription : prescriptionList) {
            this.handlePrescriptionInfo(hospitalId, busPrescription, adviceList, managementList, diagnosisList, tcmDiagnosisList) ;
        }

        if (!adviceList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (String advice : adviceList) {
                sb.append(advice).append("\n") ;
            }
            request.setAdvice(sb.toString());
        }

        if (!managementList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (String management : managementList) {
                sb.append(management).append("\n") ;
            }
            request.setManagement(sb.toString());
        }

        if (!diagnosisList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            List<String> collect = diagnosisList.stream().distinct().collect(Collectors.toList());
            for (String diagnosis : collect) {
                sb.append(diagnosis).append("\n") ;
            }
            request.setDiagnosis(sb.toString());
        }

        if (!tcmDiagnosisList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            List<String> collect = tcmDiagnosisList.stream().distinct().collect(Collectors.toList());
            for (String tcmDiagnosis : collect) {
                sb.append(tcmDiagnosis).append("\n") ;
            }
            request.setTcmDiagnosis(sb.toString());
        }

    }

    public void handlePrescriptionInfo(Long hospitalId, BusPrescription busPrescription, List<String> adviceList, List<String> managementList, List<String> diagnosisList, List<String> tcmDiagnosisList){

        // 医嘱
        if (StrUtil.isNotBlank(busPrescription.getRemark())) {
            adviceList.add(busPrescription.getRemark()) ;
        }

        List<DrugsVo> drugsVos = this.findPrescriptionDrugInfo(hospitalId , busPrescription);

        // 中药
        if (PrescriptionTypeEnum.isTcmOrZyxdf(busPrescription.getPrescriptionType())){
            this.handleChinaDrug(busPrescription, drugsVos, managementList, tcmDiagnosisList);
        }

        // 西药
        if(PrescriptionTypeEnum.isMm(busPrescription.getPrescriptionType())){

            StringBuilder stringBuilder = new StringBuilder();

            for (int i = 0; i < drugsVos.size(); i++) {
                DrugsVo vo = drugsVos.get(i);
                stringBuilder.append(vo.getStandardCommonName()+"\n规格："+vo.getDrugsSpecification()+"×"+vo.getQuantity()+"\n用法用量："+vo.getDrugsUsageValue()+"，"+vo.getMedicationFrequencyRemarks()+"，"+vo.getSingleDose()+vo.getUnit()+"。\n");
            }

            // 处置
            managementList.add(stringBuilder.toString()) ;

            String clinicalDiagnosis = busPrescription.getClinicalDiagnosis();
            JSONArray objects = JSONObject.parseArray(clinicalDiagnosis);

            String diagnosis = "";
            for (int i = 0; i < objects.size(); i++){
                JSONObject jsonObject = objects.getJSONObject(i);
                String diseaseName = jsonObject.getString("diseaseName");
                if(i == objects.size()-1){
                    diagnosis = diagnosis+ diseaseName;
                }else{
                    diagnosis = diagnosis+ diseaseName+"\n";
                }
            }
            // 诊断
            diagnosisList.add(diagnosis) ;

        }

    }


}
