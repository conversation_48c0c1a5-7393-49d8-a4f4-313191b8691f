package com.puree.hospital.app.domain.dto;

import com.puree.hospital.common.api.enums.ClientTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/8 16:42
 */
@Data
public class RefundDTO {
    /**
     * 订单编号
     */
    private String outTradeNo;
    /**
     * 订单类型（0问诊订单 1药品订单 2服务包订单 3总订单）
     */
    private String orderType;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 商户号类型（1商品 2药品）废弃
     */
    private String mchType;

    /**
     * 应用类型
     * @see ClientTypeEnum
     */
    private String appType = ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType();

    /**
     * 问诊订单退款标识（0后台审核同意 1不需要审核）
     */
    private String automaticRefund;
    /**
     * 退款订单归属（1表示从医院退款合作机构订单）
     */
    private Integer pCode;
    /**
     * 售后单ID
     */
    private Long orderAfterSaleId;
}
