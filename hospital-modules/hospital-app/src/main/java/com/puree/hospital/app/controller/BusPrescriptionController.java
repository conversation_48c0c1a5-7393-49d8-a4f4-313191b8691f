package com.puree.hospital.app.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.model.ApprovePrescriptionDTO;
import com.puree.hospital.app.api.model.BusTcmSyndromeIdDTO;
import com.puree.hospital.app.api.model.event.prescription.BasePrescriptionEvent;
import com.puree.hospital.app.api.model.event.prescription.PrescriptionDiscardedEvent;
import com.puree.hospital.app.api.model.event.prescription.PrescriptionDoctorSignedEvent;
import com.puree.hospital.app.constant.PrescriptionStatus;
import com.puree.hospital.app.domain.BusDoctorHospital;
import com.puree.hospital.app.domain.BusHospitalPa;
import com.puree.hospital.app.domain.BusOrder;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDrugs;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.dto.BusDispensingDTO;
import com.puree.hospital.app.domain.dto.BusPreorderPatientDto;
import com.puree.hospital.app.domain.dto.BusPrescriptionDto;
import com.puree.hospital.app.domain.dto.CheckChinaDrugDeliverDTO;
import com.puree.hospital.app.domain.dto.PrescriptionGenerateResultDTO;
import com.puree.hospital.app.domain.vo.BusOfficinaPharmacistVo;
import com.puree.hospital.app.domain.vo.BusPrescriptionVo;
import com.puree.hospital.app.domain.vo.MMDiagnosisVo;
import com.puree.hospital.app.domain.vo.TCMDiagnosisVo;
import com.puree.hospital.app.domain.vo.TCMSyndromeVo;
import com.puree.hospital.app.mapper.BusHospitalPaMapper;
import com.puree.hospital.app.mapper.BusPatientFamilyMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.prescription.helper.ExaminationFeeHelper;
import com.puree.hospital.app.prescription.helper.PrescriptionHelper;
import com.puree.hospital.app.queue.producer.event.prescription.PrescriptionDiscardedEventProducer;
import com.puree.hospital.app.queue.producer.event.prescription.PrescriptionDoctorSignedEventProducer;
import com.puree.hospital.app.service.IBusConsultationOrderService;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.app.service.IBusDoctorService;
import com.puree.hospital.app.service.IBusDrugsOrderService;
import com.puree.hospital.app.service.IBusHospitalPreorderDoctorService;
import com.puree.hospital.app.service.IBusHospitalService;
import com.puree.hospital.app.service.IBusOfficinaPharmacistService;
import com.puree.hospital.app.service.IBusPatientService;
import com.puree.hospital.app.service.IBusPrescriptionDrugsService;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.app.service.impl.ChinaDrugService;
import com.puree.hospital.business.api.model.enums.DispensingStatusEnum;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.AuditStatus;
import com.puree.hospital.common.core.enums.DirectoryTypeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 处方相关控制器
 */
@Slf4j
@RequestMapping("prescription")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPrescriptionController extends BaseController {
    private final IBusPrescriptionService busPrescriptionService;
    private final IBusPrescriptionDrugsService busPrescriptionDrugsService;
    private final IBusConsultationOrderService busConsultationOrderService;
    private final IBusDrugsOrderService busDrugsOrderService;
    private final IBusDoctorHospitalService busDoctorHospitalService;
    private final IBusDoctorService busDoctorService;
    private final IBusPatientService busPatientService;
    private final BusHospitalPaMapper busHospitalPaMapper;
    private final IBusHospitalPreorderDoctorService iBusHospitalPreorderDoctorService;
    private final IBusHospitalService busHospitalService;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final ChinaDrugService chinaDrugService;
    private final PureeRabbitProducer pureeRabbitProducer;
    private final ExaminationFeeHelper examinationFeeHelper;
    private final PrescriptionHelper prescriptionHelper;
    private final IBusOfficinaPharmacistService busOfficinaPharmacistService;

    @Lazy
    @Resource
    private PrescriptionDiscardedEventProducer prescriptionDiscardedEventProducer;

    @Lazy
    @Resource
    private PrescriptionDoctorSignedEventProducer prescriptionDoctorSignedEventProducer;

    /**
     * 审方药师查询待审核的处方列表
     *
     * @param hospitalId
     * @return
     */
    @Log(title = "审方药师查询待审核的处方列表")
    @GetMapping("queryNotApprovedList")
    public TableDataInfo queryNotApprovedList(HttpServletRequest httpRequest, @RequestParam("hospitalId") String hospitalId, @RequestParam("reviewContent") String reviewContent) {
        if (StringUtils.isEmpty(hospitalId) || "undefined".equals(hospitalId) || "null".equals(hospitalId)) {
            boolean isHos = false;
            String domainPre = httpRequest.getHeader("domainPre");
            logger.info("queryNotApprovedList接口请求域名请求头 domainPre:{}", domainPre);
            if (StringUtils.isNotEmpty(domainPre)) {
                hospitalId = busHospitalService.getHospitalId(domainPre, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
                if (StringUtils.isNotEmpty(hospitalId)) {
                    isHos = true;
                }
            }
            if (!isHos) {
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        }
        startPage();
        List<BusPrescriptionVo> list = busPrescriptionService.selectNotApprovedList(Long.valueOf(hospitalId), reviewContent);
        return getDataTable(list);
    }

    /**
     * 审方药师查询已审核的处方列表（通过/不通过）
     *
     * @param hospitalId
     * @return
     */
    @Log(title = "审方药师查询已审核的处方列表")
    @GetMapping("queryApprovedList")
    public TableDataInfo queryApprovedList(@RequestParam("hospitalId") Long hospitalId) {
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        startPage();
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setHospitalId(hospitalId);
        busPrescription.setReviewPharmacistId(loginUser.getUserid());
        List<BusPrescriptionVo> list = busPrescriptionService.selectApprovedList(busPrescription);
        return getDataTable(list);
    }

    /**
     * 患者查询处方列表（已通过）
     *
     * @return
     */
    @Log(title = "患者查询处方列表")
    @GetMapping("queryPassList")
    @Deprecated
    public TableDataInfo queryPassedList() {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        startPage();
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setHospitalId(loginUser.getHospitalId());
        busPrescription.setPatientId(loginUser.getUserid());
        String partnersCode = SecurityUtils.getPartnerscode();
        if (StringUtils.isNotEmpty(partnersCode)) {
            busPrescription.setPartnersCode(partnersCode);
        }
        List<BusPrescriptionVo> list = busPrescriptionService.selectApprovedPatientList(busPrescription);
        return getDataTable(list);
    }


    @Log(title = "患者查询处方列表")
    @GetMapping("/patient/list")
    public TableDataInfo patientList() {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        startPage();
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setHospitalId(loginUser.getHospitalId());
        busPrescription.setPatientId(loginUser.getUserid());
        String partnersCode = SecurityUtils.getPartnerscode();
        if (StringUtils.isNotEmpty(partnersCode)) {
            busPrescription.setPartnersCode(partnersCode);
        }
        List<BusPrescriptionVo> list = busPrescriptionService.selectApprovedPatientList(busPrescription);
        return getDataTable(list);
    }

    /**
     * 医生查询处方列表(待审核/审核通过/审核不通过)
     *
     * @param busPrescription
     * @return
     */
    @Log(title = "医生查询处方列表")
    @GetMapping("queryList")
    public TableDataInfo queryList(BusPrescription busPrescription) {
        startPage();
        List<BusPrescriptionVo> list = busPrescriptionService.selectList(busPrescription);
        if (CollectionUtil.isNotEmpty(list)) {
            //中药协定方类型，通过批量查询，不能使用for循环，容易导致db压力过大
            Map<Long, Integer> paMap = new HashMap<>();
            List<String> paIds = list.stream().map(BusPrescriptionVo::getPaId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(paIds)) {
                List<BusHospitalPa> paList = busHospitalPaMapper.selectBatchIds(paIds);
                if (CollectionUtil.isNotEmpty(paList)) {
                    paMap.putAll(paList.stream().collect(Collectors.toMap(BusHospitalPa::getId, BusHospitalPa::getType)));
                }
            }
            list.forEach(p -> {
                if (StringUtils.isNotBlank(p.getPaId())) {
                    p.setZyfjType(paMap.get(Long.valueOf(p.getPaId())));
                }
            });
        }
        return getDataTable(list);
    }

    /**
     * 查询处方各个分类的数量
     */
    @Log(title = "查询处方各个分类的数量")
    @GetMapping("getPrescriptionStatusCount")
    public AjaxResult selectPrescriptionStatusCountController(@RequestParam("hospitalId") Long hospitalId,
                                                              Long doctorId,
                                                              Long expertId) {
        return AjaxResult.success(busPrescriptionService.selectPrescriptionStatusCount(hospitalId, doctorId,expertId));
    }

    /**
     * 查询某条处方信息
     *
     * @param busPrescription
     * @return
     */
    @GetMapping("query")
    @Log(title = "查询某条处方信息", businessType = BusinessType.OTHER)
    public AjaxResult query(BusPrescription busPrescription) {
        if (Objects.nonNull(busPrescription.getPrescriptionId())) {
            busPrescription.setId(busPrescription.getPrescriptionId());
        }
        return AjaxResult.success(busPrescriptionService.select(busPrescription));
    }

    /**
     * 群组是否开处方
     *
     * @param busPrescription
     * @return
     */
    @GetMapping("check")
    @Log(title = "群组是否开处方", businessType = BusinessType.OTHER)
    public AjaxResult check(BusPrescriptionDto busPrescription) {
        boolean b = busConsultationOrderService.checkOrderState(busPrescription);
        if (b) {
            return AjaxResult.success(Boolean.TRUE);
        }
        return AjaxResult.success(busPrescriptionService.check(busPrescription));
    }

    /**
     * 创建处方
     *
     * @param dto 处方信息
     * @return
     */
    @PostMapping("create")
    @Log(title = "创建处方", businessType = BusinessType.INSERT)
    public AjaxResult create(@RequestBody BusPrescriptionDto dto) {
        PrescriptionGenerateResultDTO resultDTO = prescriptionHelper.create(dto);
        return AjaxResult.success(resultDTO);
    }


    @PostMapping("update")
    @Log(title = "更新处方信息", businessType = BusinessType.UPDATE)
    public AjaxResult update(@RequestBody BusPrescriptionDto dto) {
        dto.setStatus(String.valueOf(AuditStatus.NOTAPPROVED.getCode()));
        dto.setUpdateBy(SecurityUtils.getUsername());
        dto.setUpdateTime(DateUtils.getNowDate());
        return AjaxResult.success(busPrescriptionService.update(dto));
    }

    @PostMapping("approve")
    @Log(title = "审核处方", businessType = BusinessType.UPDATE)
    public AjaxResult approve(@RequestBody ApprovePrescriptionDTO dto) {
        // 根据处方ID查询医生就诊人信息
        BusPrescription prescription = busPrescriptionService.selectPrescription(dto.getHospitalId(), dto.getId());
        if (prescription == null || !prescription.getStatus().equals(PrescriptionStatus.NOT_EXAMINE)){
            return AjaxResult.error("处方不是待审核状态");
        }
        dto.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        return busPrescriptionService.approve(dto, prescription);
    }

    @PostMapping("discard")
    @Log(title = "作废处方", businessType = BusinessType.UPDATE)
    public AjaxResult discard(@RequestParam("id") Long id,
                              @RequestParam("hospitalId") Long hospitalId) {
        BusPrescription prescription = busPrescriptionService.selectPrescription(hospitalId, id);
        log.info("处方信息={}", JSONObject.toJSONString(prescription));
        if (Objects.isNull(prescription)) {
            return AjaxResult.error("处方不存在");
        }
        if (PrescriptionStatus.USED.equals(prescription.getStatus())) {
            return AjaxResult.error("处方已使用无法作废");
        }
        if (PrescriptionStatus.CANCELLATION.equals(prescription.getStatus())) {
            return AjaxResult.error("当前处方已经作废，请勿重复操作");
        }
        // 复核药师作废处方
        prescription.setUpdateBy(prescription.getDoctorName());
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        log.info("获取缓存信息:{}", loginUser);
        if (AppRoleEnum.DISPENSING_PHARMACIST.getCode().equals(loginUser.getIdentity())) {
            BusOfficinaPharmacistVo busOfficinaPharmacist = busOfficinaPharmacistService.getInfo(loginUser.getUserid());
            if (Objects.isNull(busOfficinaPharmacist)) {
                throw new ServiceException("暂无权限，请联系管理员！");
            }
            if (DispensingStatusEnum.isPass(prescription.getDispensingStatus())) {
                return AjaxResult.error("处方已复核通过，不可再进行复核！");
            }
            if (DispensingStatusEnum.isNotPass(prescription.getDispensingStatus())) {
                return AjaxResult.error("当前处方已经作废，请勿重复操作！");
            }
            // 修改复核状态
            prescription.setDispensingStatus(DispensingStatusEnum.DISPENSING_NOT_PASS.getCode());
            prescription.setDispensingPharmacistId(busOfficinaPharmacist.getId());
            prescription.setDispensingPharmacistName(busOfficinaPharmacist.getPharmacistName());
            prescription.setDispensingTime(new Date());
            prescription.setUpdateBy(busOfficinaPharmacist.getPharmacistName());
        }
        int discard = busPrescriptionService.discard(prescription);
        if (discard > 0) {
            if (DirectoryTypeEnum.isInner(prescription.getDrugDirectoryType())) {
                //删除诊查费标识
                examinationFeeHelper.removeCache(prescription);
            }
            //后续调整作废事件
            PrescriptionDiscardedEvent discardedEvent = new PrescriptionDiscardedEvent();
            discardedEvent.setPrescriptionId(prescription.getId());
            discardedEvent.setEventType(BasePrescriptionEvent.EventType.DISCARDED);
            discardedEvent.setOriginalStatus(prescription.getStatus());
            prescriptionDiscardedEventProducer.send(discardedEvent);
            //todo 后续这里调整监听逻辑
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(hospitalId, RegulatoryEventTypeEnum.PRESCRIPTION_DISCARD, id + "");
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
        return AjaxResult.success(discard);
    }

    /**
     * 作废旧处方
     *
     * @param rxId 处方id
     * @return 作废结果
     */
    @Log(title = "作废旧处方")
    @PostMapping("/invalidOldRx")
    public AjaxResult invalidOldRx(@RequestParam Long rxId){
        BusPrescription prescription = busPrescriptionService.getById(rxId);
        if (Objects.isNull(prescription)) {
            return AjaxResult.error("处方不存在");
        }
        int discard = busPrescriptionService.invalidOldRx(prescription);
        if (discard > 0) {
            if (DirectoryTypeEnum.isInner(prescription.getDrugDirectoryType())) {
                //删除诊查费标识
                examinationFeeHelper.removeCache(prescription);
            }
            RegulatoryCollectEvent event = new RegulatoryCollectEvent(prescription.getHospitalId(), RegulatoryEventTypeEnum.PRESCRIPTION_DISCARD, rxId + "");
            pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
        }
        return toAjax(discard);
    }

    /**
     * 西医诊断列表
     *
     * @param key
     * @return
     */
    @GetMapping("mm/diagnosis")
    @Log(title = "西医诊断列表", businessType = BusinessType.OTHER)
    public TableDataInfo getMMDiagnosis(@RequestParam("key") String key) {
        startPage();
        List<MMDiagnosisVo> list = busPrescriptionService.selectMMDiagnosisList(key);
        return getDataTable(list);
    }

    /**
     * 根据西医诊断id列表，获取西医诊断列表
     *
     * @param ids 西医诊断id列表
     * @return
     */
    @PostMapping("/mm/diagnosis-ids")
    @Log(title = "西医诊断列表", businessType = BusinessType.OTHER)
    public AjaxResult getMMDiagnosisByIds(@RequestBody List<Long> ids) {
        List<MMDiagnosisVo> list = busPrescriptionService.selectMMDiagnosisListByIds(ids);
        return AjaxResult.success(list);
    }

    /**
     * 常用西医诊断列表
     *
     * @return
     */
    @Log(title = "常用西医诊断列表")
    @PostMapping("/common/mm/diagnosis")
    public AjaxResult getCommonMMDiagnosis(@RequestBody BusPrescriptionDto dto) {
        return AjaxResult.success(busPrescriptionService.selectCommonMMDiagnosis(dto));
    }

    /**
     * 中医诊断列表
     *
     * @param key
     * @return
     */
    @GetMapping("tcm/diagnosis")
    @Log(title = "中医诊断列表", businessType = BusinessType.OTHER)
    public TableDataInfo getTCMDiagnosis(@RequestParam("key") String key) {
        startPage();
        List<TCMDiagnosisVo> list = busPrescriptionService.selectTCMDiagnosisList(key);
        return getDataTable(list);
    }

    /**
     * 根据中医诊断id列表，获取中医诊断列表
     *
     * @param dtos 中医诊断id列表
     * @return
     */
    @PostMapping("/tcm/diagnosis-ids")
    @Log(title = "中医诊断列表", businessType = BusinessType.OTHER)
    public AjaxResult getTCMDiagnosisByIds(@RequestBody List<BusTcmSyndromeIdDTO> dtos) {
        List<TCMDiagnosisVo> list = busPrescriptionService.selectTCMDiagnosisListByIds(dtos);
        return AjaxResult.success(list);
    }

    /**
     * 中医症型列表
     *
     * @param key
     * @return
     */
    @GetMapping("tcm/syndrome")
    @Log(title = "中医症型列表", businessType = BusinessType.OTHER)
    public TableDataInfo getTCMSyndrome(@RequestParam("key") String key) {
        startPage();
        List<TCMSyndromeVo> list = busPrescriptionService.selectTCMSyndromeList(key);
        return getDataTable(list);
    }

    /**
     * 查询问诊订单ID
     *
     * @param dto
     * @return
     */
    @PostMapping("/get")
    @Log(title = "查询问诊订单ID", businessType = BusinessType.OTHER)
    public AjaxResult getConsultationOrderId(@RequestBody BusPrescriptionDto dto) {
        return AjaxResult.success(busConsultationOrderService.getConsultationOrderId(dto));
    }

    /**
     * 根据问诊订单ID查询处方ID
     *
     * @param id
     * @return
     */
    @GetMapping("/select")
    @Log(title = "根据问诊订单ID查询处方ID", businessType = BusinessType.OTHER)
    public AjaxResult getPrescriptionId(@RequestParam("id") Long id) {
        return AjaxResult.success(busPrescriptionService.getPrescriptionId(id));
    }

    /**
     * 查询中药加工服务费
     *
     * @param hospitalId
     * @param prescriptionId
     * @return
     */
    @GetMapping("/query/cost")
    @Log(title = "查询中药加工服务费", businessType = BusinessType.OTHER)
    public AjaxResult queryProcessingCost(@RequestParam("hospitalId") Long hospitalId,
                                          @RequestParam("prescriptionId") Long prescriptionId,
                                          @RequestParam("code") String code) {
        return AjaxResult.success(busPrescriptionDrugsService.queryProcessingCost(hospitalId, prescriptionId, code));
    }

    /**
     * 查询药师审核角标数
     *
     * @param hospitalId
     * @param reviewContent
     * @return
     */
    @GetMapping("/get/number")
    @Log(title = "查询药师审核角标数", businessType = BusinessType.OTHER)
    public AjaxResult getNumber(HttpServletRequest httpRequest, @RequestParam("hospitalId") String hospitalId,
                                @RequestParam("reviewContent") String reviewContent) {
        if (StringUtils.isEmpty(hospitalId) || "undefined".equals(hospitalId) || "null".equals(hospitalId)) {
            boolean isHos = false;
            String domainPre = httpRequest.getHeader("domainPre");
            logger.info("/get/number接口请求域名请求头 domainPre:{}", domainPre);
            if (StringUtils.isNotEmpty(domainPre)) {
                hospitalId = busHospitalService.getHospitalId(domainPre, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
                if (StringUtils.isNotEmpty(hospitalId)) {
                    isHos = true;
                }
            }
            if (!isHos) {
                return AjaxResult.error("系统繁忙，请稍后重试");
            }
        }
        List<BusPrescriptionVo> vos = busPrescriptionService.selectNotApprovedList(Long.valueOf(hospitalId), reviewContent);
        return AjaxResult.success(CollUtil.isNotEmpty(vos) ? vos.size() : 0);
    }

    @GetMapping("getNotPassedNumber")
    @Log(title = "获取医生处方未通过的数量", businessType = BusinessType.OTHER)
    public AjaxResult getNotPassedNumber(@RequestParam("hospitalId") Long hospitalId) {
        SMSLoginUser loginUser = busDoctorService.queryDrInfo();
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setStatus(String.valueOf(AuditStatus.FAIL.getCode()));
        busPrescription.setHospitalId(hospitalId);
        // 查询医生身份是否专家
        BusDoctorHospital busDoctorHospital = busDoctorHospitalService.selectDoctorHospitalInfo(hospitalId,
                loginUser.getUserid());
        if (Objects.isNull(busDoctorHospital)) {
            return AjaxResult.error("医生不在该医院");
        }
        busPrescription.setDoctorId(loginUser.getUserid());
        long quantity = busPrescriptionService.selectFailQuantity(busPrescription,busDoctorHospital.getIsThisCourt());
        return AjaxResult.success(quantity);
    }




    /**
     * 获取患者上一次诊断信息
     *
     * @return
     */
    @GetMapping("getLastDiagnosis")
    @Log(title = "获取患者上一次诊断信息", businessType = BusinessType.OTHER)
    public AjaxResult getLastDiagnosis(@RequestParam("hospitalId") Long hospitalId,
                                       @RequestParam("familyId") Long familyId) {
        return AjaxResult.success(busPrescriptionService.getLastDiagnosis(hospitalId, familyId));
    }

    /**
     * 校验处方是否支付
     *
     * @param hospitalId
     * @param id
     * @return
     */
    @GetMapping("/check/payment")
    @Log(title = "校验处方是否支付", businessType = BusinessType.OTHER)
    public AjaxResult checkPayment(@RequestParam("hospitalId") Long hospitalId,
                                   @RequestParam("id") Long id) {
        // 查询处方信息
        BusPrescription prescription = busPrescriptionService.selectPrescription(hospitalId, id);
        // 查询订单信息
        BusOrder busDrugsOrder = busDrugsOrderService.checkPayment(hospitalId, id);
        if (PrescriptionStatus.CANCELLATION.equals(prescription.getStatus()) ||
                PrescriptionStatus.INVALID.equals(prescription.getStatus())) {
            return AjaxResult.success("0");
        } else if (Objects.isNull(busDrugsOrder)) {
            return AjaxResult.success("1");
        } else if (DrugsOrderEnum.PENDING_PAYMENT.getCode().equals(busDrugsOrder.getOrderStatus())) {
            return AjaxResult.success("2");
        } else if (DrugsOrderEnum.CANCELLED.getCode().equals(busDrugsOrder.getOrderStatus())) {
            if (PrescriptionStatus.PASS.equals(prescription.getStatus())) {
                return AjaxResult.success("1");
            }
            return AjaxResult.success("4");
        } else { // 已支付
            return AjaxResult.success("3");
        }
    }

    /**
     * 查询审核通过的处方
     *
     * @param dto
     * @return
     */
    @GetMapping("/query/reviewed")
    @Log(title = "查询审核通过的处方", businessType = BusinessType.OTHER)
    public AjaxResult queryReviewed(BusPrescriptionDto dto) {
        SMSLoginUser loginUser = busPatientService.queryPatientInfo();
        dto.setHospitalId(loginUser.getHospitalId());
        dto.setPatientId(loginUser.getUserid());
        return AjaxResult.success(busPrescriptionService.queryReviewed(dto));
    }

    /**
     * 复诊续购
     *
     * @param dto
     * @return
     */
    @PostMapping("/followUp/purchase")
    @Log(title = "复诊续购", businessType = BusinessType.OTHER)
    public AjaxResult followUpPurchase(@RequestBody BusPreorderPatientDto dto) {
        return AjaxResult.success(busPrescriptionService.followUpPurchase(dto));
    }

    /**
     * 查询医嘱列表
     *
     * @return
     */
    @GetMapping("/doctor/advice/template")
    @Log(title = "查询医嘱列表", businessType = BusinessType.OTHER)
    public AjaxResult selectPrescriptionDoctorAdviceTemplateList() {
        return AjaxResult.success(busPrescriptionService.selectPrescriptionDoctorAdviceTemplateList());
    }

    /**
     * 审核不通过原因模板
     *
     * @return
     */
    @Log(title = "审核不通过原因模板")
    @GetMapping("/query/fail/template/list")
    public AjaxResult queryFailTemplateList() {
        return AjaxResult.success(busPrescriptionService.queryFailTemplateList());
    }

    /**
     * 查询处方药品
     *
     * @param prescriptionDrugs
     * @return
     */
    @GetMapping("/query/drugs")
    @Log(title = "查询处方药品", businessType = BusinessType.OTHER)
    public AjaxResult queryPrescriptionDrugs(BusPrescriptionDrugs prescriptionDrugs) {
        return AjaxResult.success(busPrescriptionDrugsService.selectPrescriptionDrugsList(prescriptionDrugs));
    }

    /**
     * 医生签名药品清单
     *
     * @param busPrescription
     * @return
     */
    @Log(title = "医生签名药品清单")
    @GetMapping("/sign")
    public AjaxResult doctorSign(BusPrescription busPrescription) {
        int i = busPrescriptionService.doctorSign(busPrescription);
        if (i > 0) {
            PrescriptionDoctorSignedEvent doctorSignedEvent = new PrescriptionDoctorSignedEvent();
            doctorSignedEvent.setEventType(PrescriptionDoctorSignedEvent.EventType.DOCTOR_SIGNED);
            doctorSignedEvent.setPrescriptionId(busPrescription.getId());
            prescriptionDoctorSignedEventProducer.send(doctorSignedEvent);

        }
        return toAjax(i);
    }

    /**
     * 根据医院id查询默认开方医生
     *
     *
     * @param hospitalId
     * @return
     */
    @Log(title = "根据医院id查询默认开方医生")
    @GetMapping("getDefaultDoctorByHospitalId")
    public AjaxResult getDefaultDoctorByHospitalId(@RequestParam Long hospitalId) {
        return AjaxResult.success(iBusHospitalPreorderDoctorService.selectByHospitalId(hospitalId));
    }

    /**
     * 生成处方pdf文件
     *
     * @param busPrescription
     * @return
     */
    @Log(title = "生成处方pdf文件")
    @PostMapping("/feign/pdf")
    public R<String> generatePdf(@RequestBody BusPrescription busPrescription) {
        return R.ok(busPrescriptionService.generatePdf(busPrescription));
    }

    /**
     * 发送im群组自定义消息
     *
     * @param dto
     * @return
     */
    @Log(title = "发送im群组自定义消息")
    @PostMapping("/send/msg")
    public AjaxResult sendMsgToIm(@RequestBody BusPrescriptionDto dto) {
        return AjaxResult.success(busPrescriptionService.sendMsgToIm(dto));
    }

    /**
     * 远程调用处方详情
     *
     * @param id
     * @return
     */
    @PostMapping("/feign/getIdCardPrescriptionDesc")
    public R getIdCardPrescriptionDesc(@RequestParam Long id) {
        BusPrescription busPrescription = busPrescriptionMapper.selectById(id);
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(busPrescription.getFamilyId());
        return R.ok(busPatientFamily);
    }

    /**
     * 查询医保的处方详情
     *
     * @param rxNoList 处方号
     * @return 处方详情
     */
    @PostMapping("/feign/getMiRx")
    public R<List<BusPrescription>> getMiRx(@RequestBody List<String> rxNoList) {
        return R.ok(busPrescriptionService.getMiRx(rxNoList, null));
    }

    /**
     * 查询医保的处方详情
     *
     * @param rxIdList 处方号
     * @return 处方详情
     */
    @PostMapping("/feign/rx-by-ids")
    public R<List<BusPrescription>> getMiRxByIds(@RequestBody List<Long> rxIdList) {
        return R.ok(busPrescriptionService.getMiRx(null, rxIdList));
    }

    @PostMapping("/check-china-drug-deliver")
    @Log(title = "创建中药处方订单前，校验中药配送", businessType = BusinessType.INSERT)
    public AjaxResult checkChinaDrugDeliver(@Validated @RequestBody CheckChinaDrugDeliverDTO req) {
        try {
            chinaDrugService.checkChinaDrugDeliver(req.getHospitalId(), req.getDrugsIdList());
        } catch (ServiceException e) {
            return AjaxResult.success(false, e.getMessage());
        }
        return AjaxResult.success(true);
    }

    /**
     * 查询所有未复核的处方列表
     *
     * @param dto 查询参数
     * @return 处方列表
     */
    @GetMapping("/dispensing/list")
    @Log(title = "查询所有未复核的处方列表", businessType = BusinessType.QUERY)
    public TableDataInfo getDispensingList(@ModelAttribute @Validated BusDispensingDTO dto) {
        startPage();
        dto.setHospitalId(SecurityUtils.getHospitalId());
        return getDataTable(busPrescriptionService.getList(dto));
    }

    /**
     * 复核审核接口
     *
     * @param dto 参数
     * @return 结果
     */
    @PutMapping("/dispensing")
    @Log(title = "药师复核处方", businessType = BusinessType.UPDATE)
    public R<Integer> dispensingPrescription(@RequestBody BusDispensingDTO dto) {
        return R.ok(busPrescriptionService.dispensingPrescription(dto));
    }

}

