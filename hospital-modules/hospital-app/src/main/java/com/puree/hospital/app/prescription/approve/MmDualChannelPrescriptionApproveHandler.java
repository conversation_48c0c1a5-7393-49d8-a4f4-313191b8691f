package com.puree.hospital.app.prescription.approve;

import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusPrescriptionDualChannel;
import com.puree.hospital.app.api.model.ApprovePrescriptionDTO;
import com.puree.hospital.app.prescription.helper.DualChannelDigitalRxParamHelper;
import com.puree.hospital.app.prescription.helper.DualChannelRxRpcHelper;
import com.puree.hospital.app.mapper.BusPrescriptionDualChannelMapper;
import com.puree.hospital.common.core.constant.PrescriptionConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.insurance.api.enums.DigitalRxStatusEnum;
import com.puree.hospital.insurance.api.model.DigitalRxUploadDTO;
import com.puree.hospital.insurance.api.model.DigitalRxUploadResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 双通道-西药&中成药处方审批执行器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/25 19:32
 */
@Slf4j
@Component(PrescriptionConstant.MM + PrescriptionConstant.DUAL_CHANNEL + IPrescriptionApproveHandler.SUFFIX)
public class MmDualChannelPrescriptionApproveHandler extends AbstractPrescriptionApproveHandler {

    @Resource
    private OSSUtil ossUtil;

    @Resource
    private DualChannelRxRpcHelper dualChannelRxRpcHelper;

    @Resource
    private DualChannelDigitalRxParamHelper dualChannelDigitalRxParamHelper;

    @Resource
    private BusPrescriptionDualChannelMapper busPrescriptionDualChannelMapper;

    @Override
    protected Date getValidTime(BusPrescription prescription, BusConsultationSettings settings) {
        //处方新增的时候会直接设置过期时间
        return prescription.getValidTime();
    }

    @Override
    protected void doPassUpdate(ApprovePrescriptionDTO dto, BusPrescription prescription, BusConsultationSettings setting) {
        //状态已经更新，所以重新查询，保证最新
        prescription = busPrescriptionService.getById(dto.getId());
        long start = System.currentTimeMillis();
        //生成pdf文件路劲（需要测试审核药师是在pdf文件中）
        String path = busPrescriptionService.generatePdf(prescription);
        //处方pdf文件url链接
        String rxPdfUrl = ossUtil.getFullPath(path);
        DigitalRxUploadDTO rxUploadDTO = dualChannelDigitalRxParamHelper.buildUploadDTO(prescription, rxPdfUrl);
        R<DigitalRxUploadResultDTO> result = dualChannelRxRpcHelper.upload(rxUploadDTO);
        log.info("电子处方上传请求参数：{}, 上传结果：{}, 耗时：{}", rxUploadDTO, result, System.currentTimeMillis() - start);
        if (Objects.isNull(result) || !result.isSuccess()) {
            throw new ServiceException("电子处方上传失败，" + result.getMsg());
        }
        //更新信息
        DigitalRxUploadResultDTO rxUploadResult = result.getData();
        if (Objects.nonNull(rxUploadResult)) {
            BusPrescriptionDualChannel update = new BusPrescriptionDualChannel();
            update.setPrescriptionId(prescription.getId());
            update.setSignDigest(rxUploadResult.getSignDigest());
            update.setSignCertSn(rxUploadResult.getSignCertSn());
            update.setSignCertDn(rxUploadResult.getSignCertDn());
            //初始化状态，方便查询
            update.setRxStatus(StringUtils.isNotBlank(rxUploadResult.getRxStatus()) ? rxUploadResult.getRxStatus() : DigitalRxStatusEnum.EFFECTIVE.getStatus());
            update.setRxStatusName(rxUploadResult.getRxStatusName());
            update.setUpdateTime(new Date());
            update.setUpdateBy(prescription.getReviewPharmacistName());
            busPrescriptionDualChannelMapper.updateById(update);
        }
    }

    @Override
    protected void sendMsg(ApprovePrescriptionDTO dto, BusConsultationSettings setting) {
        //发送处方过期事件
        super.sendExpiredMsg(dto);
    }

    @Override
    protected void doRejectUpdate(ApprovePrescriptionDTO dto) {
        //do nothing
    }
}
