package com.puree.hospital.app.controller;

import com.puree.hospital.app.domain.dto.BusPatientMedicalRecordDTO;
import com.puree.hospital.app.domain.vo.BusPatientMedicalRecordListVO;
import com.puree.hospital.app.domain.vo.BusPatientRegRecordVO;
import com.puree.hospital.app.domain.vo.BusExamineReportDetailVO;
import com.puree.hospital.app.domain.vo.BusInspectReportDetail;
import com.puree.hospital.app.his.domain.vo.BusEmrDetailVO;
import com.puree.hospital.app.his.domain.vo.BusPrescriptionInfoVO;
import com.puree.hospital.app.service.IBusPatientMedicalInfoService;
import com.puree.hospital.common.api.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 患者就诊信息 - 控制层
 * <AUTHOR>
 * @date 2025-02-26 18:38
 **/
@Slf4j
@RequestMapping("/medical-history")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPatientMedicalInfoController {

    private final IBusPatientMedicalInfoService busPatientMedicalInfoService;

    /**
     * 获取患者就诊记录列表
     * @param dto 查询参数
     * @return 就诊记录列表
     **/
    @GetMapping("/reg-list")
    public AjaxResult<List<BusPatientRegRecordVO>> regList(@Valid BusPatientMedicalRecordDTO dto) {
        log.info("获取患者就诊记录列表-查询参数：{}", dto);
        return AjaxResult.success(busPatientMedicalInfoService.regList(dto));
    }

    /**
     * 获取患者医疗记录列表
     * @param dto 查询参数
     * @return 医疗记录列表
     **/
    @GetMapping("/record-list")
    public AjaxResult<List<BusPatientMedicalRecordListVO>> recordList(@Valid BusPatientMedicalRecordDTO dto) {
        log.info("获取患者医疗记录列表-查询参数：{}", dto);
        Assert.isTrue(dto.checkRecordType(), "记录类型不存在");
        return AjaxResult.success(busPatientMedicalInfoService.recordList(dto));
    }

    /**
     *  获取病历详情
     * @param id  门诊流水号
     * @param patientName   患者姓名
     * @return  病历详情
     */
    @GetMapping("/emr-detail")
    public AjaxResult<List<BusEmrDetailVO>> emrDetail(@RequestParam("id") String id,
                                                      @RequestParam(value = "patientName", required = false) String patientName,
                                                      @RequestParam(value = "patientId", required = false) String patientId) {
        log.info("获取电子病历详情-查询参数：id: {}, patientName: {},patientId: {}", id, patientName,patientId);
        return AjaxResult.success(busPatientMedicalInfoService.getEmrDetail(id, patientName,patientId));
    }

    /**
     * 获取患者检查记录详情
     * @param reportId 报告ID
     * @return 检查记录详情
     **/
    @GetMapping("/inspect-detail")
    public AjaxResult<List<BusInspectReportDetail>> inspectDetail(@RequestParam("reportId") String reportId) {
        log.info("获取患者检查记录详情-查询参数：{}", reportId);
        return AjaxResult.success(busPatientMedicalInfoService.inspectDetail(reportId));
    }

    /**
     * 获取患者检验记录详情
     * @param reportId 报告ID
     * @return 检验记录详情
     **/
    @GetMapping("/examine-detail")
    public AjaxResult<BusExamineReportDetailVO> examineDetail(@RequestParam("reportId") String reportId, @RequestParam(value = "itemCode", required = false) String itemCode, @RequestParam(value = "testOrder", required = false)String testOrder) {
        log.info("获取患者检验记录详情-查询参数：{}", reportId);
        return AjaxResult.success(busPatientMedicalInfoService.examineDetail(reportId, itemCode, testOrder));
    }

    /**
     * 获取患者处方详情
     * @param dto 查询参数
     * @return 处方详情
     */
    @GetMapping("/prescription-info")
    public AjaxResult<BusPrescriptionInfoVO> prescriptionInfo(@Valid BusPatientMedicalRecordDTO dto) {
        log.info("获取患者处方记录详情-查询参数：{}", dto);
        return AjaxResult.success(busPatientMedicalInfoService.getPrescriptionInfo(dto));
    }


}
