package com.puree.hospital.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.domain.BusCommunicationMessage;
import lombok.Data;

import java.util.Date;

/**
 * 会话列表VO
 * <AUTHOR>
 */
@Data
public class ConversationInfoVO {
    /**群组id*/
    private Long groupId;
    /**群组名称*/
    private String groupName;
    /**群组最新消息*/
    private BusCommunicationMessage busCommunicationMessage;
    /**消息时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date msgTime;
    /**医生id*/
    private Long doctorId;
    /**科室id*/
    private Long departmentId;
    /**患者id*/
    private Long patientId;
    /**就诊人id*/
    private Long familyId;
    /**医院id*/
    private Long hospitalId;
    /**医生名称*/
    private String doctorName;
    /**科室名称*/
    private String departmentName;
    /**就诊人名称*/
    private String familyName;
    /**医生职称*/
    private String title;
    /**患者性别*/
    private String sex;
    /**患者出生日期*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfBirth;
    /**等级 0 非vip 1 vip*/
    private Integer grade;
    /**群组类型*/
    private String type;
    /**医生头像*/
    private String photo;
    /** 医助名称*/
    private String assistantName;
    /** 医助id*/
    private Long assistantId;
    /** 医助头像*/
    private String assistantPhoto;
    /** 诊疗档案*/
    private String diagnosisArchives;
    /** 是否修改*/
    private Integer modifyFlag;
    /** 年龄 （小于1岁显示月份）*/
    private String familyAge;
    /**医生执业方式 0多点 1本院 2特约专家*/
    private Integer isThisCourt;
    /**特邀默认开方医生*/
    private Long defaultDoctorId;
    /**医生医院状态 0停用 1启用*/
    private Integer doctorHospitalStatus;
    private String delFlag;
    /**
     * 身份证号码
     */
    private String idCardNo;
    /**
     * 标记变更无法使用,0 false 1 true
     */
    private Boolean markChanges;
}
