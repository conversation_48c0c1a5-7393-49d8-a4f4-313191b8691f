package com.puree.hospital.app.prescription.sign;

import com.puree.hospital.app.api.model.enums.SignTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/12 16:13
 */
@Component
public class PrescriptionSignStrategyFactory {

    private static final Map<SignTypeEnum, IPrescriptionSignStrategy> STRATEGY_MAP = new HashMap<>();

    /**
     * 构造函数
     *
     * @param strategies 策略列表
     */
    @Autowired
    public PrescriptionSignStrategyFactory(List<IPrescriptionSignStrategy> strategies) {
        for (IPrescriptionSignStrategy strategy : strategies) {
            // 需要在每个策略实现类中添加获取对应枚举的方法
            STRATEGY_MAP.put(strategy.getSignType(), strategy);
        }
    }

    /**
     * 获取策略
     *
     * @param signType 签名类型
     * @return 策略
     */
    public IPrescriptionSignStrategy getStrategy(SignTypeEnum signType) {
        IPrescriptionSignStrategy strategy = STRATEGY_MAP.get(signType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的签名身份类型：" + signType);
        }
        return strategy;
    }












}
