package com.puree.hospital.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.app.domain.*;
import com.puree.hospital.app.domain.vo.BusDoctorVo;
import com.puree.hospital.app.domain.vo.ConversationInfoVO;
import com.puree.hospital.app.mapper.*;
import com.puree.hospital.app.service.IBusDoctorPatientGroupService;
import com.puree.hospital.app.service.IBusHospitalPreorderDoctorService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.*;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeUtil;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.AppDefinedData;
import com.puree.hospital.im.api.model.ImGroupRequest;
import com.puree.hospital.im.api.model.Member;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorPatientGroupServiceImpl extends ServiceImpl<BusDoctorPatientGroupMapper, BusDoctorPatientGroup> implements IBusDoctorPatientGroupService {
    private final BusDoctorPatientGroupMapper busDoctorPatientGroupMapper;
    private final RemoteImService remoteImService;
    private final BusDoctorMapper busDoctorMapper;
    private final BusPatientMapper busPatientMapper;
    private final BusCommunicationMessageMapper busCommunicationMessageMapper;
    private final BusDoctorDepartmentMapper busDoctorDepartmentMapper;
    private final BusFiveAssistantDoctorMapper busFiveAssistantDoctorMapper;
    private final BusImGroupMemberMapper busImGroupMemberMapper;
    private final IBusHospitalPreorderDoctorService hospitalPreorderDoctorService;
    private final BusBizDepartmentMapper busBizDepartmentMapper;
    private final BusDoctorPatientMapper busDoctorPatientMapper;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final BusDoctorHospitalMapper busDoctorHospitalMapper;
    private final BusHospitalFamilyMapper busHospitalFamilyMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createGroup(BusDoctorPatientGroup busDoctorPatientGroup, String delFlag) {
        log.info("创建im群组请求参数:group={},delFlag={}", JSON.toJSON(busDoctorPatientGroup), delFlag);
        Long groupId = busDoctorPatientGroup.getId();
        if (ObjectUtil.isNotNull(busDoctorPatientGroup.getId())) {
            busDoctorPatientGroup.setId(null);
        }
        // 创建新群组
        if (CodeEnum.NO.getCode().equals(delFlag)) {
            busDoctorPatientGroup.setCreateTime(DateUtils.getNowDate());
            if (busDoctorPatientGroup.getDepartmentId() != null) {
                busDoctorPatientGroupMapper.insert(busDoctorPatientGroup);
            } else {
                log.error("创建群组缺失科室ID：{}", busDoctorPatientGroup);
                long doctorId = busDoctorPatientGroup.getDoctorId();
                BusDoctorDepartment busDoctorDepartment = new BusDoctorDepartment();
                busDoctorDepartment.setDoctorId(doctorId);
                busDoctorPatientGroup.setDepartmentId(busDoctorDepartmentMapper.selectList(busDoctorDepartment).get(0).getDepartmentId());
                busDoctorPatientGroupMapper.insert(busDoctorPatientGroup);
            }
        } else {
            // 唤醒旧群组
            BusDoctorPatientGroup updateGroup = new BusDoctorPatientGroup();
            updateGroup.setDelFlag(CodeEnum.NO.getCode());
            updateGroup.setId(groupId);
            busDoctorPatientGroup.setId(groupId);
            busDoctorPatientGroupMapper.updateById(updateGroup);
        }
        ImGroupRequest imGroupRequest = new ImGroupRequest();
        BusDoctorVo busDoctor = busDoctorMapper.selectHospitalDoctorInfo(busDoctorPatientGroup.getDoctorId(), busDoctorPatientGroup.getHospitalId());
        BusPatient patient = busPatientMapper.selectById(busDoctorPatientGroup.getPatientId());
        BusFiveAssistantDoctor assistantDoctor = busFiveAssistantDoctorMapper.selectOne(new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                .eq(BusFiveAssistantDoctor::getDoctorId, busDoctorPatientGroup.getDoctorId())
                .eq(BusFiveAssistantDoctor::getHospitalId, busDoctorPatientGroup.getHospitalId()));
        if (busDoctor == null) {
            throw new ServiceException("医生信息缺失");
        }
        if (ObjectUtil.isNull(patient)) {
            throw new ServiceException("群组基础信息缺失");
        }
        List<BusImGroupMember> imGroupMemberList = new ArrayList<>();
        //医生用户
        BusImGroupMember doctorMember = new BusImGroupMember();
        doctorMember.setGroupId(busDoctorPatientGroup.getId());
        doctorMember.setPersonnelId(busDoctorPatientGroup.getDoctorId());
        doctorMember.setRole(busDoctor.getRole());
        doctorMember.setGroupRole("Owner");
        doctorMember.setCreateTime(DateUtils.getNowDate());
        doctorMember.setIsInto(YesNoEnum.YES.getCode());
        imGroupMemberList.add(doctorMember);
        //患者用户
        BusImGroupMember patientMember = new BusImGroupMember();
        patientMember.setGroupId(busDoctorPatientGroup.getId());
        patientMember.setPersonnelId(busDoctorPatientGroup.getPatientId());
        patientMember.setRole(ImMemberEnum.PATIENT.getCode());
        patientMember.setGroupRole("Member");
        patientMember.setCreateTime(DateUtils.getNowDate());
        patientMember.setIsInto(YesNoEnum.YES.getCode());
        imGroupMemberList.add(patientMember);
        if (ObjectUtil.isNotNull(assistantDoctor)) {
            //医助用户
            Long assistantId = assistantDoctor.getAssistantId();
            BusDoctorVo assistantVO = busDoctorMapper.selectHospitalDoctorInfo(assistantId, busDoctorPatientGroup.getHospitalId());
            BusImGroupMember assistantMember = new BusImGroupMember();
            assistantMember.setGroupId(busDoctorPatientGroup.getId());
            assistantMember.setPersonnelId(assistantDoctor.getAssistantId());
            assistantMember.setRole(assistantVO.getRole());
            assistantMember.setGroupRole("Member");
            assistantMember.setCreateTime(DateUtils.getNowDate());
            assistantMember.setIsInto(YesNoEnum.YES.getCode());
            imGroupMemberList.add(assistantMember);
        }
        log.info("群组人员信息 member={}", JSON.toJSONString(imGroupMemberList));
        if (CodeEnum.NO.getCode().equals(delFlag)) {
            busImGroupMemberMapper.batchInsert(imGroupMemberList);
        } else {
            //判断是否更换过医助
            if (ObjectUtil.isNotNull(assistantDoctor)) {
                Long assistantId = assistantDoctor.getAssistantId();
                // 原本为selectOne,限制新增时产生重复脏数据
                BusImGroupMember imGroupMember = busImGroupMemberMapper.selectOne(new LambdaQueryWrapper<BusImGroupMember>()
                        .eq(BusImGroupMember::getGroupId, groupId)
                        .eq(BusImGroupMember::getPersonnelId, assistantId)
                        .ne(BusImGroupMember::getRole, ImMemberEnum.PATIENT.getCode())
                        .last(" limit 1"));
                log.info("查询im群组结果={}", JSON.toJSONString(imGroupMember));
                if (ObjectUtil.isNull(imGroupMember)) {
                    //先删再加
                    BusDoctorVo assistantVO = busDoctorMapper.selectHospitalDoctorInfo(assistantId, busDoctorPatientGroup.getHospitalId());
                    busImGroupMemberMapper.delete(new LambdaQueryWrapper<BusImGroupMember>()
                            .eq(BusImGroupMember::getGroupId, groupId)
                            .ne(BusImGroupMember::getPersonnelId, busDoctorPatientGroup.getDoctorId())
                            .ne(BusImGroupMember::getRole, ImMemberEnum.PATIENT.getCode()));
                    // 查询是否有相同数据 防止脏数据
                    List<BusImGroupMember> imGroupMemberQueryList = busImGroupMemberMapper.selectList(new LambdaQueryWrapper<BusImGroupMember>()
                            .eq(BusImGroupMember::getGroupId, groupId)
                            .eq(BusImGroupMember::getPersonnelId, assistantDoctor.getAssistantId())
                            .ne(BusImGroupMember::getRole, assistantVO.getRole()));
                    if (CollUtil.isEmpty(imGroupMemberQueryList)) {
                        BusImGroupMember assistantMember = new BusImGroupMember();
                        assistantMember.setGroupId(groupId);
                        assistantMember.setPersonnelId(assistantDoctor.getAssistantId());
                        assistantMember.setRole(assistantVO.getRole());
                        assistantMember.setGroupRole("Member");
                        assistantMember.setCreateTime(DateUtils.getNowDate());
                        assistantMember.setIsInto(YesNoEnum.YES.getCode());
                        busImGroupMemberMapper.insert(assistantMember);
                    }
                }
            }
        }
        // 填充im群组信息
        imGroupRequest.setType(TencentyunImConstants.IM_GROUP_TYPE);
        if (CodeEnum.NO.getCode().equals(delFlag)) {
            imGroupRequest.setGroupId(String.valueOf(busDoctorPatientGroup.getId()));
            imGroupRequest.setDelFlag(CodeEnum.NO.getCode());
        } else {
            imGroupRequest.setGroupId(String.valueOf(groupId));
            imGroupRequest.setDelFlag(CodeEnum.YES.getCode());
        }
        imGroupRequest.setName(busDoctor.getFullName());
        //添加自定义字段信息
        List<AppDefinedData> appDefinedDataList = new ArrayList<>();
        //医院id
        AppDefinedData hospitalId = new AppDefinedData();
        hospitalId.setKey("hospital_id");
        hospitalId.setValue(busDoctorPatientGroup.getHospitalId() + "");
        appDefinedDataList.add(hospitalId);
        //群组类型
        AppDefinedData title = new AppDefinedData();
        title.setKey("title");
        title.setValue(ImGroupType.INQUIRIES.getCode());
        appDefinedDataList.add(title);
        //是否删除
        AppDefinedData is_binding = new AppDefinedData();
        is_binding.setKey("is_binding");
        is_binding.setValue(CodeEnum.NO.getCode());
        appDefinedDataList.add(is_binding);
        imGroupRequest.setAppDefinedData(appDefinedDataList);
        // 填充群组人员信息
        List<Member> memberList = new ArrayList<>();
        //创建群主(医生)信息
        imGroupRequest.setOwner_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctorPatientGroup.getDoctorId());
        setOwnerAccount(memberList, imGroupRequest, busDoctor);
        //创建患者信息
        setPatient(memberList, imGroupRequest, patient);
        //创建医助信息(不为空时添加医助角色)
        if (ObjectUtil.isNotNull(assistantDoctor)) {
            setAssistant(memberList, imGroupRequest, assistantDoctor.getAssistantId());
        }
        R<JSONObject> group = remoteImService.createGroup(imGroupRequest);
        log.info("远程调用创建IM群组返回：{}", JSON.toJSONString(group));
        if (Constants.FAIL.equals(group.getCode()) && ObjectUtil.isNotNull(group.getData())) {
            // 腾讯IM添加失败时
            JSONObject jsonObject = group.getData();
            Integer errorCode = jsonObject.getInteger("ErrorCode");
            if (errorCode == 10037) {
                throw new ServiceException("医生创建和加入的群组数量超过了限制，无法创建，请联系医院管理员！");
            }
            if (errorCode == 10058) {
                throw new ServiceException("腾讯IM版本群组创建上限，无法创建，请联系医院管理员！");
            }
            if (errorCode == 10036) {
                throw new ServiceException("创建的音视频聊天室数量超过限制，无法创建，请联系医院管理员！");
            }
        }
        return busDoctorPatientGroup.getId();
    }


    @Override
    public List<BusDoctorPatientGroup> selectConversationList(BusDoctorPatientGroup busDoctorPatientGroup) {
        return busDoctorPatientGroupMapper.selectConversationList(busDoctorPatientGroup);
    }

    @Override
    public List<ConversationInfoVO> conversationList(Long personnelId, String role, Long hospitalId) {
        List<ConversationInfoVO> conversationInfoVOList = new ArrayList<>();
        BusDoctorPatientGroup queryWrapper = new BusDoctorPatientGroup();
        queryWrapper.setHospitalId(hospitalId);
        //判断查询角色 0医生 1患者
        switch (role) {
            case "0":
                queryWrapper.setDoctorId(personnelId);
                break;
            case "1":
                queryWrapper.setPatientId(personnelId);
                break;
            default:
        }
        List<ConversationInfoVO> patientGroups = busDoctorPatientGroupMapper.listConversation(queryWrapper);
        List<Long> groupIdList = patientGroups.stream()
                .map(ConversationInfoVO::getGroupId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(patientGroups)) {
            return conversationInfoVOList;
        }
        String groupIds = groupIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        //查询最新一条消息
        List<BusCommunicationMessage> communicationMessages = busCommunicationMessageMapper.selectMultipleNewest(groupIds);
        if (CollUtil.isNotEmpty(communicationMessages)) {
            patientGroups.stream().forEach(c -> {
                communicationMessages.stream().forEach(m -> {
                    if (c.getGroupId().equals(Long.valueOf(m.getGroupId()))) {
                        c.setBusCommunicationMessage(m);
                        c.setMsgTime(m.getCreateTime());
                    }
                });
            });
        }
        //按照发送消息时间排序
        return patientGroups.stream().sorted(Comparator.comparing(d -> d.getMsgTime(),
                        Comparator.nullsFirst(Date::compareTo).reversed()))
                .collect(Collectors.toList());
    }

    @Override
    public BusDoctorPatientGroup checkGroup(BusDoctorPatientGroup group) {
        Assert.notNull(group, "参数不能为空");

        LambdaQueryWrapper<BusDoctorPatientGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(group.getHospitalId() != null, BusDoctorPatientGroup::getHospitalId, group.getHospitalId())
                .eq(group.getDoctorId() != null, BusDoctorPatientGroup::getDoctorId, group.getDoctorId())
                .eq(group.getId() != null, BusDoctorPatientGroup::getId, group.getId())
                .eq(group.getDepartmentId() != null, BusDoctorPatientGroup::getDepartmentId, group.getDepartmentId())
                .eq(group.getPatientId() != null, BusDoctorPatientGroup::getPatientId, group.getPatientId())
                .eq(group.getFamilyId() != null, BusDoctorPatientGroup::getFamilyId, group.getFamilyId())
                .eq(BusDoctorPatientGroup::getMarkChanges, Boolean.FALSE)
                .eq(StringUtils.isNotEmpty(group.getType()), BusDoctorPatientGroup::getType, group.getType());
        return busDoctorPatientGroupMapper.selectOne(wrapper);
    }

    @Override
    public void checkTitle(String groupId, String title) {
        BusDoctorPatientGroup group = busDoctorPatientGroupMapper.selectById(Long.valueOf(groupId));
        BusDoctorVo busDoctorVo = busDoctorMapper.selectDoctorInfoById(group.getDoctorId());
        ImGroupRequest request = new ImGroupRequest();
        if (!Long.valueOf(title).equals(busDoctorVo.getTitle())) {
            request.setGroupId(group.getId() + "");
            List<AppDefinedData> appDefinedDataList = new ArrayList<>();
            AppDefinedData appDefinedData = new AppDefinedData();
            appDefinedData.setKey("is_binding");
            appDefinedData.setValue(YesNoEnum.YES.getCode() + "");
            appDefinedDataList.add(appDefinedData);
            request.setAppDefinedData(appDefinedDataList);
            remoteImService.modifyGroupBaseInfo(request);
        }
    }

    @Override
    public int update(BusDoctorPatientGroup busDoctorPatientGroup) {
        return busDoctorPatientGroupMapper.updateById(busDoctorPatientGroup);
    }

    @Override
    public BusDoctorPatientGroup selectOne(BusDoctorPatientGroup patientGroup) {
        LambdaQueryWrapper<BusDoctorPatientGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getId()), BusDoctorPatientGroup::getId, patientGroup.getId());
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getPatientId()), BusDoctorPatientGroup::getPatientId, patientGroup.getPatientId());
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getDepartmentId()), BusDoctorPatientGroup::getDepartmentId, patientGroup.getDepartmentId());
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getDoctorId()), BusDoctorPatientGroup::getDoctorId, patientGroup.getDoctorId());
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getFamilyId()), BusDoctorPatientGroup::getFamilyId, patientGroup.getFamilyId());
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getServiceId()), BusDoctorPatientGroup::getServiceId, patientGroup.getServiceId());
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getType()), BusDoctorPatientGroup::getType, patientGroup.getType());
        queryWrapper.eq(ObjectUtil.isNotNull(patientGroup.getHospitalId()), BusDoctorPatientGroup::getHospitalId, patientGroup.getHospitalId());
        queryWrapper.eq(BusDoctorPatientGroup::getMarkChanges, Boolean.FALSE);
        queryWrapper.orderByDesc(BusDoctorPatientGroup::getId);
        queryWrapper.last("limit 1");
        return busDoctorPatientGroupMapper.selectOne(queryWrapper);
    }

    /**
     * 查询组信息
     *
     * @param id
     * @return
     */
    @Override
    public BusDoctorPatientGroup selectGroupInfo(Long id) {
        return busDoctorPatientGroupMapper.selectById(id);
    }

    @Override
    public ConversationInfoVO getGroupInfo(Long groupId) {
        log.info("获取群组入参:{}", groupId);
        ConversationInfoVO conversation = new ConversationInfoVO();
        conversation.setGroupId(groupId);
        // 补充群组信息
        BusDoctorPatientGroup doctorPatientGroup = busDoctorPatientGroupMapper.selectById(groupId);
        if (Objects.isNull(doctorPatientGroup)) {
            throw new ServiceException("查询群组失败");
        }
        conversation.setDoctorId(doctorPatientGroup.getDoctorId());
        conversation.setPatientId(doctorPatientGroup.getPatientId());
        conversation.setMarkChanges(doctorPatientGroup.getMarkChanges());
        conversation.setFamilyId(doctorPatientGroup.getFamilyId());
        conversation.setDepartmentId(doctorPatientGroup.getDepartmentId());
        conversation.setType(doctorPatientGroup.getType());
        conversation.setModifyFlag(Integer.valueOf(doctorPatientGroup.getModifyFlag()));
        conversation.setDelFlag(doctorPatientGroup.getDelFlag());
        conversation.setHospitalId(doctorPatientGroup.getHospitalId());
        // 查询医生信息
        BusDoctorVo busDoctor = busDoctorMapper.selectHospitalDoctorInfoV2(doctorPatientGroup.getDoctorId(), doctorPatientGroup.getHospitalId());
        conversation.setDoctorName(busDoctor.getFullName());
        conversation.setPhoto(busDoctor.getPhoto());
        conversation.setTitle(busDoctor.getTitleValue());
        BusDoctorHospital doctorHospital = busDoctorHospitalMapper.selectOne(new LambdaQueryWrapper<BusDoctorHospital>()
                .eq(BusDoctorHospital::getHospitalId, doctorPatientGroup.getHospitalId())
                .eq(BusDoctorHospital::getDoctorId, doctorPatientGroup.getDoctorId()));
        conversation.setIsThisCourt(doctorHospital.getIsThisCourt());
        conversation.setDoctorHospitalStatus(doctorHospital.getStatus());
        // 查询科室
        BusBizDepartment department = busBizDepartmentMapper.selectById(doctorPatientGroup.getDepartmentId());
        conversation.setDepartmentName(department.getDepartmentName());
        // 查询患者信息
        List<BusDoctorPatient> doctorPatientList = busDoctorPatientMapper.selectList(new LambdaQueryWrapper<BusDoctorPatient>()
                .eq(BusDoctorPatient::getHospitalId, doctorHospital.getHospitalId())
                .eq(BusDoctorPatient::getFamilyId, doctorPatientGroup.getFamilyId())
                .eq(BusDoctorPatient::getDoctorId, doctorPatientGroup.getDoctorId())
                .eq(BusDoctorPatient::getPatientId, doctorPatientGroup.getPatientId())
                .last("limit 1"));
        if (null != doctorPatientList && !doctorPatientList.isEmpty()) {
            BusDoctorPatient doctorPatient = doctorPatientList.get(0);
            conversation.setGrade(doctorPatient.getGrade());
        }

        // 查询就诊人信息
        BusPatientFamily patientFamily = busPatientFamilyMapper.selectById(doctorPatientGroup.getFamilyId());
        conversation.setDateOfBirth(patientFamily.getDateOfBirth());
        conversation.setFamilyName(patientFamily.getName());
        conversation.setSex(patientFamily.getSex());
        conversation.setIdCardNo(patientFamily.getIdNumber());
        BusHospitalFamily hospitalFamily = busHospitalFamilyMapper.selectOne(new LambdaQueryWrapper<BusHospitalFamily>()
                .eq(BusHospitalFamily::getPatientId, doctorPatientGroup.getPatientId())
                .eq(BusHospitalFamily::getFamilyId, doctorPatientGroup.getFamilyId())
                .eq(BusHospitalFamily::getHospitalId, doctorPatientGroup.getHospitalId()));
        if (null != hospitalFamily) {
            conversation.setDiagnosisArchives(hospitalFamily.getDiagnosisArchives());
        }
        // 默认开方医生
        if (DoctorPracticeEnum.SPECIAL_INVITATION.getCode().equals(conversation.getIsThisCourt())) {
            BusHospitalPreorderDoctor preorderDoctor = hospitalPreorderDoctorService.selectByHospitalId(conversation.getHospitalId());
            conversation.setDefaultDoctorId(Objects.isNull(preorderDoctor) ? null : preorderDoctor.getDoctorId());
        }
        BusFiveAssistantDoctor assistantDoctor = busFiveAssistantDoctorMapper.selectOne(new LambdaQueryWrapper<BusFiveAssistantDoctor>()
                .eq(BusFiveAssistantDoctor::getDoctorId, conversation.getDoctorId())
                .eq(BusFiveAssistantDoctor::getHospitalId, conversation.getHospitalId()));
        if (ObjectUtil.isNotNull(assistantDoctor)) {
            BusDoctor doctor = busDoctorMapper.selectById(assistantDoctor.getAssistantId());
            if (doctor != null) {
                conversation.setAssistantId(doctor.getId());
                conversation.setAssistantName(doctor.getFullName());
                conversation.setAssistantPhoto(doctor.getPhoto());
            }
        }
        Date dateOfBirth = conversation.getDateOfBirth();
        if (ObjectUtil.isNotNull(dateOfBirth)) {
            String time = DateUtils.parseDateToStr("yyyy-MM-dd", dateOfBirth);
            conversation.setFamilyAge(AgeUtil.getAgeDetail(time));
        }
        log.info("获取群组出参:{}", conversation);
        return conversation;
    }

    @Override
    public List<BusDoctorDepartment> getDoctorDepartment(BusDoctorPatientGroup busDoctorPatientGroup) {
        List<BusDoctorDepartment> doctorDepartmentList = busDoctorDepartmentMapper.selectDeptList(busDoctorPatientGroup.getHospitalId(), busDoctorPatientGroup.getDoctorId());
        Iterator<BusDoctorDepartment> iterator = doctorDepartmentList.iterator();
        while (iterator.hasNext()) {
            BusDoctorDepartment next = iterator.next();
            busDoctorPatientGroup.setDepartmentId(next.getDepartmentId());
            busDoctorPatientGroup.setType(CodeEnum.NO.getCode());
            BusDoctorPatientGroup checkGroup = this.checkGroup(busDoctorPatientGroup);
            next.setId(null);
            if (ObjectUtil.isNotNull(checkGroup)) {
                iterator.remove();
            }
        }
        return doctorDepartmentList;
    }

    @Override
    public boolean checkDoctorDepartment(BusDoctorPatientGroup busDoctorPatientGroup) {
        BusDoctorDepartment doctorDepartment = busDoctorDepartmentMapper.selectOne(new LambdaQueryWrapper<BusDoctorDepartment>()
                .eq(BusDoctorDepartment::getDepartmentId, busDoctorPatientGroup.getDepartmentId())
                .eq(BusDoctorDepartment::getDoctorId, busDoctorPatientGroup.getDoctorId())
                .eq(BusDoctorDepartment::getHospitalId, busDoctorPatientGroup.getHospitalId()));
        return ObjectUtil.isNotNull(doctorDepartment) ? true : false;
    }

    /**
     * @Param busDoctorPatientGroup
     * @Return java.util.List<com.puree.hospital.app.domain.BusDoctorPatientGroup>
     * @Description 获取群组信息
     * <AUTHOR>
     * @Date 2024/3/13 10:57
     **/
    @Override
    public List<BusDoctorPatientGroup> getBusDoctorPatientGroupList(BusDoctorPatientGroup busDoctorPatientGroup) {
        return busDoctorPatientGroupMapper.selectList(Wrappers.lambdaQuery(busDoctorPatientGroup));
    }

    /**
     * @Param busDoctorPatientGroup
     * @Return com.puree.hospital.app.domain.BusDoctorPatientGroup
     * @Description 获取服务包群组信息
     * <AUTHOR>
     * @Date 2024/3/14 15:22
     **/
    @Override
    public BusDoctorPatientGroup getServicePackImInfo(BusDoctorPatientGroup busDoctorPatientGroup) {
        return busDoctorPatientGroupMapper.getServicePackImInfo(busDoctorPatientGroup);
    }

    /**
     * 添加医生信息
     *
     * @param memberList     群成员信息
     * @param imGroupRequest im请求类
     * @param busDoctor      医生信息
     */
    private void setOwnerAccount(List<Member> memberList, ImGroupRequest imGroupRequest, BusDoctorVo busDoctor) {
        Member member = new Member();
        member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + busDoctor.getId());
        memberList.add(member);
        imGroupRequest.setMemberList(memberList);
    }

    /**
     * 添加患者信息
     *
     * @param memberList     群成员信息
     * @param imGroupRequest im请求类
     * @param patient        医生信息
     */
    private void setPatient(List<Member> memberList, ImGroupRequest imGroupRequest, BusPatient patient) {
        Member member = new Member();
        member.setMember_Account(TencentyunImConstants.PATIENT_IM_ACCOUNT + patient.getId());
        memberList.add(member);
        imGroupRequest.setMemberList(memberList);
    }

    /**
     * 添加医助
     *
     * @param memberList     群成员信息
     * @param imGroupRequest im请求类
     * @param assistantId    医助信息
     */
    private void setAssistant(List<Member> memberList, ImGroupRequest imGroupRequest, Long assistantId) {
        Member member = new Member();
        member.setMember_Account(TencentyunImConstants.DOCTOR_IM_ACCOUNT + assistantId);
        memberList.add(member);
        imGroupRequest.setMemberList(memberList);
    }
}
