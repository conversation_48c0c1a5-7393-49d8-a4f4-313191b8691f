package com.puree.hospital.app.prescription.approve;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.domain.BusConsultationSettings;
import com.puree.hospital.app.domain.BusEhrCaseDiagnosisHistory;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.api.model.ApprovePrescriptionDTO;
import com.puree.hospital.app.mapper.BusConsultationSettingsMapper;
import com.puree.hospital.app.mapper.BusEhrCaseDiagnosisHistoryMapper;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import com.puree.hospital.app.queue.model.Message;
import com.puree.hospital.app.queue.producer.PrescriptionInvalidProducer;
import com.puree.hospital.app.service.IBusPrescriptionService;
import com.puree.hospital.app.service.impl.BusHandlePrescriptionService;
import com.puree.hospital.common.core.enums.AuditStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 抽象处方审核执行器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/25 19:26
 */
@Slf4j
public abstract class AbstractPrescriptionApproveHandler implements IPrescriptionApproveHandler {

    @Resource
    protected BusPrescriptionMapper busPrescriptionMapper;

    @Resource
    private BusConsultationSettingsMapper busConsultationSettingsMapper;

    @Lazy
    @Resource
    private PrescriptionInvalidProducer prescriptionInvalidProducer ;

    @Resource
    private BusEhrCaseDiagnosisHistoryMapper ehrCaseDiagnosisHistoryMapper;

    @Resource
    private BusHandlePrescriptionService handlePrescriptionService;

    @Lazy
    @Resource
    protected IBusPrescriptionService busPrescriptionService;

    @Override
    public int approve(ApprovePrescriptionDTO approvePrescriptionDto, BusPrescription prescription) {
        //第一步设置过期时间
        LambdaQueryWrapper<BusConsultationSettings> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusConsultationSettings::getHospitalId, approvePrescriptionDto.getHospitalId());
        queryWrapper.last(" limit 1");
        BusConsultationSettings setting = busConsultationSettingsMapper.selectOne(queryWrapper);
        //处方审批时间
        approvePrescriptionDto.setReviewTime(new Date());
        approvePrescriptionDto.setValidTime(getValidTime(prescription, setting));
        //此处需要先更新处方的审核信息，否则生成的处方文件会没有审核药师的签名信息
        int result = busPrescriptionMapper.approve(approvePrescriptionDto);
        if (AuditStatus.isAdopt(Integer.parseInt(approvePrescriptionDto.getStatus()))) {
            //第二步如果是问诊单，更新患者的健康档案
            if (null != prescription.getConsultationOrderId() ) {
                this.updateEhrCaseDiagnosisHistory(prescription) ;
            }

            //第三步更新关联状态
            doPassUpdate(approvePrescriptionDto, prescription, setting);

            //第四步发送消息
            sendMsg(approvePrescriptionDto, setting);
        } else {
            //审核不通过
            doRejectUpdate(approvePrescriptionDto);
        }
        return result;
    }

    /**
     * 获取过期事件
     *
     * @param prescription 处方信息
     * @param settings     运营配置
     */
    protected abstract Date getValidTime(BusPrescription prescription, BusConsultationSettings settings);

    /**
     * 处方通审核过更新逻辑
     *
     * @param dto             处方审核参数
     * @param busPrescription 处方信息
     * @param setting         运营配置
     */
    protected abstract void doPassUpdate(ApprovePrescriptionDTO dto,
                                         BusPrescription busPrescription,
                                         BusConsultationSettings setting);

    /**
     * 发送消息
     *
     * @param dto      处方审核参数
     * @param setting  运营设置
     */
    protected abstract void sendMsg(ApprovePrescriptionDTO dto, BusConsultationSettings setting);

    /**
     * 处方通审核不通过更新逻辑
     *
     * @param dto 处方审核参数
     */
    protected abstract void doRejectUpdate(ApprovePrescriptionDTO dto);

    /**
     * 发送处方过期事件
     *
     * @param dto      处方审核参数
     */
    protected void sendExpiredMsg(ApprovePrescriptionDTO dto) {
        Message message = new Message();
        message.setId(dto.getId());
        message.setHospitalId(dto.getHospitalId());
        message.setFireTime(dto.getValidTime().getTime() - System.currentTimeMillis());
        prescriptionInvalidProducer.delaySend(message, message.getFireTime()) ;
    }

    /**
     * 更新患者健康档案
     *
     * @param prescription 处方信息
     */
    private void updateEhrCaseDiagnosisHistory(BusPrescription prescription) {
        LambdaQueryWrapper<BusEhrCaseDiagnosisHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusEhrCaseDiagnosisHistory::getConsultationId, prescription.getConsultationOrderId());
        List<BusEhrCaseDiagnosisHistory> ehrCaseDiagnosisHistories = ehrCaseDiagnosisHistoryMapper.selectList(wrapper);
        if (ehrCaseDiagnosisHistories.isEmpty()) {
            return ;
        }
        // 医嘱, 包含所有处方
        List<String> newAdviceList = new ArrayList<>();

        // 处置， 里面是所有处方的药品信息
        List<String> newManagementList = new ArrayList<>() ;

        // 诊断, 包含所有西药处方
        List<String> newDiagnosisList = new ArrayList<>();

        // 诊断, 包含所有中药处方
        List<String> newTcmDiagnosisList = new ArrayList<>() ;

        handlePrescriptionService.handlePrescriptionInfo(prescription.getHospitalId(), prescription, newAdviceList, newManagementList, newDiagnosisList, newTcmDiagnosisList);

        for (BusEhrCaseDiagnosisHistory ehrCaseDiagnosisHistory : ehrCaseDiagnosisHistories){
            ehrCaseDiagnosisHistory.setAdvice(StrUtil.isNotBlank(ehrCaseDiagnosisHistory.getAdvice())?ehrCaseDiagnosisHistory.getAdvice():"") ;
            StringBuilder sb1 = new StringBuilder();
            for (String advice : newAdviceList) {
                sb1.append(advice).append("\n") ;
            }
            ehrCaseDiagnosisHistory.setAdvice(ehrCaseDiagnosisHistory.getAdvice() + sb1);

            // 判断历史数据是不是以\n结尾的 如果不是则添加
            String oldManagement = ehrCaseDiagnosisHistory.getManagement();
            boolean endsWithNewline = oldManagement != null && oldManagement.endsWith("\n");
            ehrCaseDiagnosisHistory.setManagement(StrUtil.isNotBlank(oldManagement) ? (endsWithNewline ? oldManagement : oldManagement + "\n") : "");
            StringBuilder sb2 = new StringBuilder();
            for (String management : newManagementList) {
                sb2.append(management).append("\n") ;
            }
            ehrCaseDiagnosisHistory.setManagement(ehrCaseDiagnosisHistory.getManagement() + sb2);
            ehrCaseDiagnosisHistory.setUpdateTime(new Date());
            ehrCaseDiagnosisHistoryMapper.updateById(ehrCaseDiagnosisHistory);
        }


    }
}
