package com.puree.hospital.app.prescription.sign;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.puree.hospital.app.api.model.enums.SignTypeEnum;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.dto.PrescriptionSignDTO;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 处方创建签名策略
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/13 16:30
 */
@Component
public class PrescriptionCreateSignStrategy extends AbstractPrescriptionSignStrategy {
    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;

    @Override
    public String getOperatorName(PrescriptionSignDTO prescription) {
        return prescription.getDoctorName();
    }

    @Override
    public Long getOperatorId(PrescriptionSignDTO prescription) {
        return prescription.getDoctorId();
    }

    @Override
    protected String getSignFlag() {
        return "{{prescription_doctor}}";
    }

    @Override
    public SignTypeEnum getSignType() {
        return SignTypeEnum.CREATE;
    }

    @Override
    public int updateOperater(PrescriptionSignDTO prescription) {

        return 1;
    }

    @Override
    public String getDigestSignature(List<String> digestNames) {
        if(digestNames !=null && digestNames.size()>0)
        {
            return digestNames.get(0);
        }
        return null;
    }

    @Override
    public String getObjectType() {
        return "0";
    }


}
