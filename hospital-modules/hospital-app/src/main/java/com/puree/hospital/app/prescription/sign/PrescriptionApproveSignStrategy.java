package com.puree.hospital.app.prescription.sign;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.puree.hospital.app.api.model.enums.SignTypeEnum;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.BusSignature;
import com.puree.hospital.app.domain.dto.PrescriptionSignDTO;
import com.puree.hospital.app.mapper.BusPrescriptionMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 处方审核签名
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/13 16:43
 */
@Component
public class PrescriptionApproveSignStrategy extends AbstractPrescriptionSignStrategy {
    @Resource
    private BusPrescriptionMapper busPrescriptionMapper;
    @Override
    public String getOperatorName(PrescriptionSignDTO prescription) {
        return prescription.getReviewPharmacistName();
    }

    @Override
    public Long getOperatorId(PrescriptionSignDTO prescription) {
        return prescription.getReviewPharmacistId();
    }

    @Override
    protected String getSignFlag() {
        return "{{approve_pharmacist}}";
    }

    @Override
    public SignTypeEnum getSignType() {
        return SignTypeEnum.APPROVE;
    }

    @Override
    public int updateOperater(PrescriptionSignDTO prescription) {

        return 1;
    }

    @Override
    public String getDigestSignature(List<String> digestNames) {
        if(digestNames !=null && digestNames.size()>1)
        {
            return digestNames.get(1);
        }
        return null;
    }

    @Override
    public String getObjectType() {
        return "1";
    }

}
