package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.app.domain.dto.BusGuardianDTO;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.util.Date;

/**
 * 就诊人信息表
 * <AUTHOR>
 * @date 2021-12-31
 *  // TODO - 这个对象管理过于混乱，需要后期修改，职责不清，VO和DTO，实体对象要区别开
 */
@Data
public class BusPatientFamily extends Entity {

    /** 姓名 */
    private String name;
    /** 手机号 */
    private String cellPhoneNumber;
    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfBirth;
    /** 性别（0女 1男） */
    private String sex;
    /** 身份证号码 */
    private String idNumber;
    /** 患者关系（字典表获取） */
    private Long patientRelationship;
    /** 是否默认 */
    private Integer status;
    /** 民族 */
    private Long nation;
    /** 婚姻状况（0 未婚 1 已婚） */
    private Integer maritalStatus;
    /** 工作单位 */
    private String workUnit;
    /** 住址所在地区 */
    private String location;
    /** 详细地址 */
    private String detailAddress;
    /** 患者ID */
    private Long patientId;
    /** 门诊号 **/
    private String outpatientNumber;
    private String province;
    private String city;
    private String area;
    private Integer height;
    private Integer weight;
    /**
     * 职业
     */
    private String profession;
    /**
     * 文化程度
     */
    private String educationLevel;

    /**
     * 添加来源 0普通添加 1扫码添加
     */
    private String source;
    /**
     * 医生ID
     */
    private Long doctorId;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /** 诊疗档案 */
    @TableField(exist = false)
    private String diagnosisArchives;
    @TableField(exist = false)
    private String patientRelationshipValue;
    @TableField(exist = false)
    private String sexStr;
    /**
     * 验证码
     */
    @TableField(exist = false)
    private String code;
    /**
     * 手机号
     */
    @TableField(exist = false)
    private String phone;

    /**
     * 证件类型  01：居民身份证  03：护照  06：港澳居民来往内地通行证  07：台湾居民来往内地通行证
     * {@link IdCardTypeEnum}
     */
    private String idType ;


    @TableField(exist = false)
    private Integer isAll ;
    /**
     * 0 为删除
     */
    private Integer visible ;

    /**
     * his方 患者id
     */
    private String hisPatientId;

    /**
     * 九合一设备 - 受检者ID
     */
    private String leerPatientId;
    /**
     * 九合一设备 - 匿名受检者ID
     */
    private String leerVisitorId;

    /**
     * 监护人信息
     */
    @TableField(exist = false)
    private BusGuardianDTO busGuardianDTO;

    @TableField(exist = false)
    private Boolean isChild = false;

    /**
     * 查询来源 - 服务包ID
     */
    @TableField(exist = false)
    private Long groupId;

}
