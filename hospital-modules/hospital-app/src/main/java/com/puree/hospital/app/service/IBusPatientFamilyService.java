package com.puree.hospital.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.puree.hospital.app.domain.BusPatientFamily;
import com.puree.hospital.app.domain.SMSLoginUser;
import com.puree.hospital.app.domain.vo.BusPatientFamilyVo;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface IBusPatientFamilyService extends IService<BusPatientFamily> {
    Long addInterrogator(BusPatientFamily interrogator);
    List<BusPatientFamilyVo> interrogatorList(BusPatientFamily interrogator);
    int deleteInterrogator(BusPatientFamily interrogator);
    BusPatientFamily getInterrogator(BusPatientFamily interrogator);
    int updateInterrogator(BusPatientFamily interrogator);
    BusPatientFamilyVo selectPatientFamilyById(Long id, Long hospitalId);
    boolean check(Long id, Long hospitalId,Long groupId);
    int edit(BusPatientFamily patientFamily);
    BusPatientFamily getById(Long id);
    int addFamily(BusPatientFamily busPatientFamily);
    Long selectRelationShip(String relation);

    com.puree.hospital.app.api.model.BusPatientFamilyVo getInfoByIdNumber(BusPatientFamily patientFamily);

    /**
     * 根据身份证号查询就诊人信息
     * @param patientFamily 身份证号码
     * @return
     */
    BusPatientFamily selectFamilyByNo(BusPatientFamily patientFamily);

    BusPatientFamily selectFamilyById(Long id);

    List<com.puree.hospital.app.api.model.BusPatientFamilyVo> getList(BusPatientFamily patientFamily);

    Long queryInfoByUserId(Long userId, Long hospitalId);

    /**
     * 根据登录身份信息 - 允许患者查询的角色信息
     * @param loginUser 登录用户信息
     * @param patientFamily 患者查询信息
     * @return - 允许查询的患者的用户id
     */
    boolean allowPatientIdByAppRole(@NotNull SMSLoginUser loginUser,@NotNull BusPatientFamily patientFamily);

    /**
     * 根据登录身份信息 - 允许患者查询的角色信息 - 是否是符合医院的患者
     * @param loginUser 登录用户信息
     * @param interrogator 患者已经查询的信息
     * @return - 是否允许查询 - true:允许 false:不允许
     */
    boolean checkPatientFamilyWithHospital(@NotNull SMSLoginUser loginUser, BusPatientFamily interrogator);

    String getOrRegisterLeerPatientId(Long id);
}