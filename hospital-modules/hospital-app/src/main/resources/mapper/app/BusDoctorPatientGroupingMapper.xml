<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusDoctorPatientGroupingMapper">
    <resultMap id="BaseResultMap" type="BusDoctorPatientGrouping">
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="doctor_patient_id" property="doctorPatientId" />
        <result column="grouping_id" property="groupingId" />
        <result column="id" property="id" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <resultMap id="PatientResultMap" type="BusDoctorPatientVo">
        <result column="id" property="id" />
        <result column="hospital_id" property="hospitalId" />
        <result column="family_id" property="familyId" />
        <result column="doctor_id" property="doctorId" />
        <result column="patient_id" property="patientId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="patient_source" property="patientSource" />
        <result column="grade" property="grade" />
        <result column="name" property="name" />
        <result column="sex" property="sex" />
        <result column="date_of_birth" property="dateOfBirth" />
        <result column="cell_phone_number" property="cellPhoneNumber" />
        <result column="dpgId" property="dpgId"/>
        <result column="family_id" property="familyId"/>
        <result column="nation" property="nation"/>
        <result column="marital_status" property="maritalStatus"/>
        <result column="gId" property="gId"/>
        <result column="id_number" property="idCardNo"/>
        <collection property="prescriptionList" ofType="BusPrescription">
            <result column="clinical_diagnosis" property="clinicalDiagnosis"/>
        </collection>
        <collection property="patientGroupList" ofType="BusDoctorPatientGroup">
            <result column="department_id" property="departmentId"/>
            <result column="department_name" property="departmentName"/>
            <result column="gId" property="id"/>
        </collection>
    </resultMap>

    <!-- 表字段 -->
    <sql id="baseColumns">
        t.create_by
        , t.create_time
        , t.doctor_patient_id
        , t.grouping_id
        , t.id
        , t.update_by
        , t.update_time
    </sql>

    <sql id="patientColumns">
        t.id
        , t.hospital_id
        , t.doctor_id
        , t.family_id
        , t.patient_id
        , t.create_by
        , t.create_time
        , t.update_by
        , t.update_time
        , t.grade
        , t.patient_source
    </sql>

    <select id="selectPatientGrouping" resultMap="PatientResultMap">
        SELECT
        <include refid="patientColumns" />
        , a.name,a.sex,a.cell_phone_number,a.date_of_birth
--         , b.clinical_diagnosis,g.department_id,g.department_name,g.id gId
         ,dpg.id dpgId
        FROM bus_doctor_patient_grouping dpg
        LEFT JOIN bus_doctor_patient t ON dpg.doctor_patient_id=t.id
        LEFT JOIN (SELECT f.id,f.name,f.sex,f.date_of_birth,f.cell_phone_number,f.patient_id
        FROM bus_patient_family f ) a ON a.id=t.family_id AND t.patient_id=a.patient_id
        <where>
            dpg.grouping_id = #{groupingId}
        </where>
    </select>

    <select id="selectDoctorNotAddPatientGrouping" resultMap="PatientResultMap" parameterType="Long">
        SELECT
        <include refid="patientColumns" />
        , a.name,a.sex,a.cell_phone_number,a.date_of_birth,a.id_number
--         , b.clinical_diagnosis,g.department_id,g.department_name,g.id gId,dpg.id dpgId
        FROM
        bus_doctor_patient t
        LEFT JOIN
        (SELECT f.id,f.name,f.sex,f.date_of_birth,f.cell_phone_number,f.patient_id,f.id_number FROM bus_patient_family f
        ) a ON a.id=t.family_id AND t.patient_id=a.patient_id
        LEFT JOIN
        (SELECT family_id,hospital_id,doctor_id,clinical_diagnosis FROM bus_prescription ORDER BY create_time) b ON t.family_id =b.family_id AND b.hospital_id=t.hospital_id AND t.doctor_id=b.doctor_id
        LEFT JOIN
        (SELECT pg.family_id,pg.doctor_id,pg.department_id,pg.id,pg.patient_id,d.department_name FROM bus_doctor_patient_group pg
        LEFT JOIN `bus_biz_department` d ON pg.department_id=d.id) g ON g.doctor_id=t.doctor_id AND g.family_id=t.family_id AND g.patient_id AND t.patient_id
        left join bus_doctor_patient_grouping dpg on dpg.doctor_patient_id=t.id
        <where>
            dpg.grouping_id IS NULL
            and t.hospital_id=#{hospitalId} AND t.doctor_id=#{doctorId}
        </where>
        GROUP BY t.id
    </select>

    <select id="selectById" resultMap="PatientResultMap">
        SELECT
        <include refid="patientColumns" />
        , a.name,a.sex,a.cell_phone_number,a.date_of_birth
        , b.clinical_diagnosis,g.department_id,g.department_name,dpg.grouping_id gId,a.nation,a.marital_status,dpg.id dpgId
        FROM
        bus_doctor_patient t
        LEFT JOIN
        (SELECT f.id,f.name,f.sex,f.date_of_birth,f.cell_phone_number,f.patient_id,f.nation,f.marital_status FROM bus_patient_family f
        ) a ON a.id=t.family_id AND t.patient_id=a.patient_id
        LEFT JOIN
        (SELECT family_id,hospital_id,doctor_id,clinical_diagnosis FROM bus_prescription ORDER BY create_time) b ON t.family_id =b.family_id AND b.hospital_id=t.hospital_id AND t.doctor_id=b.doctor_id
        LEFT JOIN
        (SELECT pg.family_id,pg.doctor_id,pg.department_id,pg.id,pg.patient_id,d.department_name FROM bus_doctor_patient_group pg
        LEFT JOIN `bus_biz_department` d ON pg.department_id=d.id) g ON g.doctor_id=t.doctor_id AND g.family_id=t.family_id AND g.patient_id AND t.patient_id
        LEFT JOIN  bus_doctor_patient_grouping dpg on dpg.doctor_patient_id=t.id
        <where>
            t.id = #{groupingId}
        </where>
    </select>

    <!-- 插入全部字段 -->
    <insert id="batchInsert" parameterType="BusDoctorPatientGrouping"
            keyProperty="id" keyColumn="id" useGeneratedKeys="true"
    >
        INSERT INTO bus_doctor_patient_grouping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            create_time,
            doctor_patient_id,
            grouping_id,
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.createTime},
        #{item.doctorPatientId},
        #{item.groupingId}
        )
        </foreach>
    </insert>
</mapper>