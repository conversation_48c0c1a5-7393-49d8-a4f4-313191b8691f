<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusDoctorHospitalMapper">
    <resultMap id="BaseResultMap" type="com.puree.hospital.app.domain.vo.BusDoctorHospitalVo">
        <result property="hospitalName" column="hospital_name"/>
        <result property="doctorName" column="doctorName"/>
        <result property="status" column="status"/>
        <collection property="doctorDepartmentList" resultMap="busDoctorDepartmentResultMap"/>
    </resultMap>
    <resultMap id="busDoctorBoResultMap" type="com.puree.hospital.app.domain.bo.BusDoctorBO">
        <id property="id" column="id"/>
        <result property="fullName" column="full_name"/>
        <result property="doctorNumber" column="doctor_number"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="idCardNoImg" column="id_card_no_img"/>
        <result property="introduce" column="introduce"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="photo" column="photo"/>
        <result property="practiceCertificate" column="practice_certificate"/>
        <result property="qrCode" column="qr_code"/>
        <result property="qualificationCertificate" column="qualification_certificate"/>
        <result property="sex" column="sex"/>
        <result property="status" column="status"/>
        <result property="title" column="title"/>
        <result property="titleCertificate" column="title_certificate"/>
        <result property="descriptionDetails" column="description_details"/>
        <result property="settledTime" column="settled_time"/>
        <result property="titleValue" column="title_value"/>
        <result property="firstHospital" column="first_hospital"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="yes" column="yes"/>
        <result property="isTcm" column="is_tcm"/>
        <result property="isPreorderDoctor" column="is_preorder_doctor"/>
        <result property="doctorTitle" column="doctor_title"/>
        <result property="disable" column="disable"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="isAttention" column="is_attention"/>
        <result property="role" column="role"/>
        <result property="roleId" column="role_id"/>
        <result property="reviewContent" column="review_content"/>
        <result property="nationalDoctorCode" column="national_doctor_code"/>
        <result property="orderNum" column="order_num"/>
        <result property="practisingTypeCode" column="practising_type_code"/>
        <result property="practisingScopeCode" column="practising_scope_code"/>
        <result property="firstHospitalUnifiedCreditCode" column="first_hospital_unified_credit_code"/>
        <result property="hospitalName" column="hospital_name"/>
        <collection property="doctorDepartmentList" column="{doctorId=id, hospitalId=hospital_id}"  ofType="com.puree.hospital.app.domain.BusDoctorDepartment" select="selectDoctorDepartmentList"/>
    </resultMap>
    <resultMap id="busDoctorDepartmentResultMap" type="com.puree.hospital.app.domain.BusDoctorDepartment">
        <id property="departmentId" column="department_id"/>
        <result column="departmentName" property="departmentName"/>
    </resultMap>
    <select id="firstPractice" parameterType="Long" resultMap="BaseResultMap">
        SELECT f.full_name doctorName,h.hospital_name,bd.department_name departmentName,a.`status` FROM `bus_doctor_hospital` a
           LEFT JOIN `bus_hospital` h ON a.hospital_id=h.id
           LEFT JOIN bus_doctor_department dd ON a.doctor_id=dd.doctor_id AND a.hospital_id=dd.hospital_id
           LEFT JOIN  bus_biz_department bd ON bd.id=dd.department_id
           LEFT JOIN bus_doctor f ON a.doctor_id=f.id
        WHERE  a.doctor_id = #{doctorId} AND a.is_this_court=1 and a.is_authentication='3'
    </select>

    <select id="selectDoctorHospitalList" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.vo.BusDoctorHospitalVo">
        SELECT h.hospital_name,a.is_this_court,a.is_authentication,a.`status` FROM `bus_doctor_hospital` a
        LEFT JOIN `bus_hospital` h ON a.hospital_id=h.id
        WHERE  a.doctor_id = #{doctorId} AND a.hospital_id = #{hospitalId}
    </select>
    <select id="selectRole" resultType="java.lang.String">
        SELECT role FROM bus_doctor_hospital WHERE doctor_id = #{doctorId} AND hospital_id = #{hospitalId}
        limit 1
    </select>
    <select id="selectDoctorList" resultMap="busDoctorBoResultMap">
        select bd.*,bdh.hospital_id,bh.hospital_name
        from bus_doctor bd, bus_doctor_hospital bdh,bus_hospital bh
        where bd.id = bdh.doctor_id  and bdh.hospital_id = bh.id
        and bdh.online_status = 1
        <if test="fullName != null">
            AND bd.full_name like concat('%', #{fullName}, '%')
        </if>
        <if test="title != null">
            AND bd.title =#{title}
        </if>
        <if test="hospitalId != null">
            AND bdh.hospital_id =#{hospitalId}
        </if>
        <if test="role != null and role != ''">
            AND bdh.role =#{role}
        </if>
        <if test="isAuthentication != null">
            AND bdh.is_authentication =#{isAuthentication}
        </if>
        <if test="hospitalId != null">
            AND bh.id = #{hospitalId}
        </if>
        <if test="excludeHospitalIdList!= null and excludeHospitalIdList.size() > 0">
            AND bh.id NOT IN
                <foreach collection="excludeHospitalIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        <if test="status != null">
            ANd bdh.`status` =#{status}
        </if>
    </select>

    <select id="selectDoctorDepartmentList" resultType="com.puree.hospital.app.domain.BusDoctorDepartment">
        SELECT dd.department_id,bd.department_name FROM bus_doctor_department dd
        LEFT JOIN bus_biz_department bd ON dd.department_id=bd.id
        WHERE dd.doctor_id = #{doctorId} AND dd.hospital_id = #{hospitalId}
    </select>
</mapper>