<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusDoctorDepartmentMapper">


    <select id="selectList" parameterType="BusDoctorDepartment" resultType="BusDoctorDepartmentVo">
        SELECT bd.parent_id, bd.id departmentId,bd.department_name FROM bus_doctor_department bt
        left JOIN bus_biz_department bd ON bd.id=bt.department_id
        <where>
            bd.`status` = '1'
            <if test="hospitalId !=null">
                and bt.hospital_id=#{hospitalId}
            </if>
            <if test="doctorId !=null">
                and bt.doctor_id=#{doctorId}
            </if>
            <if test="departmentName !=null">
                and bd.department_name =#{departmentName}
            </if>
        </where>
    </select>

    <insert id="batchInsert" parameterType="BusDoctorDepartment" useGeneratedKeys="false">
        INSERT INTO bus_doctor_department
        (
        doctor_id, hospital_id, department_id,
        create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.doctorId}, #{item.hospitalId}, #{item.departmentId},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <select id="selectDeptList" parameterType="BusDoctorDto" resultType="BusDoctorDepartment">
        SELECT
            t2.id,
            t2.department_name,
            t1.department_id
        FROM
            bus_doctor_department t1
            LEFT JOIN bus_biz_department t2 ON t2.id = t1.department_id
        <where>
            t1.hospital_id = #{hospitalId}
            AND t1.doctor_id = #{doctorId}
            AND t2.status = '1'
        </where>
    </select>

</mapper>