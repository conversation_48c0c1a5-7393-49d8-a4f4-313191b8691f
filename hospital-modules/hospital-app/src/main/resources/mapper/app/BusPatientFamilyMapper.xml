<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.app.mapper.BusPatientFamilyMapper">
    <select id="selectInterrogatorList" parameterType="com.puree.hospital.app.domain.BusPatientFamily"
            resultType="com.puree.hospital.app.domain.vo.BusPatientFamilyVo">
        SELECT
            t1.*,
            t2.dict_label AS patientRelationship
        FROM
            bus_patient_family t1
            LEFT JOIN sys_dict_data t2 ON t1.patient_relationship = t2.dict_code
        <where>
            t1.patient_id = #{patientId}
            <if test=" null==isAll or 0==isAll ">
                and visible=1
            </if>
        </where>
        ORDER BY t1.status DESC, t1.patient_relationship ASC
    </select>

    <select id="selectPatientFamilyById" parameterType="java.lang.Long"
            resultType="com.puree.hospital.app.domain.vo.BusPatientFamilyVo">
        SELECT
        t1.id,
        t1.`name`,
        t1.cell_phone_number,
        t1.date_of_birth,
        t1.sex,
        t1.id_number,
        t1.`status`,
        t1.patient_id,
        t1.marital_status,
        t1.work_unit,
        t1.`location`,
        t1.detail_address,
        t1.province,
        t1.city,
        t1.area,
        t1.profession,
        t1.education_level,
        t1.`source`,
        t1.doctor_id,
        t1.id_type,
        t2.dict_label AS patientRelationship,
        t3.dict_label nation,
        t4.diagnosis_archives
        FROM
        bus_patient_family t1
        LEFT JOIN bus_hospital_family t4 ON t1.id = t4.family_id
        AND t1.patient_id = t4.patient_id AND t1.hospital_id = t4.hospital_id
        LEFT JOIN sys_dict_data t2 ON t1.patient_relationship = t2.dict_code
        LEFT JOIN sys_dict_data t3 ON t1.nation=t3.dict_code
        WHERE
            t1.id = #{id}
    </select>

    <select id="selectRelation" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT
            dict_label
        FROM
            sys_dict_data
        WHERE
            dict_code = #{patientRelationship}
    </select>

    <select id="selectRelationShip" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT
            dict_code
        FROM
            sys_dict_data
        WHERE
            dict_label = #{relation}
    </select>

    <select id="queryFamilyInfo" parameterType="java.lang.Long" resultType="com.puree.hospital.app.domain.vo.BusPatientFamilyVo">
        SELECT
            t1.id,
            t1.`name`,
            t1.cell_phone_number,
            t1.date_of_birth,
            t1.sex,
            t1.id_number,
            t1.patient_relationship,
            t1.`status`,
            t1.nation,
            t1.marital_status,
            t1.work_unit,
            t1.`location`,
            t1.detail_address,
            t1.patient_id,
            t1.education_level,
            t1.create_by,
            t1.create_time,
            t1.update_by,
            t1.update_time,
            t1.outpatient_number,
            t1.province,
            t1.city,
            t1.area,
            t1.`source`,
            t1.height,
            t1.weight,
            t1.doctor_id,
            t1.hospital_id,
            t1.profession,
            t1.id_type,
            t2.hospital_name
        FROM bus_patient_family t1
        LEFT JOIN bus_hospital t2 ON t1.hospital_id = t2.id
        WHERE
            t1.id = #{familyId}
    </select>

    <select id="getOne" parameterType="com.puree.hospital.app.domain.BusPatientFamily"
            resultType="com.puree.hospital.app.domain.BusPatientFamily">
        SELECT id,visible
        FROM
            bus_patient_family
        <where>
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="idType != null">
                and id_type = #{idType}
            </if>
            <if test="idNumber != null">
                and id_number = #{idNumber}
            </if>
            <if test="patientRelationship != null">
                and patient_relationship = #{patientRelationship}
            </if>
            <if test="visible != null">
                and visible = #{visible}
            </if>
        </where>
        order by create_time desc
        limit 1
    </select>

</mapper>
