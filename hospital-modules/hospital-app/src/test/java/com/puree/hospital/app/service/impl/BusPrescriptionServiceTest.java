package com.puree.hospital.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.AbstractHospitalAppTest;
import com.puree.hospital.app.domain.BusPrescription;
import com.puree.hospital.app.domain.dto.PrescriptionPharmacistSignDTO;
import com.puree.hospital.app.domain.vo.BusPrescriptionVo;
import com.puree.hospital.app.service.IBusPrescriptionService;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 处方单元测试
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/17 11:25
 */
public class BusPrescriptionServiceTest extends AbstractHospitalAppTest {

    @Resource
    private IBusPrescriptionService busPrescriptionService;

    @Test
    public void testSelectList(){
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setHospitalId(30L);
        busPrescription.setFamilyIdcard("130104197209051530");

        List<BusPrescriptionVo> list = busPrescriptionService.selectList(busPrescription);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void testSelect() {
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setPrescriptionNumber("20241230154731373");
        busPrescription.setHospitalId(30L);
        BusPrescriptionVo busPrescriptionVo = busPrescriptionService.select(busPrescription);
        System.out.println(JSON.toJSONString(busPrescriptionVo));

    }

    @Test
    public void testPharmacistSignature() {
        PrescriptionPharmacistSignDTO pharmacistSignDTO = new PrescriptionPharmacistSignDTO();
        pharmacistSignDTO.setPrescriptionNumber("20241230154731373");
        pharmacistSignDTO.setHospitalId(30L);
        pharmacistSignDTO.setSignType(PrescriptionPharmacistSignDTO.SignTypeEnum.DELIVERY);
        pharmacistSignDTO.setPharmacistIdCard("140222197705210537");
        Boolean result = busPrescriptionService.pharmacistSignature(pharmacistSignDTO);
        Assert.assertTrue(result);
    }

    @Test
    public void testGeneratePdf() {
        LambdaQueryWrapper<BusPrescription> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusPrescription::getPrescriptionNumber, "20241230154731373");
        lambdaQuery.eq(BusPrescription::getHospitalId, 30L);
        BusPrescription busPrescription = busPrescriptionService.getOne(lambdaQuery);
        String result = busPrescriptionService.generatePdf(busPrescription);
        System.out.println(result);
    }

}
