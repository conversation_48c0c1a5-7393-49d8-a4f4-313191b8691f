package com.puree.hospital.app.mapper;

import com.puree.hospital.app.domain.BusDoctorDepartment;
import com.puree.hospital.app.domain.query.BusDoctorDepartmentQuery;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class BusDoctorDepartmentMapperTest {

    @Autowired
    private BusDoctorDepartmentMapper busDoctorDepartmentMapper;

    @Test
    void testSelectByDoctorIdOnly() {
        BusDoctorDepartmentQuery query = new BusDoctorDepartmentQuery();
        query.setHospitalIds(Collections.singletonList(1L));
        query.setDoctorIds(Collections.singletonList(1L));

        List<BusDoctorDepartment> result = busDoctorDepartmentMapper.selectDoctorDepartmentList(query);
        assertNotNull(result);
        assertFalse(result.isEmpty(), "结果不应为空");
        result.forEach(item -> assertEquals(1L, item.getDoctorId()));
    }

    @Test
    void testSelectByDoctorIdListOnly() {
        BusDoctorDepartmentQuery query = new BusDoctorDepartmentQuery();
        query.setDoctorIds(Arrays.asList(1L, 12L, 3L));

        List<BusDoctorDepartment> result = busDoctorDepartmentMapper.selectDoctorDepartmentList(query);
        assertNotNull(result);
        assertFalse(result.isEmpty(), "结果不应为空");
        result.forEach(item -> assertTrue(query.getDoctorIds().contains(item.getDoctorId())));
    }

    @Test
    void testSelectByDoctorIdAndDoctorIdList() {
        BusDoctorDepartmentQuery query = new BusDoctorDepartmentQuery();
        query.setHospitalIds(Collections.singletonList(1L));
        query.setDoctorIds(Arrays.asList(1L, 2L));

        List<BusDoctorDepartment> result = busDoctorDepartmentMapper.selectDoctorDepartmentList(query);
        assertNotNull(result);
        assertFalse(result.isEmpty(), "结果不应为空");
        result.forEach(item -> assertTrue(query.getDoctorIds().contains(item.getDoctorId())));
    }

    @Test
    void testSelectWithNoConditions() {
        BusDoctorDepartmentQuery query = new BusDoctorDepartmentQuery();

        List<BusDoctorDepartment> result = busDoctorDepartmentMapper.selectDoctorDepartmentList(query);
        assertNotNull(result);
        // 可根据实际情况决定是否允许空查询
        // assertFalse(result.isEmpty(), "结果不应为空");
    }
}
