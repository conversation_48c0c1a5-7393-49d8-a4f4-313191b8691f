<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <title>电子处方笺</title>
  <style>
    @page {
      size: A4;
      margin: 0;
    }
    body {
      font-family: "Microsoft YaHei", "微软雅黑";
      font-size: 16px;
      line-height: 1.6;
      margin: 0;
      padding: 40px 20px 20px;
    }
    .container {
      width: 100%;
      height: 100%;
    }
    /* 外层大方框 */
    .prescription-box {
      width: 700px;height: 100%;
      display: block;
      margin: auto;
      border: 1px solid black;
      background-color: white;
    }
    /* 标题样式 */
    .hospital-name {
      line-height: 41px; font-size: 28px;
    }
    .title{
      text-align: center;
      padding-top: 5px;
    }

    .prescription-type{
      font-size: 26px
    }

    /* 虚线分隔 */
    .dashed-divider {
      border-top: 1px dashed #000;
      margin: 15px 0;
      clear: both;
    }
    .barcode-area {
      margin: 15px 0 15px 56px;
      width: calc(100% - 112px);  /* 如果PDF不支持calc，可改为固定值 */
      overflow: auto;  /* 清除浮动 */
    }

    .barcode-label {
      font-weight: bold;
      float: left;  /* 使用浮动代替flex */
      /*margin-right: 10px;*/
      padding-top: 15px;  /* 垂直居中 */
    }

    .barcode-img {  /* 修正类名，与HTML保持一致 */
      width: 200px;
      height: 50px;
      object-fit: contain;
      border: 1px solid #ccc;
      float: left;  /* 使用浮动代替flex */
    }

    /* 清除浮动 */
    .clearfix::after {
      content: "";
      clear: both;
      display: table;
    }
    .info-label {
      /*margin-right: 60px; !* 进一步减少间距 *!*/
      display: inline-block;
    }
    /*新加*/
    .familybox {
      min-width: 100px;
      display: inline-block;
    }
    .body-item{
      position: relative;
      padding: 5px 56px;
      line-height: 26px;
      /*display: flex;*/
      /*flex-direction: column;*/
      border-bottom: 1px dashed #A6A6A6;
      overflow: hidden; /* 清除浮动 */
    }
    .gongzhangSty {
      position: absolute;
      left: 400px; /* 相对于当前.body-item左侧偏移 */
      top: 40px; /* 相对于当前.body-item顶部偏移 */
    }

    .nowrap {
      white-space: nowrap;
    }
    .insurance-label {
      width: 75px; /* 增加医保类型标签宽度 */
    }

    /* 药品区域 */
    .body-prescription{
      line-height: 20px;
      font-size: 16px;
      padding: 5px 56px 5px 56px;
      border-bottom: 1px dashed #A6A6A6;
    }
    .rp-title {
      font-weight: bold;
      margin: 8px 0;
    }
    #medicines .medicine-item {
      margin: 10px 0;
      /*display: flex;*/
      /*flex-wrap: wrap;*/

      /*新加*/
      overflow: hidden;
    }

    /*}*/
    /* 药材列表容器：改用 Grid 布局，每行固定 2 个 */
    #tcm-medicines .medicine-item {
      /*display: grid;*/
      /*grid-template-columns: repeat(2, 1fr); !* 每行 2 列，自动平分宽度 *!*/
      /*gap: 1px 20px;*/

      /*新加*/
      overflow: hidden;
    }

    /* 单味药材容器：名称居左、克重居右 */
    #tcm-medicines .medicine-name-container {
      /*display: flex;*/
      /*justify-content: space-between; !* 关键：名称和克重分散两端 *!*/
      /*align-items: center;*/

      /*新加*/
      float: left;
      width: 45%; /* 每行2个，减去间距的一半 */
      margin-right: 20px;
      margin-bottom: 10px;
    }

    /*新加*/
    #tcm-medicines .medicine-name-container:nth-child(even) {
      margin-right: 0; /* 偶数项不设置右边距 */
    }
    .medicine-name-container {
      white-space: nowrap; /* 防止名称换行 */
      overflow: hidden;
      /*flex: 1;*/
      /*display: flex;*/
      /*justify-content: space-between;*/
    }
    /*以下3行新加*/
    /* 单味药材容器：名称居左、克重居右 */
    #tcm-medicines .medicine-name-container {
      overflow: hidden; /* 清除浮动 */
    }
    #tcm-medicines .medicine-name-container .medicine-name:first-child {
      float: left; /* 药材名称左浮动 */
    }
    #tcm-medicines .medicine-name-container .medicine-quantity {
      float: right; /* 克重右浮动 */
    }
    #medicines .medicine-name-container .medicine-quantity{
      float: right; /* 药品总数量右浮动 */
    }

    .medicine-name {
      display: inline-block;
    }
    .medicine-quantity {
      text-align: right;
    }
    .medicine-usage {
      width: 100%;
      text-align: right;
      margin-top: 3px;
    }
    /* 服用方式容器：单独占满一行（Grid 自动识别） */
    #tcm-medicines .medicine-item > div:last-child {
      /*grid-column: 1 / -1; !* 跨所有列，独占一行 *!*/
      /*display: flex;*/
      /*flex-wrap: wrap;*/
      /*gap: 10px;*/
      /*align-items: center;*/
      /*margin-top: 10px;*/
      /*justify-content: flex-end; !* 关键：让内容靠右对齐 *!*/
      clear: both; /* 清除前面的浮动，独占一行 */
      margin-top: 10px;
      text-align: right; /* 内容靠右对齐 */
      overflow: hidden; /* 清除浮动 */
    }
    /*以下3行新加*/
    #tcm-medicines .medicine-item > div:last-child p {
      margin: 0;
    }
    #tcm-medicines .medicine-item > div:last-child span {
      margin-right: 10px; /* 元素间距 */
    }
    #tcm-medicines .medicine-item > div:last-child span:last-child {
      margin-right: 0; /* 最后一个元素不设置右边距 */
    }

    /* 医嘱区域 */
    .instructions-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .instructions-blank {
      color: #666;
      margin-top: 5px;
    }
    /* 签字区域 */
    .sign-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .sign-item {
      margin: 16px 0;
    }
    .sign-label {
      display: inline-block;
      width: 140px;
    }
    .sign-d-label{
      display: inline-block;
      width: 80px; /* 减小宽度 */
    }
    .sign-p-label{
      display: inline-block;
      width: 80px; /* 减小宽度 */
    }
    .seal-area {
      margin-top: 20px;
      font-weight: bold;
      text-align: right;
      margin-right: 50px;
    }
    /* 温馨提示 */
    .tips-title {
      font-weight: bold;
      margin-bottom: 8px;
    }
    .tips-list {
      margin: 0;
      padding-left: 25px;
    }
    .tips-list li {
      margin: 5px 0;
    }
  </style>
</head>
<body>
<div class="container">
  <!-- 处方整体大方框 -->
  <div class="prescription-box">

    <!-- 医院名称 -->
    <div class="title">
    <b class="hospital-name" th:text="${prescription.HospitalName}+'互联网医院'">XXX互联网医院</b><br>
    <!-- 处方类型 -->
    <b class="prescription-type" th:text="${prescription.isdualChannel == 1 ? '电子处方笺' : '用药指导单'}">电 子 处 方 笺</b>
    <!-- 条形码区域 -->
    <div class="barcode-area">
      <span class="barcode-label" th:text="${prescription.barcodeName}">处方编号一维码:</span>
      <img th:src="|data:image/png;base64,${prescription.barcodeImg}|"
      style="height: 80px; display: block;"
      alt="条形码" />
      <!--      <span class="barcode-img" th:text="${prescription.barcodeImg}" ></span>-->
      <span style="clear: both;"></span>
    </div>

    </div>


    <!-- 基本信息区域 -->
    <div class="body-item">
      <div class="info-row nowrap">
        <span class="info-label">处方编号:</span>
        <span th:text="${prescription.prescriptionNumber}" id="familyName"></span>
<!--        <span th:text="${prescription.prescriptionNumber}" id="prescriptionNumber"></span>-->
      </div>
      <div class="info-row">
        <span class="info-label">
          <span>姓名: </span>
          <span th:text="${prescription.familyName}" id="familyName" class="familybox"></span>
        </span>
        <span class="info-label">
          <span>性别:</span>
          <span th:text="${prescription.familySex == '0' ? '女' : '男'}" id="familySex" class="familybox"></span>
        </span>
        <span class="info-label">
          <span>年龄:</span>
          <span th:text="${prescription.familyAge}" id="familyAge" class="familybox"></span>
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">
          <span>科别:</span>
          <span th:text="${prescription.departmentName}" id="departmentName" class="familybox"></span>
        </span>
        <span class="info-label">
          <span>日期:</span>
          <span th:text="${prescription.date}" id="prescriptionDate" class="familybox"></span>
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">
          <span>医保类型:</span>
          <span th:text="${prescription.feeSettleType}" id="insuranceType" class="familybox" style="min-width: 150px;"></span>
        </span>
        <span class="info-label">
          <span>电话:</span>
          <span th:text="${prescription.familyTel}" id="phoneNumber" class="familybox"></span>
        </span>
      </div>
      <div class="info-row">
        <span class="info-label">
          <span>地址:</span>
          <span th:text="${prescription.familyDetailAddress}" id="address" class="familybox"></span>
        </span>
      </div>
    </div>


    <!-- 诊断区域 -->
    <div class="body-item">
      <div class="diagnosis-title">诊断</div>
      <div class="diagnosis-content" id="diagnosis">
        <!-- 西药诊断-->
        <!-- 使用div块级元素，每个诊断项自动换行 -->
        <div th:if="${prescription.prescriptionType == '1'}"
             th:each="diagnosis, stat : ${prescription.clinicalDiagnosis}">
          <span th:text="${stat.count + '、' + diagnosis.diseaseName}"></span>
        </div>
        <!-- 中药诊断-->
        <div th:if="${prescription.prescriptionType != '1'}"
              th:each="diagnosis, stat : ${prescription.clinicalDiagnosis}">
        <span th:text="${stat.count + '、' + (diagnosis.tcmDiagnosis ?: '') + '(' + (diagnosis.tcmSyndrome?.tcmSyndrome ?: '') + ')'}"></span>
        </div>
<!--        <span th:if="${prescription.clinicalDiagnosis == null || prescription.clinicalDiagnosis == ''}">-->
<!--            无诊断信息-->
<!--        </span>-->
      </div>
    </div>


    <!-- 药品区域 -->
    <div class="body-prescription">
      <div class="rp-title">RP</div>
      <div id="medicines" th:if="${prescription.isMm}==1">
        <div class="medicine-item" th:each="medicine : ${medications}">
          <div class="medicine-name-container">
            <span class="medicine-name" th:text="${medicine.drugsName} + '(' + ${medicine.drugsSpecification} + ')'" ></span>
            <span class="medicine-name medicine-quantity" th:text="'x ' + ${medicine.quantity}"></span>
          </div>
          <div class="medicine-usage" th:if="${prescription.isShowMedicationDays}==1">
               <span th:text="'用法: ' + ${medicine.singleDose} + ${medicine.unit} + ' ' + ${medicine.medicationFrequencyRemarks} + ' ' + ${medicine.drugsUsageValue} + ', ' + '使用' + ${medicine.medicationDays} + '天'"></span>
          </div>
          <div class="medicine-usage" th:if="${prescription.isShowMedicationDays}!=1">
            <span th:text="'用法: ' + ${medicine.unit} + ' ' + ${medicine.medicationFrequencyRemarks} + ' ' + ${medicine.drugsUsageValue}"></span>
          </div>
        </div>
      </div>
      <div id="tcm-medicines" th:if="${prescription.isMm}!=1">
        <div class="medicine-item" >
<!--          <div class="medicine-name-container" th:each="medicine : ${medications}">-->
<!--            <span class="medicine-name" th:text="${medicine.drugsName}"></span>-->
<!--            <span class="medicine-name medicine-quantity" th:text="${medicine.weight}+'克'"></span>-->
<!--          </div>-->
          <!-- 循环药材列表，通过索引分组（每2个一组） -->
          <!-- 只在偶数索引（0、2、4...）时创建分组容器 -->
          <div th:each="medicine, stat : ${medications}"
               th:if="${stat.index % 2 == 0}" style="overflow: hidden;">
          <!-- 当前药材（每组第一个） -->
            <div class="medicine-name-container">
              <span class="medicine-name" th:text="${medicine.drugsName}"></span>
              <span class="medicine-name medicine-quantity" th:text="${medicine.weight + '克'}"></span>
            </div>
            <!-- 下一个药材（若存在，每组第二个） -->
            <div class="medicine-name-container"
                 th:if="${stat.index + 1 < stat.size}">  <!-- 避免索引越界 -->
              <span class="medicine-name" th:text="${medications[stat.index + 1].drugsName}"></span>
              <span class="medicine-name medicine-quantity" th:text="${medications[stat.index + 1].weight + '克'}"></span>
            </div>
          </div>

          <div th:with="s = ${prescription.usages.split(',')}">
            <p>
              <!-- 内服/外用判断 -->
              <span th:text="${s[0] == '0' ? '内服:' : '外用'}"></span>
              共<span th:text="${s[1]}"></span>剂，
              每日<span th:text="${s[2]}"></span>剂，
              1剂分<span th:text="${s[3]}"></span>次服用。

              <!-- 使用 switch 处理多种煎煮方式 -->
              <span th:switch="${prescription.processingMethod}">
                <span th:case="0">自煎</span>
                <span th:case="1">代煎</span>
                <span th:case="2">颗粒</span>
                <span th:case="3">膏方</span>
                <span th:case="4">素丸</span>
                <span th:case="5">水丸</span>
                <span th:case="6">水蜜丸</span>
                <span th:case="7">小蜜丸</span>
                <span th:case="8">浓缩丸</span>
                <span th:case="*">未知方式</span> <!-- 默认情况 -->
              </span>
            </p>
          </div>

        </div>
      </div>



    </div>
    <!-- 医嘱区域 -->
    <div class="body-item">
      <div class="instructions-title">医嘱</div>
      <p style="text-align: center;" th:text="${prescription.remark != null and !prescription.remark.empty ? prescription.remark : '以下空白'}"></p>
<!--      <div class="instructions-title" >医嘱</div>-->
<!--      <p style="text-align: center;" th:text="${prescription.remark}">以下空白</p>-->
    </div>


    <!-- 签字盖章区域及温馨提示区域 - 根据printType决定显示内容 -->
    <div th:if="${prescription.printType == '1'}">
      <!-- 签字盖章区域 -->
      <div class="body-item">
        <div class="sign-title">签字盖章</div>
        <div class="sign-item">
          <span class="sign-d-label">处方医生:</span>
          <span id="doctor-signature-anchor"style="opacity:0;position:relative;display:inline-block;width:1px;">{{prescription_doctor}}</span>
        </div>
        <div class="sign-item">
          <span class="sign-p-label">审方药师:</span>
          <span id="pharmacist-signature-anchor"style="opacity:0;position:relative;display:inline-block;width:1px;">{{approve_pharmacist}}</span>
        </div>
        <div class="sign-item">
          <span class="sign-label">调配、复核药师:</span>
          <span id="Audit-p-signature-anchor" style="opacity:0;position:relative;display:inline-block;width:1px;">{{review_pharmacist}}</span>
        </div>
        <div class="sign-item">
          <span class="sign-label">核对、发药药师:</span>
          <span id="send-s-signature-anchor" style="opacity:0;position:relative;display:inline-block;width:1px;">{{distribute_pharmacist}}</span>
        </div>
        <div class="gongzhangSty">
          <img class="imgSty" th:src="${prescription.hospitalSeal}" alt="医院公章" style="height:120px;background-color:#f9f9f9;"/>
        </div>
<!--        <div class="seal-area">电子专用章</div>-->
      </div>

      <!-- 温馨提示区域 -->
      <div class="body-item">
        <div><b>温馨提示：</b></div>
        <div>
          <span th:text="'1、处方有限期为'+${prescription.effectiveHours}+'小时，请及时取药'">1、处方有限期为1小时,请及时取药。</span><br>
          <span th:text="'2、本处方限于'+${prescription.HospitalName}+'使用,自行下载配药不具有处方效力。'">2、本处方限于金沙街新社区社区卫生服务站使用,自行下载配药不具有处方效力。</span><br>
          <span>3、按照卫生部、国家中医药管理局卫医发[2002]24号文件规定:为保证患者用药安全,药品一经发出,不得退换。</span><br>
          <span>4、请严格按原处方和药品说明书使用,严禁超量超范围使用;如用药过程中出现病情变化或其它不适症状,请立即停药并及时就医。</span><br>
        </div>
      </div>
    </div>

    <!-- 当printType不为1时显示用药指导单说明 -->
    <div th:if="${prescription.printType != '1'}">
      <!-- 虚线分隔 -->
      <!-- 用药指导单说明区域 -->
      <div class="body-item">
        <div><b>用药指导单说明</b></div>
        <div>
          <span id="preDaySty" th:text="'1、本用药指导单限于' + ${prescription.hospitalName} + '互联网医院使用，自行下载配药不具有用药指导单效力。'">
            1、本用药指导单限于医院名称使用，自行下载配药不具有用药指导单效力。
          </span><br>
          <span id="preHosSty">
            2、按照卫生部、国家中医药管理局卫医发[2002]24号文件规定：为保证患者用药安全，药品一经发出，不得退换。
          </span><br>
          <span id="preSpan1">
            3、请严格按原处方和药品说明书使用，严禁超量超范围使用；如用药过程中出现病情变化或其它不适症状，请立即停药并及时就医。
          </span><br>
        </div>
      </div>
    </div>


  </div>
</div>

<script>
  // 移除了JavaScript数据填充逻辑，数据现在由Thymeleaf在服务器端渲染
</script>
</body>
</html>