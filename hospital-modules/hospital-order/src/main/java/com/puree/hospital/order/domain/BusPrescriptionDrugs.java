package com.puree.hospital.order.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BusPrescriptionDrugs extends Entity {
    /** 处方ID */
    private Long prescriptionId;
    /** 药品ID */
    private Long drugsId;
    /** 药品制造商 */
    private String drugsManufacturer;
    /** 标准通用名 */
    private String standardCommonName;
    /** 药品名称 */
    private String drugsName;
    /** 药品图片地址 */
    private String drugsImg;
    /** 规格 */
    private String drugsSpecification;
    /** 销售价 */
    private BigDecimal sellingPrice;
    /** 用药频率 */
    private String medicationFrequency;
    /** 用药频率 中文显示 */
    private String medicationFrequencyRemarks;
    /** 单次剂量 */
    private Integer singleDose;
    /** 单位 */
    private String unit;
    /** 给药途径 */
    private String drugsUsageValue;
    /** 数量 */
    private Integer quantity;
    /** 用药天数 */
    private Integer medicationDays;
    /** 医院ID */
    private Long hospitalId;
    /** 重量（克） */
    private String weight;
    /**
     * 次/天
     */
    private BigDecimal severalTimesDay;
    /**
     * 配送方ID
     */
    private Long enterpriseId;
    /** 医生ID */
    @TableField(exist = false)
    private Long doctorId;
    @TableField(exist = false)
    private String tcmType;
    /**
     * 药剂类型
     */
    @TableField(exist = false)
    private Long drugsDosageForm;
    /**
     * 配送企业标识
     */
    @TableField(exist = false)
    private String identifying;
}
