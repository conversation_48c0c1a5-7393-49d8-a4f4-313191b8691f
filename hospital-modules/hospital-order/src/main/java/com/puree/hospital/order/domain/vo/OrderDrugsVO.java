package com.puree.hospital.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/2/17 18:40
 */
@Data
public class OrderDrugsVO {
    private Long id;
    private Long prescriptionId;
    private Long drugsId;
    private String drugsManufacturer;
    private String standardCommonName;
    private String drugsName;
    private String drugsImg;
    private String drugsSpecification;
    private BigDecimal sellingPrice;
    private String medicationFrequency;
    private Integer singleDose;
    private String unit;
    private String drugsUsageValue;
    private Integer quantity;
    private Integer medicationDays;
    private Long hospitalId;
    private String weight;
    private BigDecimal severalTimesDay;
    private String medicationFrequencyRemarks;
    private Long enterpriseId;
    private BigDecimal referencePurchasePrice;
}
