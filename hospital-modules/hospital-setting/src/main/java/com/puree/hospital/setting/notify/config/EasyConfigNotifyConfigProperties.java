package com.puree.hospital.setting.notify.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <p>
 * 业务配置模板/配置项变更通知配置
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/9 18:19
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "easy-config.notify")
@Data
public class EasyConfigNotifyConfigProperties {
    /**
     * 平台历史记录页面url
     */
    private String platformHistoryPageUrl;

    /**
     * 医院历史记录页面url
     */
    private String hospitalHistoryPageUrl;


    private Map<String, WebhookNotifyConfig> webhookConfig = new LinkedHashMap<>();

}
