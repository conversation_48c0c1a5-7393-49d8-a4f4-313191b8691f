package com.puree.hospital.setting.service.platform.impl;


import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.puree.hospital.setting.domain.dto.platform.MenuDevelopSaveDto;
import com.puree.hospital.setting.domain.dto.platform.MenuDevelopUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.mapper.platform.PlatformItemsMapper;
import com.puree.hospital.setting.mapper.platform.PlatformMenuMapper;
import com.puree.hospital.setting.service.platform.PlatformMenuService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @ClassName EasyConfigMenuServiceImpl
 * <AUTHOR>
 * @Description 菜单 serviceImpl
 * @Date 2023/12/6 11:17
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlatformMenuServiceImpl implements PlatformMenuService {

    private final PlatformMenuMapper platformMenuMapper;
    private final PlatformItemsMapper platformItemsMapper;

    /**
     * @Param
     * @Return java.util.List<cn.hutool.core.lang.tree.Tree < java.lang.Long>>
     * @Description 菜单列表树
     * <AUTHOR>
     * @Date 2023/12/8 15:19
     **/
    @Override
    public List<Tree<Long>> menuListTree() {
        List<TreeNode<Long>> tree = getTreeNodes();

        //构建树
        return TreeUtil.build(tree, -1L);
    }

    /**
     * @Param parentId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigMenu>
     * @Description 获取 父 ID 下的全部节点
     * <AUTHOR>
     * @Date 2024/1/11 14:16
     **/
    @Override
    public List<Menu> getMenuSortByParentId(Long parentId) {
        return platformMenuMapper.queryMenuSortByParentId(parentId);
    }

    /**
     * @Param
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigMenu>
     * @Description 获取 list
     * <AUTHOR>
     * @Date 2024/1/26 16:45
     **/
    @Override
    public List<Menu> getList() {
        return platformMenuMapper.list();
    }

    /**
     * @Param
     * @Return cn.hutool.core.lang.tree.Tree<java.lang.Long>
     * @Description 单 root 树
     * <AUTHOR>
     * @Date 2024/1/26 16:46
     **/
    @Override
    public Tree<Long> menuListTreeSingle() {
        List<TreeNode<Long>> nodes = getTreeNodes();
        return TreeUtil.buildSingle(nodes, -1L);
    }

    private List<TreeNode<Long>> getTreeNodes() {
        List<Menu> list = getList();
        // 防止扩容导致性能下降
        List<TreeNode<Long>> tree = new ArrayList<>(list.size() * 2);

        // 将所有节点存入 Map，以便快速查找
        Map<Long, TreeNode<Long>> nodeMap = new HashMap<>();
        for (Menu item : list) {
            TreeNode<Long> node = new TreeNode<>();
            node.setId(item.getId());
            node.setParentId(item.getParentId());
            node.setName(item.getMenuName());
            Map<String, Object> extra = new HashMap<>();
            extra.put("key", item.getMenuKey());
            extra.put("enableHttp", item.getEnableHttp());
            node.setExtra(extra);
            node.setWeight(item.getSort());

            nodeMap.put(item.getId(), node);
            tree.add(node);
        }
        return tree;
    }


    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 新增配置项菜单
     * <AUTHOR>
     * @Date 2023/12/8 15:33
     **/
    @Override
    public Boolean developInsert(MenuDevelopSaveDto dto) {
        String menuKey = dto.getMenuKey();
        String menuName = dto.getMenuName();

        Long count = platformMenuMapper.count(menuKey, menuName);
        if (count > 0) {
            log.warn("配置项菜单 key={},或 配置项菜单 name={} 已存在", menuKey, menuName);
            throw new IllegalArgumentException("配置项菜单 key={},或 配置项菜单 name={} 已存在");
        }
        Menu platformMenu = new Menu();
        BeanUtils.copyProperties(dto, platformMenu);
        platformMenu.setCreateTime(new Date());
        //初始版本号为
        platformMenu.setRevision(0);

        Long parentId = dto.getParentId();
        parentId = parentId == null ? -1L : parentId;
        //如果父节点为空，则为一级节点 -1
        platformMenu.setParentId(parentId);

        //追加菜单尾部
        Integer sort = queryMenuMaxSortIncrementerById(parentId);
        platformMenu.setSort(sort);

        return (platformMenuMapper.insert(platformMenu)) >= 1;
    }

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序,如果该菜单下没有节点，则返回 0
     * <AUTHOR>
     * @Date 2023/12/8 15:41
     **/
    @Override
    public Integer queryMenuMaxSortById(Long menuId) {
        //通过 ID  查询菜单最大排序
        Integer sort = platformMenuMapper.queryMenuMaxSortById(menuId);
        if (sort == null) {
            sort = 0;
        }
        return sort;
    }

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序 +1,用于直接取得插入的位置
     * <AUTHOR>
     * @Date 2023/12/8 15:51
     **/
    @Override
    public Integer queryMenuMaxSortIncrementerById(Long menuId) {
        return queryMenuMaxSortById(menuId) + 1;
    }

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 开发修改配置项菜单
     * <AUTHOR>
     * @Date 2023/12/8 16:29
     **/
    @Override
    public Boolean developUpdate(MenuDevelopUpdateDto dto) {
        String menuName = dto.getMenuName();

        List<Menu> platformMenus = platformMenuMapper.queryByMenuNameAndKey(null, menuName);
        if (!platformMenus.isEmpty() && (platformMenus.size() > 1 || !platformMenus.get(0).getId().equals(dto.getId()))) {
            log.warn(" 配置项菜单 name={} 已存在", menuName);
            throw new IllegalArgumentException("配置项菜单 name={} 已存在");
        }
        Menu platformMenu = new Menu();
        BeanUtils.copyProperties(dto, platformMenu);
        //这里由于只有开发能改，所以暂不考虑并发问题
        return update(platformMenu);
    }

    /**
     * @Param item
     * @Return java.lang.Boolean
     * @Description 修改菜单项
     * <AUTHOR>
     * @Date 2023/12/8 16:35
     **/
    @Override
    public Boolean update(Menu item) {
        if (Objects.isNull(item) || item.getId() == null) {
            throw new IllegalArgumentException("参数错误");
        }
        item.setUpdateTime(new Date());
        //乐观锁 版本+1 暂时不需要
        //item.setRevision(item.getRevision() + 1);
        return platformMenuMapper.update(item) >= 1;
    }

    /**
     * @Param id
     * @Param menuName
     * @Return java.lang.Boolean
     * @Description 开发删除配置项菜单
     * <AUTHOR>
     * @Date 2023/12/8 16:20
     **/
    @Override
    public Boolean developDelete(Long id, String menuName) {
        if (StringUtils.isEmpty(menuName)) {
            throw new IllegalArgumentException("配置项名称不正确");
        }
        if (platformMenuMapper.childNodeCount(id) > 0) {
            log.warn("有子级菜单，不允许删除 id={}，menuName={}", id, menuName);
            throw new IllegalArgumentException("配置项菜单 还有子级菜单，不允许删除");
        }

        if (platformItemsMapper.childItemsCount(id) > 0) {
            log.warn("该菜单还有配置项，不允许删除 id={}，menuName={}", id, menuName);
            throw new IllegalArgumentException("该菜单还有配置项，不允许删除");
        }

        if (Boolean.FALSE.equals(platformMenuMapper.deleteByIdAndItemsName(id, menuName) >= 1)) {
            log.warn("配置项不存在、配置项名称不正确，或配置项已被删除:id={},itemsName={}", id, menuName);
            throw new IllegalArgumentException("配置项不存在、配置项名称不正确，或配置项已被删除");
        }
        return true;
    }

    /**
     * @Param id
     * @Param sort
     * @Param parentId
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改父级节点或排序
     * <AUTHOR>
     * @Date 2023/12/8 16:06
     **/
    @Override
    public Boolean developUpdateSortOrParentId(Long id, Integer sort, Long parentId) {
        //同时为空或者同时不为空都为参数错误，因为排序和父级节点只能修改一个，不允许同时修改
        if ((sort == null && parentId == null) || (sort != null && parentId != null)) {
            throw new IllegalArgumentException("参数错误");
        }
        if (sort != null) {
            //修改排序
            return developUpdateSort(id, sort);
        }
        //修改父级菜单
        return developUpdateMenuId(id, parentId);
    }

    /**
     * @Param id
     * @Param parentId
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改父节点，直接尾部追加
     * <AUTHOR>
     * @Date 2023/12/8 16:17
     **/
    @Override
    public Boolean developUpdateMenuId(Long id, Long parentId) {
        //拿菜单 ID
        Menu queryById = getById(id);

        if (queryById.getParentId().equals(parentId)) {
            log.warn("源菜单与目标菜单相同，禁止修改");
            throw new IllegalArgumentException("源菜单与目标菜单相同，禁止修改");
        }
        //通过 ID 查询菜单最大排序 +1
        Integer sort = queryMenuMaxSortIncrementerById(parentId);
        return platformMenuMapper.updateMenuId(id, parentId, sort) >= 1;

    }

    /**
     * @Param id
     * @Param sort
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改排序
     * <AUTHOR>
     * @Date 2023/12/8 16:16
     **/
    @Override
    public Boolean developUpdateSort(Long id, Integer sort) {
        //拿父级 ID
        Menu queryById = getById(id);

        Integer sourceSort = queryById.getSort();
        if (sourceSort == sort) {
            return true;
        } else if (sort > sourceSort) {
            //从后向前移动
            platformMenuMapper.updateSortForward(queryById.getParentId(), sort);
        } else {
            //从前向后移动
            platformMenuMapper.updateSortMoveBack(queryById.getParentId(), sort);
        }
        //修改排序

        //不需要锁，低概率事件
        if (Boolean.FALSE.equals(platformMenuMapper.updateSort(id, sort) >=1)) {
            log.warn("修改失败，请稍后重试");
            throw new RuntimeException("修改失败，请稍后重试");
        }
        //整理排序
        return platformMenuMapper.resetSort(queryById.getParentId()) >= 1;

    }

    /**
     * @Param id
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 ID 查询
     * <AUTHOR>
     * @Date 2023/12/8 16:10
     **/
    @Override
    public Menu getById(Long id) {
        //根据 ID 查询
        Menu queryById = platformMenuMapper.queryById(id);

        if (Objects.isNull(queryById)) {
            log.warn("配置项菜单不存在，或配置项菜单已被删除:id={}", id);
            throw new IllegalArgumentException("配置项菜单不存在，或配置项菜单已被删除");
        }

        return queryById;
    }

    @Override
    public Menu queryMenu(String menuKey) {
        return platformMenuMapper.queryMenu(menuKey);
    }
}
