package com.puree.hospital.setting.controller.hospital;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.entity.hospital.HospitalItem;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.service.hospital.HospitalItemsService;
import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 内部远程方法调用
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/21 14:54
 */
@RestController
@AllArgsConstructor
@RequestMapping("/feign/easy-config/hospital")
@Slf4j
public class FeignController {

    private final HospitalTemplateService hospitalTemplateService;
    private final HospitalItemsService hospitalItemsService;

    /**
     * 根据配置项获取配置值
     *
     * @param key         配置项key
     * @param hospitalId  响应
     */
    @SneakyThrows
    @GetMapping("/getSettingValue")
    public R<String> getSettingValue(@RequestParam(value = "key") String key,
                                     @RequestParam(value = "hospitalId") Long hospitalId) {
        if (!StringUtils.hasText(key)) {
          return R.fail("key is empty");
        }
        HospitalTemplate hospitalTemplate = hospitalTemplateService.getTemplate(key);
        if (hospitalTemplate == null) {
            return R.fail(String.format("key：%s is not exist", key));
        }
        HospitalItem hospitalItem = hospitalItemsService.getHospitalItemByTemplateId(hospitalTemplate.getId(), hospitalId);
        String value = hospitalTemplate.getDefaultValue();
        if (hospitalItem != null) {
            value = hospitalItem.getItemValue();
        }
        //缓存
        hospitalTemplateService.cacheItemToRedis(hospitalTemplate,value);
        return R.ok(value);
    }

    /**
     * 根据配置项 key 批量查询
     *
     * @param key         配置项key
     */
    @SneakyThrows
    @GetMapping("/getSettingValueByKey")
    public R<List<String>> getSettingValueByKey(@RequestParam(value = "key") String key) {
        if (!StringUtils.hasText(key)) {
          return R.fail("key is empty");
        }
        HospitalTemplate hospitalTemplate = hospitalTemplateService.getTemplate(key);
        if (hospitalTemplate == null) {
            return R.fail(String.format("key：%s is not exist", key));
        }
        List<HospitalItem> hospitalItems = hospitalItemsService.getHospitalItemByTemplateId(hospitalTemplate.getId());
        List<String> itemList = new ArrayList<>();
        for (HospitalItem hospitalItem : hospitalItems) {
           String value = hospitalTemplate.getDefaultValue();
            if (hospitalItem != null) {
                value = hospitalItem.getItemValue();
            }
            itemList.add(value);
        }
        return R.ok(itemList);
    }
}
