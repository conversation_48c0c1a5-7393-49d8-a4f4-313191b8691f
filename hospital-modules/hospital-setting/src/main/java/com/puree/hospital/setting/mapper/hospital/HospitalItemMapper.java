package com.puree.hospital.setting.mapper.hospital;

import com.puree.hospital.setting.domain.entity.hospital.HospitalItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfg_hospital_item(医院参数值表)】的数据库操作Mapper
* @createDate 2024-10-22 15:42:25
* @Entity generator.domain.CfgHospitalItem
*/
@Mapper
public interface HospitalItemMapper {

    int deleteByPrimaryKey(Long id);

    int insert(HospitalItem record);

    int insertSelective(HospitalItem record);

    HospitalItem selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HospitalItem record);

    int updateByPrimaryKey(HospitalItem record);

    /**
     * 按模板 ID 和医院 ID 搜索
     *
     * @param templateId 模板 ID
     * @param hospitalId 医院 ID
     * @return {@link HospitalItem }
     */
    List<HospitalItem> selectByTemplateIdAndHospitalId(@Param("templateId") Long templateId, @Param("hospitalId") Long hospitalId);
    /**
     * @param templateIds
     * @return List<HospitalItem>
     * @description 通过id列表查询
     * <AUTHOR>
     * @date 2024/12/12 12:16
     **/
    List<HospitalItem> selectByTemplateIds(List<Long> templateIds);
    /**
     * @param templateId 模板 ID
     * @return List<HospitalItem>
     * @description 按模板 ID 搜索
     * <AUTHOR>
     * @date 2024/11/27 12:07
     **/
    List<HospitalItem> selectByTemplateId(@Param("templateId") Long templateId);

    /**
     * 删除模板下的所有配置项
     * @param templateId 模板 ID
     */
    void deleteByTemplateId(Long templateId);
}
