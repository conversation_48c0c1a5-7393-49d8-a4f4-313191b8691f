package com.puree.hospital.setting.notify.resolver;

import com.puree.hospital.setting.model.event.SettingChangeEvent;
import org.springframework.stereotype.Component;

/**
 * 默认分类名称解析器
 *
 * <AUTHOR>
 * @date 2025/8/23
 */
@Component
public class DefaultClassifyNameResolver implements ClassifyNameResolver {

    /**
     * 返回事件的默认分类名称
     *
     * @param event 配置变更事件
     * @return 分类的默认值
     */
    @Override
    public String resolveClassifyName(SettingChangeEvent event) {
        return event.getClassify().getValue();
    }
}