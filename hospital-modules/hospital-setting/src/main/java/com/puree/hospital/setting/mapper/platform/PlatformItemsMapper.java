package com.puree.hospital.setting.mapper.platform;


import com.puree.hospital.setting.domain.dto.platform.ItemsDevelopUpdateDto;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName EasyConfigItemsMapper
 * <AUTHOR>
 * @Description 配置项元数据 mapper
 * @Date 2023/12/6 11:46
 * @Version 1.0
 */
@Mapper
public interface PlatformItemsMapper {

    /**
     * @Param id
     * @Param revision 乐观锁
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 ID 和 乐观锁 查询
     * <AUTHOR>
     * @Date 2023/12/7 15:27
     **/
    PlatformItems queryItem(@Param("item") PlatformItems item);


    /**
     * @Param id
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 ID 查询
     * <AUTHOR>
     * @Date 2023/12/8 11:13
     **/
    PlatformItems queryById(@Param("id") Long id);

    /**
     * @Param key
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 key 获取数据
     * <AUTHOR>
     * @Date 2024/1/10 17:27
     **/
    PlatformItems getByKey(@Param("key") String key);


    /**
     * @Param blurSearch
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 模糊查询
     * <AUTHOR>
     * @Date 2023/12/7 15:27
     **/
    List<PlatformItems> blurSearch(@Param("blurSearch") String blurSearch);

    /**
     * @Param key
     * @Param name
     * @Return java.lang.Long
     * @Description 统计数据
     * <AUTHOR>
     * @Date 2023/12/7 15:27
     **/
    Long count(@Param("key") String key, @Param("name") String name,@Param("id")Long id);

    /**
     * @Param menuId
     * @Return java.lang.Long
     * @Description 统计菜单下的配置项数量
     * <AUTHOR>
     * @Date 2023/12/18 16:25
     **/
    Long childItemsCount(@Param("menuId") Long menuId);

    /**
     * @Param menuId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 获取菜单下的配置项数
     * <AUTHOR>
     * @Date 2024/3/22 17:26
     **/
    List<PlatformItems> getItemsByMenuId(@Param("menuId") Long menuId);

    /**
     *
     *
     * @param easyConfigItems
     * @return
     */
    /**
     * @Param easyConfigItems 实例对象
     * @Return java.lang.Integer 影响行数
     * @Description 新增数据
     * <AUTHOR>
     * @Date 2023/12/8 16:14
     **/
    Integer insert(PlatformItems platformItems);

    /**
     * @Param easyConfigItems
     * @Param sourceRevision 原始版本号
     * @Return java.lang.Integer
     * @Description 修改数据
     * <AUTHOR>
     * @Date 2023/12/7 15:26
     **/
    Integer updateTemplate(@Param("t") PlatformItems platformItems, @Param("sourceRevision") Integer sourceRevision);


    Integer updateValue(@Param("t") PlatformItems platformItems, @Param("sourceRevision") Integer sourceRevision);

    /**
     * @Param id
     * @Param itemsName 需要 ID 和 名称同时匹配才能删除
     * @Return java.lang.Integer
     * @Description 通过 ID 和 名称 删除
     * <AUTHOR>
     * @Date 2023/12/7 18:15
     **/
    Integer deleteByIdAndItemsName(@Param("id") Long id, @Param("itemsName") String itemsName);

    /**
     * @Param menuId 菜单 ID
     * @Param sort
     * @Param revision
     * @Return java.lang.Integer
     * @Description
     * <AUTHOR>
     * @Date 2023/12/7 19:05
     **/
    Integer updateSortMoveBack(@Param("menuId") Long menuId, @Param("sort") Integer sort);

    /**
     * @Param parentId
     * @Return java.lang.Integer
     * @Description 重置该父节点下的排序
     * <AUTHOR>
     * @Date 2024/1/11 12:18
     **/
    Integer resetSort(@Param("menuId") Long menuId);
    /**
     * @Param menuId
     * @Param sort
     * @Return java.lang.Integer
     * @Description 根据 sort 将值前移
     * <AUTHOR>
     * @Date 2024/1/11 16:30
     **/
    Integer updateSortForward(@Param("menuId") Long menuId, @Param("sort") Integer sort);

    /**
     * @Param id
     * @Param sort
     * @Return java.lang.Integer
     * @Description 根据 主键 修改 sort
     * <AUTHOR>
     * @Date 2023/12/8 11:17
     **/
    Integer updateSort(@Param("id") Long id, @Param("sort") Integer sort);

    /**
     * @Param id
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 根据 主键 修改 menuID
     * <AUTHOR>
     * @Date 2023/12/8 12:12
     **/
    Integer updateMenuId(@Param("id")Long id,  @Param("menuId")Long menuId, @Param("sort")Integer sort);

    /**
     * @Param menuId 菜单 ID
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序
     * <AUTHOR>
     * @Date 2023/12/8 12:17
     **/
    Integer queryMenuMaxSortById( @Param("menuId")Long menuId);
    /**
     * @Param menuKey
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 获取菜单下的配置项数
     * <AUTHOR>
     * @Date 2024/3/27 10:45
     **/
    List<PlatformItems> getItems(TemplateQuery query);

    /**
     * 按菜单 ID 获取配置项列表
     *
     * @param menuId 菜单id
     * @return {@link List }<{@link PlatformItems }>
     */
    List<PlatformItems> selectItemList(Long menuId);

    /**
     * 物理删除
     *
     * @param id        主键
     * @param itemsName
     * @return 删除行数
     */
    int physicalDeleteByIdAndItemsName(@Param("id") Long id, @Param("itemsName") String itemsName);



    /**
     * 修改模版 默认值
     * @param dto dto
     * @return int
     */
    int updateTemplateDefaultValue(PlatformItems items);
}
