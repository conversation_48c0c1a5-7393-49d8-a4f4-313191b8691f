package com.puree.hospital.setting.interceptor;

import com.puree.hospital.common.core.utils.SecurityUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自动填充 create/update 字段的 MyBatis 拦截器（优化版，处理多参数重复键）
 */
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class AutoFillInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(AutoFillInterceptor.class);

    // 字段缓存，减少反射开销
    private static final Map<Class<?>, Map<String, Field>> FIELD_CACHE = new ConcurrentHashMap<>();

    // 配置化的字段名称
    private final Set<String> insertFields = new HashSet<>(Arrays.asList("createBy", "createTime", "updateBy", "updateTime"));
    private final Set<String> updateFields = new HashSet<>(Arrays.asList("updateBy", "updateTime"));

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];

        String sqlCommandType = ms.getSqlCommandType().name();
        if (parameter != null && (sqlCommandType.equals("INSERT") || sqlCommandType.equals("UPDATE"))) {
            fillFields(parameter, sqlCommandType);
        }

        return invocation.proceed();
    }

    private void fillFields(Object parameter, String type) {
        String currentUser = getCurrentUsername();
        Date now = new Date();

        // 跟踪已处理的实体对象，避免重复填充
        Set<Object> processedEntities = Collections.newSetFromMap(new IdentityHashMap<>());

        if (parameter instanceof Map) {
            // 多参数场景：遍历 Map 的值
            Map<String, Object> paramMap = (Map<String, Object>) parameter;
            for (Object value : paramMap.values()) {
                if (value != null && isEntityClass(value.getClass()) && !processedEntities.contains(value)) {
                    fillEntityFields(value, type, currentUser, now);
                    processedEntities.add(value);
                }
            }
        } else if (isEntityClass(parameter.getClass()) && !processedEntities.contains(parameter)) {
            // 单一参数场景
            fillEntityFields(parameter, type, currentUser, now);
            processedEntities.add(parameter);
        }
    }

    private void fillEntityFields(Object target, String type, String currentUser, Date now) {
        Set<String> fields = type.equals("INSERT") ? insertFields : updateFields;
        for (String fieldName : fields) {
            try {
                Object value = fieldName.contains("Time") ? now : currentUser;
                setField(target, fieldName, value);
            } catch (Exception e) {
                if (log.isDebugEnabled()) {
                    log.debug("自动填充字段 {} 失败，实体类：{}", fieldName, target.getClass().getName(), e);
                }
            }
        }
    }

    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = getField(target.getClass(), fieldName);
        if (field != null && value != null && field.getType().isAssignableFrom(value.getClass())) {
            field.setAccessible(true);
            field.set(target, value);
        }
    }

    private Field getField(Class<?> clazz, String fieldName) {
        Map<String, Field> fieldMap = FIELD_CACHE.computeIfAbsent(clazz, k -> new ConcurrentHashMap<>());
        return fieldMap.computeIfAbsent(fieldName, k -> {
            Class<?> current = clazz;
            while (current != null && !current.equals(Object.class)) {
                try {
                    return current.getDeclaredField(fieldName);
                } catch (NoSuchFieldException e) {
                    current = current.getSuperclass();
                }
            }
            return null;
        });
    }

    private boolean isEntityClass(Class<?> clazz) {
        return !clazz.isPrimitive() && !clazz.isArray() &&
                !Map.class.isAssignableFrom(clazz) &&
                !Iterable.class.isAssignableFrom(clazz) &&
                !String.class.isAssignableFrom(clazz) &&
                !Number.class.isAssignableFrom(clazz) &&
                !Date.class.isAssignableFrom(clazz);
    }

    private String getCurrentUsername() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("获取当前用户名失败", e);
            }
            return null; // 防止空指针异常
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        String insert = properties.getProperty("insertFields", "createBy,createTime,updateBy,updateTime");
        String update = properties.getProperty("updateFields", "updateBy,updateTime");
        insertFields.clear();
        updateFields.clear();
        insertFields.addAll(Arrays.asList(insert.split(",")));
        updateFields.addAll(Arrays.asList(update.split(",")));
    }
}