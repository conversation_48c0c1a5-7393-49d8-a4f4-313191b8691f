package com.puree.hospital.setting.notify.resolver;

import com.puree.hospital.setting.model.event.SettingChangeEvent;

/**
 * 分类名称解析器接口，用于根据事件获取分类名称
 *
 * <AUTHOR>
 * @date 2025/8/23
 */
public interface ClassifyNameResolver {
    /**
     * 根据配置变更事件获取分类名称
     *
     * @param event 配置变更事件，包含分类信息和关联ID（如 hospitalId）
     * @return 分类名称，若无法解析则返回默认值
     */
    String resolveClassifyName(SettingChangeEvent event);
}