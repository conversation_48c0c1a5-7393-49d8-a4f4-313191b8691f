package com.puree.hospital.setting.enums;


import java.util.HashMap;
import java.util.Map;

/**
 * 结果异常状态枚举
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
public enum ResultExceptionStateEnum {


    METHOD_ARGUMENT_NOTVALID_EXCEPTION(2001, "字段：{}，{}", "参数绑定异常"),
    ILLEGAL_ARGUMENT_EXCEPTION(2003, "非法参数异常：{}", "非法参数异常"),
    MISSING_SERVLET_REQUEST_PARAMETER_EXCEPTION(2002, "请求参数缺失异常，{}", "缺少 Servlet 请求参数异常");

    /**
     * 初始化容量
     */
    private static final Integer InitialCapacity = 32;

    private static Map<Integer, ResultExceptionStateEnum> resultExceptionStateEnumHashMap = new HashMap<>(InitialCapacity);

    /**
     * 启动时缓存方便取 枚举
     */
    static {
        for (ResultExceptionStateEnum value : values()) {
            resultExceptionStateEnumHashMap.put(value.getCode(), value);
        }
    }

    private Integer code;
    private String msg;

    private String remark;


    ResultExceptionStateEnum(int code, String msg, String remark) {
        this.msg = msg;
        this.code = code;
        this.remark = remark;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public String getRemark() {
        return remark;
    }

    /**
     * @Param
     * @Return java.lang.String
     * @Description code 转 string
     * <AUTHOR>
     * @Date 2023/12/6 18:15
     **/
    public String getCodeStr() {
        return code + "";
    }

    /**
     * @Param codeStr
     * @Return com.puree.easycfg.common.state.ResultExceptionStateEnum
     * @Description 通过 code 获取 msg
     * <AUTHOR>
     * @Date 2023/12/6 18:15
     **/
    public static ResultExceptionStateEnum getEnumByCode(String codeStr) {
        int i = Integer.parseInt(codeStr);
        return resultExceptionStateEnumHashMap.get(i);
    }
}