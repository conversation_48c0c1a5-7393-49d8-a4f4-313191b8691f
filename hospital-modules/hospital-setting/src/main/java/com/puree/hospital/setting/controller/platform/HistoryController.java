package com.puree.hospital.setting.controller.platform;

import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import com.puree.hospital.setting.service.platform.PlatformHistoryService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName EasyConfigHistoryController
 * <AUTHOR>
 * @Description 历史数据
 * @Date 2023/12/6 12:11
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/easy/config/easy-config-history")
public class HistoryController {

    private final PlatformHistoryService platformHistoryService;

    /**
     * @Param itemsId
     * @Return com.puree.easycfg.domain.R
     * @Description 通过配置项 ID 获取历史数据
     * <AUTHOR>
     * @Date 2023/12/8 16:42
     **/
    @GetMapping("/get-by-id")
    public R<List<PlatformHistory>> getById(@RequestParam(value = "itemsId", required = true) Long itemsId) {
        return R.ok(platformHistoryService.queryByItemId(itemsId));
    }

    /**
     * @Param blurSearch
     * @Param pageSize 每页几条,不传默认十条
     * @Param pageNum 第几页，不传默认第一页
     * @Return com.puree.easycfg.domain.R
     * @Description 获取全部数据
     * <AUTHOR>
     * @Date 2024/1/16 17:46
     **/
    @GetMapping("/blur-search")
    public Paging<List<PlatformHistory>> blurSearch(@RequestParam(required = false, name = "blurSearch") String blurSearch,
                           @RequestParam(required = false, name = "pageSize") Integer pageSize,
                           @RequestParam(required = false, name = "pageNum") Integer pageNum,
                           @RequestParam(required = false, name = "type") Integer type) {
        return platformHistoryService.blurSearch(blurSearch,pageSize,pageNum,type);
    }
    /**
     * @Param
     * @Return com.puree.easycfg.domain.R
     * @Description 获取全部数据
     * <AUTHOR>
     * @Date 2024/1/16 17:43
     **/
    @GetMapping("/list")
    public R<List<PlatformHistory>> list() {
        return R.ok(platformHistoryService.list());
    }
}
