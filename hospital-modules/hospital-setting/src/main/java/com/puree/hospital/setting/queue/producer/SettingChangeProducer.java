package com.puree.hospital.setting.queue.producer;

import com.puree.hospital.common.redis.mq.RedisStreamProducer;
import com.puree.hospital.common.redis.mq.annotation.RedisProducer;
import com.puree.hospital.setting.constants.SettingChangeConstants;
import com.puree.hospital.setting.model.event.SettingChangeEvent;

/**
 * <p>
 * 模版变更
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/4 15:04
 */
@RedisProducer(topic = SettingChangeConstants.TOPIC)
public class SettingChangeProducer extends RedisStreamProducer<SettingChangeEvent> {

}
