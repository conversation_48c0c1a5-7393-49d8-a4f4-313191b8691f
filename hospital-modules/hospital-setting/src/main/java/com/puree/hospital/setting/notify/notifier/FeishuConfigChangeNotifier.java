package com.puree.hospital.setting.notify.notifier;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.utils.http.HttpUtil;
import com.puree.hospital.setting.notify.domain.FeishuCard;
import com.puree.hospital.setting.notify.domain.FeishuCardTemplate;
import com.puree.hospital.setting.notify.domain.FeishuCardVariable;
import com.puree.hospital.setting.notify.config.WebhookNotifyConfig;
import com.puree.hospital.setting.model.event.SettingChangeEvent;
import com.puree.hospital.setting.notify.domain.dto.FeishuRequestDTO;
import com.puree.hospital.setting.utils.JsonUtil;
import groovyjarjarantlr4.v4.runtime.misc.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.puree.hospital.setting.notify.notifier.AbstractConfigChangeNotifier.PROCESS_NAME_SUFFIX;
import static com.puree.hospital.setting.notify.notifier.FeishuConfigChangeNotifier.PROCESS_NAME;

/**
 * <p>
 * 飞书通知
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/22 11:24
 */
@Component(PROCESS_NAME + PROCESS_NAME_SUFFIX)
@Slf4j
public class FeishuConfigChangeNotifier extends AbstractConfigChangeNotifier{

    public static final String PROCESS_NAME = "feishu";

    /**
     * 飞书消息类型
     */
    public static final String FEISHU_MESSAGE_TYPE = "interactive";


    private WebhookNotifyConfig webhookNotifyConfig;

    /**
     * 发送卡片消息至飞书
     * 请求参数示例：
     * {
     * "msg_type": "interactive",
     * "card": {
     * "type": "template",
     * "data": {
     * "template_id": "YOUR_TEMPLATE_ID",
     * "template_version_name": "YOUR_VERSION",
     * "template_variable":{
     * // 卡片变量
     * }
     * }
     * }
     * }
     * 将 YOUR_TEMPLATE_ID 替换为卡片模板的 ID，YOUR_VERSION 替换为卡片模板的版本号，YOUR_WEBHOOK_URL 替换为自定义机器人的 Webhook 地址。
     * 模版ID 和 版本号可以在飞书开发者后台获取。
     * webhook 地址可以在飞书机器人中获取。
     * 详见 <a href="https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/quick-start/send-message-cards-with-custom-bot">...</a>
     *
     * @param event 配置变更事件
     */
    @Override
    public void notify(SettingChangeEvent event) {
        webhookNotifyConfig = getWebhookNotifyConfig(PROCESS_NAME);
        //发送飞书通知
        FeishuRequestDTO requestDto = new FeishuRequestDTO();
        requestDto.setTimestamp(System.currentTimeMillis() / 1000);
        requestDto.setSign(webhookNotifyConfig.getSecret());
        requestDto.setMsgType(FEISHU_MESSAGE_TYPE);
        FeishuCard feishuCard = getFeishuCard(event, getClassify(event), getRedirectionUrl(event));
        requestDto.setCard(feishuCard);
//        log.info("飞书通知请求参数: {}", JsonUtil.toPrettyJson(requestDto));
        JSONObject jsonObject = HttpUtil.doPost(webhookNotifyConfig.getWebhookUrl(), JsonUtil.toJson(requestDto));
        log.info("飞书通知返回结果: {}", jsonObject.toString());
    }

    private @NotNull FeishuCard getFeishuCard(SettingChangeEvent event, String classify, String url) {
        //构建飞书发送请求 参数
        FeishuCard feishuCard = new FeishuCard();

        FeishuCardTemplate feishuCardTemplate = new FeishuCardTemplate();
        feishuCardTemplate.setTemplateId(webhookNotifyConfig.getTemplateId());
        feishuCardTemplate.setTemplateVersionName(webhookNotifyConfig.getTemplateVersionName());

        FeishuCardVariable feishuCardVariable = getFeishuCardVariable(event, classify, url);

        feishuCardTemplate.setTemplateVariable(feishuCardVariable);
        feishuCard.setData(feishuCardTemplate);

        return feishuCard;
    }

    /**
     * 构建飞书卡片变量
     *
     * @param body 设置变更事件，包含类型、操作者、请求体等信息
     * @param classify 分类标识，用于标识卡片的分类
     * @param url 历史记录URL模板，需格式化为包含key和hospitalId的URL
     * @return 构建完成的飞书卡片变量对象
     */
    private @NotNull FeishuCardVariable getFeishuCardVariable(@NotNull SettingChangeEvent body, String classify, String url) {
        return FeishuCardVariable.builder()
                .type(body.getType().getValue())
                .operator(body.getOperator())
                .content(body.getRequestBody())
                .environment(body.getEnvironment())
                .classify(classify)
                .operate(body.getOperate().getValue())
                .templateName(body.getTemplateName())
                .buttonText(webhookNotifyConfig.getButtonText())
                .historyUrl(String.format(url, body.getKey(), body.getHospitalId()))
                .build();
    }

    @Override
    public String getProcessName() {
        return PROCESS_NAME;
    }
}
