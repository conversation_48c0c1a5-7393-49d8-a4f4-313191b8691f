package com.puree.hospital.setting.service.hospital;

import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.setting.domain.entity.hospital.HospitalHistory;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;

import java.util.List;


/**
 * 医院配置历史数据
 * <AUTHOR>
 * @date 2024/09/24
 */
public interface HospitalHistoryService {
    /**
     * 新增历史记录
     * @param newData
     * @param oldData
     * @return {@link Boolean }
     */
    Boolean insertHistory(HospitalTemplate newData, HospitalTemplate oldData);

    Boolean insertHospitalHistory(HospitalTemplate hospitalTemplate, HospitalTemplate item, Long hospitalId);

    /**
     * 查询医院配置历史数据
     * @param itemsKey
     * @param hospitalId
     * @param pageSize
     * @param pageNum
     * @return {@link Paging }
     */
    Paging<List<HospitalHistory>> list(String itemsKey, Integer hospitalId, Integer pageSize, Integer pageNum);
}
