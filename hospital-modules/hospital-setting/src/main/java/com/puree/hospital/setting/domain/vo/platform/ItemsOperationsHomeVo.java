package com.puree.hospital.setting.domain.vo.platform;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName EasyConfigItemsHomeVo
 * <AUTHOR>
 * @Description 配置项 运营页面 vo
 * @Date 2023/12/6 16:22
 * @Version 1.0
 */
@Data
public class ItemsOperationsHomeVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * key
     */
    private String key;
    /**
     * 菜单类型ID
     */
    private Long menuId;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String remark;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 类型;前端定义值是多少
     */
    private Integer type;
    /**
     * 约束
     */
    private String restraint;
    /**
     * 值;初始创建时为默认值
     */
    private String value;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean whetherOpenHttpApi;
    /**
     * 推送的配置中心;文件名与文件名之间用,号隔开
     */
    private String pushConfigurationCenter;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 默认值
     */
    private String defaultValue;
}