package com.puree.hospital.setting.queue.consumer;

import com.puree.hospital.common.core.utils.SpringUtils;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import com.puree.hospital.common.redis.mq.annotation.RedisConsumer;
import com.puree.hospital.setting.notify.config.EasyConfigNotifyConfigProperties;
import com.puree.hospital.setting.notify.config.WebhookNotifyConfig;
import com.puree.hospital.setting.notify.notifier.AbstractConfigChangeNotifier;
import com.puree.hospital.setting.notify.notifier.IConfigChangeNotifier;
import com.puree.hospital.setting.constants.SettingChangeConstants;
import com.puree.hospital.setting.model.event.SettingChangeEvent;
import lombok.extern.slf4j.Slf4j;


import javax.annotation.Resource;


/**
 * <p>
 * 配置项变更消息消费者
 *
 * <AUTHOR>
 * @date 2024/12/4 15:04
 */
@Slf4j
@RedisConsumer(topic = SettingChangeConstants.TOPIC, group = SettingChangeConstants.GROUP_FEISHU_NOTIFY)
public class SettingChangeNotifyConsumer extends RedisStreamConsumer<SettingChangeEvent> {

    @Resource
    private EasyConfigNotifyConfigProperties easyConfigNotifyConfigProperties;

    @Override
    public void onMessage(RedisMessage<SettingChangeEvent> message) {

        for (WebhookNotifyConfig config : easyConfigNotifyConfigProperties.getWebhookConfig().values()) {
            try {
                IConfigChangeNotifier notifier = SpringUtils.getBean(config.getProcessName() + AbstractConfigChangeNotifier.PROCESS_NAME_SUFFIX);
                notifier.notifyChanges(message.getBody());
            } catch (Exception e) {
                log.error("通知处理器处理异常: {}", e.getMessage());
            }

        }

    }
}
