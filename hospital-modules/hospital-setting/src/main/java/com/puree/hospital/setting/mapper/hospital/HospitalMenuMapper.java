package com.puree.hospital.setting.mapper.hospital;

import com.puree.hospital.setting.domain.entity.Menu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/23
 */
@Mapper
public interface HospitalMenuMapper {
    Long countHospital(@Param("menuKey") String menuKey, @Param("menuName") String menuName);

    Integer insertHospitalMenu(Menu menu);

    /**
     * Query （查询） 菜单 最大排序方式（按 ID 排序）
     *
     * @param menuId
     * @return {@link Integer }
     */
    Integer queryMenuMaxSortById(Long menuId);

    List<Menu> queryByMenuNameAndKey(@Param("menuKey") String menuKey, @Param("menuName") String menuName);

    Integer update(Menu menu);

    /**
     * 统计字节的个数
     * @param id
     * @return {@link Long }
     */
    Long childNodeCount(Long id);


    Integer deleteByIdAndItemsName(@Param("id") Long id, @Param("menuName") String menuName, @Param("username") String username);

    List<Menu> list();

    Menu queryById(Long id);

    /**
     * 更新sort之前节点的排序
     * @param parentId
     * @param sort
     */
    void updateSortForward(@Param("parentId") Long parentId, @Param("sort") Integer sort);

    /**
     * 根据sort 节点往后移
     * @param parentId
     * @param sort
     */
    void updateSortMoveBack(@Param("parentId") Long parentId, @Param("sort") Integer sort);

    Integer updateSort(@Param("id") Long id, @Param("sort") Integer sort);

    Integer resetSort(@Param("parentId") Long parentId);

    Integer updateParentId(@Param("id") Long id, @Param("parentId") Long parentId, @Param("sort") Integer sort);

    Integer updateMenuId(@Param("id") Long id, @Param("menuId") Long menuId, @Param("sort") Integer sort);

    Menu queryMenu(String menuKey);
}
