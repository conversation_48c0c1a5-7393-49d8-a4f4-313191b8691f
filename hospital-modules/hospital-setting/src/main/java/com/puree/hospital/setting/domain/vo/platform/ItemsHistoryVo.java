package com.puree.hospital.setting.domain.vo.platform;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName EasyConfigItemsHistoryVo
 * <AUTHOR>
 * @Description 开发修改的历史数据 VO
 * @Date 2024/1/26 15:52
 * @Version 1.0
 */
@Data
public class ItemsHistoryVo implements Serializable {

    /**
     * 菜单类型ID
     */
    private Long menuId;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String remark;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 类型;前端定义值是多少
     */
    private Integer type;
    /**
     * 约束
     */
    private String restraint;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean whetherOpenHttpApi;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean anonymousAccess;
    /**
     * 响应内容类型
     */
    private String responseContentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    private String accessibleRoles;
    /**
     * 推送的配置中心;文件名与文件名之间用,号隔开
     */
    private String pushConfigurationCenter;
    /**
     * 默认值
     */
    private String defaultValue;
}
