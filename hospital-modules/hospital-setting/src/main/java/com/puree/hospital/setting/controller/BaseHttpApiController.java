package com.puree.hospital.setting.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.puree.hospital.setting.constants.ItemTypeEnum;
import com.puree.hospital.setting.converter.CsvHttpMessageConverter;
import com.puree.hospital.setting.converter.ExcelHttpMessageConverter;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.dto.ResponseFormat;
import com.puree.hospital.setting.domain.entity.Template;
import com.puree.hospital.setting.utils.JsonUtil;
import com.puree.ttql.TreeConvertor;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 */
public abstract class BaseHttpApiController {

    /**
     * 处理配置项的结果，主要是对json数据进行处理，例如对table类型的数据进行分页、排序、过滤等操作，并包装成ajax结果
     *
     * @param template 根据key获取的模板
     * @param format   JSON源的输出格式，JSON/ajax/csv/excel 等
     * @param tql      tree query language
     * @param offset   表格的行偏移量
     * @param limit    表格的行数
     * @param value    配置项的值
     * <AUTHOR>
     * @date 2024/12/27
     **/

    protected ResponseEntity<?> processResult(Template template,
                                              ResponseFormat format,
                                              String tql,
                                              int offset,
                                              int limit,
                                              String value) throws IOException {

        String contentType = template.getContentType();
        ResponseEntity.BodyBuilder builder = ResponseEntity.ok().contentType(MediaType.valueOf(contentType));
        Integer type = template.getType();

        // 非json格式数据 进行 ajax包装
        if (!ItemTypeEnum.isJsonStr(type)
                && JsonUtil.isJsonContentType(contentType)
                && Boolean.TRUE.equals(template.getIsWrap())){
            return builder.body(JsonUtil.formatToAjax(value));
        }

        if (JsonUtil.isJsonContentType(contentType)) {
            // 处理json数据
            JsonNode json = JsonUtil.fromJson(value, JsonNode.class);

            //判断是否是table类型，表格支持tql查询以及分页
            if (ItemTypeEnum.isTable(type) && json instanceof ArrayNode) {
                json = filterTable((ArrayNode) json, offset, limit);
                if (tql != null && !tql.isEmpty()) {
                    TreeConvertor tc = new TreeConvertor(tql);
                    json = tc.buildTree((ArrayNode) json);
                }
            }

            //format参数转换输出格式
            return buildFormat(template, builder, json, format);
        }

        //目前不对json以外的数据做特殊处理，直入直出
        return builder.body(value);
    }

    /**
     * 根据输出格式，构建响应体
     *
     * @param template 模板
     * @param builder  响应体构建器
     * @param json     json数据
     * @param format   输出格式
     * @return 响应体
     */
    private ResponseEntity<?> buildFormat(Template template,
                                          ResponseEntity.BodyBuilder builder,
                                          JsonNode json,
                                          ResponseFormat format) {
        if (format == null) {
            //未指定输出格式，根据模板配置是否包装json决定是否包装
            format = template.getIsWrap() ? ResponseFormat.ajax : ResponseFormat.json;
        }
        switch (format) {
            case json:
                //指定json格式，直接输出
                return builder.body(json);
            case ajax:
                return builder.body(R.ok(json));
            case csv:
                builder.contentType(CsvHttpMessageConverter.CSV_TYPE);
                builder.header("Content-Disposition", getUnicodeFileName(template.getName()) + ".csv");
                break;
            case excel:
                builder.contentType(ExcelHttpMessageConverter.EXCEL_TYPE);
                builder.header("Content-Disposition", getUnicodeFileName(template.getName()) + ".xlsx");
                break;
            default:
                throw new IllegalArgumentException("Unsupported format: " + format);
        }
        return builder.body(json);
    }

    /**
     * 解决文件名中文乱码问题
     *
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    @SneakyThrows
    private String getUnicodeFileName(String fileName) {
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        return "attachment; filename*=UTF-8''" + encodedFileName;
    }

    /**
     * 过滤表格数据，分页
     *
     * @param table 表格数据
     * @param offset 偏移量
     * @param limit 行数
     * @return 过滤后的表格数据
     */
    protected ArrayNode filterTable(ArrayNode table, int offset, int limit) {
        if (offset < 0 || limit < 0) {
            throw new IllegalArgumentException("offset and limit must be non-negative");
        }

        if (offset >= table.size()) {
            return new ArrayNode(null);
        }

        if (limit == 0) {
            limit = table.size();
        }

        int size = Math.min(limit, table.size() - offset);

        ArrayNode nodes = table.arrayNode(size);
        // copy elements from offset to offset+limit to nodes
        for (int i = 0; i < size; i++) {
            nodes.add(table.get(i + offset));
        }

        return nodes;
    }


}
