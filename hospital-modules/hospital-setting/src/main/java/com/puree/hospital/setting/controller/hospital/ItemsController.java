package com.puree.hospital.setting.controller.hospital;

import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.setting.constants.RequestConstants;
import com.puree.hospital.common.api.domain.R;

import com.puree.hospital.setting.domain.dto.hospital.ItemUpdateDto;
import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 平台端医院配置项菜单管理
 * <AUTHOR>
 * @date 2024/09/23
 */
@RestController
@AllArgsConstructor
@RequestMapping("easy-config")
public class ItemsController {
    private final HospitalTemplateService hospitalTemplateService;

    /**
     * 平台端 获取医院配置项列表
     * @param blurSearch 模糊查询字段
     * @param hospitalId 医院id
     *
     * @return {@link R }
     */
    @GetMapping("platform-admin/items")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:select")
    public R<Object> platformList(@RequestParam(required = false, name = "blurSearch") String blurSearch,
                          @RequestParam(required = false, name = "hospitalId") Long hospitalId) {
       return R.ok(hospitalTemplateService.itemsList(blurSearch,hospitalId));
    }

    /**
     * 平台端 修改医院配置项
     * @param dto 新数据
     * @return {@link R }
     */
    @PutMapping("platform-admin/items")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:update")
    public R<Boolean> platformUpdate(@RequestBody ItemUpdateDto dto,
                            @RequestHeader(value = RequestConstants.HOSPITAL_ID, required = false) Long hospitalId,
                            @RequestHeader(value = RequestConstants.DETAILS_USERNAME, required = false) String username) {
        if (dto.getId() == null && StringUtils.isEmpty(dto.getKey())){
            throw new IllegalArgumentException("ID 和 Key 不能同时为空");
        }
        if (dto.getRevision() == null && dto.getId() != null){
            throw new IllegalArgumentException("Revision不能为空");
        }
        dto.setUsername(username);
        if (dto.getHospitalId() == null){
            dto.setHospitalId(hospitalId);
        }
        return R.ok(hospitalTemplateService.updateItems(dto));
    }
    /**
     * 医院端 获取配置项列表
     * @param blurSearch 模糊查询字段
     * @return {@link R }
     */
    @GetMapping("hospital-admin/items")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:select")
    public R<Object> hospitalList(@RequestParam(required = false, name = "blurSearch") String blurSearch,
                          @RequestHeader(value = RequestConstants.HOSPITAL_ID, required = false) String hospitalId) {
        if (StringUtils.isEmpty(hospitalId) || !hospitalId.matches("\\d+")){
            throw new IllegalArgumentException("hospitalId 参数异常");
        }
        return R.ok(hospitalTemplateService.itemsList(blurSearch,Long.parseLong(hospitalId)));
    }

    /**
     * 医院端修改配置项
     * @param dto 配置项
     * @return {@link R }
     */
    @PutMapping("hospital-admin/items")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:update")
    public R<Boolean> hospitalUpdate(@RequestBody ItemUpdateDto dto,
                            @RequestHeader(value = RequestConstants.HOSPITAL_ID, required = false) Long hospitalId,
                            @RequestHeader(value = RequestConstants.DETAILS_USERNAME, required = false) String username) {
        if (dto.getId() == null && StringUtils.isEmpty(dto.getKey())){
            throw new IllegalArgumentException("ID 和 Key 不能同时为空");
        }
        if (dto.getRevision() == null && dto.getId() != null){
            throw new IllegalArgumentException("Revision不能为空");
        }
        dto.setUsername(username);
        if (dto.getHospitalId() == null){
            dto.setHospitalId(hospitalId);
        }
        return R.ok(hospitalTemplateService.updateItems(dto));
    }
}
