package com.puree.hospital.setting.notify.notifier;

import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.setting.notify.config.EasyConfigNotifyConfigProperties;
import com.puree.hospital.setting.notify.config.WebhookNotifyConfig;
import com.puree.hospital.setting.model.event.SettingChangeEvent;
import com.puree.hospital.setting.notify.resolver.ClassifyNameResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 配置项变更通知抽象类
 *
 * <AUTHOR>
 * @date 2025/8/22
 */
@Slf4j
public abstract class AbstractConfigChangeNotifier implements IConfigChangeNotifier {

    @Resource
    protected EasyConfigNotifyConfigProperties easyConfigNotifyConfigProperties;

    @Resource
    protected ClassifyNameResolver classifyNameResolver;

    public static final String PROCESS_NAME = "feishu";

    public static final String PROCESS_NAME_SUFFIX = "Notifier";

    /**
     * 是否启用通知
     *
     * @param processName 通知流程名称
     * @return true 表示启用，false 表示禁用
     */
    protected boolean isEnabled(String processName) {
        return getWebhookNotifyConfig(processName).isEnable();
    }

    /**
     * 获取 webhook 通知配置
     *
     * @param processName 通知流程名称
     * @return webhook 通知配置
     * @throws ServiceException 如果未配置对应流程，抛出异常
     */
    protected WebhookNotifyConfig getWebhookNotifyConfig(String processName) {
        WebhookNotifyConfig webhookNotifyConfig =
                easyConfigNotifyConfigProperties.getWebhookConfig().get(processName);
        if (webhookNotifyConfig == null) {
            throw new ServiceException("未配置" + processName);
        }
        return webhookNotifyConfig;
    }

    /**
     * 根据配置项变更事件获取跳转链接
     *
     * @param event 配置项变更事件，包含分类信息
     * @return 跳转链接地址
     */
    protected String getRedirectionUrl(SettingChangeEvent event) {
        String redirectionUrl = "";
        if (SettingChangeEvent.Classify.HOSPITAL.equals(event.getClassify())) {
            redirectionUrl = easyConfigNotifyConfigProperties.getHospitalHistoryPageUrl();
        } else if (SettingChangeEvent.Classify.PLATFORM.equals(event.getClassify())) {
            redirectionUrl = easyConfigNotifyConfigProperties.getPlatformHistoryPageUrl();
        }
        return redirectionUrl;
    }

    /**
     * 根据配置项变更事件获取分类名称
     *
     * @param event 配置项变更事件，包含分类信息和关联ID
     * @return 分类名称，若无法解析则返回分类的默认值
     */
    protected String getClassify(SettingChangeEvent event) {
        String classify = classifyNameResolver.resolveClassifyName(event);
        return StringUtils.hasText(classify) ? classify : event.getClassify().getValue();
    }

    @Override
    public void notifyChanges(SettingChangeEvent event) {
        if (!isEnabled(getProcessName())) {
            return;
        }
        notify(event);
    }

    @Override
    public String getProcessName() {
        return PROCESS_NAME;
    }

    /**
     * 执行具体的通知逻辑，由子类实现
     *
     * @param event 配置变更事件
     */
    protected abstract void notify(SettingChangeEvent event);
}