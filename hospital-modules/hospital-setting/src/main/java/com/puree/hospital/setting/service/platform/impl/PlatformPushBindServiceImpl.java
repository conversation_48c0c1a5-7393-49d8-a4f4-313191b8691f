package com.puree.hospital.setting.service.platform.impl;


import com.puree.hospital.setting.domain.entity.platform.PlatformItemsPushDo;
import com.puree.hospital.setting.domain.entity.platform.PlatformPushBind;
import com.puree.hospital.setting.mapper.platform.PlatformPushBindMapper;
import com.puree.hospital.setting.service.platform.PlatformPushBindService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName EasyConfigPushBindServiceImpl
 * <AUTHOR>
 * @Description 配置项推送绑定表
 * @Date 2023/12/18 17:40
 * @Version 1.0
 */
@Service
@AllArgsConstructor
public class PlatformPushBindServiceImpl implements PlatformPushBindService {

    private final PlatformPushBindMapper platformPushBindMapper;

    /**
     * @Param push
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItemsPushDo>
     * @Description 获取绑定关系
     * <AUTHOR>
     * @Date 2023/12/18 19:21
     **/
    @Override
    public List<PlatformItemsPushDo> getBindingRelationship(Collection<String> push) {
        return platformPushBindMapper.getBindingRelationship(push);
    }

    /**
     * @Param easyConfigPushBind
     * @Return java.lang.Boolean
     * @Description 新增绑定关系
     * <AUTHOR>
     * @Date 2023/12/18 19:22
     **/
    @Override
    public Boolean insertBindingRelationship(PlatformPushBind platformPushBind) {
        return platformPushBindMapper.insert(platformPushBind);
    }

    /**
     * @Param itemId
     * @Return java.lang.Boolean
     * @Description 删除绑定关系
     * <AUTHOR>
     * @Date 2023/12/19 12:13
     **/
    @Override
    public Boolean deleteBindingRelationship(Long itemId){
        return platformPushBindMapper.deleteBindingRelationship(itemId);
    }

    @Override
    public List<PlatformPushBind> getBindingList(Long id) {
        return platformPushBindMapper.getBindingList(id);
    }
}
