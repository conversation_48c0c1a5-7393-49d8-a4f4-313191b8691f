package com.puree.hospital.setting.service.platform;


import com.puree.hospital.setting.domain.entity.platform.PlatformItemsPushDo;
import com.puree.hospital.setting.domain.entity.platform.PlatformPushBind;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName EasyConfigPushBindService
 * <AUTHOR>
 * @Description 配置项推送绑定表
 * @Date 2023/12/18 17:40
 * @Version 1.0
 */
public interface PlatformPushBindService {
    /**
     * @Param push
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItemsPushDo>
     * @Description 获取绑定关系
     * <AUTHOR>
     * @Date 2023/12/18 19:21
     **/
    List<PlatformItemsPushDo> getBindingRelationship(Collection<String> push);

    /**
     * @Param easyConfigPushBind
     * @Return java.lang.Boolean
     * @Description 新增绑定关系
     * <AUTHOR>
     * @Date 2023/12/18 19:23
     **/
    Boolean insertBindingRelationship(PlatformPushBind platformPushBind);

    /**
     * @Param itemId
     * @Return java.lang.Boolean
     * @Description 删除绑定关系
     * <AUTHOR>
     * @Date 2023/12/19 12:13
     **/
    Boolean deleteBindingRelationship(Long itemId);

    List<PlatformPushBind> getBindingList(Long id);
}
