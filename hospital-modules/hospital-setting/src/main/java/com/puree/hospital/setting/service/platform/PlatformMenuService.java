package com.puree.hospital.setting.service.platform;


import cn.hutool.core.lang.tree.Tree;
import com.puree.hospital.setting.domain.dto.platform.MenuDevelopSaveDto;
import com.puree.hospital.setting.domain.dto.platform.MenuDevelopUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;


import java.util.List;

/**
 * @ClassName EasyConfigMenuService
 * <AUTHOR>
 * @Description 菜单 service
 * @Date 2023/12/6 11:15
 * @Version 1.0
 */
public interface PlatformMenuService {

    /**
     * @Param
     * @Return java.util.List<cn.hutool.core.lang.tree.Tree < java.lang.Long>>
     * @Description 菜单列表树
     * <AUTHOR>
     * @Date 2023/12/8 15:19
     **/
    List<Tree<Long>> menuListTree();

    /**
     * @Param parentId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigMenu>
     * @Description 获取 父 ID 下的全部节点
     * <AUTHOR>
     * @Date 2024/1/11 14:17
     **/
    List<Menu> getMenuSortByParentId(Long parentId);

    /**
     * @Param
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigMenu>
     * @Description 获取 list
     * <AUTHOR>
     * @Date 2024/1/26 16:45
     **/
    List<Menu> getList();

    /**
     * @Param
     * @Return cn.hutool.core.lang.tree.Tree<java.lang.Long>
     * @Description 单 root 树
     * <AUTHOR>
     * @Date 2024/1/26 16:47
     **/
    Tree<Long> menuListTreeSingle();

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 开发新增配置项菜单
     * <AUTHOR>
     * @Date 2023/12/8 15:25
     **/
    Boolean developInsert(MenuDevelopSaveDto dto);

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询该菜单下最大排序
     * <AUTHOR>
     * @Date 2023/12/8 15:46
     **/
    Integer queryMenuMaxSortById(Long menuId);

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序 +1
     * <AUTHOR>
     * @Date 2023/12/8 15:51
     **/
    Integer queryMenuMaxSortIncrementerById(Long menuId);

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 开发修改配置项菜单
     * <AUTHOR>
     * @Date 2023/12/8 16:05
     **/
    Boolean developUpdate(MenuDevelopUpdateDto dto);

    /**
     * @Param item
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改数据
     * <AUTHOR>
     * @Date 2023/12/8 16:36
     **/
    Boolean update(Menu item);

    /**
     * @Param id
     * @Param menuName
     * @Return java.lang.Boolean
     * @Description 开发删除配置项
     * <AUTHOR>
     * @Date 2023/12/8 16:06
     **/
    Boolean developDelete(Long id, String menuName);

    /**
     * @Param id
     * @Param sort
     * @Param parentId
     * @Return java.lang.Boolean
     * @Description 修改父级节点或排序
     * <AUTHOR>
     * @Date 2023/12/8 16:06
     **/
    Boolean developUpdateSortOrParentId(Long id, Integer sort, Long parentId);

    /**
     * @Param id
     * @Param parentId
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改父节点，直接尾部追加
     * <AUTHOR>
     * @Date 2023/12/8 16:35
     **/
    Boolean developUpdateMenuId(Long id, Long parentId);

    /**
     * @Param id
     * @Param sort
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改排序
     * <AUTHOR>
     * @Date 2023/12/8 16:35
     **/
    Boolean developUpdateSort(Long id, Integer sort);

    /**
     * @Param id
     * @Return com.puree.easycfg.domain.easy.EasyConfigMenu
     * @Description 根据 ID 查询
     * <AUTHOR>
     * @Date 2023/12/8 16:35
     **/
    Menu getById(Long id);

    /**
     * 获取菜单
     *
     * @param menuKey 菜单键
     * @return {@link Menu }
     */
    Menu queryMenu(String menuKey);
}
