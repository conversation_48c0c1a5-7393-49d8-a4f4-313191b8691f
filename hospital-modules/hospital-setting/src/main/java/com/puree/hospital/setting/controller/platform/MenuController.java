package com.puree.hospital.setting.controller.platform;

import cn.hutool.core.lang.tree.Tree;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.dto.platform.MenuDevelopSaveDto;
import com.puree.hospital.setting.domain.dto.platform.MenuDevelopUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.service.platform.PlatformMenuService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName EasyConfigMenuController
 * <AUTHOR>
 * @Description 菜单 controller
 * @Date 2023/12/6 12:11
 * @Version 1.0
 */

@RestController
@AllArgsConstructor
@RequestMapping("/easy/config/easy-config-menu")
public class MenuController {

    private final PlatformMenuService platformMenuService;


    /**
     * @Param
     * @Return com.puree.easycfg.domain.R
     * @Description 菜单列表树
     * <AUTHOR>
     * @Date 2023/12/8 15:19
     **/
    @GetMapping("/menu/list-tree")
    @PreAuthorize(hasPermi = "business:setting:platform:menu:select")
    public R<List<Tree<Long>>> menuListTree() {
        return R.ok(platformMenuService.menuListTree());
    }

    /**
     * @Param id
     * @Return com.puree.easycfg.domain.R
     * @Description 开发通过 ID 获取详情
     * <AUTHOR>
     * @Date 2023/12/21 16:28
     **/
    @GetMapping("/get-by-id/develop")
    @PreAuthorize(hasPermi = "business:setting:platform:menu:update")
    public R<Menu> getByIdDevelop(@RequestParam(required = false, name = "id") Long id) {
        return R.ok(platformMenuService.getById(id));
    }


    /**
     * @Param dto
     * @Return com.puree.easycfg.domain.R
     * @Description 开发新增配置项菜单
     * <AUTHOR>
     * @Date 2023/12/8 15:25
     **/
    @PostMapping("/develop/insert")
    @PreAuthorize(hasPermi = "business:setting:platform:menu:insert")
    public R<Boolean> developInsert(@RequestBody @Validated MenuDevelopSaveDto dto) {
        return R.ok(platformMenuService.developInsert(dto));
    }

    /**
     * @Param dto
     * @Return com.puree.easycfg.domain.R
     * @Description 开发修改配置项菜单
     * <AUTHOR>
     * @Date 2023/12/8 15:56
     **/
    @PutMapping("/develop/update")
    @PreAuthorize(hasPermi = "business:setting:platform:menu:update")
    public R<Boolean> developUpdate(@RequestBody @Validated MenuDevelopUpdateDto dto) {
        return R.ok(platformMenuService.developUpdate(dto));
    }

    /**
     * @Param id
     * @Param itemsName
     * @Return com.puree.easycfg.domain.R
     * @Description 开发删除配置项
     * <AUTHOR>
     * @Date 2023/12/8 15:57
     **/
    @DeleteMapping("/develop/delete/{id}")
    @PreAuthorize(hasPermi = "business:setting:platform:menu:delete")
    public R<Boolean> developDelete(@PathVariable(value = "id", required = true) Long id,
                           @RequestParam("menuName") String menuName) {
        return R.ok(platformMenuService.developDelete(id, menuName));
    }

    /**
     * @Param id
     * @Param sort
     * @Param parentId
     * @Return com.puree.easycfg.domain.R
     * @Description 修改父级节点或排序
     * <AUTHOR>
     * @Date 2023/12/8 16:06
     **/
    @PutMapping("/develop/update/sort-or-parentId")
    @PreAuthorize(hasPermi = "business:setting:platform:menu:move")
    public R<Boolean> developUpdateSortOrParentId(@RequestParam(value = "id", required = true) Long id,
                                         @RequestParam(value = "sort", required = false) Integer sort,
                                         @RequestParam(value = "parentId", required = false) Long parentId) {
        return R.ok(platformMenuService.developUpdateSortOrParentId(id, sort, parentId));
    }

}
