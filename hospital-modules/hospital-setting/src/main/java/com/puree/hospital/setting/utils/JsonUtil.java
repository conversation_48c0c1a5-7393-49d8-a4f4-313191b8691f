package com.puree.hospital.setting.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.puree.hospital.common.api.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

/**
 * JSON 处理工具类，提供 JSON 序列化、反序列化及 AJAX 响应格式化功能。
 *
 * <AUTHOR>
 * @date 2024/10/16 12:21
 */
public class JsonUtil {

    private static final Logger log = LoggerFactory.getLogger(JsonUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    private static final String JSON_PARSE_ERROR = "JSON parsing error, please check the value: {}";
    private static final String JSON_CONVERSION_ERROR = "JSON conversion failed, please check JSON format or class compatibility: {}";

    /**
     * 检查是否为 JSON Content-Type
     *
     * @param contentType Content-Type 字符串
     * @return 是否为 JSON 类型
     */
    public static boolean isJsonContentType(String contentType) {
        if (contentType == null) {
            return false;
        }
        try {
            MediaType mediaType = MediaType.parseMediaType(contentType);
            return MediaType.APPLICATION_JSON.isCompatibleWith(mediaType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为 JSON 字符串 并且是数组
     * @param jsonString json
     * @return 是否为 JSON 数组
     */
    public static boolean isJsonArray(String jsonString) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            return jsonNode.isArray(); // 检查是否是 JSON 数组
        } catch (Exception e) {
            return false; // 不是合法的 JSON 或不是数组
        }
    }

    /**
     * 检查是否为 JSON 字符串 并且是对象
     * @param jsonString json
     * @return 是否为 JSON 对象
     */
    public static boolean isJson(String jsonString) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            return jsonNode.isObject(); // 检查是否是 JSON 数组
        } catch (Exception e) {
            return false; // 不是合法的 JSON 或不是数组
        }
    }

    /**
     * 将输入包装成 AJAX 格式的 JSON 字符串
     *
     * @param value 输入值（JSON 字符串或对象）
     * @return AJAX 格式的 JSON 字符串
     */
    public static String formatToAjax(Object value) {
        R result;
        try {
            Object data = value instanceof String ? objectMapper.readTree((String) value) : value;
            result = R.ok(data);
        } catch (JsonProcessingException e) {
            // 针对基础类型，不做转换直接返回
            result = R.ok(value);
        }
        try {
            return objectMapper.writeValueAsString(result);
        } catch (JsonProcessingException e) {
            log.error(JSON_PARSE_ERROR,value, e);
            throw new IllegalArgumentException(JSON_PARSE_ERROR.replace("{}", String.valueOf(value)), e);
        }
    }

    /**
     * 将 JSON 字符串转换为 Java 对象
     *
     * @param json JSON 字符串
     * @param clazz 目标类
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.warn(JSON_CONVERSION_ERROR, json, e);
            throw new IllegalArgumentException(JSON_CONVERSION_ERROR.replace("{}", json), e);
        }
    }

    /**
     * 将 Java 对象转换为 JSON 字符串
     *
     * @param object 输入对象
     * @return JSON 字符串
     */
    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.warn(JSON_CONVERSION_ERROR, object, e);
            throw new IllegalArgumentException(JSON_CONVERSION_ERROR.replace("{}", String.valueOf(object)), e);
        }
    }


    /**
     * 严格合并两个JSON字符串：
     * - 如果是数组，比较元素结构(第一个元素作为模板)，不要求长度相同
     * - 如果是对象，严格匹配字段(新增字段使用默认值)
     * - 基本类型直接返回value(如果存在)
     *
     * @param valueStr value的JSON字符串
     * @param defaultValueStr defaultValue的JSON字符串
     * @return 合并后的JSON字符串
     * @throws Exception 如果JSON解析失败
     */
    public static String mergeJson(String valueStr, String defaultValueStr) throws Exception {
        JsonNode valueNode = objectMapper.readTree(valueStr);
        JsonNode defaultValueNode = objectMapper.readTree(defaultValueStr);

        JsonNode mergedNode = mergeNodes(valueNode, defaultValueNode);
        return objectMapper.writeValueAsString(mergedNode);
    }

    /**
     * 递归合并两个JsonNode节点
     */
    private static JsonNode mergeNodes(JsonNode valueNode, JsonNode defaultValueNode) {
        // 处理数组情况
        if (defaultValueNode.isArray()) {
            return mergeArrays(valueNode, defaultValueNode);
        }

        // 处理对象情况
        if (defaultValueNode.isObject()) {
            return mergeObjects(valueNode, defaultValueNode);
        }

        // 基本类型情况
        return valueNode != null && !valueNode.isNull() ? valueNode : defaultValueNode;
    }

    /**
     * 合并两个数组节点
     */
    private static ArrayNode mergeArrays(JsonNode valueNode, JsonNode defaultValueNode) {
        if (!valueNode.isArray()) {
            // value不是数组，返回defaultValue数组
            return defaultValueNode.deepCopy();
        }

        ArrayNode mergedArray = objectMapper.createArrayNode();

        // 如果defaultValue数组为空，无法获取元素模板，直接返回value数组
        if (defaultValueNode.isEmpty()) {
            valueNode.forEach(mergedArray::add);
            return mergedArray;
        }

        // 使用defaultValue的第一个元素作为模板
        JsonNode templateNode = defaultValueNode.get(0);

        // 处理value中的每个元素
        valueNode.forEach(valueElement -> {
            if (valueElement.isObject()) {
                // 元素是对象，使用模板合并
                mergedArray.add(mergeObjectWithTemplate(valueElement, templateNode));
            } else {
                // 元素不是对象，不符合模板要求，返回空对象
                mergedArray.add(objectMapper.createObjectNode());
            }
        });

        return mergedArray;
    }

    /**
     * 使用模板合并对象
     */
    private static ObjectNode mergeObjectWithTemplate(JsonNode valueElement, JsonNode templateNode) {
        return getJsonNodes(valueElement, templateNode);
    }

    private static ObjectNode getJsonNodes(JsonNode valueElement, JsonNode templateNode) {
        ObjectNode mergedObject = objectMapper.createObjectNode();

        // 严格匹配模板字段
        templateNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode defaultField = entry.getValue();

            if (valueElement.has(key)) {
                // 值存在，递归合并
                mergedObject.set(key, mergeNodes(valueElement.get(key), defaultField));
            } else {
                // 值不存在，使用默认值
                mergedObject.set(key, defaultField.deepCopy());
            }
        });

        return mergedObject;
    }

    /**
     * 合并两个对象节点
     */
    private static ObjectNode mergeObjects(JsonNode valueNode, JsonNode defaultValueNode) {
        if (!valueNode.isObject()) {
            // value不是对象，返回defaultValue
            return defaultValueNode.deepCopy();
        }

        return getJsonNodes(valueNode, defaultValueNode);
    }

}