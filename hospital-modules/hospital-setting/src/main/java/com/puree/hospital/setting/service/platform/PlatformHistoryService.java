package com.puree.hospital.setting.service.platform;

import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;


import java.util.List;

/**
 * @ClassName EasyConfigHistoryService
 * <AUTHOR>
 * @Description 历史数据
 * @Date 2023/12/6 11:36
 * @Version 1.0
 */
public interface PlatformHistoryService {
    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Param type 类型 0 开发 1 运维 如果为开发修改则只需要记录元数据，如果为运营修改只需要记录value值
     * @Return java.lang.Boolean
     * @Description 新增历史数据
     * <AUTHOR>
     * @Date 2023/12/7 18:01
     **/
    Boolean insertHistory(PlatformItems sourceData, PlatformItems targetData, Integer type);

    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Return java.lang.Boolean
     * @Description 开发新增历史数据
     * <AUTHOR>
     * @Date 2023/12/7 18:01
     **/
    Boolean developInsertHistory(PlatformItems sourceData, PlatformItems targetData);

    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Return java.lang.Boolean
     * @Description 运营新增历史数据
     * <AUTHOR>
     * @Date 2023/12/7 18:01
     **/
    Boolean operationsInsertHistory(PlatformItems sourceData, PlatformItems targetData);

    /**
     * @Param itemsId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 通过数据项ID 查询历史数据
     * <AUTHOR>
     * @Date 2023/12/8 16:41
     **/
    List<PlatformHistory> queryByItemId(Long itemsId);

    /**
     * @Param blurSearch
     * @Param pageSize 每页几条
     * @Param pageNum 第几页
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 模糊查询
     * <AUTHOR>
     * @Date 2024/1/16 17:45
     **/
    Paging<List<PlatformHistory>> blurSearch(String blurSearch, Integer pageSize, Integer pageNum, Integer type);

    /**
     * @Param blurSearch
     * @Param pageSize
     * @Param pageNo
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 模糊查询
     * <AUTHOR>
     * @Date 2024/1/18 14:57
     **/
    List<PlatformHistory> queryByKey(String blurSearch, Integer pageSize, Integer pageNum, Integer type);

    /**
     * @Param blurSearch
     * @Return java.lang.Long
     * @Description 分页计数
     * <AUTHOR>
     * @Date 2024/1/18 14:28
     **/
    Long queryByKeyCount(String blurSearch, Integer type);

    /**
     * @Param
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 获取全部数据
     * <AUTHOR>
     * @Date 2024/1/16 17:43
     **/
    List<PlatformHistory> list();
}
