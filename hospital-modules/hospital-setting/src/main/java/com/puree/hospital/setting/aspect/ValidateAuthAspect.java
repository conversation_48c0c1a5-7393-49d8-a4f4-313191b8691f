package com.puree.hospital.setting.aspect;

import com.puree.hospital.setting.domain.entity.TemplateAuth;
import com.puree.hospital.setting.service.hospital.HospitalMenuService;
import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import com.puree.hospital.setting.service.platform.PlatformItemsService;
import com.puree.hospital.setting.service.platform.PlatformMenuService;
import com.puree.hospital.setting.utils.PropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.puree.hospital.setting.annotation.ValidateAuth;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * HTTP_API权限校验切面
 */
@Aspect
@Component
@Slf4j
public class ValidateAuthAspect {
    @Autowired
    private PlatformItemsService platformItemService;

    @Autowired
    private PlatformMenuService platformMenuService;

    @Autowired
    private HospitalTemplateService hospitalTemplateService;

    @Autowired
    private HospitalMenuService hospitalMenuService;

    // 环绕通知
    @Around("@annotation(annotation)")
    public Object around(ProceedingJoinPoint joinPoint, ValidateAuth annotation) throws Throwable {
        // Get the method signature
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
        String templateKey = getTemplateKey(methodSignature.getMethod(), args, annotation.key());

        TemplateAuth template = queryTemplate(annotation, templateKey);
        // 是否开启Http访问
        if (!template.isHttpEnabled()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("RESOURCE NOT FOUND");
        }

        // 是否开启 匿名访问
        if (!template.isAnonymousEnabled()) {
            // 权限校验
            ServletRequestAttributes servlet = (ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes());
            String roles = servlet.getRequest().getHeader(PropertiesUtil.AUTHENTICATION_HEADER);
            if (!authorization(template.getAllowedRoles(), roles)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("无权限访问");
            }
        }


        // Assuming the last argument is where you want to inject the result
        args[args.length - 1] = template;

        // 继续执行业务逻辑
        return joinPoint.proceed(args);
    }

    private TemplateAuth queryTemplate(ValidateAuth annotation, String templateKey) {
        switch (annotation.value()) {
            case HOSPITAL_ITEM:
                return hospitalTemplateService.getTemplate(templateKey);
            case HOSPITAL_MENU:
                return hospitalMenuService.queryMenu(templateKey);
            case PLATFORM_MENU:
                return platformMenuService.queryMenu(templateKey);
        }

        return platformItemService.getByKey(templateKey);
    }

    private String getTemplateKey(Method method, Object[] args, String variableName) {
        // Get parameter annotations
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        // Iterate over parameters to find @PathVariable
        for (int i = 0; i < parameterAnnotations.length; i++) {
            for (Annotation annotation : parameterAnnotations[i]) {
                if (annotation instanceof PathVariable) {
                    PathVariable pathVariable = (PathVariable) annotation;
                    String pathVariableName = pathVariable.value();
                    log.debug("Found path variable: {}", pathVariableName);
                    if (pathVariableName.equals(variableName)) {
                        log.debug("Found path variable: {} with value: {}", pathVariableName, args[i]);
                        return (String) args[i];
                    }
                }
            }
        }

        throw new IllegalArgumentException("Could not find path variable with name: " + variableName);
    }

    private boolean authorization(String allowedRoles, String encryptedRoles) {
        log.debug("accessible roles:{}, current user roles: {}", allowedRoles, encryptedRoles);
        //如果为无角色限制，则直接返回 true
        if (!StringUtils.hasText(allowedRoles)) {
            return true;
        }

        //解密
        String roles = PropertiesUtil.headerDecryptStr(encryptedRoles);
        log.debug("decrypted header roles:{}", roles);
        if (!StringUtils.hasText(roles)) {
            return false;
        }

        List<String> sourceRoles = Arrays.asList(roles.split(","));
        List<String> targetRoles = Arrays.asList(allowedRoles.split(","));
        // Create a copy of list1
        List<String> matchedRoles = new ArrayList<>(sourceRoles);
        // Retain only the elements that are also in list2
        matchedRoles.retainAll(targetRoles);
        log.debug("matched roles: {}", matchedRoles);

        return !matchedRoles.isEmpty();
    }
}
