package com.puree.hospital.setting.domain.vo.hospital;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医院配置模板列表展示vo类
 * <AUTHOR>
 * @date 2024/09/24
 */
@Data
public class ItemsOperationsHomeVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * key
     */
    private String key;
    /**
     * 菜单类型ID
     */
    private Long menuId;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String remark;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 类型;前端定义值是多少
     */
    private String type;
    /**
     * 约束
     */
    private String restraint;
    /**
     * 值;初始创建时为默认值
     */
    private String defaultValue;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean enableHttp;
    /**
     * 是否开启Redis缓存;0 为关闭，1为开启
     */
    private Boolean enableCache;

    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     *配置项的值
     */
    @JsonProperty("value")
    private String itemValue;
    /**
     * 缓存分钟数
     */
    private Integer cacheMinutes;
    /**
     *配置项的版本
     */
    private Integer itemRevision;
    /**
     *医院id
     */
    private Long hospitalId;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean allowAnonymous;
    /**
     * 响应内容类型
     */
    private String contentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    private String allowedRoles;
    /**
     * 可显示该配置模板的医院id;医院id与医院id之间用,号隔开
     */
    private String whiteList;

    /**
     * 删除标记;0未删除1已删除
     */
    private Boolean isDelete;

    /** 是否包装成ajax格式 */
    private Boolean isWrap;

    /**
     * 配置项的过滤模式;
     * @see com.puree.hospital.setting.constants.FilterModeEnum
     */
    private String filterMode;

    /**
     * 配置项的过滤列表;
     * 配合 filterMode {@link com.puree.hospital.setting.constants.FilterModeEnum} 使用
     */
    private String filterList;

}