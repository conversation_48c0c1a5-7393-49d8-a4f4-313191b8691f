package com.puree.hospital.setting.controller.hospital;

import com.mysql.cj.util.StringUtils;

import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.setting.constants.RequestConstants;
import com.puree.hospital.setting.domain.entity.hospital.HospitalHistory;
import com.puree.hospital.setting.service.hospital.HospitalHistoryService;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 平台端医院历史数据
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@RestController
@AllArgsConstructor
@RequestMapping("easy-config")
public class HistoryController {
    private static final Logger log = LoggerFactory.getLogger(HistoryController.class);
    private final HospitalHistoryService hospitalHistoryService;

    /**
     * 平台端获取医院配置项历史数据列表
     * @param itemsKey 配置项key
     * @param hospitalId
     * @param pageSize
     * @param pageNum
     * @return {@link Paging }
     */
    @GetMapping("platform-admin/history/list")
    public Paging<List<HospitalHistory>> platformList(@RequestParam(required = false, name = "itemsKey") String itemsKey,
                               @RequestParam(name = "hospitalId") Integer hospitalId,
                               @RequestParam(required = false, name = "pageSize") Integer pageSize,
                               @RequestParam(required = false, name = "pageNum") Integer pageNum) {
        return hospitalHistoryService.list(itemsKey,hospitalId,pageSize,pageNum);
    }

    /**
     * 医院端获取医院配置项历史数据列表
     * @param itemsKey 配置项key
     * @param pageSize
     * @param pageNum
     * @param request
     * @return {@link Paging }
     */
    @GetMapping("hospital-admin/history/list")
    public Paging<List<HospitalHistory>> hospitalList(@RequestParam(required = false, name = "itemsKey") String itemsKey,
                                                      @RequestParam(required = false, name = "pageSize") Integer pageSize,
                                                      @RequestParam(required = false, name = "pageNum") Integer pageNum, HttpServletRequest request) {
        String hospitalId = request.getHeader(RequestConstants.HOSPITAL_ID);
        log.debug("hospitalId:{}",hospitalId);
        if (StringUtils.isNullOrEmpty(hospitalId) || !hospitalId.matches("\\d+")){
            throw new IllegalArgumentException("hospitalId 参数异常");
        }
        return hospitalHistoryService.list(itemsKey, Integer.parseInt(hospitalId), pageSize, pageNum);
    }
}
