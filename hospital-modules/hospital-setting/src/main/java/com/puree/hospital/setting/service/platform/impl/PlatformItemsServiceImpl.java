package com.puree.hospital.setting.service.platform.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.map.MapUtil;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.setting.config.NacosPushConfigurationProperties;
import com.puree.hospital.setting.constants.ItemTypeEnum;
import com.puree.hospital.setting.domain.dto.TemplateDefaultValueDTO;
import com.puree.hospital.setting.domain.dto.platform.ItemsDevelopSaveDto;
import com.puree.hospital.setting.domain.dto.platform.ItemsDevelopUpdateDto;
import com.puree.hospital.setting.domain.dto.platform.ItemsOperationsUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.entity.platform.PlatformItemsPushDo;
import com.puree.hospital.setting.domain.entity.platform.PlatformPushBind;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.vo.ItemDescriptionVO;
import com.puree.hospital.setting.domain.vo.hospital.TemplateVO;
import com.puree.hospital.setting.domain.vo.platform.ItemsDevelopHomeVo;
import com.puree.hospital.setting.domain.vo.platform.ItemsOperationsHomeVo;
import com.puree.hospital.setting.mapper.platform.PlatformItemsMapper;
import com.puree.hospital.setting.service.platform.*;
import com.puree.hospital.setting.utils.JsonUtil;
import com.puree.hospital.setting.utils.PropertiesUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName EasyConfigItemsServiceImpl
 * <AUTHOR>
 * @Description 配置项数据
 * @Date 2023/12/6 11:45
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlatformItemsServiceImpl implements PlatformItemsService {

    private final PlatformItemsMapper platformItemsMapper;

    private final PlatformHistoryService platformHistoryService;

    private final NacosService nacosService;

    private final PlatformPushBindService platformPushBindService;

    private final PlatformMenuService platformMenuService;

    private final NacosPushConfigurationProperties nacosPushConfigurationProperties;


    /**
     * @Param blurSearch
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 模糊查询
     * <AUTHOR>
     * @Date 2023/12/7 12:15
     **/
    @Override
    public List<PlatformItems> blurSearch(String blurSearch) {
        return platformItemsMapper.blurSearch(blurSearch);
    }

    /**
     * @Param menuId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 获取菜单下的配置项数
     * <AUTHOR>
     * @Date 2024/3/22 17:28
     **/
    @Override
    public List<PlatformItems> getItemsByMenuId(Long menuId) {
        return platformItemsMapper.getItemsByMenuId(menuId);
    }

    /**
     * @Param menuKey
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 获取菜单下的配置项数
     * <AUTHOR>
     * @Date 2024/3/27 10:45
     **/
    @Override
    public List<PlatformItems> getItems(TemplateQuery templateQuery) {
        return platformItemsMapper.getItems(templateQuery);
    }

    /**
     * @Param blurSearch 模糊查询字段，支持配置项名称、描述、key
     * @Return java.util.List<com.puree.easycfg.domain.easy.vo.EasyConfigItemsOperationsHomeVo>
     * @Description 模糊查询运营配置项
     * <AUTHOR>
     * @Date 2023/12/6 17:05
     **/
    @Override
    public Map<String, Object> blurSearchConfigItems(String blurSearch) {

        List<PlatformItems> list = blurSearch(blurSearch);

        List<TreeNode<Long>> treeNodes = new ArrayList<>();

        Tree<Long> trees = platformMenuService.menuListTreeSingle();

        //转换 VO 后输出
        List<ItemsOperationsHomeVo> collect = list.stream().map(item -> {
            if (item.getValue() == null){
                item.setValue(item.getDefaultValue());
            }
            ItemsOperationsHomeVo vo = new ItemsOperationsHomeVo();
            BeanUtils.copyProperties(item, vo);
            treeToNode(trees, vo.getMenuId(), treeNodes);
            return vo;
        }).collect(Collectors.toList());


        Map<String, Object> map = new HashMap<>();
        map.put("list", collect);
        map.put("trees", TreeUtil.build(treeNodes, -1L));

        return map;
    }

    private static void treeToNode(Tree<Long> trees, Long treeId, List<TreeNode<Long>> treeNodes) {
        Tree<Long> node = TreeUtil.getNode(trees, treeId);
        if (node != null) {
            TreeNode<Long> treeNode = new TreeNode<Long>();
            treeNode.setId(node.getId());
            treeNode.setParentId(node.getParentId());
            treeNode.setName(node.getName());
            treeNode.setExtra(MapUtil.builder("key", (Object) node.get("key")).build());
            treeNode.setWeight(node.getWeight());
            treeNodes.add(treeNode);
            if (node.getParentId() != null) {
                treeToNode(trees, node.getParentId(), treeNodes);
            }
        }
    }

    /**
     * @Param blurSearch 模糊查询字段，支持配置项名称、描述、key
     * @Return java.util.List<com.puree.easycfg.domain.easy.vo.EasyConfigItemsDevelopHomeVo>
     * @Description 模糊查询 开发 配置项
     * <AUTHOR>
     * @Date 2023/12/6 17:12
     **/
    @Override
    public List<ItemsDevelopHomeVo> blurSearchDevelopConfigItems(String blurSearch) {
        List<PlatformItems> list = blurSearch(blurSearch);
        //转换 VO 后输出
        return list.stream().map(item -> {
            ItemsDevelopHomeVo vo = new ItemsDevelopHomeVo();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 开发新增配置项
     * <AUTHOR>
     * @Date 2023/12/6 17:39
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertTemplate(ItemsDevelopSaveDto dto) {
        String key = dto.getKey();
        String name = dto.getName();

        Long count = platformItemsMapper.count(key, name, null);
        if (count > 0) {
            log.warn("配置项 key={},或 配置项 name={} 已存在", key, name);
            throw new IllegalArgumentException("配置项 key 或 配置项 name 已存在");
        }

        PlatformItems item = new PlatformItems();
        BeanUtils.copyProperties(dto, item);
        //追加菜单尾部
        Integer sort = queryMenuMaxSortIncrementerById(item.getMenuId());

        //当前时间
        item.setCreateTime(new Date());
        //初始版本号
        item.setRevision(0);
        //追加菜单尾部
        item.setSort(sort);
        item.setCreateBy(SecurityUtils.getUsername());
        item.setUpdateBy(SecurityUtils.getUsername());
        if (Boolean.FALSE.equals(platformItemsMapper.insert(item) >= 1)) {
            log.warn("新增配置项失败");
            throw new IllegalArgumentException("新增配置项失败");
        }
        //刷新绑定关系
        Boolean refreshPushBind = refreshPushBind(item);
        if (Boolean.FALSE.equals(refreshPushBind)) {
            log.warn("刷新绑定关系");
            throw new IllegalArgumentException("刷新绑定关系");
        }
        //全量更新涉及到的配置项文件
        return refreshNacosConfig(item, null);
    }


    /**
     * @Param items
     * @Return java.lang.Boolean
     * @Description 推送 Nacos
     * <AUTHOR>
     * @Date 2023/12/8 18:56
     **/
    @Override
    public Boolean updateNacosConfigItem(PlatformItems platformItems) {
        //推送中心
        String pushConfigurationCenter = platformItems.getPushConfigurationCenter();
        String value = platformItems.getValue();
        //value 为空则赋值 ""
        if (StringUtils.isEmpty(value)) {
            value = "";
        }
        //为空则不推送
        if (StringUtils.isEmpty(pushConfigurationCenter)) {
            return true;
        }
        String[] split = pushConfigurationCenter.split(",");
        //为空则不推送
        if (split.length == 0) {
            return true;
        }
        //推送
        for (String s : split) {
            if (!nacosService.updateNacosConfigItem(s, platformItems.getKey(), value)) {
                return false;
            }
        }
        return true;
    }


    /**
     * @Param items
     * @Return java.lang.Boolean
     * @Description 刷新绑定关系
     * <AUTHOR>
     * @Date 2023/12/19 12:14
     **/
    @Override
    public Boolean refreshPushBind(PlatformItems platformItems) {
        if (Objects.isNull(platformItems)) {
            throw new IllegalArgumentException("参数错误");
        }
        Long id = platformItems.getId();
        //删除绑定关系
        platformPushBindService.deleteBindingRelationship(id);
        String pushConfigurationCenter = platformItems.getPushConfigurationCenter();
        if (StringUtils.isEmpty(pushConfigurationCenter)) {
            return true;
        }
        String[] split = pushConfigurationCenter.split(",");
        //为空则不处理
        if (split.length == 0) {
            return true;
        }

        //理论上来说，不应该有一个配置项推送到很多文件的需求，如果有则改为批量
        for (String s : split) {
            PlatformPushBind platformPushBind = new PlatformPushBind();
            platformPushBind.setItemId(id);
            platformPushBind.setPushName(s);
            platformPushBind.setCreateTime(new Date());
            platformPushBindService.insertBindingRelationship(platformPushBind);
        }
        return true;
    }

    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Return java.lang.Boolean
     * @Description 刷新 Nacos 配置
     * <AUTHOR>
     * @Date 2023/12/18 19:12
     **/
    @Override
    public Boolean refreshNacosConfig(PlatformItems sourceData, PlatformItems targetData) {

        if (Objects.isNull(sourceData) && Objects.isNull(targetData)) {
            throw new IllegalArgumentException("源配置项数据 与 目标配置项数据 不允许都为空");
        }
        //初始化防止空指针，可以有一个为空
        if (Objects.isNull(sourceData)) {
            sourceData = new PlatformItems();
        }
        if (Objects.isNull(targetData)) {
            targetData = new PlatformItems();
        }

        //源数据推送配置中心
        String sourceDataPushConfigurationCenter = sourceData.getPushConfigurationCenter();
        //修改后的推送配置中心
        String targetDataPushConfigurationCenter = targetData.getPushConfigurationCenter();

        //这里拿到的就是需要被修改到的配置文件
        List<String> push = new ArrayList<>();
        if (!StringUtils.isEmpty(sourceDataPushConfigurationCenter)) {
            List<String> sourceSplit = Arrays.asList(sourceDataPushConfigurationCenter.split(","));
            push.addAll(sourceSplit);
        }
        if (!StringUtils.isEmpty(targetDataPushConfigurationCenter)) {
            List<String> targetSplit = Arrays.asList(targetDataPushConfigurationCenter.split(","));
            push.addAll(targetSplit);
        }

        log.debug("需要写入的 Nacos 配置文件为 push：{}", push);
        //为空则不进行操作
        if (push.isEmpty()) {
            return true;
        }
        //查询相关的配置项
        List<PlatformItemsPushDo> pushItems = platformPushBindService.getBindingRelationship(push);
        //相关的配置项为空，但是 push 非空，则代表可能为该映射关系中最后一条数据

        Map<String, Properties> map = new HashMap<>();

        //对 push 做初始化，防止这个配置文件只有他一个配置项的存在导致无法删除
        for (String s : push) {
            map.put(s, new Properties());
        }
        //如果相关的配置项为空，则不会进行替换
        for (PlatformItemsPushDo item : pushItems) {
            //实际名称
            String pushName = item.getPushName();

            String key = item.getKey();
            String value = item.getValue() == null? item.getDefaultValue() : item.getValue();

            Properties properties = map.computeIfAbsent(pushName, k -> new Properties());
            properties.put(key, value == null ? "" : value);
        }

        //替换，即使是失败，下次也会刷掉
        map.forEach(nacosService::FullReplacementNacosConfig);
        return true;
    }

    /**
     * @Param items
     * @Return java.lang.Boolean
     * @Description 删除 Nacos 配置项
     * <AUTHOR>
     * @Date 2023/12/8 19:05
     **/
    @Override
    public Boolean deleteNacosConfigItem(PlatformItems platformItems) {
        if (Objects.isNull(platformItems)) {
            throw new IllegalArgumentException("参数错误");
        }
        //推送中心映射
        String pushConfigurationCenter = platformItems.getPushConfigurationCenter();
        //为空则不执行
        if (StringUtils.isEmpty(pushConfigurationCenter)) {
            return true;
        }
        String[] split = pushConfigurationCenter.split(",");
        //为空则不执行
        if (split.length == 0) {
            return true;
        }
        //推送删除命令
        for (String s : split) {
            if (!nacosService.deleteNacosConfigItem(s, platformItems.getKey())) {
                return false;
            }
        }
        return true;
    }

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 开发修改配置项
     * <AUTHOR>
     * @Date 2023/12/7 14:09
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTemplate(ItemsDevelopUpdateDto dto) throws Exception {

        String name = dto.getName();
        Long id = dto.getId();

        //判断是否重复 Name
        Long count = platformItemsMapper.count(null, name, id);
        if (count > 0) {
            log.warn("配置项 name={} 已存在", name);
            throw new IllegalArgumentException("配置项 name 已存在");
        }

        PlatformItems item = new PlatformItems();
        BeanUtils.copyProperties(dto, item);

        //拿到当前数据
        PlatformItems queryById = getCurrentVersionData(item);

        //记录历史数据
        item.setUpdateBy(SecurityUtils.getUsername());
        platformHistoryService.developInsertHistory(queryById, item);

        Boolean update = updateTemplate(item);
        if (Boolean.FALSE.equals(update)) {
            log.warn("修改配置项失败");
            throw new IllegalArgumentException("修改配置项失败");
        }
        //刷新绑定关系
        Boolean refreshPushBind = refreshPushBind(item);
        if (Boolean.FALSE.equals(refreshPushBind)) {
            log.warn("刷新绑定关系");
            throw new IllegalArgumentException("刷新绑定关系");
        }
        //全量替换
        return refreshNacosConfig(item, queryById);
    }

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 运营修改配置项
     * <AUTHOR>
     * @Date 2023/12/7 17:45
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateItems(ItemsOperationsUpdateDto dto) {
        PlatformItems item = new PlatformItems();
        BeanUtils.copyProperties(dto, item);

        //拿到当前数据
        PlatformItems queryById = getCurrentVersionData(item);
        item.setUpdateBy(SecurityUtils.getUsername());
        //记录历史数据
        platformHistoryService.operationsInsertHistory(queryById, item);
        //医院管理端调用没有版本信息
        if (item.getRevision() == null) {
            item.setRevision(queryById.getRevision());
        }
        Boolean update = updateValue(item);
        if (Boolean.FALSE.equals(update)) {
            log.warn("修改配置项失败");
            throw new IllegalArgumentException("修改配置项失败");
        }
        //刷新绑定关系
        //全量替换
        return refreshNacosConfig(item, queryById);
    }

    /**
     * 修改模版参数
     * @param item 模版数据
     * @return java.lang.Boolean
     */
    @Override
    public Boolean updateTemplate(PlatformItems item) {
        if (Objects.isNull(item)) {
            throw new IllegalArgumentException("参数错误");
        }
        Integer revision = item.getRevision();
        item.setUpdateTime(new Date());
        //判断如果修改失败，则抛异常方便回滚
        if (Boolean.FALSE.equals(platformItemsMapper.updateTemplate(item, revision) >= 1)) {
            log.warn("并发冲突，请稍后重试");
            throw new RuntimeException("并发冲突，请稍后重试");
        }
        return true;
    }

    /**
     * 修改配置项值
     * @param item 配置项数据
     * @return java.lang.Boolean
     */
    public Boolean updateValue(PlatformItems item) {
        if (Objects.isNull(item)) {
            throw new IllegalArgumentException("参数错误");
        }
        Integer revision = item.getRevision();
        item.setUpdateTime(new Date());
        //判断如果修改失败，则抛异常方便回滚
        if (Boolean.FALSE.equals(platformItemsMapper.updateValue(item, revision) >= 1)) {
            log.warn("并发冲突，请稍后重试");
            throw new RuntimeException("并发冲突，请稍后重试");
        }
        return true;
    }

    /**
     * @Param id
     * @Param revision 版本号
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 获得 该 ID 下当前版本号数据
     * <AUTHOR>
     * @Date 2023/12/7 17:52
     **/
    @Override
    public PlatformItems getCurrentVersionData(PlatformItems item) {
        //拿到当前版本号数据
        PlatformItems queryById = platformItemsMapper.queryItem(item);

        if (Objects.isNull(queryById)) {
            log.warn("配置项不存在，或配置项已被修改:EasyConfigItems.id={},revision={},key={}", item.getId(), item.getRevision(), item.getKey());
            throw new IllegalArgumentException("配置项不存在，或配置项已被修改");
        }

        return queryById;
    }

    /**
     * @Param id
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 ID 查询
     * <AUTHOR>
     * @Date 2023/12/8 14:39
     **/
    @Override
    public PlatformItems getById(Long id) {
        //根据 ID 查询
        PlatformItems queryById = platformItemsMapper.queryById(id);
        if (Objects.isNull(queryById)) {
            log.warn("配置项不存在，或配置项已被删除:id={}", id);
            throw new IllegalArgumentException("配置项不存在，或配置项已被删除");
        }

        //设置默认值
        if (queryById.getValue() == null){
            queryById.setValue(queryById.getDefaultValue());
        }

        return queryById;
    }

    /**
     * @Param key
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 key 查询
     * <AUTHOR>
     * @Date 2024/1/10 17:29
     **/
    @Override
    public PlatformItems getByKey(String key) {
        //根据 ID 查询
        PlatformItems queryById = platformItemsMapper.getByKey(key);

        if (Objects.isNull(queryById)) {
            log.warn("配置项不存在，或配置项已被删除:key={}", key);
            throw new IllegalArgumentException("配置项不存在，或配置项已被删除");
        }

        return queryById;
    }

    /**
     * @Param id
     * @Param itemsName 需要 ID 和 名称同时匹配才能删除
     * @Return java.lang.Boolean
     * @Description 开发删除配置项
     * <AUTHOR>
     * @Date 2023/12/7 18:13
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTemplate(Long id, String itemsName) {
        //方便删除 nacos 的
        PlatformItems byId = getById(id);
        if (StringUtils.isEmpty(itemsName)) {
            throw new IllegalArgumentException("配置项名称不正确");
        }

        if (Boolean.FALSE.equals(platformItemsMapper.deleteByIdAndItemsName(id, itemsName) >= 1)) {
            log.warn("配置项不存在、配置项名称不正确，或配置项已被删除:id={},itemsName={}", id, itemsName);
            throw new IllegalArgumentException("配置项不存在、配置项名称不正确，或配置项已被删除");
        }
        //删除绑定关系
        List<PlatformPushBind> bindingList = platformPushBindService.getBindingList(id);
        if (!bindingList.isEmpty()) {
            Boolean deleteBindingRelationship = platformPushBindService.deleteBindingRelationship(id);
            if (Boolean.FALSE.equals(deleteBindingRelationship)) {
                log.warn("刷新绑定关系失败 ");
                throw new IllegalArgumentException("刷新绑定关系");
            }
        }
        return refreshNacosConfig(null, byId);
    }


    /**
     * @Param id
     * @Param sort
     * @Param menuId
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改 菜单 或 排序
     * <AUTHOR>
     * @Date 2023/12/7 18:48
     **/
    @Override
    public Boolean updateTemplateSortOrMenuId(Long id, Integer sort, Long menuId) {
        //同时为空或者同时不为空都为参数错误，因为排序和菜单只能修改一个，不允许同时修改
        if ((sort == null && menuId == null) || (sort != null && menuId != null)) {
            throw new IllegalArgumentException("参数错误");
        }
        if (sort != null) {
            //修改排序
            return updateTemplateSort(id, sort);
        }
        //修改父级菜单
        return updateTemplateMenuId(id, menuId);
    }

    /**
     * @Param id
     * @Param sort
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改排序
     * <AUTHOR>
     * @Date 2023/12/7 18:48
     **/
    @Override
    public Boolean updateTemplateSort(Long id, Integer sort) {
        //拿菜单 ID
        PlatformItems queryById = getById(id);

        Integer sourceSort = queryById.getSort();
        if (sourceSort == sort) {
            return true;
        } else if (sort > sourceSort) {
            //从后向前移动
            platformItemsMapper.updateSortForward(queryById.getMenuId(), sort);
        } else {
            //从前向后移动
            platformItemsMapper.updateSortMoveBack(queryById.getMenuId(), sort);
        }
        //大于 sort 值的后移
        //不需要锁，低概率事件
        if (Boolean.FALSE.equals(platformItemsMapper.updateSort(id, sort) >= 1)) {
            log.warn("修改失败，请稍后重试");
            throw new RuntimeException("修改失败，请稍后重试");
        }
        //修改 sort
        return platformItemsMapper.resetSort(queryById.getMenuId()) >= 1;
    }

    /**
     * @Param id
     * @Param menuId
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改菜单
     * <AUTHOR>
     * @Date 2023/12/7 18:48
     **/
    @Override
    public Boolean updateTemplateMenuId(Long id, Long menuId) {
        //拿菜单 ID
        PlatformItems queryById = getById(id);

        if (Objects.equals(queryById.getMenuId(), menuId)) {
            log.warn("源菜单与目标菜单相同，禁止修改");
            throw new IllegalArgumentException("源菜单与目标菜单相同，禁止修改");
        }
        //通过 ID 查询菜单最大排序 +1
        Integer sort = queryMenuMaxSortIncrementerById(menuId);
        return platformItemsMapper.updateMenuId(id, menuId, sort) >= 1;
    }

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序,如果该菜单下没有节点，则返回 0
     * <AUTHOR>
     * @Date 2023/12/8 15:41
     **/
    @Override
    public Integer queryMenuMaxSortById(Long menuId) {
        //通过 ID  查询菜单最大排序
        Integer sort = platformItemsMapper.queryMenuMaxSortById(menuId);
        if (sort == null) {
            sort = 0;
        }
        return sort;
    }

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序 +1
     * <AUTHOR>
     * @Date 2023/12/8 15:51
     **/
    @Override
    public Integer queryMenuMaxSortIncrementerById(Long menuId) {
        return queryMenuMaxSortById(menuId) + 1;
    }

    /**
     * @Param
     * @Return java.util.Collection<java.lang.String>
     * @Description 获取别名列表
     * <AUTHOR>
     * @Date 2023/12/18 15:05
     **/
    @Override
    public Collection<Map<String, String>> getAliasMapStringKeyList() {
        Collection<String> keyList = nacosPushConfigurationProperties.getAliasMap().keySet();
        return keyList.stream().map(item -> {
            Map<String, String> map = new HashMap<>();
            map.put("value", item);
            map.put("key", nacosPushConfigurationProperties.getAliasMap().get(item));
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * @Param menuId
     * @Return java.lang.Long
     * @Description 获取菜单下子节点数量
     * <AUTHOR>
     * @Date 2023/12/18 16:28
     **/
    @Override
    public Long childItemsCount(Long menuId) {
        return platformItemsMapper.childItemsCount(menuId);
    }


    @Override
    public ItemDescriptionVO getItemsDescribeByKey(String key) {
        PlatformItems platformItems = platformItemsMapper.getByKey(key);
        if (Objects.isNull(platformItems)) {
            throw new IllegalArgumentException("配置项不存在 key = " + key);
        }
        ItemDescriptionVO itemDescriptionVO = new ItemDescriptionVO();
        BeanUtils.copyProperties(platformItems, itemDescriptionVO);
        itemDescriptionVO.setRestraint(JsonUtil.fromJson(platformItems.getRestraint(), Object.class));
        return itemDescriptionVO;
    }

    @Override
    public List<TemplateVO> selectItemList(Menu menu, String roles) {
        // 获取菜单下所有模板基础信息
        List<PlatformItems> platformItems = platformItemsMapper.selectItemList(menu.getId());
        List<TemplateVO> templateVOS = new ArrayList<>(platformItems.size());
        platformItems.forEach(e -> {
            //HTTP访问 0 为关闭，1为开启
            if (Boolean.FALSE.equals(e.getWhetherOpenHttpApi())) {
                return;
            }
            //匿名访问 0 为关闭，1为开启
            if (Boolean.FALSE.equals(e.getAnonymousAccess())) {
                if (!authentication(e.getAccessibleRoles(), roles)) {
                    return;
                }
            }
            TemplateVO templateVO = new TemplateVO();
            BeanUtils.copyProperties(e, templateVO);
            templateVOS.add(templateVO);
        });
        return templateVOS;
    }

    @Override
    public Map<String, Object> selectItemMap(Menu menu, String roles) {
        // 获取菜单下所有模板基础信息
        List<PlatformItems> platformItems = platformItemsMapper.selectItemList(menu.getId());
        Map<String, Object> map = new HashMap<>(platformItems.size());
        platformItems.forEach(e -> {
            //HTTP访问 0 为关闭，1为开启
            if (Boolean.FALSE.equals(e.getWhetherOpenHttpApi())) {
                return;
            }
            //匿名访问 0 为关闭，1为开启
            if (Boolean.FALSE.equals(e.getAnonymousAccess())) {
                if (!authentication(e.getAccessibleRoles(), roles)) {
                    return;
                }
            }
            // 没设置值使用默认值
            if (e.getValue() == null) {
                e.setValue(e.getDefaultValue());
            }
            Object value = null;
            if (JsonUtil.isJsonContentType(e.getResponseContentType())) {
                value = JsonUtil.fromJson(e.getValue(), Object.class);
            } else {
                value = e.getValue();
            }
            map.put(e.getKey(), value);
        });
        return map;
    }

    @Override
    public Map<String, Object> selectItemMapAdmin(Menu menu) {
        // 获取菜单下所有模板基础信息
        List<PlatformItems> platformItems = platformItemsMapper.selectItemList(menu.getId());
        Map<String, Object> map = new HashMap<>(platformItems.size());
        platformItems.forEach(e -> {
            // 没设置值使用默认值
            if (e.getValue() == null) {
                e.setValue(e.getDefaultValue());
            }
            Object value = null;
            if (JsonUtil.isJsonContentType(e.getResponseContentType())) {
                value = JsonUtil.fromJson(e.getValue(), Object.class);
            } else {
                value = e.getValue();
            }
            map.put(e.getKey(), value);
        });
        return map;
    }

    @Override
    public List<TemplateVO> selectItemListAdmin(Menu menu) {
        // 获取菜单下所有模板基础信息
        List<PlatformItems> platformItems = platformItemsMapper.selectItemList(menu.getId());
        List<TemplateVO> templateVOS = new ArrayList<>(platformItems.size());
        platformItems.forEach(e -> {
            TemplateVO templateVO = new TemplateVO();
            BeanUtils.copyProperties(e, templateVO);
            templateVOS.add(templateVO);
        });
        return templateVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateItems(List<ItemsOperationsUpdateDto> items) {
        items.forEach(this::updateItems);
    }

    @Override
    public Boolean updateTemplateDefaultValue(TemplateDefaultValueDTO dto) throws Exception {
        //拿到当前数据
        PlatformItems sourcePlatformItems = platformItemsMapper.queryById(dto.getId());

        PlatformItems platformItems = new PlatformItems();
        BeanUtils.copyProperties(dto, platformItems);
        platformItems.setUpdateTime(new Date());

        if(Boolean.FALSE.equals(platformItemsMapper.updateTemplateDefaultValue(platformItems) > 0)) {
            log.warn("配置项不存在、配置项名称不正确，或配置项已被删除:id={}", dto.getId());
            throw new IllegalArgumentException("配置项不存在、配置项名称不正确，或配置项已被删除");
        }
        //更新nacos配置
        Boolean b = refreshNacosConfig(sourcePlatformItems, platformItems);
        if (Boolean.FALSE.equals(b)) {
            log.warn("刷新nacos配置失败");
            throw new IllegalArgumentException("刷新nacos配置失败");
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean developPhysicalDelete(Long id, String itemsName) {

        if (Boolean.FALSE.equals(platformItemsMapper.physicalDeleteByIdAndItemsName(id, itemsName) >= 1)) {
            log.warn("配置项不存在、配置项名称不正确，或配置项已被删除:id={},itemsName={}", id, itemsName);
            throw new IllegalArgumentException("配置项不存在、配置项名称不正确，或配置项已被删除");
        }

        return true;
    }


    public boolean authentication(String accessibleRoles, String roles) {
        //如果为无角色限制，则直接返回 true
        if (StringUtils.isEmpty(accessibleRoles)) {
            return true;
        }
        //解密
        String header = PropertiesUtil.headerDecryptStr(roles);
        log.debug("header:{}", header);
        log.debug("roles:{}", roles);
        if (StringUtils.isEmpty(header)) {
            return false;
        }
        List<String> sourceRoles = Arrays.asList(header.split(","));
        List<String> targetRoles = Arrays.asList(accessibleRoles.split(","));
        HashSet<String> set = new HashSet<>();
        //取交集，如果交集数量少于两者之和，则说明有交集
        set.addAll(sourceRoles);
        set.addAll(targetRoles);
        int size = set.size();

        int sourceSize = sourceRoles.size();
        int targetSize = targetRoles.size();

        return size != sourceSize + targetSize;
    }
}
