package com.puree.hospital.setting.service.platform;



import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.vo.platform.ItemListByMenuKey;

import java.util.List;

/**
 * @ClassName EasyConfigHttpApiService
 * <AUTHOR>
 * @Description http api
 * @Date 2024/1/10 17:15
 * @Version 1.0
 */
public interface PlatformConfigHttpApiService {


    /**
     * @Param menuId
     * @Return java.util.Map<java.lang.String,java.lang.String>
     * @Description 获取菜单下所有的 Key / Name
     * <AUTHOR>
     * @Date 2024/3/22 17:30
     **/
    List<ItemListByMenuKey> getItemListByMenuKey(TemplateQuery templateQuery);


    /**
     * 允许角色访问校验
     *
     * @param allowedRoles 允许角色 list
     * @param roles   当前用户角色
     * @return boolean
     */
    boolean authentication(String allowedRoles, String roles);

    String processorJsonData(String responseContentType, String value, Boolean wrap);
}
