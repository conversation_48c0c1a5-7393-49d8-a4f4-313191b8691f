package com.puree.hospital.setting.controller.hospital;

import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.setting.annotation.ValidateAuth;
import com.puree.hospital.setting.constants.RequestConstants;
import com.puree.hospital.setting.controller.BaseHttpApiController;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.dto.ResponseFormat;
import com.puree.hospital.setting.domain.dto.hospital.ItemUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.domain.entity.hospital.HospitalItem;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.vo.ItemDescriptionVO;
import com.puree.hospital.setting.domain.vo.hospital.TemplateVO;
import com.puree.hospital.setting.service.hospital.HospitalItemsService;
import com.puree.hospital.setting.service.hospital.HospitalMenuService;
import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import com.puree.hospital.setting.utils.JsonUtil;
import com.puree.hospital.setting.utils.PropertiesUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 医院配置 HTTP API 请求controller
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@RestController
@AllArgsConstructor
@RequestMapping("/easy-config")
@Slf4j
public class HttpApiController extends BaseHttpApiController {

    private final HospitalItemsService hospitalItemsService;
    private final HospitalTemplateService hospitalTemplateService;
    private final HospitalMenuService hospitalMenuService;

    // <editor-fold desc="配置项查询接口">

    /**
     * 医院配置 HTTP API
     *
     * @param key        配置项key
     * @param hospitalId 医院ID todo 防止当前医院请求到别的医院的配置 需要修改网关代码
     * @param tql        tree query language
     * @param offset     表格的行偏移量
     * @param limit      表格的行数
     * @param format    JSON源的输出格式，json/ajax/csv/excel 等
     * @param template   根据模板key查询到的医院配置模板
     */
    @SneakyThrows
    @ValidateAuth(ValidateAuth.Scope.HOSPITAL_ITEM)
    @GetMapping("/hospital/{key}")
    public ResponseEntity<?> getItemValueByKey(@PathVariable(value = "key") @NotBlank String key,
                                               @RequestParam(value = "hospitalId") @Pattern(regexp = "\\d+") Long hospitalId,
                                               @RequestParam(value = "tql", required = false) String tql,
                                               @RequestParam(value = "offset", defaultValue = "0") int offset,
                                               @RequestParam(value = "limit", defaultValue = "0") int limit,
                                               @RequestParam(value = "format", required = false) ResponseFormat format,
                                               HospitalTemplate template) {
        //  获取配置值
        HospitalItem hospitalItem = hospitalItemsService.getHospitalItemByTemplateId(template.getId(), hospitalId);
        String value = (hospitalItem == null || hospitalItem.getItemValue() == null) ? template.getDefaultValue() : hospitalItem.getItemValue();
        return processResult(template, format, tql, offset, limit, value);
    }




    /**
     * 获取医院配置项的元数据
     *
     * @param key 配置项key
     * @param template 根据模板key查询到的医院配置模板
     * @return {@link R }
     */
    @ValidateAuth(ValidateAuth.Scope.HOSPITAL_ITEM)
    @GetMapping("hospital-describe/{key}")
    public ResponseEntity<ItemDescriptionVO> getHospitalItemDescription(@PathVariable(value = "key") @NotBlank String key,
                                           HospitalTemplate template) {
        ItemDescriptionVO itemDescriptionVO = new ItemDescriptionVO();
        BeanUtils.copyProperties(template, itemDescriptionVO);
        itemDescriptionVO.setRestraint(JsonUtil.fromJson(template.getRestraint(), Object.class));
        return ResponseEntity.ok(itemDescriptionVO);
    }

    // </editor-fold>

    // <editor-fold desc="菜单项查询接口">

    /**
     * 获取医院配置菜单下的配置项列表
     *
     * @param key 菜单键
     * @param hospitalId 医院ID
     * @param roles 角色
     * @param menu 根据菜单key查询到的菜单元数据
     * @return {@link R }
     */
    @ValidateAuth(ValidateAuth.Scope.HOSPITAL_MENU)
    @GetMapping("hospital-list/{key}")
    public ResponseEntity<List<TemplateVO>> getHospitalMenuList(@PathVariable(value = "key") @NotBlank String key,
                                                                @RequestParam(value = "hospitalId") @Pattern(regexp = "\\d+") Long hospitalId,
                                                                @RequestHeader(value = PropertiesUtil.AUTHENTICATION_HEADER, required = false) String roles,
                                                                Menu menu) {
        List<HospitalTemplate> templateList = hospitalTemplateService.getTemplateList(menu, hospitalId, roles);
        List<TemplateVO> templateVOList = new ArrayList<>();
        templateList.forEach(e->{
            TemplateVO templateVO = new TemplateVO();
            BeanUtils.copyProperties(e, templateVO);
            templateVOList.add(templateVO);
        });
        return ResponseEntity.ok(templateVOList);
    }

    /**
     * 获取医院配置菜单下的配置项字典
     *
     * @param key 菜单键
     * @param hospitalId 医院ID
     * @param roles 角色
     * @param menu 根据菜单key查询到的菜单元数据
     * @return {@link R }
     */
    @ValidateAuth(ValidateAuth.Scope.HOSPITAL_MENU)
    @GetMapping("hospital-map/{key}")
    public ResponseEntity<Map<String, Object>> getHospitalMap(@PathVariable(value = "key") @NotBlank String key,
                                                              @RequestParam(value = "hospitalId") @Pattern(regexp = "\\d+") Long hospitalId,
                                                              @RequestHeader(value = PropertiesUtil.AUTHENTICATION_HEADER, required = false) String roles,
                                                              Menu menu) {
        return ResponseEntity.ok(hospitalTemplateService.getItemsAsMap(menu, roles, hospitalId));
    }

    // </editor-fold>



    /**
     * 医院配置 HTTP API 供医院运营后台接口调用
     *
     * @param key        配置项key
     * @param hospitalId 医院ID todo 防止当前医院请求到别的医院的配置 需要修改网关代码
     * @param tql        tree query language
     * @param offset     表格的行偏移量
     * @param limit      表格的行数
     * @param format    JSON源的输出格式，json/ajax/csv/excel 等
     */
    @SneakyThrows
    @GetMapping("/hospital/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:select")
    public ResponseEntity<?> getItemValueByKeyAdmin(@PathVariable(value = "key") @NotBlank String key,
                                                    @RequestParam(value = "hospitalId") @Pattern(regexp = "\\d+") Long hospitalId,
                                                    @RequestParam(value = "tql", required = false) String tql,
                                                    @RequestParam(value = "offset", defaultValue = "0") int offset,
                                                    @RequestParam(value = "limit", defaultValue = "0") int limit,
                                                    @RequestParam(value = "format", required = false) ResponseFormat format) {
        HospitalTemplate template = hospitalTemplateService.getTemplate(key);
        //  获取配置值
        HospitalItem hospitalItem = hospitalItemsService.getHospitalItemByTemplateId(template.getId(), hospitalId);
        String value = hospitalItem == null ? template.getDefaultValue() : hospitalItem.getItemValue();
        return processResult(template, format, tql, offset, limit, value);
    }


    /**
     * 获取医院配置项的元数据 供医院运营后台接口调用
     *
     * @param key 配置项key
     * @return {@link R }
     */
    @GetMapping("hospital-describe/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:select")
    public ResponseEntity<ItemDescriptionVO> getHospitalItemDescriptionAdmin(@PathVariable(value = "key") @NotBlank String key) {
        HospitalTemplate template = hospitalTemplateService.getTemplate(key);
        ItemDescriptionVO itemDescriptionVO = new ItemDescriptionVO();
        BeanUtils.copyProperties(template, itemDescriptionVO);
        itemDescriptionVO.setRestraint(JsonUtil.fromJson(template.getRestraint(), Object.class));
        return ResponseEntity.ok(itemDescriptionVO);
    }


    /**
     * 获取医院配置菜单下的配置项列表 供医院运营后台接口调用
     *
     * @param key 菜单键
     * @param hospitalId 医院ID
     * @return {@link R }
     */
    @GetMapping("hospital-list/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:select")
    public ResponseEntity<List<TemplateVO>> getHospitalMenuListAdmin(@PathVariable(value = "key") @NotBlank String key,
                                                                     @RequestParam(value = "hospitalId") Long hospitalId) {
        Menu menu = hospitalMenuService.queryMenu(key);
        List<HospitalTemplate> hospitalTemplates = hospitalTemplateService.getTemplateListAdmin(menu, hospitalId);
        List<TemplateVO> templateVO = new ArrayList<>(hospitalTemplates.size());
        hospitalTemplates.forEach(e -> {
            TemplateVO menuTemplateListVo = new TemplateVO();
            BeanUtils.copyProperties(e, menuTemplateListVo);
            templateVO.add(menuTemplateListVo);
        });
        return ResponseEntity.ok(templateVO);
    }


    /**
     * 获取医院配置菜单下的配置项字典 供医院运营后台接口调用
     *
     * @param key 菜单键
     * @param hospitalId 医院ID
     * @return {@link R }
     */
    @GetMapping("hospital-map/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:select")
    public ResponseEntity<Map<String, Object>> getHospitalMap(@PathVariable(value = "key") @NotBlank String key,
                                                              @RequestParam(value = "hospitalId")  Long hospitalId) {
        Menu menu = hospitalMenuService.queryMenu(key);
        return ResponseEntity.ok(hospitalTemplateService.getItemsAsMapAdmin(menu,hospitalId));
    }

    /**
     * 获取医院配置菜单下的配置项字典 供医院运营后台接口调用
     *
     * @return {@link R }
     */
    @PutMapping("hospital-map/admin")
    @PreAuthorize(hasPermi = "business:setting:hospital:items:update")
    public ResponseEntity<?> updateHospitalMap(@RequestParam("hospitalId") Long hospitalId,
                                               @RequestBody List<ItemUpdateDto> items,
                                               @RequestHeader(value = RequestConstants.DETAILS_USERNAME, required = false) String username) {
        items.forEach(e->{
            e.setUsername(username);
            e.setHospitalId(hospitalId);
        });
        hospitalTemplateService.updateHospitalItems(items);
        return ResponseEntity.ok(R.ok("更新成功"));
    }
}
