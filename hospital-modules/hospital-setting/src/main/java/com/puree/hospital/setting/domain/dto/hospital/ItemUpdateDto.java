package com.puree.hospital.setting.domain.dto.hospital;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * @ClassName EasyConfigItemsOperationsUpdateDto
 * <AUTHOR>
 * @Description 运营修改配置项 dto
 * @Date 2023/12/7 17:36
 * @Version 1.0
 */
@Data
public class ItemUpdateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置模板 ID
     */
    private Long id;
    /**
     * 配置项 value
     */
    @JsonProperty("value")
    private String itemValue;
    /**
     * 配置项 value 版本号
     */
    private Integer itemRevision;
    /**
     * 版本号
     */
    private Integer revision;
    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 配置项 key
     */
    private String key;

    /**
     * 操作人
     */
    private String username;
}
