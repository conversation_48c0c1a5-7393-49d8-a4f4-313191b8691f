package com.puree.hospital.setting.notify.notifier;

import com.puree.hospital.setting.model.event.SettingChangeEvent;

/**
 * <p>
 * 配置变更通知接口
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/22 11:23
 */
public interface IConfigChangeNotifier {

    /**
     * 通知配置变更
     * @param event 配置变更事件
     */
    void notifyChanges(SettingChangeEvent event);

    /**
     * 获取通知器名称
     * @return 通知器名称
     */
    String getProcessName();
}
