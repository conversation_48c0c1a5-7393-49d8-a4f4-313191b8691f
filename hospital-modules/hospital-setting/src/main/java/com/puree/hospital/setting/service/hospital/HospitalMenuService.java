package com.puree.hospital.setting.service.hospital;


import cn.hutool.core.lang.tree.Tree;
import com.puree.hospital.setting.domain.dto.MenuSaveDto;
import com.puree.hospital.setting.domain.entity.Menu;


import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 医院业务配置菜单
 * <AUTHOR>
 * @date 2024/09/23
 */
public interface HospitalMenuService {


    Object insert(MenuSaveDto dto, HttpServletRequest request);


    /**查询当前菜单最大sort
     *
     * @param menuId
     * @return {@link Integer }
     */
    Integer queryMenuMaxSortById(Long menuId);

    /**
     * @param dto
     * @param request
     * @return {@link Object }
     */
    Boolean update(MenuSaveDto dto, HttpServletRequest request);

    Boolean delete(Long id, String menuName, HttpServletRequest request);

    /**
     * 获取菜单列表树
     *
     * @return {@link List }<{@link Tree }<{@link Long }>>
     */
    List<Tree<Long>> menuListTree();

    Menu getMenuInfoById(Long id);

    /**
     * 更新排序或父 ID
     *
     * @param id       身份证
     * @param sort     排序
     * @param parentId 父 ID
     * @return {@link Boolean }
     */
    Boolean updateSortOrParentId(Long id, Integer sort, Long parentId);

    Tree<Long> menuListTreeSingle();


    /**
     * 查询菜单信息
     *
     * @param menuKey 菜单键
     * @return {@link Menu }
     */
    Menu queryMenu(String menuKey);


}
