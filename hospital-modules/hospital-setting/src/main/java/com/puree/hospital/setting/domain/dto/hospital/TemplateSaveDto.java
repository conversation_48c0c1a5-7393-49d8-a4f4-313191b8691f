package com.puree.hospital.setting.domain.dto.hospital;

import com.puree.hospital.setting.constants.ItemTypeEnum;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 医院配置模板 新增dto
 * <AUTHOR>
 * @date 2024/09/24
 */
@Data
public class TemplateSaveDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * key
     * 新增时，未删除的数据中 key 不能重复
     */
    @NotBlank(message = "key 不能为空")
    @Size( max = 64, message = "参数 key 不能超过 64 个字符")
    private String key;
    /**
     * 菜单类型ID
     */
    @NotNull(message = "菜单类型 ID 不能为空")
    private Long menuId;
    /**
     * 缓存分钟数
     */
    @Min(value = 0, message = "参数不正确")
    private Integer cacheMinutes;
    /**
     * 名称
     * 新增时，未删除的数据中 名称 不能重复
     */
    @NotBlank(message = "名称 不能为空")
    @Size( max = 64, message = "参数 名称 不能超过 64 个字符")
    private String name;
    /**
     * 描述
     */
    @Size( max = 255, message = "参数 描述 不能超过 255 个字符")
    private String remark;
    /**
     * 排序字段
     * 新增时，默认在尾部追加排序
     */
    private Integer sort;
    /**
     * 类型;前端定义值是多少
     */
    @NotNull(message = "类型 不能为空")
    private String type;
    /**
     * 约束
     */
    @NotBlank(message = "约束 不能为空")
    private String restraint;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean enableHttp;

    /**
     * 是否开启Redis缓存;0 为关闭，1为开启
     */
    private Boolean enableCache;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean allowAnonymous;
    /**
     * 响应内容类型
     */
    private String contentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    @Size( max = 1024, message = "参数 可访问角色 不能超过 1024 个字符")
    private String allowedRoles;
    /**
     * 可显示该配置模板的医院id;医院id与医院id之间用,号隔开
     */
    @Size( max = 1024, message = "参数 可访问角色 不能超过 1024 个字符")
    private String whiteList;

    /**
     * 返回值是否包装成ajax类型 只在返回类型为json时生效 1 包装 0 不包装
     */
    private Boolean isWrap;

    /**
     * 过滤模式;
     * @see com.puree.hospital.setting.constants.FilterModeEnum
     */
    private String filterMode;

    /**
     * 过滤列表;
     * 多个值用,号隔开
     */
    private String filterList;

}
