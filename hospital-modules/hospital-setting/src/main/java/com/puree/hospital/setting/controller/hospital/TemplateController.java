package com.puree.hospital.setting.controller.hospital;
import com.github.pagehelper.PageInfo;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.setting.config.TemplatePhysicalConfig;
import com.puree.hospital.setting.constants.ItemTypeEnum;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.dto.TemplateDefaultValueDTO;
import com.puree.hospital.setting.domain.dto.hospital.TemplateSaveDto;
import com.puree.hospital.setting.domain.dto.hospital.TemplateUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.vo.hospital.ItemsOperationsHomeVo;
import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 平台端医院配置模板管理
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@RestController
@RequestMapping("easy-config/platform-admin/template")
public class TemplateController {

    @Autowired
    private TemplatePhysicalConfig templatePhysicalConfig;

    @Autowired
    private HospitalTemplateService hospitalTemplateService;

    @PostMapping
    @PreAuthorize(hasPermi = "business:setting:hospital:template:insert")
    public R<Boolean> insert(@RequestBody @Validated TemplateSaveDto dto, HttpServletRequest request) {
        return R.ok(hospitalTemplateService.insert(dto, request));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize(hasPermi = "business:setting:hospital:template:delete")
    public R<Boolean> delete(@PathVariable(value = "id") Long id,
                    @RequestParam("itemsName") String itemsName, HttpServletRequest request) {
        return R.ok(hospitalTemplateService.delete(id, itemsName, request));
    }

    @DeleteMapping("/physical/{id}")
    @PreAuthorize(hasPermi = "business:setting:hospital:template:delete")
    public R physicalDelete(@PathVariable(value = "id") Long id,@RequestParam("itemsName") String itemsName) {
        if (!templatePhysicalConfig.isPhysicalDeletion()) {
            throw new IllegalArgumentException("当前环境禁止物理删除，请与系统管理员联系！");
        }
        return R.ok(hospitalTemplateService.physicalDelete(id,itemsName));
    }

    @PutMapping
    @PreAuthorize(hasPermi = "business:setting:hospital:template:update")
    public R<Boolean> update(@RequestBody @Validated TemplateUpdateDto dto, HttpServletRequest request) {
        return R.ok(hospitalTemplateService.update(dto, request));
    }

    /**
     *  修改模版默认值
     * @param dto {@link TemplateDefaultValueDTO}
     * @return {@link R}
     */
    @PutMapping("/default-value")
    @PreAuthorize(hasPermi = "business:setting:hospital:template:update")
    public R updateDefaultValue(@RequestBody @Validated TemplateDefaultValueDTO dto) {
        return R.ok(hospitalTemplateService.updateDefaultValue(dto));
    }

    /**
     * 模糊搜索
     * @param blurSearch 模糊搜索关键字
     * @return {@link R }
     */
    @GetMapping("/list")
    @PreAuthorize(hasPermi = "business:setting:hospital:template:select")
    public R<Object> list(@RequestParam(required = false, name = "blurSearch") String blurSearch) {
        return R.ok(hospitalTemplateService.list(blurSearch));
    }

    /**
     * 获取医院配置模板列表
     * @param query 查询条件
     * @return {@link R }
     */
    @GetMapping("/list-by-menu-key")
    @PreAuthorize(hasPermi = "business:setting:hospital:template:select")
    public R listByMenuKey(TemplateQuery query) {
        Menu menu = new Menu();
        menu.setMenuKey(query.getMenuKey());
        List<HospitalTemplate> templateListAdmin = hospitalTemplateService.getTemplateListAdmin(menu, query);
        PageInfo<HospitalTemplate> pageInfo = new PageInfo<>(templateListAdmin);
        return R.ok(pageInfo);
    }


    @GetMapping("/{id}")
    @PreAuthorize(hasPermi = "business:setting:hospital:template:update")
    public R<ItemsOperationsHomeVo> getById(@PathVariable("id") Long id) {
        HospitalTemplate hospitalTemplate = hospitalTemplateService.getById(id);
        ItemsOperationsHomeVo templateVO = new ItemsOperationsHomeVo();
        BeanUtils.copyProperties(hospitalTemplate, templateVO);
        templateVO.setType(ItemTypeEnum.getType(hospitalTemplate.getType()));
        return R.ok(templateVO);
    }

    /**
     * @Param id
     * @Param sort 排序
     * @Param menuId 菜单ID
     * @Return com.puree.easycfg.domain.R
     * @Description 修改菜单或排序
     * <AUTHOR>
     * @Date 2023/12/7 18:46
     **/
    @PutMapping("/sort-or-menuId")
    @PreAuthorize(hasPermi = "business:setting:hospital:template:move")
    public R updateSortOrMenuId(@RequestParam(value = "id") Long id,
                                @RequestParam(value = "sort", required = false) Integer sort,
                                @RequestParam(value = "menuId", required = false) Long menuId) {
        return R.ok(hospitalTemplateService.updateSortOrMenuId(id, sort, menuId));
    }
}
