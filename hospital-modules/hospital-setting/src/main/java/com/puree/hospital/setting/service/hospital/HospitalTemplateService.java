package com.puree.hospital.setting.service.hospital;
import com.puree.hospital.setting.domain.dto.TemplateDefaultValueDTO;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.dto.hospital.ItemUpdateDto;
import com.puree.hospital.setting.domain.dto.hospital.TemplateSaveDto;
import com.puree.hospital.setting.domain.dto.hospital.TemplateUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.vo.hospital.TemplateVO;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 *
 *
 * <AUTHOR>
 * @date 2024/09/24
 */
public interface HospitalTemplateService {
    Boolean insert(TemplateSaveDto dto, HttpServletRequest request);


    Boolean delete(Long id, String itemsName, HttpServletRequest request);

    Boolean update(TemplateUpdateDto dto, HttpServletRequest request);

    Object list(String blurSearch);

    HospitalTemplate getById(Long id);

    /**
     * 获取医院配置参数
     *
     * @param blurSearch
     * @param hospitalId
     *
     * @return {@link Object }
     */
    Object itemsList(String blurSearch, Long hospitalId);

    /**
     * 更新医院配置项的值
     *
     * @param dto     DTO
     * @return {@link Boolean }
     */
    Boolean updateItems(ItemUpdateDto dto);

    /**
     * @param hospitalTemplate
     * @return void
     * @description 配置项缓存到 Redis
     * <AUTHOR>
     * @date 2024/12/9 16:32
     **/
    void cacheItemToRedis(HospitalTemplate hospitalTemplate, String itemValue);

    /**
     * 更新排序或菜单 ID
     *
     * @param id     模板id
     * @param sort   排序
     * @param menuId 菜单 ID
     * @return {@link Object }
     */
    Object updateSortOrMenuId(Long id, Integer sort, Long menuId);

    /**
     * 通过menuId 查询 医院配置模板基础信息并进行权限校验
     *
     * @param menu       菜单键
     * @param hospitalId 医院 ID
     * @param roles      当前用户的角色
     * @return {@link List }<{@link HospitalTemplate }>
     */
    List<HospitalTemplate> getTemplateList(Menu menu, Long hospitalId, String roles);

    /**
     * 通过模板白名单过滤配置项
     *
     * @param list       列表
     * @param hospitalId 医院 ID
     */
    void filterByWhitList(List<HospitalTemplate> list, Long hospitalId);
    /**
     * 通过key 获取医院配置项的值
     *
     * @param key
     */
    HospitalTemplate getTemplate(String key);

    /**
     * 按菜单键 获取模板 map  key：key value:itemValue
     *
     * @param menu       医院菜单
     * @param roles      角色
     * @param hospitalId 医院id
     * @return {@link R }
     */
    Map<String, Object> getItemsAsMap(Menu menu, String roles, Long hospitalId);

    /**
     * @param cacheMinutes
     * @return List<HospitalTemplate>
     * @description 查询指定缓存分钟数的数据
     * <AUTHOR>
     * @date 2024/12/9 19:00
     **/
    List<HospitalTemplate> getCacheMinutesTemplateList(Integer cacheMinutes);
    /**
     * @param
     * @return boolean
     * @description 对永久的进行续期
     * <AUTHOR>
     * @date 2024/12/12 15:05
     **/
    boolean renewPermanentItem();

    /**
     * @param template
     * @return boolean
     * @description 续期操作
     * <AUTHOR>
     * @date 2024/12/12 15:07
     **/
    boolean processTemplateRenewal(HospitalTemplate template);

    /**
     * @param templateList
     * @return boolean
     * @description 续期操作
     * <AUTHOR>
     * @date 2024/12/12 15:05
     **/
    boolean processTemplateRenewal(List<HospitalTemplate> templateList);

    /**
     * @param menu 菜单
     * @param hospitalId 医院id
     * @return {@link List }<{@link TemplateVO }> 配置项基础信息列表
     */
    List<HospitalTemplate> getTemplateListAdmin(Menu menu, Long hospitalId);

    /**
     * @param menu 菜单
     * @param query 查询条件
     * @return {@link List }<{@link TemplateVO }> 配置项基础信息列表
     */
    List<HospitalTemplate> getTemplateListAdmin(Menu menu, TemplateQuery query);

    /**
     * 获取菜单下所有配置项  供运营后台使用
     * @param menu 菜单
     * @param hospitalId 医院id
     * @return {@link Map }<{@link String},{@link Object }> 配置项 map  key：key value:itemValue
     */
    Map<String, Object> getItemsAsMapAdmin(Menu menu, Long hospitalId);

    /**
     * 更新医院配置项  供运营后台使用
     *
     * @param items      配置项列表
     * @return
     */
    void updateHospitalItems(List<ItemUpdateDto> items);

    /**
     * 物理删除配置项  供运营后台使用
     * @param id        模版id
     * @param itemsName  配置项名称
     * @return
     */
    Boolean physicalDelete(Long id,String itemsName);

    /**
     *  修改模版默认值
     * @param dto
     * @return
     */
    Boolean updateDefaultValue(TemplateDefaultValueDTO dto);
}
