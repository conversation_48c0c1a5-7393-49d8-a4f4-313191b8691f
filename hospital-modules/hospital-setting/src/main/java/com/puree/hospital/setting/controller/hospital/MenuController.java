package com.puree.hospital.setting.controller.hospital;

import cn.hutool.core.lang.tree.Tree;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.common.api.domain.R;

import com.puree.hospital.setting.domain.dto.MenuSaveDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.service.hospital.HospitalMenuService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 平台端医院配置项菜单管理
 * <AUTHOR>
 * @date 2024/09/23
 */
@RestController
@AllArgsConstructor
@RequestMapping("easy-config/platform-admin/menu")
public class MenuController {

    private final HospitalMenuService hospitalMenuService;

    /**
     * @param dto
     * @return {@link R }
     */
    @PostMapping
    @PreAuthorize(hasPermi = "business:setting:hospital:menu:insert")
    public R<Object> insert(@RequestBody @Validated MenuSaveDto dto, HttpServletRequest request) {
        return R.ok(hospitalMenuService.insert(dto,request));
    }

    /**
     * @param dto
     * @return {@link R }
     */
    @PutMapping
    @PreAuthorize(hasPermi = "business:setting:hospital:menu:update")
    public R<Boolean> update(@RequestBody @Validated MenuSaveDto dto, HttpServletRequest request) {
        return R.ok(hospitalMenuService.update(dto,request));
    }

    /**
     * @param id
     * @param menuName
     * @return {@link R }
     */
    @DeleteMapping("/{id}")
    @PreAuthorize(hasPermi = "business:setting:hospital:menu:delete")
    public R<Boolean> delete(@PathVariable(value = "id", required = true) Long id,
                           @RequestParam("menuName") String menuName, HttpServletRequest request) {
        return R.ok(hospitalMenuService.delete(id, menuName,request));
    }

    /**
     * 返回树形菜单列表
     * @return {@link R }
     */
    @GetMapping("/list-tree")
    @PreAuthorize(hasPermi = "business:setting:hospital:menu:select")
    public R<List<Tree<Long>>> menuListTree() {
        return R.ok(hospitalMenuService.menuListTree());
    }


    @GetMapping("/{id}")
    @PreAuthorize(hasPermi = "business:setting:hospital:menu:update")
    public R<Menu> getMenuInfoById(@PathVariable(required = false, name = "id") Long id) {
        return R.ok(hospitalMenuService.getMenuInfoById(id));
    }

    /**
     * 修改父级节点或排序
     * @param id
     * @param sort 排序
     * @param parentId 父节点
     * @return {@link R }
     */
    @PutMapping("/sort-or-parentId")
    @PreAuthorize(hasPermi = "business:setting:hospital:menu:move")
    public R<Boolean> developUpdateSortOrParentId(@RequestParam(value = "id") Long id,
                                         @RequestParam(value = "sort", required = false) Integer sort,
                                         @RequestParam(value = "parentId", required = false) Long parentId) {
        return R.ok(hospitalMenuService.updateSortOrParentId(id, sort, parentId));
    }
}
