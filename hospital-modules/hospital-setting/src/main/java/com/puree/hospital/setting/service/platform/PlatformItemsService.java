package com.puree.hospital.setting.service.platform;
import com.puree.hospital.setting.domain.dto.TemplateDefaultValueDTO;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.vo.ItemDescriptionVO;
import com.puree.hospital.setting.domain.dto.platform.ItemsDevelopSaveDto;
import com.puree.hospital.setting.domain.dto.platform.ItemsDevelopUpdateDto;
import com.puree.hospital.setting.domain.dto.platform.ItemsOperationsUpdateDto;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.vo.hospital.TemplateVO;
import com.puree.hospital.setting.domain.vo.platform.ItemsDevelopHomeVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @ClassName EasyConfigItemsService
 * <AUTHOR>
 * @Description 配置项数据
 * @Date 2023/12/6 11:45
 * @Version 1.0
 */
public interface PlatformItemsService {


    /**
     * @Param blurSearch 模糊查询字段，支持配置项名称、描述、key
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 模糊查询
     * <AUTHOR>
     * @Date 2023/12/6 17:13
     **/
    List<PlatformItems> blurSearch(String blurSearch);

    /**
     * @Param menuId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 获取菜单下的配置项数
     * <AUTHOR>
     * @Date 2024/3/22 17:26
     **/
    List<PlatformItems> getItemsByMenuId(Long menuId);

    /**
     * @Param menuKey
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItems>
     * @Description 获取菜单下的配置项数
     * <AUTHOR>
     * @Date 2024/3/27 10:45
     **/
    List<PlatformItems> getItems(TemplateQuery query);

    /**
     * @Param blurSearch 模糊查询字段，支持配置项名称、描述、key
     * @Return java.util.List<com.puree.easycfg.domain.easy.vo.EasyConfigItemsOperationsHomeVo>
     * @Description 模糊查询 运营 配置项
     * <AUTHOR>
     * @Date 2023/12/6 17:05
     **/
//    List<EasyConfigItemsOperationsHomeVo> blurSearchOperationsConfigItems(String blurSearch);
    Map<String, Object> blurSearchConfigItems(String blurSearch);

    /**
     * @Param blurSearch 模糊查询字段，支持配置项名称、描述、key
     * @Return java.util.List<com.puree.easycfg.domain.easy.vo.EasyConfigItemsDevelopHomeVo>
     * @Description 模糊查询 开发 配置项
     * <AUTHOR>
     * @Date 2023/12/6 17:12
     **/
    List<ItemsDevelopHomeVo> blurSearchDevelopConfigItems(String blurSearch);

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 开发新增配置项
     * <AUTHOR>
     * @Date 2023/12/6 17:39
     **/
    Boolean insertTemplate(ItemsDevelopSaveDto dto);

    /**
     * @Param items
     * @Return java.lang.Boolean
     * @Description 修改 Nacos 配置项,推送单一KV
     * <AUTHOR>
     * @Date 2023/12/8 18:56
     **/
    Boolean updateNacosConfigItem(PlatformItems platformItems);

    /**
     * @Param items
     * @Return java.lang.Boolean
     * @Description 刷新绑定关系
     * <AUTHOR>
     * @Date 2023/12/19 12:15
     **/
    Boolean refreshPushBind(PlatformItems platformItems);

    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Return java.lang.Boolean
     * @Description  刷新 Nacos 配置
     * <AUTHOR>
     * @Date 2023/12/18 19:12
     **/
    Boolean refreshNacosConfig(PlatformItems sourceData, PlatformItems targetData);

    /**
     * @Param items
     * @Return java.lang.Boolean
     * @Description 删除 Nacos 配置项，删除单一 K
     * <AUTHOR>
     * @Date 2023/12/8 19:04
     **/
    Boolean deleteNacosConfigItem(PlatformItems platformItems);

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 开发修改配置项
     * <AUTHOR>
     * @Date 2023/12/7 14:08
     **/
    Boolean updateTemplate(ItemsDevelopUpdateDto dto) throws Exception;

    /**
     * @Param dto
     * @Return java.lang.Boolean
     * @Description 运营修改配置项
     * <AUTHOR>
     * @Date 2023/12/7 17:45
     **/
    Boolean updateItems(ItemsOperationsUpdateDto dto);

    /**
     * @Param easyConfigItems
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改
     * <AUTHOR>
     * @Date 2023/12/7 17:55
     **/
    Boolean updateTemplate(PlatformItems platformItems);

    /**
     *  查询当前版本的配置项数据
     * @param item 配置项
     * @return {@link PlatformItems }配置项 数据
     */
    PlatformItems getCurrentVersionData(PlatformItems item);

    /**
     * @Param id
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 ID 获取数据
     * <AUTHOR>
     * @Date 2023/12/8 14:41
     **/
    PlatformItems getById(Long id);

    /**
     * @Param key
     * @Return com.puree.easycfg.domain.easy.EasyConfigItems
     * @Description 根据 key 查询
     * <AUTHOR>
     * @Date 2024/1/10 17:29
     **/
    PlatformItems getByKey(String key);

    /**
     * @Param id
     * @Param itemsName itemsName 需要 ID 和 名称同时匹配才能删除
     * @Return java.lang.Boolean
     * @Description 开发删除配置项
     * <AUTHOR>
     * @Date 2023/12/7 18:13
     **/
    Boolean deleteTemplate(Long id, String itemsName);

    /**
     * @Param id
     * @Param sort
     * @Param menuId
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改 菜单 或 排序
     * <AUTHOR>
     * @Date 2023/12/7 18:41
     **/
    Boolean updateTemplateSortOrMenuId(Long id, Integer sort, Long menuId);

    /**
     * @Param id
     * @Param sort
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改排序
     * <AUTHOR>
     * @Date 2023/12/7 18:47
     **/
    Boolean updateTemplateSort(Long id, Integer sort);

    /**
     * @Param id
     * @Param menuId
     * @Return java.lang.Boolean
     * @Description 根据 ID 修改菜单
     * <AUTHOR>
     * @Date 2023/12/7 18:48
     **/
    Boolean updateTemplateMenuId(Long id, Long menuId);

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序,如果该菜单下没有节点，则返回 0
     * <AUTHOR>
     * @Date 2023/12/8 15:42
     **/
    Integer queryMenuMaxSortById(Long menuId);

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询菜单最大排序 +1
     * <AUTHOR>
     * @Date 2023/12/8 15:51
     **/
    Integer queryMenuMaxSortIncrementerById(Long menuId);

    /**
     * @Param
     * @Return java.util.Collection<java.lang.String>
     * @Description 获取别名列表
     * <AUTHOR>
     * @Date 2023/12/18 15:05
     **/
    Collection<Map<String, String>> getAliasMapStringKeyList();

    /**
     * @Param menuName
     * @Return java.lang.Long
     * @Description 获取菜单下子节点数量
     * <AUTHOR>
     * @Date 2023/12/18 16:27
     **/
    Long childItemsCount(Long menuName);


    /**
     * 获取配置项描述
     *
     * @param key 钥匙
     * @return {@link List }<{@link ItemDescriptionVO }>
     */
    ItemDescriptionVO getItemsDescribeByKey(String key);

    /**
     * 获取配置项列表并进行权限过滤
     *
     * @param platformMenu 平台菜单
     * @param roles        当前用户的角色
     * @return {@link R }
     */
    List<TemplateVO> selectItemList(Menu platformMenu, String roles);

    /**
     * 按菜单键 获取配置项 map   key:itemsKey value:itemsValue
     *
     * @param platformMenu 平台菜单
     * @param roles        角色
     * @return {@link R }
     */
    Map<String, Object> selectItemMap(Menu platformMenu, String roles);

    /**
     * 按菜单键 获取配置项 map   key:itemsKey value:itemsValue
     * 供医院管理后台使用
     *
     * @param menu 平台菜单
     * @return {@link R }
     */
    Map<String, Object> selectItemMapAdmin(Menu menu);
    /**
     * 获取配置项列表并进行权限过滤
     * 供医院管理后台使用
     *
     * @param menu 平台菜单
     * @return {@link R }
     */
    List<TemplateVO> selectItemListAdmin(Menu menu);


    /**
     * 修改多个配置项
     * @param items 配置项列表
     * @return  {@link R }
     */
    void updateItems(List<ItemsOperationsUpdateDto> items);

    /**
     *
     * 修改模版默认值
     * @param dto
     * @return
     */
    Boolean updateTemplateDefaultValue(TemplateDefaultValueDTO dto) throws Exception;
    /**
     * 物理删除配置项
     * @param id        配置项ID
     * @param itemsName 配置项名称
     * @return {@link R }
     */
    Boolean developPhysicalDelete(Long id,String itemsName);

}
