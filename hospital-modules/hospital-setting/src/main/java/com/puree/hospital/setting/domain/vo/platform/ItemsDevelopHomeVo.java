package com.puree.hospital.setting.domain.vo.platform;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName EasyConfigItemsDevelopHomeVo
 * <AUTHOR>
 * @Description 配置项 开发页面 vo
 * @Date 2023/12/6 17:07
 * @Version 1.0
 */
@Data
public class ItemsDevelopHomeVo implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * key
     */
    private String key;
    /**
     * 菜单类型ID
     */
    private Long menuId;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String remark;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 类型;前端定义值是多少
     */
    private Integer type;
    /**
     * 约束
     */
    private String restraint;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean whetherOpenHttpApi;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean anonymousAccess;
    /**
     * 响应内容类型
     */
    private String responseContentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    private String accessibleRoles;
    /**
     * 推送的配置中心;文件名与文件名之间用,号隔开
     */
    private String pushConfigurationCenter;
    /**
     * 默认值
     */
    private String defaultValue;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
}