package com.puree.hospital.setting.mapper.hospital;


import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/23
 */
@Mapper
public interface HospitalTemplateMapper {


    /**
     * 统计菜单下子节点的数量
     * @param id
     * @return {@link Long }
     */
    Long childItemsCount(Long id);

    /**
     * 统计配置项的个数
     * @param key
     * @param name
     * @param id
     * @return {@link Long }
     */
    Long count(@Param("key") String key, @Param("name") String name, @Param("id") Long id);

    Integer save(HospitalTemplate item);

    /**
     * 通过template_id 统计配置项个数
     *
     * @return {@link Integer }
     */
    Integer countItems(@Param("template")HospitalTemplate hospitalTemplate );

    Integer deleteByIdAndItemsName(@Param("id") Long id, @Param("itemsName") String itemsName, @Param("username") String username);

    Integer physicalDeleteByIdAndItemsName(@Param("id") Long id, @Param("itemsName") String itemsName);

    HospitalTemplate query(@Param("template") HospitalTemplate template);

    Integer update(@Param("hospitalItem") HospitalTemplate item, @Param("revision") Integer revision);

    List<HospitalTemplate> list(String blurSearch);

    HospitalTemplate getById(Long id);

    /**
     * 根据医院id 获取配置项的值
     * @param blurSearch
     * @param hospitalId
     * @return {@link List }<{@link HospitalTemplate }>
     */
    List<HospitalTemplate> itemsList(@Param("blurSearch") String blurSearch, @Param("hospitalId") Long hospitalId);

    /**
     * 根据医院id 和版本号获取配置项的值
     *
     * @return {@link HospitalTemplate }
     */
    HospitalTemplate queryItem(@Param("template") HospitalTemplate template);

    /**
     * 添加医院配置项的值
     * @param item
     * @return {@link Boolean }
     */
    Boolean updateItems(HospitalTemplate item);

    Boolean insertItems(@Param("hospitalItem") HospitalTemplate item);

    /**
     * 更新向前排序
     *
     * @param menuId 菜单 ID
     * @param sort   排序
     * @return {@link Integer }
     */
    Integer updateSortForward(@Param("menuId") Long menuId, @Param("sort") Integer sort);

    /**
     * 更新向后排序
     *
     * @param menuId 菜单 ID
     * @param sort   排序
     * @return {@link Integer }
     */
    Integer updateSortMoveBack(@Param("menuId") Long menuId, @Param("sort") Integer sort);

    Integer updateSort(@Param("id") Long id, @Param("sort") Integer sort);

    /**
     * 重置排序
     *
     * @param menuId 菜单 ID
     * @return {@link Integer }
     */
    Integer resetSort(Long menuId);

    List<HospitalTemplate> selectTemplateList(TemplateQuery query);

    /**
     * 按键获取模板
     *
     * @param key 钥匙
     * @return {@link HospitalTemplate }
     */
    HospitalTemplate getTemplate(String key);
    /**
     * @param cacheMinutes
     * @return List<HospitalTemplate>
     * @description 查询指定缓存分钟数的数据
     * <AUTHOR>
     * @date 2024/12/9 18:54
     **/
    List<HospitalTemplate> getCacheMinutesTemplateList(@Param("cacheMinutes") Integer cacheMinutes);

    /**
     * 查询模板最大按 ID 排序
     *
     * @param menuId 菜单 ID
     * @return {@link Integer }
     */
    Integer queryTemplateMaxSortById(Long menuId);

    /**
     * 更新默认值
     * @param template
     * @return
     */
    Long updateDefaultValue(HospitalTemplate template);
}
