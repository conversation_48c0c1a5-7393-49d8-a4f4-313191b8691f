package com.puree.hospital.setting.notify.domain;

import lombok.Builder;
import lombok.Data;

/**
 * <p>
 * 飞书卡片参数 详情 <a href="https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/feishu-card-cardkit/configure-card-variables">...</a>
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/13 10:28
 */
@Data
@Builder
public class FeishuCardVariable {
    /**
     * 类型
     */
    private String type;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 更新数据
     */
    private String content;
    /**
     * 环境
     */
    private String environment;
    /**
     * 分类
     */
    private String classify;
    /**
     * 操作
     */
    private String operate;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 按钮文本
     */
    private String buttonText;
    /**
     * 跳转历史数据页面 URL
     */
    private String historyUrl;
}
