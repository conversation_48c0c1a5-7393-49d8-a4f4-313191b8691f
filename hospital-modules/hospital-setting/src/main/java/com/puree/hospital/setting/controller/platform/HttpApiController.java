package com.puree.hospital.setting.controller.platform;


import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.setting.annotation.ValidateAuth;
import com.puree.hospital.setting.controller.BaseHttpApiController;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.dto.ResponseFormat;
import com.puree.hospital.setting.domain.dto.platform.ItemsOperationsUpdateDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.vo.ItemDescriptionVO;
import com.puree.hospital.setting.service.platform.PlatformItemsService;
import com.puree.hospital.setting.service.platform.PlatformMenuService;
import com.puree.hospital.setting.utils.JsonUtil;
import com.puree.hospital.setting.utils.PropertiesUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 平台配置 HTTP API 请求controller
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@SuppressWarnings("unused")
@RestController
@AllArgsConstructor
@Slf4j
public class HttpApiController extends BaseHttpApiController {

    private final PlatformItemsService platformItemsService;

    private final PlatformMenuService platformMenuService;

    // <editor-fold desc="配置项查询接口">

    /**
     * 查询平台配置项的值，支持json ajax格式封装，表格数据支持tql查询，分页
     * <p>TODO: 兼容老接口地址/easy/config/easy-config/http-api/{key}</p>
     *
     * @param key        配置项的key
     * @param tql        tree query language
     * @param offset     表格的行偏移量
     * @param limit      表格的行数
     * @param format    JSON源的输出格式，json/ajax/csv/excel 等
     * @param template   根据key获取的模板
     * <AUTHOR>
     * @date 2024/1/11 16:42
     **/
    @ValidateAuth(ValidateAuth.Scope.PLATFORM_ITEM)
    @GetMapping({"/easy/config/easy-config/http-api/{key}", "/easy-config/platform/{key}"})
    public ResponseEntity<?> getHttpApiByKey(@PathVariable(value = "key") String key,
                                             @RequestParam(value = "tql", required = false) String tql,
                                             @RequestParam(value = "offset", defaultValue = "0") int offset,
                                             @RequestParam(value = "limit", defaultValue = "0") int limit,
                                             @RequestParam(value = "format", required = false) ResponseFormat format,
                                             PlatformItems template) throws IOException {
        String value = template.getValue() == null ? template.getDefaultValue() : template.getValue();
        return processResult(template, format, tql, offset, limit, value);
    }

    /**
     * 获取平台配置项信息
     *
     * @param key 配置项的key
     * @return {@link R }
     */
    @ValidateAuth(ValidateAuth.Scope.PLATFORM_ITEM)
    @GetMapping("/easy-config/platform-describe/{key}")
    public ResponseEntity<?> getPlatformItemDescription(@PathVariable(value = "key") String key, PlatformItems template) {
        ItemDescriptionVO itemDescriptionVO = new ItemDescriptionVO();
        BeanUtils.copyProperties(template, itemDescriptionVO);
        itemDescriptionVO.setRestraint(JsonUtil.fromJson(template.getRestraint(), Object.class));
        return ResponseEntity.ok(R.ok(itemDescriptionVO));
    }


    // </editor-fold>

    // <editor-fold desc="配置项菜单查询接口">

    /**
     * 获取平台菜单下的配置项的基础信息列表
     *
     * @param key 菜单键
     */
    @ValidateAuth(ValidateAuth.Scope.PLATFORM_MENU)
    @GetMapping("/easy-config/platform-list/{key}")
    public ResponseEntity<?> getPlatformMenuList(@PathVariable(value = "key") String key,
                                                                @RequestHeader(value = PropertiesUtil.AUTHENTICATION_HEADER, required = false) String roles,
                                                                Menu platformMenu) {
        return ResponseEntity.ok(R.ok(platformItemsService.selectItemList(platformMenu, roles)));
    }

    /**
     * 获取平台菜单下的配置项的值，并将这些配置项组成一个map
     *
     * @param key 菜单键
     */
    @ValidateAuth(ValidateAuth.Scope.PLATFORM_MENU)
    @GetMapping("/easy-config/platform-map/{key}")
    public ResponseEntity<?> getPlatformMap(@PathVariable(value = "key") String key,
                                                                 @RequestHeader(value = PropertiesUtil.AUTHENTICATION_HEADER, required = false) String roles,
                                                                 Menu platformMenu) {
        return ResponseEntity.ok(R.ok(platformItemsService.selectItemMap(platformMenu, roles)));
    }
    // </editor-fold>

    /**
     * 查询平台配置项的值，支持json ajax格式封装，表格数据支持tql查询，分页
     * 提供 医院管理后台使用
     * <p>TODO: 兼容老接口地址/easy/config/easy-config/http-api/{key}</p>
     *
     * @param key        配置项的key
     * @param tql        tree query language
     * @param offset     表格的行偏移量
     * @param limit      表格的行数
     * @param format    JSON源的输出格式，json/ajax/csv/excel 等
     * <AUTHOR>
     * @date 2024/1/11 16:42
     **/
    @GetMapping("/easy-config/platform/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:platform:items:select")
    public ResponseEntity<?> getHttpApiByKeyAdmin(@PathVariable(value = "key") String key,
                                                  @RequestParam(value = "tql", required = false) String tql,
                                                  @RequestParam(value = "offset", defaultValue = "0") int offset,
                                                  @RequestParam(value = "limit", defaultValue = "0") int limit,
                                                  @RequestParam(value = "format", required = false) ResponseFormat format) throws IOException {
        PlatformItems template = platformItemsService.getByKey(key);
        String value = template.getValue() == null ? template.getDefaultValue() : template.getValue();
        return processResult(template, format, tql, offset, limit, value);
    }


    /**
     * 获取平台配置项信息
     *
     * @param key 配置项的key
     * @return {@link R }
     */
    @GetMapping("/easy-config/platform-describe/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:platform:items:select")
    public ResponseEntity<?> getPlatformItemDescription(@PathVariable(value = "key") String key) {
        PlatformItems template = platformItemsService.getByKey(key);
        ItemDescriptionVO itemDescriptionVO = new ItemDescriptionVO();
        BeanUtils.copyProperties(template, itemDescriptionVO);
        itemDescriptionVO.setRestraint(JsonUtil.fromJson(template.getRestraint(), Object.class));
        return ResponseEntity.ok(R.ok(itemDescriptionVO));
    }


    // </editor-fold>

    // <editor-fold desc="配置项菜单查询接口">

    /**
     * 获取平台菜单下的配置项的基础信息列表
     *
     * @param key 菜单键
     */
    @GetMapping("/easy-config/platform-list/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:platform:items:select")
    public ResponseEntity<?> getPlatformMenuListAdmin(@PathVariable(value = "key") String key) {
        Menu menu = platformMenuService.queryMenu(key);
        return ResponseEntity.ok(R.ok(platformItemsService.selectItemListAdmin(menu)));
    }

    /**
     * 获取平台菜单下的配置项的值，并将这些配置项组成一个map
     *
     * @param key 菜单键
     */
    @GetMapping("/easy-config/platform-map/admin/{key}")
    @PreAuthorize(hasPermi = "business:setting:platform:items:select")
    public ResponseEntity<?> getPlatformMapAdmin(@PathVariable(value = "key") String key) {
        Menu menu = platformMenuService.queryMenu(key);
        return ResponseEntity.ok(R.ok(platformItemsService.selectItemMapAdmin(menu)));
    }

    /**
     * 获取平台菜单下的配置项的值，并将这些配置项组成一个map
     * @param   items   配置项列表
     */
    @PutMapping("/easy-config/platform-map/admin")
    @PreAuthorize(hasPermi = "business:setting:platform:items:update")
    public ResponseEntity<?> updatePlatformMapAdmin(@RequestBody List<ItemsOperationsUpdateDto> items) {
        platformItemsService.updateItems(items);
        return ResponseEntity.ok(R.ok("更新成功"));
    }

}