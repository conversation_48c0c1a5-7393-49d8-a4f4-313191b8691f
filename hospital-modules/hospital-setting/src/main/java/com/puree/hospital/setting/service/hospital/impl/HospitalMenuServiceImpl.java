package com.puree.hospital.setting.service.hospital.impl;


import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.puree.hospital.setting.constants.RequestConstants;

import com.puree.hospital.setting.domain.dto.MenuSaveDto;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.mapper.hospital.HospitalMenuMapper;
import com.puree.hospital.setting.mapper.hospital.HospitalTemplateMapper;
import com.puree.hospital.setting.service.hospital.HospitalMenuService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/09/23
 */
@Slf4j
@Service
@AllArgsConstructor
public class HospitalMenuServiceImpl implements HospitalMenuService {

    private final HospitalMenuMapper hospitalMenuMapper;
    private final HospitalTemplateMapper hospitalTemplateMapper;

    @Override
    public Object insert(MenuSaveDto dto, HttpServletRequest request) {
        String menuKey = dto.getMenuKey();
        String menuName = dto.getMenuName();
        if (menuKey == null){
            throw new IllegalArgumentException("配置项菜单 key 不能为空");
        }
        if (menuName == null){
            throw new IllegalArgumentException("配置项菜单 name 不能为空");
        }
        Long count = hospitalMenuMapper.countHospital(menuKey, menuName);
        if (count > 0) {
            log.warn("配置项菜单 key={},或 配置项菜单 name={} 已存在", menuKey, menuName);
            throw new IllegalArgumentException("配置项菜单 key=" + menuKey + ",或 配置项菜单 name=" + menuName + " 已存在");
        }

        Menu menu = new Menu();
        BeanUtils.copyProperties(dto, menu);

        menu.setCreateTime(new Date());
        menu.setUpdateTime(new Date());
        menu.setCreateBy(request.getHeader(RequestConstants.DETAILS_USERNAME));
        menu.setUpdateBy(request.getHeader(RequestConstants.DETAILS_USERNAME));
        //初始版本号为
        menu.setRevision(0);
        menu.setIsDelete(false);
        Long parentId = dto.getParentId();
        parentId = parentId == null ? 0L : parentId;
        //如果父节点为空，则为一级节点 -1
        menu.setParentId(parentId);

        //追加菜单尾部
        Integer sort = queryMenuMaxSortById(parentId);
        menu.setSort(sort);

        return hospitalMenuMapper.insertHospitalMenu(menu) >= 1;

    }

    @Override
    public Integer queryMenuMaxSortById(Long menuId) {
        //通过 ID  查询菜单最大排序
        Integer sort = hospitalMenuMapper.queryMenuMaxSortById(menuId);
        if (sort == null) {
            sort = 0;
        }
        return sort + 1;
    }

    @Override
    public Boolean update(MenuSaveDto dto, HttpServletRequest request) {
        String menuName = dto.getMenuName();
        Long id = dto.getId();
        if (menuName == null){
            throw new IllegalArgumentException("配置项菜单 name 不能为空");
        }
        if (id == null){
            throw new IllegalArgumentException("配置项菜单 id 不能为空");
        }
        List<Menu> easyConfigMenus = hospitalMenuMapper.queryByMenuNameAndKey(null, menuName);

        if (!easyConfigMenus.isEmpty() &&
                (easyConfigMenus.size() > 1 ||
                        !easyConfigMenus.get(0).getId().equals(id))) {
            log.warn(" 配置项菜单 name={} 已存在", menuName);
            throw new IllegalArgumentException("配置项菜单 name=" + menuName + " 已存在");
        }
        Menu menu = new Menu();
         BeanUtils.copyProperties(dto, menu);
        //这里由于只有开发能改，所以暂不考虑并发问题
        if (menu.getId() == null) {
            throw new IllegalArgumentException("参数错误");
        }
        menu.setUpdateBy(request.getHeader(RequestConstants.DETAILS_USERNAME));
        //乐观锁 版本+1 暂时不需要
        //item.setRevision(item.getRevision() + 1);
        return hospitalMenuMapper.update(menu) >= 1;

    }

    @Override
    public Boolean delete(Long id, String menuName, HttpServletRequest request) {
        if (StringUtils.isEmpty(menuName)) {
            throw new IllegalArgumentException("配置项名称不正确");
        }
        if (hospitalMenuMapper.childNodeCount(id) > 0) {
            log.warn("有子级菜单，不允许删除 id={}，menuName={}", id, menuName);
            throw new IllegalArgumentException("配置项菜单 还有子级菜单，不允许删除");
        }

        if (hospitalTemplateMapper.childItemsCount(id) > 0) {
            log.warn("该菜单还有配置项，不允许删除 id={}，menuName={}", id, menuName);
            throw new IllegalArgumentException("该菜单还有配置项，不允许删除");
        }
        String username = request.getHeader(RequestConstants.DETAILS_USERNAME);

        if (Boolean.FALSE.equals(hospitalMenuMapper.deleteByIdAndItemsName(id, menuName, username) >= 1)) {
            log.warn("配置项不存在、配置项名称不正确，或配置项已被删除:id={},itemsName={}", id, menuName);
            throw new IllegalArgumentException("配置项不存在、配置项名称不正确，或配置项已被删除");
        }
        return true;
    }

    @Override
    public List<Tree<Long>> menuListTree() {
        List<Menu> list = hospitalMenuMapper.list();
        //防止扩容导致性能下降
        List<TreeNode<Long>> tree = getTreeNodeList(list);
        //构建树
        return TreeUtil.build(tree, 0L);
    }

    private List<TreeNode<Long>> getTreeNodeList(List<Menu> list) {
        List<TreeNode<Long>> tree = new ArrayList<>(list.size() * 2);

        for (Menu item : list) {
            TreeNode<Long> node = new TreeNode<Long>();
            node.setId(item.getId());
            node.setParentId(item.getParentId());
            node.setName(item.getMenuName());
            Map<String, Object> extra = new HashMap<>();
            extra.put("key", item.getMenuKey());
            extra.put("enableHttp", item.getEnableHttp());
            node.setExtra(extra);
            node.setWeight(item.getSort());
            tree.add(node);
        }
        return tree;
    }

    @Override
    public Menu getMenuInfoById(Long id) {
        //根据 ID 查询
        Menu menu = hospitalMenuMapper.queryById(id);

        if (Objects.isNull(menu)) {
            log.warn("配置项菜单不存在，或配置项菜单已被删除:id={}", id);
            throw new IllegalArgumentException("配置项菜单不存在，或配置项菜单已被删除");
        }
        return menu;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSortOrParentId(Long id, Integer sort, Long parentId) {
        //同时为空或者同时不为空都为参数错误，因为排序和父级节点只能修改一个，不允许同时修改
        if ((sort == null && parentId == null) || (sort != null && parentId != null)) {
            throw new IllegalArgumentException("参数错误");
        }
        if (sort != null) {
            //修改排序
            return updateSort(id, sort);
        }
        //修改父级菜单
        return updateMenuId(id, parentId);
    }

    @Override
    public Tree<Long> menuListTreeSingle() {
        List<Menu> list = getList();
        //防止扩容导致性能下降
        List<TreeNode<Long>> treeNodeList = getTreeNodeList(list);
        log.debug("treeNodeList={}", treeNodeList);
        //构建树
        return TreeUtil.buildSingle(treeNodeList, 0L);
    }

    @Override
    public Menu queryMenu(String menuKey) {
        Menu menu = hospitalMenuMapper.queryMenu(menuKey);
        if (menu == null){
            throw new IllegalArgumentException("配置项菜单不存在，或配置项菜单已被删除:menuKey=" + menuKey);
        }
        return hospitalMenuMapper.queryMenu(menuKey);
    }

    private List<Menu> getList() {
        return hospitalMenuMapper.list();
    }

    private Boolean updateMenuId(Long id, Long parentId) {
        //拿菜单 ID
        Menu menu = getMenuInfoById(id);

        if (menu.getParentId().equals(parentId)) {
            log.warn("源菜单与目标菜单相同，禁止修改");
            throw new IllegalArgumentException("源菜单与目标菜单相同，禁止修改");
        }
        //通过 ID 查询菜单最大排序 +1
        Integer sort = queryMenuMaxSortById(parentId);
        return hospitalMenuMapper.updateParentId(id, parentId, sort) >= 1;
    }

    private Boolean updateSort(Long id, Integer sort) {
        //拿父级 ID
        Menu menu = getMenuInfoById(id);

        Integer sourceSort = menu.getSort();
        if (Objects.equals(sourceSort, sort)) {
            return true;
        } else if (sort > sourceSort) {
            //从后向前移动
            hospitalMenuMapper.updateSortForward(menu.getParentId(), sort);
        } else {
            //从前向后移动
            hospitalMenuMapper.updateSortMoveBack(menu.getParentId(), sort);
        }
        //修改排序
        //不需要锁，低概率事件
        if (Boolean.FALSE.equals(hospitalMenuMapper.updateSort(id, sort) >= 1)) {
            log.warn("修改失败，请稍后重试");
            throw new RuntimeException("修改失败，请稍后重试");
        }
        //整理排序
        return hospitalMenuMapper.resetSort(menu.getParentId()) >= 1;
    }


}
