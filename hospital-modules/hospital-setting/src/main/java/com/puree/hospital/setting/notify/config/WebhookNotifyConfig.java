package com.puree.hospital.setting.notify.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <p>
 * webhook
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/22 11:06
 */
@Data
public class WebhookNotifyConfig {

    /**
     * 是否通知
     */
    private boolean enable;

    /**
     * 业务名称
     */
    private String processName;

    /**
     * Webhook URL
     */
    private String webhookUrl;

    /**
     * 飞书机器人文档
     *<a href="https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot?lang=zh-CN">...</a>
     * 飞书机器人 秘钥
     *
     */
    private String secret;

    /**
     * 飞书卡片 模板ID
     */
    private String templateId;


    /**
     *飞书卡片 模板版本
     */
    private String templateVersionName;

    /**
     * 按钮文本
     */
    private String buttonText;

    /**
     * 飞书消息类型
     */
    private String msgType;


}
