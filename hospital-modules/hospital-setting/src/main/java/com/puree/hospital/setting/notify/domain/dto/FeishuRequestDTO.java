package com.puree.hospital.setting.notify.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.puree.hospital.setting.notify.domain.FeishuCard;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * <p>
 * 飞书webhook请求  dot
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/11 10:06
 */
@Data
@ToString
@Slf4j
public class FeishuRequestDTO {

    @JsonProperty("msg_type")
    private String msgType;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 标志
     */
    private String sign;
    /**
     * 飞书卡片 json
     */
    private FeishuCard card;


    public void setSign(String secret){
        try {
            //把timestamp+"\n"+密钥当做签名字符串
            String stringToSign = timestamp + "\n" + secret;
            //使用HmacSHA256算法计算签名
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(new byte[]{});
            this.sign = new String(Base64.encodeBase64(signData));
        }catch (Exception e){
            log.error("飞书卡片签名失败", e);
            throw new RuntimeException("飞书卡片签名失败", e);
        }
    }

}
