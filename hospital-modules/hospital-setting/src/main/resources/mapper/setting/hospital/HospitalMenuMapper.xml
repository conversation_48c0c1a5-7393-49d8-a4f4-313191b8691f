<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.hospital.HospitalMenuMapper">
    <resultMap type="com.puree.hospital.setting.domain.entity.Menu" id="CfgHospitalMenuMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
        <result property="menuKey" column="menu_key" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="enableHttp" column="enable_http" jdbcType="TINYINT"/>
        <result property="allowAnonymous" column="allow_anonymous" jdbcType="TINYINT"/>
        <result property="allowedRoles" column="allowed_roles" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="insertHospitalMenu">
        insert into cfg_hospital_menu
        (`menu_name`, `menu_key`, `parent_id`, `sort`, `remark`, `revision`, `create_by`, `create_time`, `update_by`,
        `update_time`, `is_delete`, `enable_http`, `allow_anonymous`, `allowed_roles`)
        values (#{menuName}, #{menuKey}, #{parentId}, #{sort}, #{remark}, #{revision}, #{createBy}, #{createTime},
                #{updateBy}, #{updateTime}, #{isDelete}, #{enableHttp}, #{allowAnonymous}, #{allowedRoles})
    </insert>
    <update id="update">
        update cfg_hospital_menu
        <set>
            <if test="menuName != null and menuName != ''">
                menu_name = #{menuName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="revision != null">
                revision = #{revision},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="enableHttp != null">
                enable_http = #{enableHttp},
            </if>
            <if test="allowAnonymous != null">
                allow_anonymous = #{allowAnonymous},
            </if>
            <if test="allowedRoles != null">
                allowed_roles = #{allowedRoles},
            </if>

        </set>
        where id = #{id} and is_delete = 0
    </update>
    <update id="updateSortForward">
        UPDATE cfg_hospital_menu
        SET sort = sort - 1
        WHERE parent_id = #{parentId}
        and sort &lt;= #{sort}
        and is_delete = 0
    </update>
    <update id="updateSortMoveBack">
        UPDATE cfg_hospital_menu
        SET sort = sort + 1
        WHERE parent_id = #{parentId}
        and sort &gt;= #{sort}
        and is_delete = 0
    </update>
    <update id="updateSort">
        UPDATE cfg_hospital_menu
        SET sort = #{sort},update_time = now()
        WHERE id = #{id}
        and is_delete = 0
    </update>
    <update id="resetSort">
        UPDATE cfg_hospital_menu AS ecm
        INNER JOIN (
        SELECT id AS bid,
        row_number() over ( ORDER BY sort ASC,update_time DESC) `rank`
        FROM cfg_hospital_menu
        WHERE parent_id = #{parentId} and is_delete = 0
        ) AS seq
        ON ecm.id = seq.bid
        SET ecm.sort = seq.`rank`
    </update>
    <update id="updateParentId">
        UPDATE cfg_hospital_menu
        SET sort      = #{sort},
        parent_id = #{parentId}
        WHERE id = #{id}
        and is_delete = 0
    </update>



    <update id="deleteByIdAndItemsName">
        update cfg_hospital_menu
        set is_delete=1,
        update_by=#{username}
        where id = #{id}
        and `menu_name` = #{menuName}
        and is_delete = 0
    </update>
    <update id="updateMenuId">
        UPDATE cfg_hospital_menu
        SET sort = #{sort} , menu_id = #{menuId}
        WHERE id = #{id} and del_flag = 0
    </update>
    <select id="countHospital" resultType="java.lang.Long">
        select count(1)
        from cfg_hospital_menu
        <where>
            <if test="menuKey != null and menuKey != ''">
                and `menu_key` = #{menuKey}
            </if>
            <if test="menuName != null and menuName != ''">
                or `menu_name` = #{menuName}
            </if>
            and is_delete = 0
        </where>
    </select>
    <select id="queryMenuMaxSortById" resultType="java.lang.Integer">
        select max(sort)
        from cfg_hospital_menu
        where parent_id = #{menuId}
          and is_delete = 0
    </select>
    <select id="queryByMenuNameAndKey" resultType="com.puree.hospital.setting.domain.entity.Menu">
        select id,
        menu_name,
        menu_key,
        parent_id,
        sort,
        remark,
        revision,
        create_by,
        create_time,
        update_by,
        update_time,
        is_delete,
        enable_http,
        allow_anonymous,
        allowed_roles
        from cfg_hospital_menu
        <where>
            <if test="menuKey != null and menuKey != ''">
                and `menu_key` = #{menuKey}
            </if>
            <if test="menuName != null and menuName != ''">
                or `menu_name` = #{menuName}
            </if>
            and is_delete = 0
        </where>
    </select>
    <select id="childNodeCount" resultType="java.lang.Long">
        select count(1)
        from cfg_hospital_menu
        where `parent_id` = #{parentId}
          and is_delete = 0
    </select>
    <select id="list" resultType="com.puree.hospital.setting.domain.entity.Menu">
        select id,
               menu_name,
               menu_key,
               parent_id,
               sort,
               remark,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               is_delete,
               enable_http,
               allow_anonymous,
               allowed_roles
        from cfg_hospital_menu
        where is_delete = 0
    </select>
    <select id="queryById" resultType="com.puree.hospital.setting.domain.entity.Menu">
        select id,
               menu_name,
               menu_key,
               parent_id,
               sort,
               remark,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               is_delete,
               enable_http,
               allow_anonymous,
               allowed_roles
        from cfg_hospital_menu
        where id = #{id}
          and is_delete = 0
    </select>
    <select id="queryMenu" resultType="com.puree.hospital.setting.domain.entity.Menu">
        select id,
               menu_name,
               menu_key,
               parent_id,
               sort,
               remark,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               is_delete,
               enable_http,
               allow_anonymous,
               allowed_roles
        from cfg_hospital_menu
        where menu_key = #{menuKey}
          and is_delete = 0
    </select>


</mapper>