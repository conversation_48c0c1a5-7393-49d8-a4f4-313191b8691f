<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.pay.mapper.BusOrderRefundRecordMapper">

    <resultMap id="BaseResultMap" type="com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO">
        <id property="id" column="ref_id"/>
        <result property="hospitalId" column="ref_hospital_id"/>
        <result property="orderNo" column="ref_order_no"/>
        <result property="promoter" column="ref_promoter"/>
        <result property="outTradeNo" column="ref_out_trade_no"/>
        <result property="outRefundNo" column="ref_out_refund_no"/>
        <result property="refundType" column="ref_refund_type"/>
        <result property="payAmount" column="ref_pay_amount"/>
        <result property="refundAmount" column="ref_refund_amount"/>
        <result property="refundStatus" column="ref_refund_status"/>
        <result property="refundSuccessTime" column="ref_refund_success_time"/>
        <result property="refundRemark" column="ref_refund_remark"/>
        <result property="refundId" column="ref_refund_id"/>
        <result property="notifySnapshot" column="ref_notify_snapshot"/>
        <result property="createBy" column="ref_create_by"/>
        <result property="createTime" column="ref_create_time"/>
        <result property="updateBy" column="ref_update_by"/>
        <result property="updateTime" column="ref_update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        refund.id as ref_id,
        refund.hospital_id as ref_hospital_id,
        refund.order_no as ref_order_no,
        refund.promoter as ref_promoter,
        refund.out_trade_no as ref_out_trade_no,
        refund.out_refund_no as ref_out_refund_no,
        refund.refund_type as ref_refund_type,
        refund.pay_amount as ref_pay_amount,
        refund.refund_amount as ref_refund_amount,
        refund.refund_status as ref_refund_status,
        refund.refund_success_time as ref_refund_success_time,
        refund.refund_remark as ref_refund_remark,
        refund.refund_id as ref_refund_id,
        refund.notify_snapshot as ref_notify_snapshot,
        refund.create_by as ref_create_by,
        refund.create_time as ref_create_time,
        refund.update_by as ref_update_by,
        refund.update_time as ref_update_time
    </sql>

    <select id="queryList" parameterType="com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO" 
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bus_order_refund_record refund
        <where>
            <trim prefixOverrides="and">
                <if test="hospitalId != null">
                    and refund.hospital_id = #{hospitalId}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and refund.order_no = #{orderNo}
                </if>
                <if test="promoter != null and promoter != ''">
                    and refund.promoter = #{promoter}
                </if>
                <if test="outTradeNo != null and outTradeNo != ''">
                    and refund.out_trade_no = #{outTradeNo}
                </if>
                <if test="outRefundNo != null and outRefundNo != ''">
                    and refund.out_refund_no = #{outRefundNo}
                </if>
                <if test="refundType != null and refundType != ''">
                    and refund.refund_type = #{refundType}
                </if>
                <if test="payAmount != null">
                    and refund.pay_amount = #{payAmount}
                </if>
                <if test="refundAmount != null">
                    and refund.refund_amount = #{refundAmount}
                </if>
                <if test="refundSuccessTime != null">
                    and refund.refund_success_time = #{refundSuccessTime}
                </if>
                <if test="refundStatus != null and refundStatus != ''">
                    and refund.refund_status = #{refundStatus}
                </if>
                <if test="refundId != null and refundId != ''">
                    and refund.refund_id = #{refundId}
                </if>
                <if test="createTime != null">
                    and refund.create_time = #{createTime}
                </if>
                <if test="createBy != null">
                    and refund.create_by = #{createBy}
                </if>
                <if test="updateTime != null">
                    and refund.update_time = #{updateTime}
                </if>
                <if test="updateBy != null">
                    and refund.update_by = #{updateBy}
                </if>
                <if test="outTradeNoList != null and outTradeNoList.size() > 0">
                    and refund.out_trade_no in
                    <foreach collection="outTradeNoList" item="each" open="(" separator="," close=")">
                        #{each}
                    </foreach>
                </if>

            </trim>
        </where>
    </select>


</mapper>
