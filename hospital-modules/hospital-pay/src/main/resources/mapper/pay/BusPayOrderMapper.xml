<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.pay.mapper.BusPayOrderMapper">

    <resultMap id="BaseResultMap" type="com.puree.hospital.pay.api.model.vo.BusPayOrderVO">
        <id property="id" column="id"/>
        <result property="amount" column="amount"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="mchType" column="mch_type"/>
        <result property="openid" column="openid"/>
        <result property="orderNo" column="order_no"/>
        <result property="orderOtherNo" column="order_other_no"/>
        <result property="outTradeNo" column="out_trade_no"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="orderType" column="order_type"/>
        <result property="partnersCode" column="partners_code"/>
        <result property="payWay" column="pay_way"/>
        <result property="refundTime" column="refund_time"/>
        <result property="status" column="status"/>
        <result property="mchId" column="mch_id"/>
        <result property="appid" column="appid"/>
        <result property="payRemark" column="pay_remark"/>
        <result property="notifyInfo" column="notify_info"/>
        <result property="paySuccessTime" column="pay_success_time"/>
        <result property="userId" column="user_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        pay.id,
        pay.amount,
        pay.hospital_id,
        pay.mch_type,
        pay.openid,
        pay.order_no,
        pay.order_other_no,
        pay.out_trade_no,
        pay.transaction_id,
        pay.order_type,
        pay.partners_code,
        pay.pay_way,
        pay.refund_time,
        pay.status,
        pay.mch_id,
        pay.appid,
        pay.pay_remark,
        pay.notify_info,
        pay.pay_success_time,
        pay.user_id,
        pay.patient_id,
        pay.create_by,
        pay.create_time,
        pay.update_by,
        pay.update_time
    </sql>

    <update id="updateByOutOrderNo">
        UPDATE bus_pay_order
        <set>
            <trim suffixOverrides=",">
                <if test="amount != null">
                    amount = #{amount},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="payWay != null">
                    pay_way = #{payWay},
                </if>
                <if test="partnersCode != null and partnersCode != ''">
                    partners_code = #{partnersCode},
                </if>
                <if test="updateTime != null">
                    update_time = NOW(),
                </if>
                <if test="updateBy != null and updateBy != ''">
                    update_by = #{updateBy},
                </if>
                <if test="delFlag != null">
                    del_flag = #{delFlag},
                </if>
                <if test="orderType != null">
                    order_type = #{orderType},
                </if>
                <if test="mchId != null and mchId != ''">
                    mch_id = #{mchId},
                </if>
                <if test="mchType != null and mchType != ''">
                    mch_type = #{mchType},
                </if>
                <if test="openid != null and openid != ''">
                    openid = #{openid},
                </if>
                <if test="appid != null and appid != ''">
                    appid = #{appid},
                </if>
                <if test="transactionId != null and transactionId != ''">
                    transaction_id = #{transactionId},
                </if>
                <if test="payRemark != null and payRemark != ''">
                    pay_remark = #{payRemark},
                </if>
                <if test="notifyInfo != null">
                    notify_info = #{notifyInfo},
                </if>
                <if test="paySuccessTime != null">
                    pay_success_time = #{paySuccessTime},
                </if>
                <if test="userId != null">
                    user_id = #{userId},
                </if>
                <if test="patientId != null">
                    patient_id = #{patientId},
                </if>
                update_time = NOW(),
            </trim>
        </set>
        <where>
            <if test="outTradeNo != null and outTradeNo != ''">
                and out_trade_no = #{outTradeNo}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
        </where>
    </update>


    <select id="queryList" parameterType="com.puree.hospital.pay.api.model.dto.BusPayOrderQueryDTO"
            resultMap="com.puree.hospital.pay.mapper.BusPayOrderMapper.BaseResultMap">
        select
            <include refid="com.puree.hospital.pay.mapper.BusPayOrderMapper.Base_Column_List"/>
        from bus_pay_order pay
        left join bus_order_refund_record refund on refund.order_no = pay.order_no and refund.hospital_id = pay.hospital_id and refund.out_trade_no = pay.out_trade_no
        <where>
            pay.del_flag = '0'
            <if test="hospitalId != null">
                and pay.hospital_id = #{hospitalId}
            </if>
            <if test="id != null">
                and pay.id = #{id}
            </if>
            <if test="amount != null">
                and pay.amount = #{amount}
            </if>
            <if test="mchType != null and mchType != ''">
                and pay.mch_type = #{mchType}
            </if>
            <if test="openid != null and openid != ''">
                and pay.openid = #{openid}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and pay.order_no = #{orderNo}
            </if>
            <if test="orderOtherNo != null and orderOtherNo != ''">
                and pay.order_other_no = #{orderOtherNo}
            </if>
            <if test="outTradeNo != null and outTradeNo != ''">
                and pay.out_trade_no = #{outTradeNo}
            </if>
            <if test="transactionId != null and transactionId != ''">
                and pay.transaction_id = #{transactionId}
            </if>
            <if test="orderType != null and orderType != ''">
                and pay.order_type = #{orderType}
            </if>
            <if test="partnersCode != null and partnersCode != ''">
                and pay.partners_code = #{partnersCode}
            </if>
            <if test="payWay != null and payWay != ''">
                and pay.pay_way = #{payWay}
            </if>
            <if test="refundTime != null">
                and pay.refund_time = #{refundTime}
            </if>
            <if test="status != null">
                and pay.status = #{status}
            </if>
            <if test="mchId != null and mchId != ''">
                and pay.mch_id = #{mchId}
            </if>
            <if test="appid != null and appid != ''">
                and pay.appid = #{appid}
            </if>
            <if test="payRemark != null and payRemark != ''">
                and pay.pay_remark = #{payRemark}
            </if>
            <if test="notifyInfo != null and notifyInfo != ''">
                and pay.notify_info = #{notifyInfo}
            </if>
            <if test="userId != null">
                and pay.user_id = #{userId}
            </if>
            <if test="patientId != null">
                and pay.patient_id = #{patientId}
            </if>
            <if test="paySuccessTime != null">
                and pay.pay_success_time = #{paySuccessTime}
            </if>
            <if test="paySuccessStartTime != null">
                and pay.pay_success_time &gt;= #{paySuccessStartTime}
            </if>
            <if test="paySuccessEndTime != null">
                and pay.pay_success_time &lt;= #{paySuccessEndTime}
            </if>
            <if test="refundSuccessStartTime != null">
                and refund.refund_success_time &gt;= #{refundSuccessStartTime}
            </if>
            <if test="refundSuccessEndTime != null">
                and refund.refund_success_time &lt;= #{refundSuccessEndTime}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and pay.status in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 结算方式 -->
            <if test="settlementType != null">
                <choose>
                    <when test="settlementType == 1">
                        and pay.pay_way in (1, 2)
                    </when>
                    <when test="settlementType == 2">
                        and pay.pay_way in (3)
                    </when>
                    <otherwise>
                        and 1 = 2
                    </otherwise>
                </choose>
            </if>

            <if test="(familyName != null and familyName != '') and (patientIdList == null or patientIdList.size == 0)">
                and 1 = 2
            </if>
            <if test="patientIdList != null and patientIdList.size() > 0">
                and pay.patient_id in
                <foreach collection="patientIdList" item="each" open="(" separator="," close=")">
                    #{each}
                </foreach>
            </if>
        </where>
        group by pay.id order by pay.create_time desc
    </select>


</mapper>