<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.pay.mapper.BusPayConfigMapper">

    <resultMap id="BaseResultMap" type="com.puree.hospital.pay.api.model.BusPayConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
            <result property="mchType" column="mch_type" jdbcType="CHAR"/>
            <result property="appId" column="app_id" jdbcType="CHAR"/>
            <result property="mchId" column="mch_id" jdbcType="VARCHAR"/>
            <result property="apiV3Key" column="api_v3_key" jdbcType="VARCHAR"/>
            <result property="mchSerialNo" column="mch_serial_no" jdbcType="CHAR"/>
            <result property="platformCertificateSerialno" column="platform_certificate_serialNo" jdbcType="CHAR"/>
            <result property="payNotifyUrl" column="pay_notify_url" jdbcType="VARCHAR"/>
            <result property="refundNotifyUrl" column="refund_notify_url" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="certificateUrl" column="certificate_url" jdbcType="VARCHAR"/>
            <result property="payWay" column="pay_way" jdbcType="CHAR"/>
            <result property="privateKey" column="private_key" jdbcType="VARCHAR"/>
            <result property="certificate" column="certificate" jdbcType="VARCHAR"/>
            <result property="wxPublicKey" column="wx_public_key" jdbcType="VARCHAR"/>
            <result property="wxPublicKeyId" column="wx_public_key_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,hospital_id,mch_type,
        app_id,mch_id,api_v3_key,
        mch_serial_no,platform_certificate_serialNo,pay_notify_url,
        refund_notify_url,create_by,create_time,
        update_by,update_time,certificate_url,
        pay_way,private_key,certificate,
        wx_public_key,wx_public_key_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bus_pay_config
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="queryBySerialNo" resultType="com.puree.hospital.pay.api.model.BusPayConfig">
        select
        <include refid="Base_Column_List" />
        from bus_pay_config
        where
            platform_certificate_serialNo = #{serialNo} or wx_public_key_id = #{serialNo}
        limit 1
    </select>
    <select id="selectListByHospitalIds" resultType="com.puree.hospital.pay.api.model.BusPayConfig">
        select
        <include refid="Base_Column_List" />
        from bus_pay_config
        where
        hospital_id in
        <foreach collection="hospitalIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.puree.hospital.pay.api.model.BusPayConfig" useGeneratedKeys="true">
        insert into bus_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="hospital_id != null">hospital_id,</if>
                <if test="mch_type != null">mch_type,</if>
                <if test="app_id != null">app_id,</if>
                <if test="mch_id != null">mch_id,</if>
                <if test="api_v3_key != null">api_v3_key,</if>
                <if test="mch_serial_no != null">mch_serial_no,</if>
                <if test="platform_certificate_serialNo != null">platform_certificate_serialNo,</if>
                <if test="pay_notify_url != null">pay_notify_url,</if>
                <if test="refund_notify_url != null">refund_notify_url,</if>
                <if test="create_by != null">create_by,</if>
                <if test="create_time != null">create_time,</if>
                <if test="update_by != null">update_by,</if>
                <if test="update_time != null">update_time,</if>
                <if test="certificate_url != null">certificate_url,</if>
                <if test="pay_way != null">pay_way,</if>
                <if test="private_key != null">private_key,</if>
                <if test="certificate != null">certificate,</if>
                <if test="wx_public_key != null">wx_public_key,</if>
                <if test="wx_public_key_id != null">wx_public_key_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="hospital_id != null">#{hospital_id,jdbcType=BIGINT},</if>
                <if test="mch_type != null">#{mch_type,jdbcType=CHAR},</if>
                <if test="app_id != null">#{app_id,jdbcType=CHAR},</if>
                <if test="mch_id != null">#{mch_id,jdbcType=VARCHAR},</if>
                <if test="api_v3_key != null">#{api_v3_key,jdbcType=VARCHAR},</if>
                <if test="mch_serial_no != null">#{mch_serial_no,jdbcType=CHAR},</if>
                <if test="platform_certificate_serialNo != null">#{platform_certificate_serialNo,jdbcType=CHAR},</if>
                <if test="pay_notify_url != null">#{pay_notify_url,jdbcType=VARCHAR},</if>
                <if test="refund_notify_url != null">#{refund_notify_url,jdbcType=VARCHAR},</if>
                <if test="create_by != null">#{create_by,jdbcType=VARCHAR},</if>
                <if test="create_time != null">#{create_time,jdbcType=TIMESTAMP},</if>
                <if test="update_by != null">#{update_by,jdbcType=VARCHAR},</if>
                <if test="update_time != null">#{update_time,jdbcType=TIMESTAMP},</if>
                <if test="certificate_url != null">#{certificate_url,jdbcType=VARCHAR},</if>
                <if test="pay_way != null">#{pay_way,jdbcType=CHAR},</if>
                <if test="private_key != null">#{private_key,jdbcType=VARCHAR},</if>
                <if test="certificate != null">#{certificate,jdbcType=VARCHAR},</if>
                <if test="wx_public_key != null">#{wx_public_key,jdbcType=VARCHAR},</if>
                <if test="wx_public_key_id != null">#{wx_public_key_id,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.puree.hospital.pay.api.model.BusPayConfig">
        update bus_pay_config
        <set>
                <if test="hospitalId != null">
                    hospital_id = #{hospitalId},
                </if>
                <if test="mchType != null">
                    mch_type = #{mchType},
                </if>
                <if test="appId != null">
                    app_id = #{appId},
                </if>
                <if test="mchId != null">
                    mch_id = #{mchId},
                </if>
                <if test="apiV3Key != null">
                    api_v3_key = #{apiV3Key},
                </if>
                <if test="mchSerialNo != null">
                    mch_serial_no = #{mchSerialNo},
                </if>
                <if test="platformCertificateSerialno != null">
                    platform_certificate_serialNo = #{platformCertificateSerialno},
                </if>
                <if test="payNotifyUrl != null">
                    pay_notify_url = #{payNotifyUrl},
                </if>
                <if test="refundNotifyUrl != null">
                    refund_notify_url = #{refundNotifyUrl},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime},
                </if>
                <if test="certificateUrl != null">
                    certificate_url = #{certificateUrl},
                </if>
                <if test="payWay != null">
                    pay_way = #{payWay},
                </if>
                <if test="privateKey != null">
                    private_key = #{privateKey},
                </if>
                <if test="certificate != null">
                    certificate = #{certificate},
                </if>
                <if test="wxPublicKey != null">
                    wx_public_key = #{wxPublicKey},
                </if>
                <if test="wxPublicKey == null">
                    wx_public_key = NULL,
                </if>
                <if test="wxPublicKeyId != null">
                    wx_public_key_id = #{wxPublicKeyId},
                </if>
                <if test="wxPublicKeyId == null">
                    wx_public_key_id = NULL,
                </if>
        </set>
        where   id = #{id}
    </update>


</mapper>
