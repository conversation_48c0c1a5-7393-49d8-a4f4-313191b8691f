package com.puree.hospital.pay.service.impl.payment;

import com.puree.hospital.app.api.model.event.order.BaseOrderEvent;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.order.api.RemoteBusOrderService;
import com.puree.hospital.order.api.RemoteUniAppSyncLogisticsService;
import com.puree.hospital.order.api.model.BusOrder;
import com.puree.hospital.order.api.model.UniAppSyncLogisticsDTO;
import com.puree.hospital.pay.api.constants.BusPayOrderStatusEnum;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.dto.BusPayDTO;

import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import com.puree.hospital.pay.api.model.enums.RefundTypeEnum;
import com.puree.hospital.pay.domain.dto.Transaction;
import com.puree.hospital.pay.service.IBusNotifyService;
import com.puree.hospital.pay.service.IBusOrderRefundRecordService;
import com.puree.hospital.pay.service.IBusPayConfigService;
import com.puree.hospital.pay.service.IBusPayOrderService;
import com.puree.hospital.pay.service.IBusPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public abstract class BasePayService<P ,R, Q, T, N> implements IBusPayService<P, R, Q>, IBusNotifyService<T, N> {

    protected final IBusPayConfigService busPayConfigService;

    protected final IBusPayOrderService busPayOrderService;

    @Resource
    private RemoteUniAppSyncLogisticsService remoteUniAppSyncLogisticsService;

    @Resource
    private RemoteBusOrderService remoteBusOrderService;

    @Resource
    protected IBusOrderRefundRecordService busOrderRefundRecordService;

    /**
     * 根据id查询配置
     * @param id - 配置id
     * @return BusPayConfig
     */
    public BusPayConfig getBusPayConfigById(Long id) {
        if (id == null) {
            log.error("配置查询失败");
            throw new ServiceException("配置查询失败");
        }
        return busPayConfigService.selectPayConfig(id,null);
    }

    public BusPayOrder getBaseBusPayOrder(BusPayDTO dto, BusPayConfig busPayConfig) {
        String orderNo = dto.getOrderNo();
        BusPayOrder busPayOrder = new BusPayOrder();
        busPayOrder.setOrderNo(orderNo.split("_")[0]);
        busPayOrder.setAmount(dto.getPayAmount());
        busPayOrder.setPayWay(dto.getPayWay());
        busPayOrder.setCreateTime(DateUtils.getNowDate());
        busPayOrder.setPartnersCode(dto.getPCode()==null? null : dto.getPCode().toString());
        busPayOrder.setCreateBy(SecurityUtils.getUsername());
        busPayOrder.setOrderType(getOrderTypeByOutTradeNo(orderNo));
        busPayOrder.setHospitalId(dto.getHospitalId());
        busPayOrder.setStatus(BusPayOrderStatusEnum.PAYING.getValue());
        busPayOrder.setOpenid(dto.getOpenid());
        busPayOrder.setAppid(dto.getAppid());
        busPayOrder.setMchId(busPayConfig.getMchId());
        busPayOrder.setUserId(dto.getUserId());
        busPayOrder.setPatientId(dto.getPatientId());
        return busPayOrder;
    }

    public String getOrderTypeByOutTradeNo(String outTradeNo) {
        if (StringUtils.isEmpty(outTradeNo) || outTradeNo.length() < 2){
            return null;
        }
        switch (outTradeNo.substring(0, 2)){
            case OrderTypeConstant.DRUGS_ORDER:
                return "1";
            case OrderTypeConstant.CONSULATION_ORDER:
                return "0";
            case OrderTypeConstant.SERVICE_PACK_ORDER:
                return "2";
            case OrderTypeConstant.TOTAL_ORDER:
                return "3";
            default:
                // 走新版的 - 通联总订单做了一个调整
                BusPayOrder busPayOrder = busPayOrderService.queryPayOrder(outTradeNo, null, null);
                if (Objects.isNull(busPayOrder)) {
                    log.error("支付订单流水不存在 outTradeNo: {}", outTradeNo);
                    return null;
                }
                return getOrderTypeByOutTradeNo(busPayOrder.getOrderNo());
        }
    }

    public void uploadLogistics(Transaction payResult, String orderType) {
        log.info("上传物流信息 {} {}", orderType, payResult);
        String orderNo = payResult.getOutTradeNo();
        String deliveryType = "0";
        UniAppSyncLogisticsDTO uniAppSyncLogisticsDTO = new UniAppSyncLogisticsDTO();
        if (orderType.equals(OrderTypeConstant.TOTAL_ORDER)){
            com.puree.hospital.common.api.domain.R<List<BusOrder>> orderR = remoteBusOrderService.getOrderByTransactionId(payResult.getTransactionId());
            if (!orderR.isSuccess() || orderR.getData() == null || orderR.getData().isEmpty()) {
                log.error("订单不存在 orderNo: {}", orderNo);
                return;
            }
            BusOrder busOrder = orderR.getData().get(0);
            orderNo = busOrder.getOrderNo();
            deliveryType = busOrder.getDeliveryType();
            orderType = BaseOrderEvent.OrderType.SHOP.getCode().equals(busOrder.getSubOrderType())?OrderTypeConstant.GOODS_ORDER:OrderTypeConstant.DRUGS_ORDER;
            uniAppSyncLogisticsDTO.setReceiver(busOrder.getReceiver());
            uniAppSyncLogisticsDTO.setReceivePhone(busOrder.getReceivePhone());
            uniAppSyncLogisticsDTO.setReceiveAddress(busOrder.getReceiveAddress());
        }
        uniAppSyncLogisticsDTO.setOrderNo(orderNo);
        uniAppSyncLogisticsDTO.setTransactionId(payResult.getTransactionId());
        uniAppSyncLogisticsDTO.setAppid(payResult.getAppid());
        uniAppSyncLogisticsDTO.setLogisticsType(orderType, deliveryType);
        uniAppSyncLogisticsDTO.setOrderType(orderType);
        uniAppSyncLogisticsDTO.setRelPrice(new BigDecimal(payResult.getAmount().getPayerTotal()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        uniAppSyncLogisticsDTO.setStatus(0);
        uniAppSyncLogisticsDTO.setOpenid(payResult.getPayer().getOpenid());
        uniAppSyncLogisticsDTO.setCreateTime(new Date());
        uniAppSyncLogisticsDTO.setUpdateTime(new Date());
        remoteUniAppSyncLogisticsService.createUniAppSyncLogistics(uniAppSyncLogisticsDTO);
    }

    /**
     * 获取退款记录
     *
     * @param refundPayDTO 退款记录
     * @return BusOrderRefundRecordDTO
     */
    protected BusOrderRefundRecordDTO getRefundRecordDTO(BusRefundPayDTO refundPayDTO) {
        BusOrderRefundRecordDTO refundRecordDTO = new BusOrderRefundRecordDTO();
        refundRecordDTO.setHospitalId(refundPayDTO.getHospitalId());
        //退款金额从实际参数中取
        BigDecimal refundAmount = getRefundAmount(refundPayDTO);
        BigDecimal orderAmount = getPayOrderAmount(refundPayDTO);
        refundRecordDTO.setPayAmount(orderAmount);
        refundRecordDTO.setRefundAmount(refundAmount);
        refundRecordDTO.setRefundType(getRefundType(orderAmount, refundAmount));
        refundRecordDTO.setRefundStatus(getRefundStatus());
        refundRecordDTO.setOutRefundNo(getOutRefundNo(refundPayDTO));
        refundRecordDTO.setRefundSuccessTime(getRefundSuccessTime());
        String orderNo = refundPayDTO.getOrderNo();
        String transactionId = getOrderTransactionId(refundPayDTO);
        BusPayOrder paidOrder;
        if (StringUtils.isNotEmpty(orderNo)) {
            refundRecordDTO.setOrderNo(orderNo);
            paidOrder = busPayOrderService.queryPaidOrder(refundPayDTO.getOrderNo());
        } else {
            paidOrder = busPayOrderService.queryPayOrder(refundPayDTO.getOutTradeNo(), transactionId, null);
        }
        if (Objects.isNull(paidOrder)) {
            return refundRecordDTO;
        }
        refundRecordDTO.setOrderNo(paidOrder.getOrderNo());
        refundRecordDTO.setOutTradeNo(paidOrder.getOutTradeNo());
        return refundRecordDTO;
    }

    /**
     * 获取订单的我方交易流水号
     * @param orderNo 原来上游传入的我方交易流水
     * @return outTradeNo
     */
    protected abstract String getOutTradeNo(String orderNo);
    /**
     * 根据退款参数获取订单的三方交易流水号
     * @param refundPayDTO 退款请求参数
     * @return transactionId
     */
    protected abstract String getOrderTransactionId(BusRefundPayDTO refundPayDTO);

    /**
     * 获取退款流水号
     *
     * @param refundPayDTO 退款请求参数
     * @return refundNo
     */
    protected abstract String getOutRefundNo(BusRefundPayDTO refundPayDTO);

    /**
     * 获取退款状态
     *
     * @return refundStatus
     */
    protected abstract String getRefundStatus();

    /**
     * 获取退款成功时间
     *
     * @return refundSuccessTime
     */
    protected abstract Date getRefundSuccessTime();

    /**
     * 获取退款类型
     *
     * @param payAmount     支付金额
     * @param refundAmount  退款金额
     * @return 退款类型
     */
    private String getRefundType(BigDecimal payAmount, BigDecimal refundAmount) {
        return payAmount.compareTo(refundAmount) > 0 ? RefundTypeEnum.PARTIAL.name() : RefundTypeEnum.FULL.name();
    }

    /**
     * 从退款参数中获取订单金额
     * @param busRefundPayDTO - 退款参数
     * @return 返回订单金额
     */
    public abstract BigDecimal getPayOrderAmount(BusRefundPayDTO busRefundPayDTO);

    /**
     * 从退款参数中获取退款的金额
     * @param busRefundPayDTO - 退款参数
     * @return 返回退款的金额
     */
    public abstract BigDecimal getRefundAmount(BusRefundPayDTO busRefundPayDTO);

}
