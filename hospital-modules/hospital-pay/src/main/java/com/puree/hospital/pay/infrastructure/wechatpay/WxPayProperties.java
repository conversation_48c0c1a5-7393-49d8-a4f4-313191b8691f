package com.puree.hospital.pay.infrastructure.wechatpay;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "wxpay")
public class WxPayProperties {
//    /**
//     * 服务商公众号ID
//     */
//    private String spAppId;
//    /**
//     * 服务商商户号
//     */
//    private String spMchId;
//    /**
//     * 子商户应用ID
//     */
//    private String subAppId;
//    /**
//     * 子商户号
//     */
//    private String subMchId;
    /**
     * 公众号ID
     */
    private String appId;
    /**
     * 商户号1
     */
    private String mchId1;
    /**
     * 商户号2
     */
    private String mchId2;
    /**
     * V3密钥
     */
    private String apiV3Key1;
    /**
     * V3密钥
     */
    private String apiV3Key2;
    /**
     * 商户证书序列号
     */
    private String mchSerialNo1;
    /**
     * 商户证书序列号
     */
    private String mchSerialNo2;
    /**
     * 平台证书序列号
     */
    private String platformCertificateSerialNo1;
    /**
     * 平台证书序列号
     */
    private String platformCertificateSerialNo2;
    /**
     * 微信支付回调地址
     */
    private String notifyUrl;
    /**
     * 微信退款回调地址
     */
    private String refundNotifyUrl;

}
