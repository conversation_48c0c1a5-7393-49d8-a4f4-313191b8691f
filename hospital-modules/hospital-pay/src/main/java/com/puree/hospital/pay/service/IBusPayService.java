package com.puree.hospital.pay.service;

import com.puree.hospital.pay.api.model.dto.BusPayDTO;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;

/***
 * 支付服务接口
 *  - 回调参数类型
 *  P 支付返回结果
 *  R 退款返回结果
 *  Q 订单查询返回结果
 * <AUTHOR>
 */
public interface IBusPayService<P, R, Q> {

    String PAY_SUFFIX = "PAYMENT_SERVICE";

    /**
     * 支付
     * @param busPayDTO - 支付参数
     * @return - JSONObject支付返回结果
     */
    P pay(BusPayDTO busPayDTO);

    /**
     * 退款
     * @param busRefundPayDTO - 退款参数
     * @return - JSONObject退款返回结果
     */
    R refund(BusRefundPayDTO busRefundPayDTO);

    /**
     * 交易结果查询
     * @param busQueryDTO - 交易查询参数
     * @return - JSONObject交易查询结果
     */
    Q query(BusQueryDTO busQueryDTO);

}
