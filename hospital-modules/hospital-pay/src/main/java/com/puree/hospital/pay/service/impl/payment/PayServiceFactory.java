package com.puree.hospital.pay.service.impl.payment;

import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.utils.SpringUtils;
import com.puree.hospital.pay.service.IBusNotifyService;
import com.puree.hospital.pay.service.IBusPayService;
import org.springframework.stereotype.Component;

/***
 * PayServiceFactory
 * 支付服务工厂类
 *<AUTHOR>
 *@version 1.0
 *@date  2025/1/10 19:04
 * P 支付返回结果
 * R 退款返回结果
 * Q 订单查询返回结果
 * N 通知验签内容
 * T 通知处理内容
 */
@Component
public class PayServiceFactory {

    /**
     * 根据支付方式获取对应的支付服务
     * @param payWay 支付方式
     * @return 支付服务
     * @param <P> 支付返回结果
     * @param <R> 退款返回结果
     * @param <Q> 订单查询返回结果
     */
    public <P, R, Q> IBusPayService<P, R, Q> getPayService(String payWay) {
        // 根据支付方式获取对应的支付服务
        PaysTypeEnum paysTypeEnum = PaysTypeEnum.getEnum(payWay);
        return SpringUtils.getBean(paysTypeEnum.getConstantName() + "_" + IBusPayService.PAY_SUFFIX);
    }

    /**
     * 根据支付方式获取对应的通知服务
     * @param payWay 支付方式
     * @return 通知服务
     * @param <T> 通知验签内容
     * @param <N> 通知处理内容
     */
    public <T, N> IBusNotifyService<T, N> getNotifyService(String payWay) {
        // 根据支付方式获取对应的支付服务
        PaysTypeEnum paysTypeEnum = PaysTypeEnum.getEnum(payWay);
        return SpringUtils.getBean(paysTypeEnum.getConstantName() + "_" + IBusPayService.PAY_SUFFIX);
    }

}
