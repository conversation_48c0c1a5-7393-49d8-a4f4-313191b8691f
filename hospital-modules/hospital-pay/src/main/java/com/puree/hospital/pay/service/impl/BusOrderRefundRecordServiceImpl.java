package com.puree.hospital.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import com.puree.hospital.pay.domain.BusOrderRefundRecord;
import com.puree.hospital.pay.mapper.BusOrderRefundRecordMapper;
import com.puree.hospital.pay.service.IBusOrderRefundRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单退款记录接口实现
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/29 20:54
 */
@Service
public class BusOrderRefundRecordServiceImpl implements IBusOrderRefundRecordService {

    @Resource
    private BusOrderRefundRecordMapper busOrderRefundRecordMapper;

    @Override
    public Boolean sava(BusOrderRefundRecordDTO refundRecordDTO) {
        if (refundRecordDTO == null) {
            return false;
        }
        BusOrderRefundRecord entity = new BusOrderRefundRecord();
        BeanUtils.copyProperties(refundRecordDTO, entity);
        return SqlHelper.retBool(busOrderRefundRecordMapper.insert(entity));
    }

    @Override
    public Boolean update(BusOrderRefundRecordDTO refundRecordDTO) {
        if (refundRecordDTO == null) {
            return false;
        }
        LambdaQueryWrapper<BusOrderRefundRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOrderRefundRecord::getId, refundRecordDTO.getId());
        queryWrapper.eq(Objects.nonNull(refundRecordDTO.getHospitalId()), BusOrderRefundRecord::getHospitalId, refundRecordDTO.getHospitalId());
        queryWrapper.eq(StringUtils.isNotEmpty(refundRecordDTO.getOutRefundNo()), BusOrderRefundRecord::getOutRefundNo, refundRecordDTO.getOutRefundNo());
        queryWrapper.last("limit 1");
        BusOrderRefundRecord dbRecord = busOrderRefundRecordMapper.selectOne(queryWrapper);
        if (Objects.isNull(dbRecord)) {
            return false;
        }
        BusOrderRefundRecord update = new BusOrderRefundRecord();
        BeanUtils.copyProperties(refundRecordDTO, update);
        update.setId(dbRecord.getId());
        return SqlHelper.retBool(busOrderRefundRecordMapper.updateById(update));
    }

    @Override
    public BusOrderRefundRecordVO getById(Long id) {
        BusOrderRefundRecord record = busOrderRefundRecordMapper.selectById(id);
        if (Objects.isNull(record)) {
            return null;
        }
        BusOrderRefundRecordVO dto = new BusOrderRefundRecordVO();
        BeanUtils.copyProperties(record, dto);
        return dto;
    }

    @Override
    public List<BusOrderRefundRecordVO> queryList(BusOrderRefundRecordDTO query) {
        return busOrderRefundRecordMapper.queryList(query);
    }

    @Override
    public BusOrderRefundRecordVO getByOutRefundNo(String outRefundNo) {
        LambdaQueryWrapper<BusOrderRefundRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOrderRefundRecord::getOutRefundNo, outRefundNo);
        queryWrapper.last(" limit 1");
        BusOrderRefundRecord record = busOrderRefundRecordMapper.selectOne(queryWrapper);
        if (Objects.isNull(record)) {
            return null;
        }
        BusOrderRefundRecordVO vo = new BusOrderRefundRecordVO();
        BeanUtils.copyProperties(record, vo);
        return vo;
    }
}
