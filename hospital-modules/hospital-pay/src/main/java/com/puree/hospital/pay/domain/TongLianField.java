package com.puree.hospital.pay.domain;

import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2022/09/28/15:42
 * @Description:
 */
@Data
public class TongLianField {
    /**
     * @name 交易金额
     * @unit 单位为分
     * @isnull 必填
     * @length 8
     */
    private long trxamt;
    /**
     * @name 商户交易单号
     * @unit 商户的交易订单号
     * @isnull 必填
     * @length 50
     * 保证商户平台唯一
     */
    private String reqsn;
    /**
     * @name 交易方式
     * @unit 详见附录交易方式
     * @isnull 必填
     * @length
     */
    private String paytype;
    /**
     * @name 订单标题
     * @unit 订单商品名称，为空则以商户名作为商品名称
     * @isnull 非必填
     * @length 100
     * 最大100个字节(注：utf8下，一个中文字符是3个字节)
     */
    private String body;
    /**
     * @name 备注
     * @unit 备注信息
     * @isnull 非必填
     * @length 160
     * 最大160个字节(80个中文字符)禁止出现+，空格，/，?，%，#，&，=这几类特殊符号
     */
    private String remark;
    /**
     * @name 支付平台用户标识
     * @unit JS支付时使用、微信支付-用户的微信openid 、支付宝支付-用户user_id 、微信小程序-用户小程序的openid、云闪付JS-用户userId
     * @isnull 非必填
     * @length 32
     */
    private String acct;
    /**
     * @name 有效时间
     * @unit 订单有效时间，以分为单位，不填默认为5分钟
     * @isnull 非必填
     * @length 4
     * 最大120分钟
     */
    private String validtime;
    /**
     * @name 交易结果通知地址
     * @unit 接收交易结果的通知回调地址，通知url必须为直接可访问的url，不能携带参数。https只支持默认端口
     * @isnull 非必填
     * @length 256
     * 若使用https,需使用默认443端口
     */
    private String notifyUrl;
    /**
     * @name 支付限制
     * @unit no_credit--指定不能使用信用卡支付
     * @isnull 非必填
     * @length 32
     * 暂时只对微信支付和支付宝有效,仅支持no_credit
     */
    private String limitPay;
    /**
     * @name 证件号
     * @unit 实名交易必填.填了此字段就会验证证件号和姓名
     * @isnull 非必填
     * @length 32
     * 暂只支持支付宝支付,微信支付(微信支付的刷卡支付除外)
     */
    private String idno;
    /**
     * @name 付款人真实姓名
     * @unit 实名交易必填.填了此字段就会验证证件号和姓名
     * @isnull 非必填
     * @length 32
     * 暂只支持支付宝支付,微信支付(微信支付的刷卡支付除外)
     */
    private String truename;
    /**
     * @name 分账信息
     * @unit 格式:cusid:type:amount;cusid:type:amount… 其中cusid:接收分账的通联商户号、type分账类型（01：按金额  02：按比率）
     * 如果分账类型为02，则分账比率为0.5表示50%。如果分账类型为01，则分账金额以元为单位表示
     * @isnull 非必填
     * @length 1024
     * 开通此业务需开通分账配置
     */
    private String asinfo;
    /**
     * @name 微信子appid
     * @unit 微信小程序/微信公众号/APP的appid
     * @isnull 非必填
     * @length 1024
     * 只对微信支付有效
     */
    private String subAppid;
    /**
     * @name 交易结果通知地址
     * @unit 接收交易结果的通知回调地址，通知url必须为直接可访问的url，不能携带参数。https只支持默认端口
     * @isnull 非必填
     * @length 256
     * 若使用https,需使用默认443端口
     */
    private String goods_tag;
    /**
     * @name 优惠信息
     * @unit Benefitdetail的json字符串, 注意是String 填写格式详见附录5.8
     * @isnull 非必填
     * 微信单品优惠
     * W01交易方式不支持
     * 支付宝智慧门店
     * 支付宝单品优惠
     */
    private String benefitdetail;
    /**
     * @name 渠道门店编号
     * @unit 商户在支付渠道端的门店编号
     * @isnull 非必填
     */
    private String chnlstoreid;
    /**
     * @name 门店号
     * @isnull 非必填
     * @length 4
     *  通联系统门店号
     */
    private String subbranch;
    /**
     * @name 	拓展参数
     * @unit json字符串，注意是String一般用于渠道的活动参数填写
     * @isnull 非必填
     * @length 16
     * payType=U02云闪付JS支付不为空
     */
    private String extendparams;
    /**
     * @name 终端ip
     * @unit 用户下单和调起支付的终端ip地址
     * @isnull 非必填
     * @length 16
     * payType=U02云闪付JS支付不为空
     */
    private String cusip;
    /**
     * @name 花呗分期
     * @unit 3-花呗分期3期 、6-花呗分期6期 、12-花呗分期12期
     * @isnull 非必填
     * @length 4
     * 若使用https,需使用默认443端口
     */
    private String fqnum;
    /**
     * 公钥
     */
    private String publicKey;
    /**
     * 私钥
     */
    private String privateKey;
    /**
     * 商户号
     */
    private String cusId;
    /**
     * AppId
     */
    private String appId;

    /**
     * 支付完成后跳转
     */
    private String frontUrl;

    private String returl;
}
