package com.puree.hospital.pay.controller;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import com.puree.hospital.pay.service.IBusOrderRefundRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 订单退款记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/31 15:54
 */
@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOrderRefundController {

    private final IBusOrderRefundRecordService busOrderRefundRecordService;

    /**
     * 保存退款记录
     *
     * @param refundRecordDTO 退款记录
     * @return 保存结果
     */
    @PostMapping("/feign/refund-record/save")
    public R<Boolean> saveRecord(@RequestBody BusOrderRefundRecordDTO refundRecordDTO) {
        return R.ok(busOrderRefundRecordService.sava(refundRecordDTO));
    }

    /**
     * 更新退款记录
     *
     * @param refundRecordDTO 退款记录
     * @return 更新结果
     */
    @PutMapping("/feign/refund-record/update")
    public R<Boolean> updateRecord(@RequestBody BusOrderRefundRecordDTO refundRecordDTO) {
        return R.ok(busOrderRefundRecordService.update(refundRecordDTO));
    }

    /**
     * 根据订单号查询退款记录
     *
     * @param query 查询参数
     * @return 退款记录
     */
    @PostMapping("/feign/refund-record/query")
    public R<List<BusOrderRefundRecordVO>> query(@RequestBody BusOrderRefundRecordDTO query) {
        return R.ok(busOrderRefundRecordService.queryList(query));
    }

    /**
     * 退款成功
     *
     * @param query 退款记录
     * @return 退款结果
     */
    @PostMapping("/feign/refund-record/refund-success")
    public R<Boolean> refundSuccess(@RequestBody BusOrderRefundRecordDTO query) {
        return R.ok(busOrderRefundRecordService.refundSuccess(query));
    }

}
