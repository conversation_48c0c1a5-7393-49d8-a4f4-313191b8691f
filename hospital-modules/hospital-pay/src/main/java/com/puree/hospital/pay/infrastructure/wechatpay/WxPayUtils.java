package com.puree.hospital.pay.infrastructure.wechatpay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.pay.api.constants.WxPayHeaderConstants;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.api.model.dto.WxNotifyDTO;
import com.puree.hospital.pay.domain.dto.Transaction;
import com.puree.hospital.pay.domain.dto.WxRefundDO;
import com.puree.hospital.pay.service.IBusPayConfigService;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.auth.Validator;
import com.wechat.pay.java.core.cipher.AeadCipher;
import com.wechat.pay.java.core.cipher.Verifier;
import com.wechat.pay.java.core.http.DefaultHttpClientBuilder;
import com.wechat.pay.java.core.http.HttpHeaders;
import com.wechat.pay.java.core.http.HttpMethod;
import com.wechat.pay.java.core.http.HttpRequest;
import com.wechat.pay.java.core.http.HttpResponse;
import com.wechat.pay.java.core.http.JsonRequestBody;
import com.wechat.pay.java.core.http.MediaType;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.RSAPublicKeyNotificationConfig;
import com.wechat.pay.java.core.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.SignatureException;
import java.util.Base64;
import java.util.Random;

/**
 * 微信支付工具类
 */
@SuppressWarnings("SpellCheckingInspection")
@Slf4j
@Component
public class WxPayUtils {
    private final IBusPayConfigService busPayConfigService;

    @Autowired
    public WxPayUtils(IBusPayConfigService busPayConfigService) {
        this.busPayConfigService = busPayConfigService;
    }

    /**
     * 根据请求头参数获取回调DTO
     * @param request - 请求
     * @return - 回调DTO
     */
    public WxNotifyDTO getNotifyDTO(HttpServletRequest request) {
        WxNotifyDTO wxNotifyDTO = new WxNotifyDTO();
        // 获取回调请求头
        wxNotifyDTO.setTimestamp(request.getHeader(WxPayHeaderConstants.WECHATPAY_TIMESTAMP));
        wxNotifyDTO.setNonce(request.getHeader(WxPayHeaderConstants.WECHATPAY_NONCE));
        wxNotifyDTO.setSerialNo(request.getHeader(WxPayHeaderConstants.WECHATPAY_SERIAL));
        wxNotifyDTO.setSignature(request.getHeader(WxPayHeaderConstants.WECHATPAY_SIGNATURE));
        log.info("微信请求头参数{}", wxNotifyDTO);
        wxNotifyDTO.setResult(this.readData(request));
        log.info("回调退款密文{}", wxNotifyDTO.getResult());
        return wxNotifyDTO;
    }
    /**
     * 读取请求体中数据
     *
     * @param request - 请求
     * @return - 请求体数据
     */
    public String readData(HttpServletRequest request) {
        try(ServletInputStream inputStream = request.getInputStream()) {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取请求体数据失败",e);
            throw new RuntimeException(e);
        }
    }


    public <T> T sendHttpToWechat(Config payConfig,HttpMethod httpMethod, String url, JSONObject body,HttpHeaders headers,Class<T> response) {
        // TODO -请求看下能不能改okhttp
        // 1. 创建请求对象okhttpclient
        // 2. 设置请求头
        // 3. 设置请求体
        // 4. 执行请求
        // 5. 解析响应结果
        // 6. 返回结果
        // 7. 验证结果-签名是否正确
        // 8. 类型转换
        headers.addHeader("Accept", MediaType.APPLICATION_JSON.getValue());
        headers.addHeader("Content-Type", MediaType.APPLICATION_JSON.getValue());
        HttpRequest.Builder httpRequestBuilder = (new HttpRequest.Builder())
                .httpMethod(httpMethod)
                .url(url)
                .headers(headers);
        //如果是get没有请求体
        HttpRequest httpRequest;
        if (HttpMethod.GET.equals(httpMethod)) {
            httpRequest = httpRequestBuilder.build();
        } else if (HttpMethod.POST.equals(httpMethod)){
            httpRequest = httpRequestBuilder
                    .body((new JsonRequestBody.Builder())
                            .body(body.toString())
                            .build())
                    .build();
        } else {
            throw new ServiceException("不支持的请求方式");
        }
        HttpResponse<T> httpResponse = (new DefaultHttpClientBuilder())
                .config(payConfig)
                .build()
                .execute(httpRequest, response);
        T serviceResponse = httpResponse.getServiceResponse();
        if (serviceResponse == null) {
            log.error("微信支付相关接口请求失败:{}",httpResponse.getBody());
            throw new ServiceException("微信支付相关接口请求失败");
        }
        return httpResponse.getServiceResponse();
    }

    /**
     * 获取调起微信支付签名
     *
     * @param nonceStr - 随机字符串
     * @param timestamp  - 时间戳
     * @param prepayId  - 预支付订单id
     * @return - 签名
     */
    public String getPaySign(String nonceStr, long timestamp, String prepayId, BusPayConfig busWechatPayConfig) {
        String message = busWechatPayConfig.getAppId() + "\n" + timestamp + "\n" + nonceStr + "\n" + prepayId + "\n";
        String signature = null;
//        JSONObject jsonObject = JSON.parseObject(busWechatPayConfig.getCertificateUrl());
        String certificate = busWechatPayConfig.getCertificate();
        signature = sign(message.getBytes(StandardCharsets.UTF_8), certificate);
        return signature;
    }

    /**
     * 微信支付回调签名验证并对加密请求参数解密
     * @param wxNotifyDTO - 微信支付回调参数
     * @return -     解密后的参数
     */
    public String verifyAndDecrypt(WxNotifyDTO wxNotifyDTO) {
        // 验签-获取解密器
        AeadCipher aeadCipher = getAeadCipher(wxNotifyDTO);
        // 解密参数
        JSONObject resultObject = JSON.parseObject(wxNotifyDTO.getResult());
        JSONObject resource = resultObject.getJSONObject("resource");
        String cipherText = resource.getString("ciphertext");
        String nonceStr = resource.getString("nonce");
        String associatedData = resource.getString("associated_data");
        return aeadCipher.decrypt(associatedData.getBytes(StandardCharsets.UTF_8),
                nonceStr.getBytes(StandardCharsets.UTF_8),
                Base64.getDecoder().decode(cipherText));
    }

    /**
     * 验签-获取解密器
     * @param wxNotifyDTO -微信支付回调参数
     * @return - 解密器
     */
    private AeadCipher getAeadCipher(WxNotifyDTO wxNotifyDTO) {
        String serialNo = wxNotifyDTO.getSerialNo();
        BusPayConfig busPayConfig = wxNotifyDTO.getBusHospitalWechatConfig();
        NotificationConfig config = getNotificationConfig(busPayConfig, serialNo);
        // 创建验签器
        Verifier verifier = config.createVerifier();
        // 构造验签串
        String message = wxNotifyDTO.getTimestamp() + "\n" + wxNotifyDTO.getNonce() + "\n" + wxNotifyDTO.getResult() + "\n";
        // 验证签名
        boolean verify = verifier.verify(serialNo, message, wxNotifyDTO.getSignature());
        if (!verify) {
            log.error("微信支付回调签名验证失败,请求参数以及支付配置：{}", wxNotifyDTO);
            throw new ServiceException("验签失败");
        }
        return config.createAeadCipher();
    }

    /**
     * 获取通知配置
     * @param busPayConfig - 支付配置
     * @param serialNo - 证书序列号/支付keyId
     * @return 通知配置
     */
    private NotificationConfig getNotificationConfig(BusPayConfig busPayConfig, String serialNo) {
        String wxPublicKey = busPayConfig.getWxPublicKey();
        String wxPublicKeyId = busPayConfig.getWxPublicKeyId();
        NotificationConfig config;
        if (busPayConfig.isWxPublicKeyMode(serialNo)) {
            // 公钥模式
            config = new RSAPublicKeyNotificationConfig.Builder()
                    .publicKey(wxPublicKey)
                    .publicKeyId(wxPublicKeyId)
                    .apiV3Key(busPayConfig.getApiV3Key())
                    .build();
        } else {
            // 证书模式- 未来会下线
            config =
                    new RSAAutoCertificateConfig.Builder()
                            .merchantId(busPayConfig.getMchId())
                            .privateKey(busPayConfig.getCertificate())
                            .merchantSerialNumber(busPayConfig.getMchSerialNo())
                            .apiV3Key(busPayConfig.getApiV3Key())
                            .build();
        }
        return config;
    }

    /**
     * 获取支付配置
     * @param busPayConfig  - 支付配置
     * @return 支付配置
     */
    public Config getPayConfig(BusPayConfig busPayConfig) {
        Config config;
        if (busPayConfig.isWxPublicKeyMode(busPayConfig.getWxPublicKeyId())) {
            // 公钥模式
            config = new RSAPublicKeyConfig.Builder()
                            .merchantId(busPayConfig.getMchId())
                            .privateKey(busPayConfig.getCertificate())
                            .publicKey(busPayConfig.getWxPublicKey())
                            .publicKeyId(busPayConfig.getWxPublicKeyId())
                            .merchantSerialNumber(busPayConfig.getMchSerialNo())
                            .apiV3Key(busPayConfig.getApiV3Key())
                            .build();
        } else {
            // 证书模式-未来会下线
            config =
                    new RSAAutoCertificateConfig.Builder()
                            .merchantId(busPayConfig.getMchId())
                            .privateKey(busPayConfig.getCertificate())
                            .merchantSerialNumber(busPayConfig.getMchSerialNo())
                            .apiV3Key(busPayConfig.getApiV3Key())
                            .build();
            if (StringUtils.isEmpty(busPayConfig.getPlatformCertificateSerialno())) {
                Validator validator = config.createValidator();
                String serialNumber = validator.getSerialNumber();
                busPayConfigService.updatePlatformCertificateSerialNoById(busPayConfig.getId(), serialNumber);
            }
        }
        return config;
    }

    /**
     * 对字节数据进行私钥签名（加密）
     */
    private static String sign(byte[] message, String serial) {
        try {
            // 签名方式（固定SHA256withRSA）
            Signature sign = Signature.getInstance("SHA256withRSA");
            // 使用私钥进行初始化签名（私钥需要从私钥文件【证书】中读取）
//            InputStream inputStream = new URL(serialPath).openConnection().getInputStream();
            sign.initSign(PemUtil.loadPrivateKeyFromString(serial));
            // 签名更新
            sign.update(message);
            // 对签名结果进行Base64编码
            return Base64.getEncoder().encodeToString(sign.sign());
        } catch (SignatureException | InvalidKeyException | NoSuchAlgorithmException e) {
            log.error("微信支付进行私钥签名失败,message:{}", message, e);
        }
        return null;
    }

    /**
     * 获取随机字符串 Nonce Str
     * 随机字符从symbols获取
     * SecureRandom真随机数
     *
     * @return String 随机字符串
     */
    public static String generateNonceStr() {
        String symbols = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new SecureRandom();
        char[] nonceChars = new char[32];
        for (int index = 0; index < nonceChars.length; ++index) {
            nonceChars[index] = symbols.charAt(random.nextInt(symbols.length()));
        }
        return new String(nonceChars);
    }

    /**
     * 申请退款
     * @param params - 退款参数
     * @param payConfig - 支付配置
     * @return 退款结果
     */
    public WxRefundDO refund(JSONObject params, BusPayConfig payConfig) {
        try {

            Config config = getPayConfig(payConfig);
            HttpHeaders httpHeaders = new HttpHeaders();
            if (payConfig.getWxPublicKeyId() != null) {
                httpHeaders.addHeader("Wechatpay-Serial", payConfig.getWxPublicKeyId());
            }
            return sendHttpToWechat(config, HttpMethod.POST, WxConstants.REFUND, params,httpHeaders, WxRefundDO.class);
        } catch (Exception e) {
            log.error("微信退款异常: {}", e.getMessage(),e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 查询交易结果
     * @param outTradeNo 我们自己的订单号
     * @param transationId 微信平台订单号
     * @param busPayConfig 支付配置
     * @return 交易结果
     */
    public Transaction query(String outTradeNo,String transationId, BusPayConfig busPayConfig) {
        Config config = getPayConfig(busPayConfig);
        String url;
        if (StringUtils.isNotEmpty(outTradeNo)) {
            url = WxConstants.QUERY_OUT_TRADE_NO + outTradeNo + "?mchid=" + busPayConfig.getMchId();
        } else {
            url = WxConstants.QUERY + transationId + "?mchid=" + busPayConfig.getMchId();
        }
        Transaction transaction = sendHttpToWechat(config, HttpMethod.GET, url, null,new HttpHeaders(), Transaction.class);
        if (transaction == null){
            log.error("微信交易查询失败,outTradeNo={}", outTradeNo);
            throw new ServiceException("微信交易查询失败");
        }
        return transaction;
    }
}
