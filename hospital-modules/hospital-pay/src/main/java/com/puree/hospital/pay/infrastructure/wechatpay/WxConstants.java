package com.puree.hospital.pay.infrastructure.wechatpay;

/**
 * 微信相关常量类
 */
public class WxConstants {

//    /**
//     * 获取二维码ticket
//     */
//    public final static String API = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=";
//    /**
//     * 通过ticket换取二维码
//     */
//    public final static String MP = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=";
    /**
     * v3版JSAPI下单
     */
    public final static String PAY = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";
    /**
     * v3版JSAPI退款
     */
    public final static String REFUND = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds";
    /**
     * v3版JSAPI交易结果查询
     */
    public final static String QUERY = "https://api.mch.weixin.qq.com/v3/pay/transactions/id/";

    /**
     * v3版JSAPI查询订单
     */
    public final static String QUERY_OUT_TRADE_NO = "https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/";
    /**
     * 微信回调探测流量前缀
     */
    public static final String WECHATPAY_SIGNATURE = "WECHATPAY/SIGNTEST/";
}
