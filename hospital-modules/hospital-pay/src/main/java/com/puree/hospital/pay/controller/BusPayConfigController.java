package com.puree.hospital.pay.controller;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.domain.BusPayConfigBO;
import com.puree.hospital.pay.domain.vo.BusPayConfigVo;
import com.puree.hospital.pay.service.IBusPayConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/***
 * 支付配置管理
 */
@RestController
@RequestMapping
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class BusPayConfigController extends BaseController {
    private final IBusPayConfigService busPayConfigService;

    /**
     * 新增/编辑
     *
     * @param busPayConfig - 支付配置
     * @return - 返回结果
     */
    @PreAuthorize(hasPermi = "business:wechatPay:config:add")
    @PostMapping("/config/insert-or-update")
    @Log(title = "支付配置信息", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public R<Integer> insertOrUpdate(@RequestBody @Validated BusPayConfig busPayConfig) {
        return R.ok(busPayConfigService.insertOrUpdate(busPayConfig));
    }

    /**
     * 获取详细信息
     *
     * @param id ID
     * @return AjaxResult
     */
    @PreAuthorize(hasPermi = "business:wechatPay:config:query")
    @GetMapping("/config/query")
    @Log(title = "支付配置信息", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public AjaxResult queryInfo(@RequestParam("id") Long id) {
        return AjaxResult.success(busPayConfigService.selectPayConfig(id,null));
    }

    /**
     * 支付配置信息列表
     *
     * @param hospitalName 医院名称
     * @return AjaxResult
     */
    @PreAuthorize(hasPermi = "business:wechatPay:config:list")
    @GetMapping("/config/list")
    @Log(title = "支付配置信息列表", businessType = BusinessType.QUERY, operatorType = OperatorType.MANAGE)
    public TableDataInfo list(@RequestParam(value = "hospitalName", required = false) String hospitalName,
                              @RequestParam(value = "hospitalId", required = false) Long hospitalId) {
        List<BusPayConfigBO> list = busPayConfigService.list(hospitalName,hospitalId);
        List<BusPayConfigVo> busPayConfigVos = new ArrayList<>();
        for (BusPayConfigBO busPayConfigBO : list){
            BusPayConfigVo busPayConfigVo = new BusPayConfigVo();
            BeanUtils.copyProperties(busPayConfigBO, busPayConfigVo);
            busPayConfigVos.add(busPayConfigVo);
        }
        return getDataTable(PageUtil.buildPage(list,busPayConfigVos));
    }

    /**
     * 获取支付配置
     * @param id - 支付配置id
     * @return - 返回支付配置
     */
    @Log(title = "支付配置信息", businessType = BusinessType.QUERY)
    @GetMapping("/feign/pay/config/{id}")
    R<BusPayConfig> selectPayConfigById(@PathVariable("id") Long id){
        if (id == null) {
            log.error("配置查询失败");
            return R.fail("配置查询失败");
        }
        return R.ok(busPayConfigService.selectPayConfig(id, null));
    }

    /**
     * 根据商户号查询支付配置(医院一家可以多个商户,目前未考虑多个医院用同一个商户的情况)
     * @param mchId - 商户号
     * @return - 返回支付配置
     */
    @GetMapping("/feign/pay/config/query-by-mch-id")
    @Log(title = "根据商户号查询支付配置", businessType = BusinessType.OTHER)
    R<BusPayConfig> selectPayConfigById(@RequestParam("mchId")String mchId){
        if (StringUtils.isEmpty(mchId)) {
            log.error("配置查询商户号传递值为空");
            return R.fail("配置查询失败");
        }
        return R.ok(busPayConfigService.selectPayConfig(null, mchId));
    }

}
