package com.puree.hospital.pay.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import com.puree.hospital.pay.domain.BusOrderRefundRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 订单退款记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/29 20:40
 */
@Mapper
public interface BusOrderRefundRecordMapper extends BaseMapper<BusOrderRefundRecord> {

    /**
     * 查询退款记录列表
     *
     * @param recordDTO 退款记录查询参数
     * @return 退款记录列表
     */
    List<BusOrderRefundRecordVO> queryList(BusOrderRefundRecordDTO recordDTO);

}
