package com.puree.hospital.pay.service.impl.payment;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.RemoteBasePaymentService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.constant.PayWayConstant;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DoubleUtils;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.pay.api.constants.BusPayOrderStatusEnum;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.enums.RefundStatusEnum;
import com.puree.hospital.pay.constant.TongLianConstant;
import com.puree.hospital.pay.api.model.dto.BusPayDTO;
import com.puree.hospital.pay.infrastructure.sybpay.HttpConnectionUtil;
import com.puree.hospital.pay.infrastructure.sybpay.SybConstants;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import com.puree.hospital.pay.infrastructure.sybpay.SybUtil;
import com.puree.hospital.pay.service.IBusPayConfigService;
import com.puree.hospital.pay.service.IBusPayOrderService;
import com.puree.hospital.pay.service.IBusPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@SuppressWarnings("SpellCheckingInspection")
@Service(PayWayConstant.TONGLIAN_PAY + "_"+ IBusPayService.PAY_SUFFIX)
@Slf4j
public class SybPayService extends BasePayService<JSONObject, JSONObject, JSONObject, TreeMap<String, String>, TreeMap<String, String>> {

	private final static String CUSORDERID = "cusorderid";
	private final static String TRXID = "trxid";
	private final static String SUCCESS = "0000";
	private final static String CUSID = "cusid";
	private final static String TRXSTATUS = "trxstatus";
	private final static String PAYTIME = "paytime";
	private final static String REQSN = "reqsn";

	@Resource
	private RemoteBasePaymentService remoteBasePaymentService;

	@Resource
	private RemoteServicePackOrderService remoteServicePackOrderService;

	@Lazy
	@Resource
	private SybPayService sybPayService;

	@Autowired
	protected SybPayService(IBusPayConfigService busPayConfigService, IBusPayOrderService busPayOrderService) {
		super(busPayConfigService, busPayOrderService);
	}
	/**
	 * 通联支付
	 * @param dto - 支付参数
	 * @return - 返回支付结果
	 */
	@Override
	public JSONObject pay(BusPayDTO dto) {
		// 返回前端数据集合
		JSONObject payMap = new JSONObject();
		// 1.获取支付配置
		BusPayConfig busPayConfig = getBusPayConfigById(dto.getPayConfigId());
		// 2.根据支付方式调用相应的支付接口
		TreeMap<String, String> payParams = getPayParams(dto, busPayConfig);
		HttpConnectionUtil http = new HttpConnectionUtil(SybConstants.SYB_APIURL+"/pay");
		try {
			http.init();
			String appkey = busPayConfig.getApiV3Key();
			payParams.put("sign", SybUtil.unionSign(payParams,appkey,SybConstants.SIGN_TYPE));
			byte[] bys = http.postParams(payParams, true);
			String result = new String(bys, StandardCharsets.UTF_8);
			Map<String,String> map = handleResult(result);
			payMap = this.checkResult(payMap, map);
		} catch (Exception ex) {
			log.error("通联支付失败原因={}", ex.getMessage(), ex);
			throw new ServiceException("支付失败，请稍后再试！");
		}
		insertOrUpdateBusPayOrder(dto, payMap ,busPayConfig);
		log.info("新支付流程已完成，通联订单号：{}" , payMap.getString(TRXID));
		if (log.isDebugEnabled()) {
			log.debug("支付返回结果：{}", payMap);
		}
		return payMap;
	}

	/**
	 * 处理通联支付流水
	 * @param dto - 支付参数
	 * @param payMap 	- 支付结果
	 */
	private void insertOrUpdateBusPayOrder(BusPayDTO dto, JSONObject payMap,BusPayConfig busPayConfig) {
		/*插入支付记录到订单表*/
		BusPayOrder busPayOrder = getBaseBusPayOrder(dto,busPayConfig);
		busPayOrder.setTransactionId(payMap.getString(TRXID));
		busPayOrder.setOutTradeNo(payMap.getString(REQSN));
		busPayOrderService.insertOrUpdateBusPayOrder(busPayOrder);
	}

	/**
	 * 组装通联支付参数
	 * @param dto - 支付参数
	 * @param busPayConfig - 支付配置
	 */
	protected TreeMap<String, String> getPayParams(BusPayDTO dto, BusPayConfig busPayConfig) {
		TreeMap<String,String> params = new TreeMap<>();
		if(!SybUtil.isEmpty(SybConstants.SYB_ORGID)) {
			params.put("orgid", SybConstants.SYB_ORGID);
		}
		params.put("cusid", busPayConfig.getMchId());
		params.put("appid", busPayConfig.getAppId());
		params.put("version", "11");
		BigDecimal mul = new BigDecimal(100).multiply(dto.getPayAmount());
		params.put("trxamt", mul.longValue() + "");
		params.put("reqsn", getOutTradeNo(dto.getOrderNo()));
		// 默认走	微信扫码支付？？？？？
		params.put("paytype", "W01");
		if (ClientTypeEnum.isWxUniApp(dto.getAppType())){
			// 走微信小程序支付
			params.put("paytype", "W06");
			// 用户小程序openid
			params.put("acct", dto.getOpenid());

			params.put("sub_appid", dto.getAppid());
		}
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("body", dto.getBody());
		params.put("remark", "备注");
		params.put("validtime", "");
		params.put("notify_url", busPayConfig.getPayNotifyUrl());
		params.put("limit_pay", "");
		params.put("goods_tag", "");
		params.put("benefitdetail", "");
		params.put("chnlstoreid", "");
		params.put("subbranch", "");
		params.put("extendparams", "");
		params.put("cusip", "");
		// 支付跳转只支持云闪付JS支付和微信JS支付
		//	params.put("front_url",tongLianField.getFrontUrl());
		params.put("idno", "");
		params.put("truename", "");
		params.put("asinfo", "");
		params.put("fqnum","");
		//	params.put("returl", tongLianField.getReturl());
		params.put("signtype", SybConstants.SIGN_TYPE);
		log.info("支付入参{}",JSONObject.toJSONString(params));
		return params;
	}

	/**
	 * 统一退款
	 * @param dto - 退款参数
	 * @return - 返回退款结果
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public JSONObject refund(BusRefundPayDTO dto) {
		log.info("新退款入参busRefundPayDTO ={}", dto);
		JSONObject payMap = new JSONObject();
		BusPayConfig busPayConfig = getBusPayConfigById(dto.getPayConfigId());
		dto.setTrxamt(DoubleUtils.mul(dto.getRefundAmount(), 100.0).longValue());
		dto.setCusid(busPayConfig.getMchId());
		dto.setPublicKey(busPayConfig.getApiV3Key());
		dto.setAppid(busPayConfig.getAppId());
		BusPayOrder busPayOrder = busPayOrderService.queryPayOrder(null, dto.getOldtrxid(), PaysTypeEnum.TONGLIAN_PAY.getCode());
		if (ObjectUtil.isNotNull(busPayOrder)) {
			dto.setOutTradeNo(busPayOrder.getOutTradeNo());
		} else {
			// 兼容历史数据，如果支付记录为空，则取订单号为订单号
			dto.setOutTradeNo(dto.getOrderNo());
		}
		BusOrderRefundRecordDTO refundRecordDTO = getRefundRecordDTO(dto);
		Map<String, String> map = sybPayRefund(dto, refundRecordDTO.getOutRefundNo());
		payMap = this.checkResult(payMap, map);
		// 退款成功
		if (TongLianConstant.tl_0.equals(payMap.getString(TRXSTATUS))) {
			refundRecordDTO.setRefundId(payMap.getString(TRXID));
			busOrderRefundRecordService.sava(refundRecordDTO);
			//更新退款状态
			busPayOrderService.updatePayOrderAfterRefunded(refundRecordDTO.getOutTradeNo(), refundRecordDTO.getOrderNo());
		}
		log.info("退款返回结果={}", payMap);
		return payMap;
	}


	/**
	 * 查询支交易结果
	 * @param dto 查询参数
	 * @return 返回查询的结果
	 */
	@Override
	public JSONObject query(BusQueryDTO dto) {
        // 查询结果
		JSONObject payMap = new JSONObject();
		BusPayConfig busPayConfig = getBusPayConfigById(dto.getPayConfigId());
		dto.setCusid(busPayConfig.getMchId());
		dto.setAppid(busPayConfig.getAppId());
		dto.setPublicKey(busPayConfig.getApiV3Key());
		//新版改动需要同步上
		BusPayOrder busPayOrder = busPayOrderService.queryPayOrder(null, dto.getTrxid(), PaysTypeEnum.TONGLIAN_PAY.getCode());
		if (StringUtils.isNotEmpty(busPayOrder.getOutTradeNo())){
			// 如果支付记录不为空，则取记录中的我方生成的流水号
			dto.setReqsn(busPayOrder.getOutTradeNo());
		}
		try {
			// 通联查询
			Map<String, String> map = queryOrder(dto);
			payMap = this.checkResult(payMap, map);
			BusPayOrder order = new BusPayOrder();
			// "1"-支付成功 ， "0"-待支付
			order.setStatus(payMap.getString("status"));
			order.setTransactionId(payMap.getString(TRXID));
			order.setOrderType(getOrderTypeByOutTradeNo(payMap.getString("reqsn")));
			order.setOutTradeNo(payMap.getString("reqsn"));
			order.setMchId(payMap.getString(CUSID));
			busPayOrderService.updatePayOrder(order);
		} catch (Exception ex) {
			log.error("查询通联交易结果失败！",ex);
			throw new ServiceException("查询通联交易结果失败！");
		}
		return payMap;
	}

	@Override
	public String verifyAndDecryptNotify(TreeMap<String, String> notifyDTO, String scene) {
		return "";
	}

	@Override
	public void handlePaymentNotify(TreeMap<String, String> payNotifyInfo) {
		sybPayService.updateBusPayOrder(payNotifyInfo);
		/*获取订单编号*/
		String outTradeNo = payNotifyInfo.get(CUSORDERID);
		if (StringUtils.isEmpty(outTradeNo)) {
			throw new ServiceException("通联支付回调获取订单失败");
		}
		String trxid = payNotifyInfo.get(TRXID);
		BusPayOrder busPayOrder = busPayOrderService.queryPayOrder(outTradeNo, trxid, PaysTypeEnum.TONGLIAN_PAY.getCode());
		String orderNo = busPayOrder.getOrderNo();
		if (StringUtils.isEmpty(orderNo)) {
			throw new ServiceException("通联支付回调获取订单失败");
		}
		/*如果订单是:药品订单*/
        switch (orderNo.substring(0, 2)) {
            case OrderTypeConstant.DRUGS_ORDER:
				remoteBasePaymentService.paySuccessDrugsOrderHandler(orderNo,PaysTypeEnum.TONGLIAN_PAY.getCode());
                /*如果订单是:问诊订单*/
                break;
            case OrderTypeConstant.CONSULATION_ORDER:
				remoteBasePaymentService.paySuccessConsultationOrderHandler(orderNo);
                /*如果订单是:服务包订单*/
                break;
            case OrderTypeConstant.SERVICE_PACK_ORDER:
				remoteServicePackOrderService.paySuccessServicePackOrderHandler(orderNo);
                break;
            default:
				remoteBasePaymentService.paySuccessTotalOrderHandler(orderNo,trxid);
                break;
        }
	}

	/**
	 * 更新支付流水
	 * @param payNotifyInfo - 支付通知内容
	 */
	@Transactional(rollbackFor = Exception.class)
	public void updateBusPayOrder(TreeMap<String, String> payNotifyInfo) {
		BusPayOrder busPayOrder = new BusPayOrder();
		busPayOrder.setOutTradeNo(payNotifyInfo.get(CUSORDERID));
		busPayOrder.setOrderType(getOrderTypeByOutTradeNo(payNotifyInfo.get(CUSORDERID)));
		busPayOrder.setMchId(payNotifyInfo.get(CUSID));
		busPayOrder.setTransactionId(payNotifyInfo.get(TRXID));
		busPayOrder.setNotifyInfo(payNotifyInfo.toString());
		busPayOrder.setStatus(getBusPayOrderStatusByTongLianPayStatus(payNotifyInfo.get(TRXSTATUS)));
		busPayOrder.setPaySuccessTime(DateUtil.parse(payNotifyInfo.get(PAYTIME), DatePattern.PURE_DATETIME_PATTERN));
		busPayOrderService.updatePayOrder(busPayOrder);
	}

	/**
	 * 根据通联支付的状态获取订单状态
	 * @param trxstatus - 通联支付状态
	 * @return  - 订单状态
	 */
	private String getBusPayOrderStatusByTongLianPayStatus(String trxstatus) {
		return SUCCESS.equals(trxstatus) ?
				BusPayOrderStatusEnum.PAYED.getValue() : BusPayOrderStatusEnum.PAYING.getValue();
	}

	/**
	 * 退款通知处理
	 *
	 * @param refundNotifyInfo
	 *         - 退款通知内容
	 */
	@Override
	public String handleRefundNotify(String refundNotifyInfo) {
		return null;
	}

	public Map<String,String> cancel(long trxamt,String reqsn,String oldtrxid,String oldreqsn) throws Exception{
		HttpConnectionUtil http = new HttpConnectionUtil(SybConstants.SYB_APIURL+"/cancel");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SybConstants.SYB_ORGID)){
			params.put("orgid", SybConstants.SYB_ORGID);
		}
		params.put("cusid", SybConstants.SYB_CUSID);
		params.put("appid", SybConstants.SYB_APPID);
		params.put("version", "11");
		params.put("trxamt", String.valueOf(trxamt));
		params.put("reqsn", reqsn);
		params.put("oldtrxid", oldtrxid);
		params.put("oldreqsn", oldreqsn);
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("signtype", SybConstants.SIGN_TYPE);
		String appkey = "";
		// 已经写死用RSA了-没必要做判断
//		if(SybConstants.SIGN_TYPE.equals("RSA")) {
			appkey = SybConstants.SYB_RSACUSPRIKEY;
//		}
//		else if(SybConstants.SIGN_TYPE.equals("SM2")){
//			appkey = SybConstants.SYB_SM2PPRIVATEKEY;
//		}
//		else {
//			appkey = SybConstants.SYB_MD5_APPKEY;
//		}
		params.put("sign", SybUtil.unionSign(params,appkey,SybConstants.SIGN_TYPE));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys, StandardCharsets.UTF_8);
        return handleResult(result);
	}
	
	public Map<String,String> sybPayRefund(BusRefundPayDTO busRefundPayDTO, String refundNo) {
		HttpConnectionUtil http = new HttpConnectionUtil(SybConstants.SYB_APIURL+"/refund");
		Map<String,String> map;
        try {
            http.init();
        	TreeMap<String,String> params = new TreeMap<>();
			if(!SybUtil.isEmpty(SybConstants.SYB_ORGID)) {
				params.put("orgid", SybConstants.SYB_ORGID);
			}
			params.put("cusid", busRefundPayDTO.getCusid());
			params.put("appid", busRefundPayDTO.getAppid());
			params.put("version", "11");
			params.put("trxamt", String.valueOf(busRefundPayDTO.getTrxamt()));
			params.put("reqsn", refundNo);
			params.put("oldreqsn", busRefundPayDTO.getOutTradeNo());
//			params.put("oldtrxid", busRefundPayDTO.getOldtrxid());
			params.put("randomstr", SybUtil.getValidatecode(8));
			params.put("signtype", SybConstants.SIGN_TYPE);
			String appkey = busRefundPayDTO.getPublicKey();
			params.put("sign", SybUtil.unionSign(params,appkey,SybConstants.SIGN_TYPE));
			log.info("通联退款入参={}", JSONObject.toJSONString(params));
			byte[] bys = http.postParams(params, true);
			String result = new String(bys, StandardCharsets.UTF_8);
			map = handleResult(result);
		} catch (Exception e) {
			log.error("通联退款异常，异常信息=", e);
			throw new RuntimeException(e);
		}
		return map;
	}
	
	public Map<String,String> queryOrder(BusQueryDTO busQueryDTO) throws Exception{
		HttpConnectionUtil http = new HttpConnectionUtil(SybConstants.SYB_APIURL+"/query");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SybConstants.SYB_ORGID)) {
			params.put("orgid", SybConstants.SYB_ORGID);
		}
		params.put("cusid", busQueryDTO.getCusid());
		params.put("appid", busQueryDTO.getAppid());
		params.put("version", "11");
		params.put("reqsn", busQueryDTO.getReqsn());
		params.put("trxid", busQueryDTO.getTrxid());
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("signtype", SybConstants.SIGN_TYPE);
		// 已经写死用RSA了
//		String appkey = "";
//		if(SybConstants.SIGN_TYPE.equals("RSA")) {
//			appkey = busQueryDTO.getPublicKey();
//		}
//		else if(SybConstants.SIGN_TYPE.equals("SM2")){
//			appkey =busQueryDTO.getPublicKey();
//		}
//		else {
//			appkey = busQueryDTO.getPublicKey();
//		}
		String appkey = busQueryDTO.getPublicKey();
		params.put("sign", SybUtil.unionSign(params,appkey,SybConstants.SIGN_TYPE));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys, StandardCharsets.UTF_8);
        return handleResult(result);
	}



	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static Map<String,String> handleResult(String result) throws Exception{
		log.info("ret : {}" , result);
		Map map = SybUtil.json2Obj(result, Map.class);
		if(map == null){
			throw new Exception("返回数据错误");
		}
		if("SUCCESS".equals(map.get("retcode"))){
            TreeMap tmap = new TreeMap(map);
			String appkey = SybConstants.SYB_RSATLPUBKEY;
//			if(SybConstants.SIGN_TYPE.equals("RSA")) {
//				appkey = SybConstants.SYB_RSATLPUBKEY;
//			}
//			else if(SybConstants.SIGN_TYPE.equals("SM2")) {
//				appkey = SybConstants.SYB_SM2TLPUBKEY;
//			}
//			else {
//				appkey = SybConstants.SYB_MD5_APPKEY;
//			}
			if(SybUtil.validSign(tmap, appkey, SybConstants.SIGN_TYPE)){
				System.out.println("签名成功");
				return map;
			}else{
				throw new Exception("验证签名失败");
			}
		}else{
			throw new Exception(map.get("retmsg").toString());
		}
	}

	public Map<String, String> scanPay(long trxamt,String reqsn,String body,String remark,String authcode,String limitPay,String idno,String truename,String asinfo) throws Exception{
		// TODO Auto-generated method stub
		HttpConnectionUtil http = new HttpConnectionUtil(SybConstants.SYB_APIURL+"/scanqrpay");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SybConstants.SYB_ORGID)) {
			params.put("orgid", SybConstants.SYB_ORGID);
		}
		params.put("cusid", SybConstants.SYB_CUSID);
		params.put("appid", SybConstants.SYB_APPID);
		params.put("version", "11");
		params.put("trxamt", String.valueOf(trxamt));
		params.put("reqsn", reqsn);
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("body", body);
		params.put("remark", remark);
		params.put("authcode", authcode);
		params.put("limit_pay", limitPay);
		params.put("asinfo", asinfo);
		params.put("signtype", SybConstants.SIGN_TYPE);
		String appkey = "";
		// 已经写死用RSA了
//		if(SybConstants.SIGN_TYPE.equals("RSA")) {
			appkey = SybConstants.SYB_RSACUSPRIKEY;
//		} else if(SybConstants.SIGN_TYPE.equals("SM2")) {
//			appkey = SybConstants.SYB_SM2PPRIVATEKEY;
//		} else {
//			appkey = SybConstants.SYB_MD5_APPKEY;
//		}
		params.put("sign", SybUtil.unionSign(params,appkey,SybConstants.SIGN_TYPE));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys, StandardCharsets.UTF_8);
        return handleResult(result);
	}


	JSONObject checkResult(JSONObject payMap,Map<String, String> map) {
		log.info("通联返回结果={}", map);
		payMap.putAll(map);
		if (Constants.RETURN_SUCCESS.equals(map.get("retcode"))){
			String trxstatus = map.get(TRXSTATUS);
			if (StringUtils.isEmpty(trxstatus)){
				//payMap.put("result", "交易正在进行中,尚未完成");
				payMap.put("status", "0");
				return payMap;
			}else if(TongLianConstant.tl_0.equals(trxstatus)){
				payMap.put("status", "1");
			}else{
				//if (trxstatus.equals("1001")){
				//    payMap.put("result", "交易不存在");
				//}else if(trxstatus.equals("3888")){
				//    payMap.put("result", "流水号重复");
				//}else if(trxstatus.equals("3014")){
				//    payMap.put("result", "交易金额小于应收手续费");
				//}else if(trxstatus.equals("3050")){
				//    payMap.put("result", "交易已被撤销");
				//}else if(trxstatus.equals("2008") || trxstatus.equals("2000") ){
				//    payMap.put("result", "交易处理中,请查询交易");
				//}
				payMap.put("status", "0");
				return payMap;
			}
		}
		return payMap;
	}


	/**
	 * 获取订单的我方交易流水号
	 *
	 * @param orderNo
	 *         原来上游传入的我方交易流水
	 *
	 * @return outTradeNo
	 */
	@Override
	protected String getOutTradeNo(String orderNo) {
		return "SYB" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + RandomStringUtils.randomAlphanumeric(3);
	}

	/**
	 * 根据退款参数获取订单的三方交易流水号
	 *
	 * @param refundPayDTO
	 *         退款请求参数
	 *
	 * @return transactionId
	 */
	@Override
	protected String getOrderTransactionId(BusRefundPayDTO refundPayDTO) {
		return refundPayDTO.getOldtrxid();
	}

	@Override
	protected String getOutRefundNo(BusRefundPayDTO refundPayDTO) {
		return "TLREF" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + RandomStringUtils.randomAlphanumeric(3);
	}

	@Override
	protected String getRefundStatus() {
		return RefundStatusEnum.REFUND_SUCCESS.name();
	}

	@Override
	protected Date getRefundSuccessTime() {
		return new Date();
	}

	/**
	 * 从退款参数中获取订单金额
	 *
	 * @param busRefundPayDTO
	 *         - 退款参数
	 *
	 * @return 返回订单金额
	 */
	@Override
	public BigDecimal getPayOrderAmount(BusRefundPayDTO busRefundPayDTO) {
		return BigDecimal.valueOf(busRefundPayDTO.getOrderAmount());
	}

	/**
	 * 从退款参数中获取退款的金额
	 *
	 * @param busRefundPayDTO
	 *         - 退款参数
	 *
	 * @return 返回退款的金额
	 */
	@Override
	public BigDecimal getRefundAmount(BusRefundPayDTO busRefundPayDTO) {
		return BigDecimal.valueOf(busRefundPayDTO.getRefundAmount());
	}
}
