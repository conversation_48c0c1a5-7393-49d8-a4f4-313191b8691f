package com.puree.hospital.pay.service;


import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusPayOrderQueryDTO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderDetailVO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderVO;

import java.util.List;

/**
 * 订单支付记录服务
 *
 * <AUTHOR>
 */
public interface IBusPayOrderService {

    /**
     * 添加支付流水
     * @param busPayOrder - 支付流水信息
     */
    void insertOrUpdateBusPayOrder(BusPayOrder busPayOrder);

    /**
     * 查询支付流水信息
     * @param outTradeNo - 我方流水号
     * @param transactionId - 三方流水号
     * @param payWay - 支付方式
     * @return 支付流水信息
     */
    BusPayOrder queryPayOrder(String outTradeNo, String transactionId,String payWay);

    /**
     * 查询支付流水信息（已支付的） - 订单号
     * @param orderNo - 订单号
     * @return - 返回成功的支付流水信息，没有返回null
     */
    BusPayOrder queryPaidOrder(String orderNo);

    /**
     * 更新支付流水 - 推荐使用id 订单号也可以
     * @param busPayOrder - 支付流水信息
     */
    int updatePayOrder(BusPayOrder busPayOrder);

    /**
     * 查询支付流水列表
     * @param busPayOrderQueryDTO - 查询条件
     * @return - 支付流水列表
     */
    List<BusPayOrderVO> queryList(BusPayOrderQueryDTO busPayOrderQueryDTO);

    /**
     * 查询支付流水详情
     *
     * @param id 支付流水id
     * @return 支付流水详情
     */
    BusPayOrderDetailVO getDetail(Long id);
}