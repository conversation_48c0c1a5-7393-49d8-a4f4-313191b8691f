package com.puree.hospital.pay.controller;


import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusPayOrderQueryDTO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderDetailVO;
import com.puree.hospital.pay.service.IBusPayOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/***
 * 支付流水Controller
 * <AUTHOR>
 * @date 2021-12-07
 * @since 1.0.0
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPayOrderController extends BaseController {
    private final IBusPayOrderService busPayOrderService;

    /**
     * 查询支付流水
     * @param outTradeNo 我方交易流水号
     * @param transactionId 三方流水号
     * @param payWay 支付方式
     * @return R<BusPayOrder>
     */
    @GetMapping("/feign/pay/order")
    public R<BusPayOrder> queryPayOrder(@RequestParam(value = "outTradeNo",required = false) String outTradeNo,
                                        @RequestParam(value = "transactionId",required = false) String transactionId,
                                        @RequestParam(value = "payWay",required = false) String payWay) {
        log.info("查询支付流水入参值 outTradeNo={},transactionId={},支付方式payWay={}",outTradeNo,transactionId,payWay);
        if (StringUtils.isEmpty(outTradeNo) && StringUtils.isEmpty(transactionId)) {
            return R.fail("流水号不能同时为空");
        }
        BusPayOrder busPayOrder;
        try {
            busPayOrder = busPayOrderService.queryPayOrder(outTradeNo, transactionId, payWay);
        } catch (Exception e) {
            log.error("查询支付流水失败", e);
            return R.fail("查询支付流水失败");
        }
        return R.ok(busPayOrder);
    }

    /**
     * 更新支付流水
     *
     */
    @PutMapping("/feign/pay/order")
    public int updatePayOrder(@RequestBody @Validated BusPayOrder busPayOrder) {
        log.info("更新支付流水入参值 busPayOrder={}",busPayOrder);
        try {
           return busPayOrderService.updatePayOrder(busPayOrder);
        } catch (Exception e) {
            log.error("更新支付流水失败", e);
        }
        return 0;
    }

    /**
     * 支付成功的支付流水
     * @param orderNo 订单号
     * @return 支付流水
     */
    @GetMapping("/feign/pay/order/paid-record")
    public R<BusPayOrder> queryPayOrder(@RequestParam(value = "orderNo") String orderNo) {
        return R.ok(busPayOrderService.queryPaidOrder(orderNo));
    }

    /**
     * 新增或更新支付流水
     * @param busPayOrder 支付流水
     */
    @PostMapping("/feign/pay/order")
    public void insertOrUpdatePayOrder(@RequestBody @Validated BusPayOrder busPayOrder) {
        busPayOrderService.insertOrUpdateBusPayOrder(busPayOrder);
    }

    /**
     * 支付流水列表
     *
     * @param busPayOrderQueryDTO 查询参数
     * @return R<List<BusPayOrderVO>>
     */
    @PreAuthorize(hasPermi = "business:pay:record:list")
    @GetMapping("/pay/order/list")
    public TableDataInfo list(BusPayOrderQueryDTO busPayOrderQueryDTO) {
        return getDataTable(busPayOrderService.queryList(busPayOrderQueryDTO));
    }

    /**
     * 支付流水详情
     * @param id 支付流水id
     * @return R<BusPayOrderDetailVO>
     */
    @PreAuthorize(hasPermi = "business:pay:record:detail")
    @GetMapping("/pay/order/detail/{id}")
    public R<BusPayOrderDetailVO> getDetail(@PathVariable("id") Long id) {
        return R.ok(busPayOrderService.getDetail(id));
    }

}