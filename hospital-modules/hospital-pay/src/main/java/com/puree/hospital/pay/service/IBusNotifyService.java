package com.puree.hospital.pay.service;

/**
 * 业务通知服务接口
 * <AUTHOR>
 * @param <T> 支付通知受理参数
 */
public interface IBusNotifyService<T, N> {

    /**
     * 回调验签和解密
     * @param notifyDTO - 回调参数
     * @param scene     - 业务场景
     * @return - 返回结果（解密后的参数）
     */
    String verifyAndDecryptNotify(T notifyDTO,  String scene);

    /**
     * 支付通知处理
     * @param payNotifyInfo - 支付通知内容
     */
    void handlePaymentNotify(N payNotifyInfo);

    /**
     * 退款通知处理
     * @param refundNotifyInfo - 退款通知内容
     * @return - 返回需要的参数
     */
    String handleRefundNotify(String refundNotifyInfo);
}
