package com.puree.hospital.pay.domain.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * 微信查询订单返回结果
 */
@SuppressWarnings("SpellCheckingInspection")
@Data
public class Transaction {
    @SerializedName("amount")
    private TransactionAmount amount;
    @SerializedName("appid")
    private String appid;
    @SerializedName("attach")
    private String attach;
    @SerializedName("bank_type")
    private String bankType;
    @SerializedName("mchid")
    private String mchid;
    @SerializedName("out_trade_no")
    private String outTradeNo;
    @SerializedName("payer")
    private TransactionPayer payer;
    @SerializedName("promotion_detail")
    private List<PromotionDetail> promotionDetail;
    @SerializedName("success_time")
    private String successTime;
    @SerializedName("trade_state")
    private TradeStateEnum tradeState;
    @SerializedName("trade_state_desc")
    private String tradeStateDesc;
    @SerializedName("trade_type")
    private TradeTypeEnum tradeType;
    @SerializedName("transaction_id")
    private String transactionId;
    //忽略scene_info字段，里面有device_id商户端设备号

    public Transaction() {
    }


    public enum TradeStateEnum {
        @SerializedName("SUCCESS")
        SUCCESS,
        @SerializedName("REFUND")
        REFUND,
        @SerializedName("NOTPAY")
        NOTPAY,
        @SerializedName("CLOSED")
        CLOSED,
        @SerializedName("REVOKED")
        REVOKED,
        @SerializedName("USERPAYING")
        USERPAYING,
        @SerializedName("PAYERROR")
        PAYERROR,
        @SerializedName("ACCEPT")
        ACCEPT;

        TradeStateEnum() {
        }
    }

    public enum TradeTypeEnum {
        @SerializedName("JSAPI")
        JSAPI,
        @SerializedName("NATIVE")
        NATIVE,
        @SerializedName("APP")
        APP,
        @SerializedName("MICROPAY")
        MICROPAY,
        @SerializedName("MWEB")
        MWEB,
        @SerializedName("FACEPAY")
        FACEPAY;

        TradeTypeEnum() {
        }
    }

    @Data
    public static class TransactionPayer {
        @SerializedName("openid")
        private String openid;

        public TransactionPayer() {
        }
    }
    @Data
    public static class TransactionAmount {
        @SerializedName("currency")
        private String currency;
        @SerializedName("payer_currency")
        private String payerCurrency;
        @SerializedName("payer_total")
        private Integer payerTotal;
        @SerializedName("total")
        private Integer total;

        public TransactionAmount() {
        }
    }
}