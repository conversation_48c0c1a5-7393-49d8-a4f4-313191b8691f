package com.puree.hospital.pay.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.pay.api.model.BusPayConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface BusPayConfigMapper extends BaseMapper<BusPayConfig> {

    int insertSelective(BusPayConfig record);

    BusPayConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BusPayConfig record);

    BusPayConfig queryBySerialNo(String serialNo);


    /**
     * 根据医院ID列表查询支付配置列表，返回Vo对象
     * @param hospitalIds 医院ID列表
     * @return 支付配置列表
     */
    List<BusPayConfig> selectListByHospitalIds(@Param("hospitalIds") Set<Long> hospitalIds);
}
