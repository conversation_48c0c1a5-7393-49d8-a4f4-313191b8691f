package com.puree.hospital.pay.domain.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * 微信查询订单返回结果明细
 */
@Data
@SuppressWarnings("SpellCheckingInspection")
public class PromotionDetail {
    @SerializedName("coupon_id")
    private String couponId;
    @SerializedName("name")
    private String name;
    @SerializedName("scope")
    private ScopeEnum scope;
    @SerializedName("type")
    private TypeEnum type;
    @SerializedName("amount")
    private Integer amount;
    @SerializedName("stock_id")
    private String stockId;
    @SerializedName("wechatpay_contribute")
    private Integer wechatpayContribute;
    @SerializedName("merchant_contribute")
    private Integer merchantContribute;
    @SerializedName("other_contribute")
    private Integer otherContribute;
    @SerializedName("currency")
    private String currency;
    @SerializedName("goods_detail")
    private List<PromotionGoodsDetail> goodsDetail;

    public PromotionDetail() {
    }

    public enum ScopeEnum {
        @SerializedName("GLOBAL")
        GLOBAL,
        @SerializedName("SINGLE")
        SINGLE;

        ScopeEnum() {
        }
    }


    public enum TypeEnum {
        @SerializedName("CASH")
        CASH,
        @SerializedName("NOCASH")
        NOCASH;

        TypeEnum() {
        }
    }
    @Data
    public static class PromotionGoodsDetail {
        @SerializedName("goods_id")
        private String goodsId;
        @SerializedName("quantity")
        private Integer quantity;
        @SerializedName("unit_price")
        private Integer unitPrice;
        @SerializedName("discount_amount")
        private Integer discountAmount;
        @SerializedName("goods_remark")
        private String goodsRemark;

        public PromotionGoodsDetail() {
        }
    }
}