package com.puree.hospital.pay.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.RemoteBusOrderAfterSalesService;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.pay.api.model.dto.WxNotifyDTO;
import com.puree.hospital.pay.api.model.dto.BusPayDTO;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import com.puree.hospital.pay.infrastructure.sybpay.SybUtil;
import com.puree.hospital.pay.infrastructure.wechatpay.WxPayUtils;
import com.puree.hospital.pay.service.IBusNotifyService;
import com.puree.hospital.pay.service.IBusPayService;
import com.puree.hospital.pay.service.impl.payment.PayServiceFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPayController extends BaseController {

    private final PayServiceFactory payServiceFactory;
    private final WxPayUtils wxPayUtils;
    @Resource
    private RemoteBusOrderAfterSalesService remoteBusOrderAfterSalesService;

    /**
     * 微信/通联统一下单支付
     * @return AjaxResult
     */
    @PostMapping("/feign/pay/payment")
    @Log(title = "支付", businessType = BusinessType.OTHER)
    public <P> AjaxResult pay(@RequestBody @Validated BusPayDTO busPayDTO) {
        P pay;
        log.info("支付入参值={}",busPayDTO);
        IBusPayService<P,?,?> busPayService = payServiceFactory.getPayService(busPayDTO.getPayWay());
        pay = busPayService.pay(busPayDTO);
        log.info("支付返回值={}",pay);
        return AjaxResult.success(pay);
    }

    /**
     * 微信/通联申请退款
     * @return AjaxResult
      */
    @PostMapping("/feign/pay/refund")
    @Log(title = "退款", businessType = BusinessType.OTHER)
    public <T> AjaxResult refund(@RequestBody @Validated BusRefundPayDTO busRefundPayDTO) {
        T refund;
        log.info("退款入参值={}", busRefundPayDTO);
        IBusPayService<?, T,?> busPayService = payServiceFactory.getPayService(busRefundPayDTO.getPayWay());
        refund = busPayService.refund(busRefundPayDTO);
        log.info("退款返回值={}", refund);
        return AjaxResult.success(refund);
    }

    /**
     * 微信/通联交易结果查询
     * @return AjaxResult
     */
    @PostMapping("/feign/pay/query")
    @Log(title = "查询支付结果", businessType = BusinessType.OTHER)
    public <Q> AjaxResult query(@RequestBody @Validated BusQueryDTO busQueryDTO) {
        Q query;
        log.info("查询支付结果入参值={}", busQueryDTO);
        IBusPayService<?,?,Q> busPayService = payServiceFactory.getPayService(busQueryDTO.getPayWay());
        query = busPayService.query(busQueryDTO);
        log.info("查询支付结果返回值={}", query);
        return AjaxResult.success(query);
    }

    /**
     * 问诊/药品/服务包支付回调
     *
     * @param request - 请求对象
     * @param response - 响应对象
     */
    @PostMapping("/wx/pay/notify")
    @Log(title = "问诊/药品/服务包支付回调")
    public void payNotify(HttpServletRequest request, HttpServletResponse response) {
        JSONObject resObj = new JSONObject();
        try {
            IBusNotifyService<WxNotifyDTO,String> busPayService = payServiceFactory.getNotifyService(PaysTypeEnum.WECHAT_PAY.getCode());
            WxNotifyDTO notifyDTO = wxPayUtils.getNotifyDTO(request);
            String plainText = busPayService.verifyAndDecryptNotify(notifyDTO, "微信支付回调");
            if (StringUtils.isNotEmpty(plainText)) {
                busPayService.handlePaymentNotify(plainText);
                response.setStatus(200);
                resObj.put("code", "SUCCESS");
                resObj.put("message", "SUCCESS");
            } else {
                response.setStatus(500);
                resObj.put("code", "FAIL");
                resObj.put("message", "失败");
            }
            // 响应请求结果
            response.setHeader("Content-type", "application/json");
            response.getOutputStream().write(resObj.toJSONString().getBytes(StandardCharsets.UTF_8));
            response.flushBuffer();
        } catch (Exception e) {
            log.error("微信支付回调业务异常:{}", e.getMessage(),e);
        }
    }

    /**
     * 问诊/药品/服务包退款回调
     *
     * @param request - 请求对象
     * @param response - 响应对象
     */
    @Log(title = "问诊/药品/服务包退款回调")
    @PostMapping("/wx/refund/notify")
    public void refundNotify(HttpServletRequest request, HttpServletResponse response) {
        JSONObject resObj = new JSONObject();
        Long orderAfterSaleId = null;
        try {
            IBusNotifyService<WxNotifyDTO,String> busPayService = payServiceFactory.getNotifyService(PaysTypeEnum.WECHAT_PAY.getCode());
            WxNotifyDTO notifyDTO = wxPayUtils.getNotifyDTO(request);
            String plainText = busPayService.verifyAndDecryptNotify(notifyDTO, "微信退款回调");
            if (StringUtils.isNotEmpty(plainText)) {
                String orderAfterSaleIdStr = busPayService.handleRefundNotify(plainText);
                orderAfterSaleId = Optional.ofNullable(orderAfterSaleIdStr)
                        .filter(StringUtils::isNotEmpty)
                        .map(Long::valueOf)
                        .orElse(null);;
                response.setStatus(200);
                resObj.put("code", "SUCCESS");
                resObj.put("message", "SUCCESS");
            } else {
                response.setStatus(500);
                resObj.put("code", "ERROR");
                resObj.put("message", "签名错误");
            }
            // 响应请求结果
            response.setHeader("Content-type", "application/json");
            response.getOutputStream().write(resObj.toJSONString().getBytes(StandardCharsets.UTF_8));
            response.flushBuffer();
        } catch (Exception e) {
            log.error("微信退款回调接口处理失败,售后ID={}，原因:", orderAfterSaleId, e);
            if (!Objects.isNull(orderAfterSaleId)) {
                remoteBusOrderAfterSalesService.updateStatusById(orderAfterSaleId,
                        CodeEnum.REFUSE.getCode(),OrderAfterSalesStatusEnum.UNDER_REFUND.getCode());
            }
        }
    }


    /**
     * 通联支付回调
     *
     * @param request - 请求
     * @param response - 响应
     */
    @Log(title = "通联支付回调")
    @PostMapping("/tl/pay/notify")
    public void tongLianPayNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            IBusNotifyService<TreeMap<String, String>,TreeMap<String,String>> busPayService = payServiceFactory.getNotifyService(PaysTypeEnum.TONGLIAN_PAY.getCode());
//            busPayService.verifyAndDecryptNotify(request);
            TreeMap<String, String> notifyDTO = SybUtil.getNotifyMap(request);
            response.setCharacterEncoding("UTF-8");
            log.info("通联支付回调 resultTreeMap={}", JSON.toJSONString(notifyDTO));
            /*业务处理*/
            busPayService.handlePaymentNotify(notifyDTO);
        } catch (Exception e) {
            log.error("通联支付错误日志e={}",e.getMessage(), e);
        }finally{
            response.getOutputStream().write("success".getBytes());
            response.flushBuffer();
        }
    }
}
