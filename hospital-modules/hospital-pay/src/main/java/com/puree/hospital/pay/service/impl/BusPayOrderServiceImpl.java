package com.puree.hospital.pay.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.puree.hospital.business.api.RemoteBusPatientService;
import com.puree.hospital.business.api.model.BusPatientDto;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.business.api.model.BusPatientVO;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.insurance.api.RemotePaymentService;
import com.puree.hospital.insurance.api.model.MiPayResult;
import com.puree.hospital.pay.api.constants.BusPayOrderStatusEnum;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.dto.BusPayOrderQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderDetailVO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderVO;
import com.puree.hospital.pay.mapper.BusOrderRefundRecordMapper;
import com.puree.hospital.pay.mapper.BusPayOrderMapper;
import com.puree.hospital.pay.service.IBusPayOrderService;
import com.puree.hospital.pay.service.impl.payment.PayServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单支付记录服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusPayOrderServiceImpl implements IBusPayOrderService {

    @Resource
    private BusPayOrderMapper busPayOrderMapper;

    @Resource
    private BusOrderRefundRecordMapper busOrderRefundRecordMapper;

    @Resource
    private PayServiceFactory payServiceFactory;

    @Resource
    private RemotePaymentService remotePaymentService;

    @Resource
    private RemoteBusPatientService remoteBusPatientService;

    /**
     * 添加支付流水
     * @param busPayOrder - 支付流水
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void insertOrUpdateBusPayOrder(BusPayOrder busPayOrder) {
        if (busPayOrder == null || busPayOrder.getOutTradeNo() == null ) {
            throw new ServiceException("支付流水信息有误");
        }
        int result = busPayOrderMapper.updateByOutOrderNo(busPayOrder);
        if (result == 0){
            busPayOrderMapper.insert(busPayOrder);
            log.info("插入支付流水, busPayOrder: {}", busPayOrder);
        }
    }

    /**
     * 查询支付流水信息 - 根据订单号
     * @param outTradeNo - 我方流水号
     * @param transactionId - 三方流水号
     * @param payWay - 支付方式
     * @return 支付流水信息
     */
    @Override
    public BusPayOrder queryPayOrder(String outTradeNo, String transactionId, String payWay) {
        LambdaQueryWrapper<BusPayOrder> busPayOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        busPayOrderLambdaQueryWrapper
                .eq(StringUtils.isNotEmpty(outTradeNo), BusPayOrder::getOutTradeNo, outTradeNo)
                .eq(StringUtils.isNotEmpty(transactionId), BusPayOrder::getTransactionId, transactionId)
                .eq(StringUtils.isNotEmpty(payWay), BusPayOrder::getPayWay, payWay)
                .last("limit 1");
        BusPayOrder busPayOrder = busPayOrderMapper.selectOne(busPayOrderLambdaQueryWrapper);
        if (busPayOrder == null && StringUtils.isNotEmpty(payWay)) {
            BusQueryDTO busQueryDTO = new BusQueryDTO();
            busQueryDTO.setOutTradeNo(outTradeNo);
            busQueryDTO.setTransactionId(transactionId);
            payServiceFactory.getPayService(payWay).query(busQueryDTO);
            return busPayOrderMapper.selectOne(busPayOrderLambdaQueryWrapper);
        }
        return busPayOrder;
    }

    /**
     * 查询状态为已经支付成功的支付流水信息
     * @param orderNo - 订单号
     * @return - 支付流水信息
     */
    @Override
    public BusPayOrder queryPaidOrder(String orderNo) {
        if (StringUtils.isEmpty(orderNo)){
            return null;
        }
        LambdaQueryWrapper<BusPayOrder> busPayOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        busPayOrderLambdaQueryWrapper
                .eq(BusPayOrder::getOrderNo, orderNo)
                .eq(BusPayOrder::getStatus, BusPayOrderStatusEnum.PAYED.getValue())
                .last("limit 1");
        return busPayOrderMapper.selectOne(busPayOrderLambdaQueryWrapper);
    }

    /**
     * 更新支付流水信息 - 根据id或者订单号
     * @param busPayOrder - 支付流水信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public int updatePayOrder(BusPayOrder busPayOrder) {
        int result = busPayOrderMapper.updateByOutOrderNo(busPayOrder);
        if (result == 0){
            log.error("更新支付流水失败, busPayOrder: {}", busPayOrder);
            return 0;
        }
        log.info("更新支付流水结果, busPayOrder: {}, result: {}", busPayOrder,result);
        return result;
    }

    @Override
    public List<BusPayOrderVO> queryList(BusPayOrderQueryDTO busPayOrderQueryDTO) {
        if (Objects.isNull(busPayOrderQueryDTO) || Objects.isNull(busPayOrderQueryDTO.getHospitalId())) {
            return new Page<>();
        }
        if (StringUtils.isNotBlank(busPayOrderQueryDTO.getFamilyName())) {
            BusPatientDto dto = new BusPatientDto();
            dto.setHospitalId(busPayOrderQueryDTO.getHospitalId());
            dto.setName(busPayOrderQueryDTO.getFamilyName());
            //通过远程调用获取就诊人信息
            R<List<BusPatientFamilyVo>> listR = remoteBusPatientService.listFamilyInfo(dto);
            if (hasSuccessData(listR)) {
                busPayOrderQueryDTO.setPatientIdList(listR.getData().stream().map(BusPatientFamilyVo::getId).distinct().collect(Collectors.toList()));
            }
        }
        PageUtil.startPage();
        return queryListByParam(busPayOrderQueryDTO);
    }

    @Override
    public BusPayOrderDetailVO getDetail(Long id) {
        BusPayOrderQueryDTO queryDTO = new BusPayOrderQueryDTO();
        queryDTO.setId(id);
        List<BusPayOrderVO> list = this.queryListByParam(queryDTO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        BusPayOrderDetailVO detailVO = new BusPayOrderDetailVO();
        BeanUtils.copyProperties(list.get(0), detailVO);
        //医保支付需要将医保支付相关明细返回
        if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(detailVO.getPayWay())) {
            R<MiPayResult> miPayResultR = remotePaymentService.queryPayResult(detailVO.getOrderNo());
            if (Objects.nonNull(miPayResultR) && miPayResultR.isSuccess() && Objects.nonNull(miPayResultR.getData())) {
                detailVO.setMiMedType(miPayResultR.getData().getMedType());
                detailVO.setOwnPayAmt(miPayResultR.getData().getOwnPayAmt());
                detailVO.setFundPay(miPayResultR.getData().getFundPay());
                detailVO.setPsnAcctPay(miPayResultR.getData().getPsnAcctPay());
                detailVO.setOtherCashAmount(miPayResultR.getData().getOtherCashAmount());
            }
        }
        //根据订单类型查询就诊人相关信息
        if (Objects.nonNull(detailVO.getPatientId())) {
            BusPatientDto dto = new BusPatientDto();
            dto.setId(detailVO.getPatientId());
            R<BusPatientFamilyVo> result = remoteBusPatientService.userInfo(dto);
            if (hasSuccessData(result)) {
                detailVO.setFamilyPhoneNumber(result.getData().getCellPhoneNumber());
                detailVO.setFamilyIdCard(result.getData().getIdNumber());
                detailVO.setFamilyName(result.getData().getName());
                detailVO.setFamilySex(result.getData().getSex());
            }
        } else if (Objects.nonNull(detailVO.getUserId())) {
            R<List<BusPatientVO>> result = remoteBusPatientService.getPhonesByPatientIds(Collections.singletonList(detailVO.getUserId()));
            if (hasSuccessData(result)) {
                detailVO.setFamilyPhoneNumber(result.getData().get(0).getPhoneNumber());
            }
        }
        return detailVO;
    }

    private boolean hasSuccessData(R<?> result) {
        return Objects.nonNull(result) && result.isSuccess() && Objects.nonNull(result.getData());
    }

    /**
     * 根据查询条件查询支付流水信息
     *
     * @param busPayOrderQueryDTO - 查询条件
     * @return - 支付流水信息
     */
    private List<BusPayOrderVO> queryListByParam(BusPayOrderQueryDTO busPayOrderQueryDTO) {
        List<BusPayOrderVO> list = busPayOrderMapper.queryList(busPayOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> outTradeNoList = list.stream().map(BusPayOrderVO::getOutTradeNo).distinct().collect(Collectors.toList());
            BusOrderRefundRecordDTO refundRecordDTO = new BusOrderRefundRecordDTO();
            refundRecordDTO.setOutTradeNoList(outTradeNoList);
            //查询退款关联的退款记录
            List<BusOrderRefundRecordVO> refundRecordList = busOrderRefundRecordMapper.queryList(refundRecordDTO);
            Map<String, List<BusOrderRefundRecordVO>> map = new HashMap<>(list.size());
            if (CollectionUtil.isNotEmpty(refundRecordList)) {
                map.putAll(refundRecordList.stream().collect(Collectors.groupingBy(BusOrderRefundRecordVO::getOutTradeNo)));
            }
            list.forEach(p -> {
                p.setOrderRefundList(map.get(p.getOutTradeNo()));
            });
        }
        return list;
    }
}
