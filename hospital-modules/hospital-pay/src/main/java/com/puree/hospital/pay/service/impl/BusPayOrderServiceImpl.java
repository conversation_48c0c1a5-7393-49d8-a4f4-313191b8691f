package com.puree.hospital.pay.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.app.api.model.dto.BusPatientFamilyQueryDTO;
import com.puree.hospital.business.api.RemoteBusPatientService;
import com.puree.hospital.business.api.model.BusPatientVO;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.insurance.api.RemotePaymentService;
import com.puree.hospital.insurance.api.model.MiPayResult;
import com.puree.hospital.pay.api.constants.BusPayOrderStatusEnum;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.dto.BusPayOrderQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.enums.RefundStatusEnum;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderDetailVO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderVO;
import com.puree.hospital.pay.mapper.BusOrderRefundRecordMapper;
import com.puree.hospital.pay.mapper.BusPayOrderMapper;
import com.puree.hospital.pay.service.IBusPayOrderService;
import com.puree.hospital.pay.service.impl.payment.PayServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单支付记录服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusPayOrderServiceImpl implements IBusPayOrderService {

    /**
     * 已支付状态列表
     */
    private static final List<String> PAID_STATUS_LIST = Lists.newArrayList(BusPayOrderStatusEnum.PAYED.getValue(),
            BusPayOrderStatusEnum.PARTIAL_REFUNDED.getValue(),
            BusPayOrderStatusEnum.REFUNDED.getValue());

    @Resource
    private BusPayOrderMapper busPayOrderMapper;

    @Resource
    private PayServiceFactory payServiceFactory;

    @Resource
    private RemotePaymentService remotePaymentService;

    @Resource
    private RemotePatientFamilyService remotePatientFamilyService;

    @Resource
    private RemoteBusPatientService remoteBusPatientService;

    @Resource
    private BusOrderRefundRecordMapper busOrderRefundRecordMapper;

    /**
     * 添加支付流水
     * @param busPayOrder - 支付流水
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void insertOrUpdateBusPayOrder(BusPayOrder busPayOrder) {
        if (busPayOrder == null || busPayOrder.getOutTradeNo() == null ) {
            throw new ServiceException("支付流水信息有误");
        }
        int result = busPayOrderMapper.updateByOutOrderNo(busPayOrder);
        if (result == 0){
            busPayOrderMapper.insert(busPayOrder);
            log.info("插入支付流水, busPayOrder: {}", busPayOrder);
        }
    }

    /**
     * 查询支付流水信息 - 根据订单号
     * @param outTradeNo - 我方流水号
     * @param transactionId - 三方流水号
     * @param payWay - 支付方式
     * @return 支付流水信息
     */
    @Override
    public BusPayOrder queryPayOrder(String outTradeNo, String transactionId, String payWay) {
        LambdaQueryWrapper<BusPayOrder> busPayOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        busPayOrderLambdaQueryWrapper
                .eq(StringUtils.isNotEmpty(outTradeNo), BusPayOrder::getOutTradeNo, outTradeNo)
                .eq(StringUtils.isNotEmpty(transactionId), BusPayOrder::getTransactionId, transactionId)
                .eq(StringUtils.isNotEmpty(payWay), BusPayOrder::getPayWay, payWay)
                .last("limit 1");
        BusPayOrder busPayOrder = busPayOrderMapper.selectOne(busPayOrderLambdaQueryWrapper);
        if (busPayOrder == null && StringUtils.isNotEmpty(payWay)) {
            BusQueryDTO busQueryDTO = new BusQueryDTO();
            busQueryDTO.setOutTradeNo(outTradeNo);
            busQueryDTO.setTransactionId(transactionId);
            payServiceFactory.getPayService(payWay).query(busQueryDTO);
            return busPayOrderMapper.selectOne(busPayOrderLambdaQueryWrapper);
        }
        return busPayOrder;
    }

    /**
     * 查询状态为已经支付成功的支付流水信息
     * @param orderNo - 订单号
     * @return - 支付流水信息
     */
    @Override
    public BusPayOrder queryPaidOrder(String orderNo) {
        if (StringUtils.isEmpty(orderNo)){
            return null;
        }
        LambdaQueryWrapper<BusPayOrder> busPayOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        busPayOrderLambdaQueryWrapper
                .eq(BusPayOrder::getOrderNo, orderNo)
                .in(BusPayOrder::getStatus, PAID_STATUS_LIST)
                .last("limit 1");
        return busPayOrderMapper.selectOne(busPayOrderLambdaQueryWrapper);
    }

    /**
     * 更新支付流水信息 - 根据id或者订单号
     * @param busPayOrder - 支付流水信息
     */
    @Override
    public int updatePayOrder(BusPayOrder busPayOrder) {
        int result = busPayOrderMapper.updateByOutOrderNo(busPayOrder);
        if (result == 0){
            log.error("更新支付流水失败, busPayOrder: {}", busPayOrder);
            return 0;
        }
        log.info("更新支付流水结果, busPayOrder: {}, result: {}", busPayOrder,result);
        return result;
    }

    @Override
    public List<BusPayOrderVO> queryList(BusPayOrderQueryDTO busPayOrderQueryDTO) {
        if (Objects.isNull(busPayOrderQueryDTO) || Objects.isNull(busPayOrderQueryDTO.getHospitalId())) {
            return new Page<>();
        }
        if (StringUtils.isNotBlank(busPayOrderQueryDTO.getFamilyName())) {
            BusPatientFamilyQueryDTO queryDTO = new BusPatientFamilyQueryDTO();
            queryDTO.setHospitalId(busPayOrderQueryDTO.getHospitalId());
            queryDTO.setName(busPayOrderQueryDTO.getFamilyName());
            //通过远程调用获取就诊人信息
            R<List<BusPatientFamilyVo>> listR = remotePatientFamilyService.feignList(queryDTO);
            if (hasSuccessData(listR)) {
                busPayOrderQueryDTO.setPatientIdList(listR.getData().stream().map(BusPatientFamilyVo::getId).distinct().collect(Collectors.toList()));
            }
        }
        PageUtil.startPage();
        return queryListByParam(busPayOrderQueryDTO);
    }

    @Override
    public BusPayOrderDetailVO getDetail(Long id) {
        BusPayOrderQueryDTO queryDTO = new BusPayOrderQueryDTO();
        queryDTO.setId(id);
        List<BusPayOrderVO> list = this.queryListByParam(queryDTO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        BusPayOrderDetailVO detailVO = new BusPayOrderDetailVO();
        BeanUtils.copyProperties(list.get(0), detailVO);
        //医保支付需要将医保支付相关明细返回
        if (PaysTypeEnum.isInsurancePay(detailVO.getPayWay())) {
            R<MiPayResult> miPayResultR = remotePaymentService.queryPayResult(detailVO.getOrderNo());
            if (Objects.nonNull(miPayResultR) && miPayResultR.isSuccess() && Objects.nonNull(miPayResultR.getData())) {
                detailVO.setMiMedType(miPayResultR.getData().getMedType());
                detailVO.setOwnPayAmt(miPayResultR.getData().getOwnPayAmt());
                detailVO.setFundPay(miPayResultR.getData().getFundPay());
                detailVO.setPsnAcctPay(miPayResultR.getData().getPsnAcctPay());
                detailVO.setOtherCashAmount(miPayResultR.getData().getOtherCashAmount());
            }
        }
        BusOrderRefundRecordDTO query = new BusOrderRefundRecordDTO();
        query.setOutTradeNo(detailVO.getOutTradeNo());
        detailVO.setOrderRefundList(busOrderRefundRecordMapper.queryList(query));
        return detailVO;
    }

    @Override
    public void updatePayOrderAfterRefunded(String outTradeNo, String orderNo) {
        //查询参数
        BusOrderRefundRecordDTO query = new BusOrderRefundRecordDTO();
        query.setOutTradeNo(outTradeNo);
        query.setRefundStatus(RefundStatusEnum.REFUND_SUCCESS.name());
        List<BusOrderRefundRecordVO> refundRecordList = busOrderRefundRecordMapper.queryList(query);
        //支付订单
        BusPayOrder paidOrder = this.queryPaidOrder(orderNo);
        if (paidOrder == null) {
            throw new ServiceException("当前订单支付记录不存在");
        }
        //查询当前支付单已经退款的总金额
        BigDecimal totalRefundAmount = refundRecordList.stream().map(BusOrderRefundRecordVO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BusPayOrder update = new BusPayOrder();
        update.setOrderNo(orderNo);
        log.info("支付金额：{}, 已退款金额：{}", paidOrder.getAmount(), totalRefundAmount);
        //判断是否全部退款
        if (paidOrder.getAmount().compareTo(totalRefundAmount) == 0) {
            update.setStatus(BusPayOrderStatusEnum.REFUNDED.getValue());
        } else {
            update.setStatus(BusPayOrderStatusEnum.PARTIAL_REFUNDED.getValue());
        }
        updatePayOrder(update);
    }

    private boolean hasSuccessData(R<?> result) {
        return Objects.nonNull(result) && result.isSuccess() && Objects.nonNull(result.getData());
    }

    /**
     * 根据查询条件查询支付流水信息
     *
     * @param busPayOrderQueryDTO - 查询条件
     * @return - 支付流水信息
     */
    private List<BusPayOrderVO> queryListByParam(BusPayOrderQueryDTO busPayOrderQueryDTO) {
        List<BusPayOrderVO> list = busPayOrderMapper.queryList(busPayOrderQueryDTO);
        if (CollectionUtil.isNotEmpty(list)) {
            List<Long> userIdList = list.stream().map(BusPayOrderVO::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, BusPatientVO> userMap = new HashMap<>(userIdList.size());
            if (CollectionUtil.isNotEmpty(userIdList)) {
                R<List<BusPatientVO>> result = remoteBusPatientService.getPhonesByPatientIds(userIdList);
                if(hasSuccessData(result)) {
                    userMap.putAll(result.getData().stream().collect(Collectors.toMap(BusPatientVO::getId, v -> v)));
                }
            }
            List<Long> patientIdList = list.stream().map(BusPayOrderVO::getPatientId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, BusPatientFamilyVo> patientMap = new HashMap<>(patientIdList.size());
            if (CollectionUtil.isNotEmpty(patientIdList)) {
                BusPatientFamilyQueryDTO queryDTO = new BusPatientFamilyQueryDTO();
                queryDTO.setHospitalId(busPayOrderQueryDTO.getHospitalId());
                queryDTO.setIdList(patientIdList);
                queryDTO.setNeedQueryHisPatientInfo(true);
                R<List<BusPatientFamilyVo>> result = remotePatientFamilyService.feignList(queryDTO);
                if (hasSuccessData(result)) {
                    patientMap.putAll(result.getData().stream().collect(Collectors.toMap(BusPatientFamilyVo::getId, v -> v)));
                }
            }
            for (BusPayOrderVO vo : list) {
                //设置医保支付类型
                vo.setSettlementType(PaysTypeEnum.isInsurancePay(vo.getPayWay()) ? 2 : 1);
                if (Objects.nonNull(vo.getPatientId())) {
                    BusPatientFamilyVo patient = patientMap.getOrDefault(vo.getPatientId(), new BusPatientFamilyVo());
                    vo.setFamilyPhoneNumber(patient.getCellPhoneNumber());
                    vo.setFamilyIdCard(patient.getIdNumber());
                    vo.setFamilyName(patient.getName());
                    vo.setFamilySex(patient.getSex());
                    vo.setHisPatientNo(patient.getHisPatientNo());
                } else {
                    BusPatientVO patient = userMap.getOrDefault(vo.getUserId(), new BusPatientVO());
                    vo.setFamilyPhoneNumber(patient.getPhoneNumber());
                }
            }
        }
        return list;
    }
}
