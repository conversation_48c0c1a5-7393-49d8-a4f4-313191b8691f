package com.puree.hospital.pay;
import com.puree.hospital.common.security.annotation.EnableCustomConfig;
import com.puree.hospital.common.security.annotation.EnableHospitalFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@EnableCustomConfig
@EnableHospitalFeignClients
@SpringBootApplication
public class HospitalPayApplication {
    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod","false");
        SpringApplication.run(HospitalPayApplication.class, args);
        System.out.println("PAY模块启动成功");
    }
}
