package com.puree.hospital.pay.infrastructure.wechatpay;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "certificate.side")
public class CertificateProperties {
    /**
     * 临时文件目录
     */
    private String tempFileDir;
    /**
     * 阿里云地址
     */
    private String ossUrl;
}
