package com.puree.hospital.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospitalVo;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.domain.BusPayConfigBO;
import com.puree.hospital.pay.mapper.BusPayConfigMapper;
import com.puree.hospital.pay.service.IBusPayConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.puree.hospital.common.core.base.page.PageUtil.startPage;

/**
 * <AUTHOR>
 */
@Service
public class BusPayConfigServiceImpl implements IBusPayConfigService {
    private static final Logger log = LoggerFactory.getLogger(BusPayConfigServiceImpl.class);

    private final BusPayConfigMapper busPayConfigMapper;
    private final RemoteHospitalService hospitalService;

    @Autowired
    public BusPayConfigServiceImpl(BusPayConfigMapper busPayConfigMapper, RemoteHospitalService hospitalService) {
        this.busPayConfigMapper = busPayConfigMapper;
        this.hospitalService = hospitalService;
    }

    @Override
    public int insertOrUpdate(BusPayConfig busPayConfig) {
        log.info("入参={}", busPayConfig);
        // 编辑
        if (busPayConfig.getId() != null) {
            busPayConfig.setUpdateTime(new Date());
            return busPayConfigMapper.updateByPrimaryKeySelective(busPayConfig);
        } else { // 新增
            busPayConfig.setCreateBy(SecurityUtils.getUsername());
            busPayConfig.setCreateTime(new Date());
            try {
                return busPayConfigMapper.insert(busPayConfig);
            } catch (DuplicateKeyException e) {
                log.error("新增支付配置失败,结果={}", e.getMessage(), e);
                throw new ServiceException("该医院下已存在该商户配置，请勿重复添加");
            } catch (Exception e) {
                log.error("新增支付配置失败,结果={}", e.getMessage(), e);
                throw new ServiceException("新增支付配置失败");
            }
        }
    }


    /**
     * 根据医院id查询支付配置信息
     * @param hospitalList  - 医院id列表
     * @return - 支付配置信息
     */
    public List<BusPayConfig> queryInfoReturnVo(Set<Long> hospitalList) {
        return busPayConfigMapper.selectListByHospitalIds(hospitalList);
    }

    /**
     * 根据证书序列号/支付keyId查询支付配置信息
     * @param serialNo 证书序列号/支付keyId
     * @return - 支付配置信息
     */
    @Override
    public BusPayConfig queryBySerialNo(String serialNo) {
        BusPayConfig busPayConfig = busPayConfigMapper.queryBySerialNo(serialNo);
        if (busPayConfig == null) {
            log.error("查询支付配置信息失败 serialNo={}", serialNo);
            throw new ServiceException("该医院支付信息异常");
        }
        return busPayConfigMapper.queryBySerialNo(serialNo);
    }


    /**
     * 更新平台证书编号
     * @param id - 支付配置id
     * @param serialNumber - 平台证书编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updatePlatformCertificateSerialNoById(Long id, String serialNumber) {
        BusPayConfig busPayConfig = new BusPayConfig();
        busPayConfig.setId(id);
        busPayConfig.setPlatformCertificateSerialno(serialNumber);
        busPayConfigMapper.updateById(busPayConfig);
    }

    /**
     * 根据id/mchId查询支付配置
     * @param id - 支付配置id
     * @param mchId - 商户号
     * @return - 返回支付配置
     */
    @Override
    public BusPayConfig selectPayConfig(Long id, String mchId) {
        LambdaQueryWrapper<BusPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(id != null, BusPayConfig::getId, id);
        queryWrapper.eq(StringUtils.isNotEmpty(mchId), BusPayConfig::getMchId, mchId);
        queryWrapper.last(" limit 1");
        BusPayConfig busPayConfig = busPayConfigMapper.selectOne(queryWrapper);
        if (busPayConfig != null) {
            return busPayConfig;
        }
        log.error("查询支付配置信息失败 id={} , mchId={}", id, mchId);
        throw new ServiceException("该支付配置不存在");
    }

    @Override
    public List<BusPayConfigBO> list(String hospitalName, Long hospitalId) {
        // 医院名称模糊查询
        List<BusHospitalVo> hospitalVoList = hospitalService.selectHospitalByName(hospitalName);
        if (hospitalVoList == null || hospitalVoList.isEmpty()){
            return Collections.emptyList();
        }
        Stream<BusHospitalVo> stream;
        if (hospitalId == null) {
            stream = hospitalVoList.stream();
        } else {
            stream = hospitalVoList.stream().filter(h -> h.getId().equals(hospitalId));
        }
        Map<Long, String> collect =stream.collect(Collectors.toMap(BusHospitalVo::getId, BusHospitalVo::getHospitalName));
        startPage();
        List<BusPayConfig> busPayConfigs = queryInfoReturnVo(collect.keySet());
        if (CollectionUtils.isEmpty(busPayConfigs)) {
            return Collections.emptyList();
        }
        List<BusPayConfigBO> list = new ArrayList<>();
        busPayConfigs.forEach(c -> {
            BusPayConfigBO busPayConfigBO = new BusPayConfigBO();
            busPayConfigBO.setHospitalName(collect.get(c.getHospitalId()));
            BeanUtils.copyProperties(c, busPayConfigBO);
            list.add(busPayConfigBO);
        });
        return PageUtil.buildPage(busPayConfigs, list);
    }

}
