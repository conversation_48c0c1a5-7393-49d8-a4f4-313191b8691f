package com.puree.hospital.pay.domain.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * 微信退款返回参数
 */
@Data
public class WxRefundDO {
    @SerializedName("refund_id")
    private String refundId;
    @SerializedName("out_refund_no")
    private String outRefundNo;
    @SerializedName("transaction_id")
    private String transactionId;
    @SerializedName("out_trade_no")
    private String outTradeNo;
    @SerializedName("user_received_account")
    private String userReceivedAccount;
    @SerializedName("success_time")
    private String successTime;
    @SerializedName("create_time")
    private String createTime;
    @SerializedName("promotion_detail")
    private List<Promotion> promotionDetail;
    @SerializedName("amount")
    private Amount amount;
    @SerializedName("channel")
    private Channel channel;
    @SerializedName("funds_account")
    private FundsAccount fundsAccount;
    @SerializedName("status")
    private Status status;


    public enum Status {
        @SerializedName("SUCCESS")
        SUCCESS,
        @SerializedName("CLOSED")
        CLOSED,
        @SerializedName("PROCESSING")
        PROCESSING,
        @SerializedName("ABNORMAL")
        ABNORMAL;

        Status() {
        }
    }

    @SuppressWarnings("SpellCheckingInspection")
    public enum FundsAccount {
        @SerializedName("UNSETTLED")
        UNSETTLED,
        @SerializedName("AVAILABLE")
        AVAILABLE,
        @SerializedName("UNAVAILABLE")
        UNAVAILABLE,
        @SerializedName("OPERATION")
        OPERATION,
        @SerializedName("BASIC")
        BASIC,
        @SerializedName("ECNY_BASIC")
        ECNY_BASIC;

        FundsAccount() {
        }
    }

    public enum Channel {
        @SerializedName("ORIGINAL")
        ORIGINAL,
        @SerializedName("BALANCE")
        BALANCE,
        @SerializedName("OTHER_BALANCE")
        OTHER_BALANCE,
        @SerializedName("OTHER_BANKCARD")
        OTHER_BANKCARD;

        Channel() {
        }
    }
    @Data
    public static class Amount {
        @SerializedName("total")
        private Long total;
        @SerializedName("refund")
        private Long refund;
        @SerializedName("from")
        private List<FundsFromItem> from;
        @SerializedName("payer_total")
        private Long payerTotal;
        @SerializedName("payer_refund")
        private Long payerRefund;
        @SerializedName("settlement_refund")
        private Long settlementRefund;
        @SerializedName("settlement_total")
        private Long settlementTotal;
        @SerializedName("discount_refund")
        private Long discountRefund;
        @SerializedName("currency")
        private String currency;
        @SerializedName("refund_fee")
        private Long refundFee;

        public Amount() {
        }

        public static class FundsFromItem {
            @SerializedName("amount")
            private Long amount;
            @SerializedName("account")
            private Account account;

            public FundsFromItem() {
            }
        }

        public enum Account {
            @SerializedName("AVAILABLE")
            AVAILABLE,
            @SerializedName("UNAVAILABLE")
            UNAVAILABLE;

            Account() {
            }
        }
    }
}