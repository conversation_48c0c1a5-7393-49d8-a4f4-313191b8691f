package com.puree.hospital.pay.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 微信支付配置
 *
 * <AUTHOR>
 * @date 2024/12/24
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class BusPayConfigBO extends Entity {

    /**
     * 医院ID
     */
    @NotNull(message = "医院ID不能为空")
    private Long hospitalId;
    /**
     * 公众号/小程序 APP ID
     */
    private String appId;
    /** 商户号类型 1 公众号商品 2公众号药品*/
    private String mchType;

    /**
     * 医院名称
     */
    @TableField(exist = false)
    private String hospitalName;

    /**
     * 商户号ID
     */
    @NotBlank(message = "商户号ID不能为空")
    private String mchId;
    /**
     * 密钥
     */
    @NotBlank(message = "密钥不能为空")
    private String apiV3Key;
    /**
     * 商户证书编号
     */
    private String mchSerialNo;
    /**
     * 平台证书编号
     */
    private String platformCertificateSerialno;
    /**
     * 支付成功回调地址
     */
    @NotBlank(message = "支付成功回调地址不能为空")
    private String payNotifyUrl;
    /**
     * 退款成功回调地址
     */
    @NotBlank(message = "退款成功回调地址不能为空")
    private String refundNotifyUrl;
    /**
     * 平台证书地址
     */
    @Deprecated
    private String certificateUrl;
    /**
     * 证书-私钥内容
     */
    private String certificate;
    /**
     * 支付方式 1 微信支付 2 通联支付
     */
    private String payWay;
    /**
     * 私钥
     */
    private String privateKey;


    /**
     * 微信支付公钥 ID
     */
    private String wxPublicKeyId;
    /**
     * 微信支付公钥
     */
    private String wxPublicKey;

    /** 应用程序类型 */
    @TableField(exist = false)
    private String appType;

}
