package com.puree.hospital.pay.service;

import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;

import java.util.List;

/**
 * <p>
 * 订单退款记录接口
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/29 20:53
 */
public interface IBusOrderRefundRecordService {

    /**
     * 保存退款记录
     *
     * @param refundRecordDTO 退款记录
     * @return 保存结果
     */
    Boolean sava(BusOrderRefundRecordDTO refundRecordDTO);

    /**
     * 更新退款记录
     *
     * @param refundRecordDTO 退款记录
     * @return 更新结果
     */
    Boolean update(BusOrderRefundRecordDTO refundRecordDTO);

    /**
     * 根据id查询退款记录
     *
     * @param id 退款记录id
     * @return 退款记录
     */
    BusOrderRefundRecordVO getById(Long id);

    /**
     * 根据条件查询退款记录
     *
     * @param query 查询条件
     * @return 退款记录列表
     */
    List<BusOrderRefundRecordVO> queryList(BusOrderRefundRecordDTO query);

    /**
     * 根据退款单号查询退款记录
     *
     * @param outRefundNo 退款单号
     * @return 退款记录
     */
    BusOrderRefundRecordVO getByOutRefundNo(String outRefundNo);

}
