package com.puree.hospital.pay.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusPayOrderQueryDTO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderVO;

import java.util.List;

/**
 * 订单支付记录流水接口
 *
 * <AUTHOR>
 */
public interface BusPayOrderMapper extends BaseMapper<BusPayOrder> {

    /**
     * 根据交易流水号或者三方订单号更新支付流水信息
     *
     * @param busPayOrder 支付流水信息
     * @return 更新数量
     */
    int updateByOutOrderNo(BusPayOrder busPayOrder);

    /**
     * 查询订单支付记录列表
     *
     * @param queryDTO 查询参数
     * @return 列表西悉尼
     */
    List<BusPayOrderVO> queryList(BusPayOrderQueryDTO queryDTO);
}