package com.puree.hospital.pay.domain.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/***
 * 微信退款返回结果明细
 */
@Data
public class Promotion {
    @SerializedName("promotion_id")
    private String promotionId;
    @SerializedName("amount")
    private Long amount;
    @SerializedName("refund_amount")
    private Long refundAmount;
    @SerializedName("goods_detail")
    private List<GoodsDetail> goodsDetail;
    @SerializedName("scope")
    private Scope scope;
    @SerializedName("type")
    private Type type;

    public Promotion() {
    }

    public enum Type {
        @SerializedName("COUPON")
        COUPON,
        @SerializedName("DISCOUNT")
        DISCOUNT;

        private Type() {
        }
    }
    public enum Scope {
        @SerializedName("GLOBAL")
        GLOBAL,
        @SerializedName("SINGLE")
        SINGLE;

        private Scope() {
        }
    }

    @Data
    public static class GoodsDetail {
        @SerializedName("merchant_goods_id")
        private String merchantGoodsId;
        @SerializedName("wechatpay_goods_id")
        private String wechatpayGoodsId;
        @SerializedName("goods_name")
        private String goodsName;
        @SerializedName("unit_price")
        private Long unitPrice;
        @SerializedName("refund_amount")
        private Long refundAmount;
        @SerializedName("refund_quantity")
        private Integer refundQuantity;

        public GoodsDetail() {
        }
    }
}