package com.puree.hospital.pay.service;


import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.domain.BusPayConfigBO;

import java.util.List;

public interface IBusPayConfigService {
    /**
     * 新增支付配置信息-如果存在则更新
     * @param busPayConfig - 支付配置信息
     * @return - 返回结果
     */
    int insertOrUpdate(BusPayConfig busPayConfig);


    /**
     * 根据回调的证书序列号/支付keyId查询支付配置
     * @param serialNo 证书序列号/支付keyId
     * @return 支付配置
     */
    BusPayConfig queryBySerialNo(String serialNo);
    /**
     * 根据id更新平台证书序列号
     * @param id - 支付配置id
     * @param serialNumber - 平台证书序列号
     */
    void updatePlatformCertificateSerialNoById(Long id, String serialNumber);

    /**
     * 根据id/mchId查询支付配置
     * @param id - 支付配置id
     * @param mchId - 商户号
     * @return - 返回支付配置
     */
    BusPayConfig selectPayConfig(Long id, String mchId);

    /**
     * 支付配置列表
     * @param hospitalId
     * @return - 返回支付配置列表
     */
    List<BusPayConfigBO> list(String hospitalName, Long hospitalId);
}
