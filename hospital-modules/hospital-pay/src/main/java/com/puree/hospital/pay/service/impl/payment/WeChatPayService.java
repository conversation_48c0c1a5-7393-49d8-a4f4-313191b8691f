package com.puree.hospital.pay.service.impl.payment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.puree.hospital.app.api.RemoteBasePaymentService;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.common.core.constant.CacheConstants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.constant.PayWayConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.five.api.RemoteServicePackOrderService;
import com.puree.hospital.pay.api.constants.BusPayOrderStatusEnum;
import com.puree.hospital.pay.api.model.BusPayConfig;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.dto.PrepayResponseDTO;
import com.puree.hospital.pay.api.model.dto.TransactionDTO;
import com.puree.hospital.pay.api.model.dto.WxNotifyDTO;
import com.puree.hospital.pay.api.model.dto.BusPayDTO;
import com.puree.hospital.pay.api.model.dto.BusQueryDTO;
import com.puree.hospital.pay.api.model.dto.BusRefundPayDTO;
import com.puree.hospital.pay.api.model.dto.WxRefundDTO;
import com.puree.hospital.pay.api.model.enums.RefundStatusEnum;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import com.puree.hospital.pay.domain.dto.PrepayResponseDO;
import com.puree.hospital.pay.domain.dto.Transaction;
import com.puree.hospital.pay.domain.dto.WxRefundDO;
import com.puree.hospital.pay.infrastructure.wechatpay.WxConstants;
import com.puree.hospital.pay.infrastructure.wechatpay.WxPayUtils;
import com.puree.hospital.pay.service.IBusOrderRefundRecordService;
import com.puree.hospital.pay.service.IBusPayConfigService;
import com.puree.hospital.pay.service.IBusPayOrderService;
import com.puree.hospital.pay.service.IBusPayService;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.http.HttpHeaders;
import com.wechat.pay.java.core.http.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/***
 * 微信支付实现类
 *<AUTHOR>
 *@version 1.0
 *@date  2025/1/10 18:21
 */
@SuppressWarnings("SpellCheckingInspection")
@Slf4j
@Service(PayWayConstant.WECHAT_PAY + "_"+ IBusPayService.PAY_SUFFIX)
public class WeChatPayService extends BasePayService<PrepayResponseDTO, WxRefundDTO, TransactionDTO, WxNotifyDTO, String> {

    private final WxPayUtils wxPayUtils;
    @Resource
    private RemoteServicePackOrderService remoteServicePackOrderService;
    @Resource
    private RemoteBasePaymentService remoteBasePaymentService;
    @Lazy
    @Resource
    private WeChatPayService weChatPayService;
    @Resource
    private RedisService redisService;

    @Resource
    private RemoteHospitalService remoteHospitalService;

    @Resource
    private IBusPayOrderService busPayOrderService;

    @Resource
    private IBusOrderRefundRecordService busOrderRefundRecordService;


    @Autowired
    protected WeChatPayService(IBusPayConfigService busPayConfigService,
                            IBusPayOrderService busPayOrderService, WxPayUtils wxPayUtils) {
        super(busPayConfigService, busPayOrderService);
        this.wxPayUtils = wxPayUtils;
    }

    /**
     * 微信支付
     * @param dto - 支付参数
     * @return - 返回支付结果
     */
    @Override
    public PrepayResponseDTO pay(BusPayDTO dto) {
        // 1.获取支付配置
        BusPayOrder paidOrder = busPayOrderService.queryPaidOrder(dto.getOrderNo());
        if (Objects.nonNull(paidOrder)) {
            throw new ServiceException("当前已支付，请勿重复支付");
        }
        BusPayConfig busPayConfig = getBusPayConfigById(dto.getPayConfigId());
        JSONObject params = getPayParams(dto, busPayConfig);
        // 2.根据支付方式调用相应的支付接口
        Config payConfig = wxPayUtils.getPayConfig(busPayConfig);
        HttpHeaders httpHeaders = new HttpHeaders();
        if (busPayConfig.getWxPublicKeyId() != null) {
            httpHeaders.addHeader("Wechatpay-Serial", busPayConfig.getWxPublicKeyId());
        }
        // 获取预支付订单id
        PrepayResponseDO prepayResponseDO = wxPayUtils.sendHttpToWechat(payConfig, HttpMethod.POST, WxConstants.PAY,
                params,httpHeaders, PrepayResponseDO.class);
        log.info("微信订单预支付返回结果：{}" , prepayResponseDO);
        // 3. 记录支付流水-存在就修改，不存在就新增 - 只有外部流水号
        BusPayOrder busPayOrder = getBaseBusPayOrder(dto, busPayConfig);
        busPayOrder.setOutTradeNo(getOutTradeNo(dto.getOrderNo()));
        busPayOrderService.insertOrUpdateBusPayOrder(busPayOrder);
        PrepayResponseDTO prepayResponseDTO = new PrepayResponseDTO();
        prepayResponseDTO.setPrepayId(prepayResponseDO.getPrepayId());
        return prepayResponseDTO;
    }

    /**
     * 获取支付参数
     *
     * @param dto
     *         - 业务支付参数
     * @param busPayConfig
     *         - 支付配置
     *
     * @return 支付参数
     */
    protected JSONObject getPayParams(BusPayDTO dto, BusPayConfig busPayConfig) {
        JSONObject params = new JSONObject();
        log.info("配置信息={}", busPayConfig);
        params.put("appid", dto.getAppid());
        params.put("notify_url", dto.getNotifyUrl());
        params.put("mchid", busPayConfig.getMchId());
        params.put("description", dto.getContent());
        params.put("out_trade_no", dto.getOrderNo());
        params.put("attach", dto.getAutoActivate());
        // 商品金额
        JSONObject amount = new JSONObject();
        BigDecimal total = new BigDecimal("100").multiply(dto.getPayAmount());
        amount.put("total", total.intValue());
        params.put("amount", amount);
        // 支付者
        JSONObject payer = new JSONObject();
        payer.put("openid", dto.getOpenid());
        params.put("payer", payer);
        return params;
    }


    /**
     * 微信支付退款
     * @param dto - 退款参数
     * @return - 返回退款结果
     */
    @Override
    public WxRefundDTO refund(BusRefundPayDTO dto) {
        BusPayConfig busPayConfig = getBusPayConfigById(dto.getPayConfigId());
        BusOrderRefundRecordDTO refundRecordDTO = getRefundRecordDTO(dto);
        busOrderRefundRecordService.sava(refundRecordDTO);
        WxRefundDO refundDO = wxPayUtils.refund(dto.getParams(),busPayConfig);
        return OrikaUtils.convert(refundDO, WxRefundDTO.class);
    }

    /**
     * 微信交易订单查询
     *
     * @param dto - 交易查询参数 - 交易流水号（优先），微信流水号
     * @return - 返回交易查询结果
     */
    @Override
    public TransactionDTO query(BusQueryDTO dto) {
        // todo - 接口可以查到该商户下的任意订单的支付信息，权限控制没有做
        // 待开发 非空判断
        String outTradeNo = dto.getOutTradeNo();
        BusPayConfig busPayConfig = getBusPayConfigById(dto.getPayConfigId());
        // 查询结果
        Transaction query = wxPayUtils.query(outTradeNo, dto.getTransactionId(), busPayConfig);
        // 获取支付流水
        BusPayOrder busPayOrder = getQueryBusPayOrder(query ,dto);
        // 更新支付流水
        busPayOrderService.insertOrUpdateBusPayOrder(busPayOrder);
        return OrikaUtils.convert(query, TransactionDTO.class);
    }

    /**
     * 根据查询结果获取支付流水
     * @param query - 查询结果
     * @return - 返回支付流水
     */
    private BusPayOrder getQueryBusPayOrder(Transaction query,BusQueryDTO dto) {
        BusPayOrder busPayOrder = new BusPayOrder();
        if (!Transaction.TradeStateEnum.CLOSED.equals(query.getTradeState())
                && !Transaction.TradeStateEnum.NOTPAY.equals(query.getTradeState()) && Objects.nonNull(query.getAmount())) {
            busPayOrder.setAmount(BigDecimal.valueOf(query.getAmount().getTotal() / 100.00d));
        } else {
            busPayOrder.setAmount(BigDecimal.ZERO);
        }
        busPayOrder.setOutTradeNo(query.getOutTradeNo());
        busPayOrder.setOrderNo(query.getOutTradeNo().split("_")[0]);
        busPayOrder.setOrderType(getOrderTypeByOutTradeNo(query.getOutTradeNo()));
        busPayOrder.setMchId(query.getMchid());
        busPayOrder.setAppid(query.getAppid());
        // 微信查询订单的时候不一定会返回这个字段
        if (query.getPayer() != null) {
            busPayOrder.setOpenid(query.getPayer().getOpenid());
        }
        busPayOrder.setTransactionId(query.getTransactionId());
        busPayOrder.setPayWay(PaysTypeEnum.WECHAT_PAY.getCode());
        busPayOrder.setStatus(getQueryStatus(query.getTradeState()));
        busPayOrder.setUpdateTime(new Date());
        busPayOrder.setUpdateBy(SecurityUtils.getUsername());
        busPayOrder.setCreateTime(new Date());
        busPayOrder.setCreateBy(SecurityUtils.getUsername());
        busPayOrder.setHospitalId(dto.getHospitalId());
        return busPayOrder;
    }

    /**
     * 根据微信查询的交易状态获取支付状态
     * @param tradeState     - 交易状态
     * @return - 返回支付状态
     */
    private String getQueryStatus(Transaction.TradeStateEnum tradeState) {
        switch (tradeState) {
            case SUCCESS:
                return "1";
            case REFUND:
                return "3";
            case NOTPAY:
                return "0";
            case CLOSED:
                return "2";
            default:
                return "5";
        }
    }

    @Override
    public String verifyAndDecryptNotify(WxNotifyDTO wxNotifyDTO, String scene) {
        log.info("{}通知：{}", scene, wxNotifyDTO);
        // 微信回调探测流量则不解析
        if (wxNotifyDTO.getSignature().startsWith(WxConstants.WECHATPAY_SIGNATURE)) {
            return null;
        }
        // 有人支付就会更新为最新的支付配置，而且现在全部逐步过渡到微信支付公钥
        BusPayConfig busPayConfig = busPayConfigService.queryBySerialNo(wxNotifyDTO.getSerialNo());
        wxNotifyDTO.setBusHospitalWechatConfig(busPayConfig);
        String notifyInfo = wxPayUtils.verifyAndDecrypt(wxNotifyDTO);
        log.info("微信支付回调通知解密结果：{}", notifyInfo);
        return notifyInfo;
    }

    /**
     * 支付通知处理
     *
     * @param payNotifyInfo - 支付通知结果
     */
    @Override
    public void handlePaymentNotify(String payNotifyInfo) {
        Transaction payResult = weChatPayService.updatePayOrderAndReturnJsonInfo(payNotifyInfo);
        // 注: 业务处理应先查本地数据库订单支付状态 未处理时再进行后面业务处理 微信支付可能会多次调用回调接口
        String orderNo = payResult.getOutTradeNo();
        // 微信支付系统生成的订单号
        String transactionId = payResult.getTransactionId();
        String orderType = orderNo.substring(0, 2);
        switch (orderType) {
            case OrderTypeConstant.DRUGS_ORDER:
                // 修改药品订单状态
                remoteBasePaymentService.paySuccessDrugsOrderHandler(orderNo,PaysTypeEnum.WECHAT_PAY.getCode());
                break;
            case OrderTypeConstant.CONSULATION_ORDER:
                // 修改问诊订单状态
                remoteBasePaymentService.paySuccessConsultationOrderHandler(orderNo);
                break;
            case OrderTypeConstant.SERVICE_PACK_ORDER:
                // 修改服务包订单状态
                String autoActivate = payResult.getAttach();
                log.info("是否自动激活={}", autoActivate);
                remoteServicePackOrderService.paySuccessServicePackOrderHandler(orderNo);
                break;
            default:
                // 保存微信支付系统生成的订单号用于申请退款时传入
                // 截取的第一位为业务订单号，做订单状态变更操作
                remoteBasePaymentService.paySuccessTotalOrderHandler(interceptOrderNo(orderNo),transactionId);
                break;
        }
        BusPayOrder busPayOrder = busPayOrderService.queryPaidOrder(interceptOrderNo(orderNo));

        R<BusHospitalWechatConfig> configR = remoteHospitalService.queryConfigInfo(busPayOrder.getHospitalId(), ClientTypeEnum.WX_UNI_APP.getType());
        if (!configR.isSuccess() || configR.getData() == null){
            log.error("获取微信支付配置失败 {}",configR.getMsg());
            return;
        }
       if (Objects.equals(configR.getData().getAppid(),payResult.getAppid())){
           uploadLogistics(payResult,orderType);
       }
    }


    /**
     * 截取交易订单编号
     *
     * @param orderNo - 订单编号
     * @return - 截取后的订单编号
     */
    private String interceptOrderNo(String orderNo) {
        return orderNo.split("_")[0];
    }

    /**
     * 退款通知处理
     *
     * @param refundNotifyInfo - 退款通知结果
     */
    @Override
    public String handleRefundNotify(String refundNotifyInfo) {
        log.info("微信退款回调明文：{}", refundNotifyInfo);
        JSONObject payResult = JSON.parseObject(refundNotifyInfo);
        String orderNo = payResult.getString("out_trade_no");
        String transactionId = payResult.getString("transaction_id");
        String outRefundNo = payResult.getString("out_refund_no");
        String refundId = payResult.getString("refund_id");
        String lockKey = CacheConstants.REFUND_CALLBACK + transactionId;
        String lockValue = UUID.randomUUID().toString();
        String flag = orderNo.substring(0, 2);
        try {
            boolean absent = redisService.tryLock(lockKey, lockValue);
            if (!absent){
                log.warn("微信退款回调接口处理失败,订单号={}，原因:获取锁超时", orderNo);
                throw new ServiceException("获取锁超时，退款处理中");
            }
            // 退款记录已存在，则更新退款记录
            BusOrderRefundRecordVO refundRecord = busOrderRefundRecordService.getByOutRefundNo(outRefundNo);
            if (Objects.nonNull(refundRecord) && RefundStatusEnum.isUnfinished(refundRecord.getRefundStatus())) {
                BusOrderRefundRecordDTO update = new BusOrderRefundRecordDTO();
                update.setId(refundRecord.getId());
                update.setRefundId(refundId);
                update.setRefundStatus(RefundStatusEnum.REFUND_SUCCESS.name());
                update.setRefundSuccessTime(new Date());
                update.setNotifySnapshot(refundNotifyInfo);
                busOrderRefundRecordService.update(update);
                //更新支付记录的状态
                busPayOrderService.updatePayOrderAfterRefunded(refundRecord.getOutTradeNo(), refundRecord.getOrderNo());

            }
            Long orderAfterSaleId = null;
            switch (flag) {
                case OrderTypeConstant.DRUGS_ORDER:
                    remoteBasePaymentService.refundSuccessDrugsOrderHandler(orderNo);
                    break;
                case OrderTypeConstant.CONSULATION_ORDER:
                    remoteBasePaymentService.refundSuccessConsultationOrderHandler(orderNo);
                    break;
                case OrderTypeConstant.SERVICE_PACK_ORDER:
                    remoteBasePaymentService.refundSuccessServicePackOrderHandler(orderNo);
                    break;
                default:
                    String[] split = outRefundNo.split("a");
                    if (split.length > 1) {
                        orderAfterSaleId = Long.valueOf(split[1]);
                    }
                    // 截取的第一位为业务订单号，做订单状态变更操作
                    remoteBasePaymentService.refundSuccessTotalOrderHandler(orderAfterSaleId, interceptOrderNo(orderNo));
            }
            return orderAfterSaleId == null ? null : orderAfterSaleId + "";
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            redisService.unlock(lockKey, lockValue);
        }
    }

    /**
     * 根据微信支付回调通知结果获取支付流水
     *
     * @param notifyInfo - 通知结果
     * @return - 返回解析结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Transaction updatePayOrderAndReturnJsonInfo(String notifyInfo) {
        BusPayOrder busPayOrder = new BusPayOrder();
        Transaction notifyJson = new Gson().fromJson(notifyInfo, Transaction.class);
        busPayOrder.setOutTradeNo(notifyJson.getOutTradeNo());
        busPayOrder.setTransactionId(notifyJson.getTransactionId());
        busPayOrder.setOrderType(getOrderTypeByOutTradeNo(notifyJson.getOutTradeNo()));
        busPayOrder.setStatus(getNotifyStatus(notifyJson.getTradeState()));
        busPayOrder.setNotifyInfo(notifyInfo);
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(notifyJson.getSuccessTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        // 转Date对象
        busPayOrder.setPaySuccessTime(Date.from(zonedDateTime.toInstant()));
        busPayOrderService.updatePayOrder(busPayOrder);
        return notifyJson;
    }

    private String getNotifyStatus(Transaction.TradeStateEnum tradeState) {
        switch (tradeState) {
            case SUCCESS:
                return BusPayOrderStatusEnum.PAYED.getValue();
            case CLOSED:
                return BusPayOrderStatusEnum.CANCEL.getValue();
            case REFUND:
                return BusPayOrderStatusEnum.REFUNDED.getValue();
            default:
                return BusPayOrderStatusEnum.PAYING.getValue();
        }
    }

    /**
     * 获取订单的我方交易流水号
     *
     * @param orderNo
     *         原来上游传入的我方交易流水
     *
     * @return outTradeNo
     */
    @Override
    protected String getOutTradeNo(String orderNo) {
        return orderNo;
    }

    /**
     * 根据退款参数获取订单的三方交易流水号
     *
     * @param refundPayDTO
     *         退款请求参数
     *
     * @return transactionId
     */
    @Override
    protected String getOrderTransactionId(BusRefundPayDTO refundPayDTO) {
        return refundPayDTO.getParams().getString("transaction_id");
    }

    @Override
    protected String getOutRefundNo(BusRefundPayDTO refundPayDTO) {
        JSONObject params = refundPayDTO.getParams();
        //由于退款流水号是在调用的地方生成的，所以这里直接返回即可
        return params.getString("out_refund_no");
    }

    @Override
    protected String getRefundStatus() {
        return RefundStatusEnum.REFUNDING.name();
    }

    @Override
    protected Date getRefundSuccessTime() {
        return null;
    }

    /**
     * 从退款参数中获取订单金额
     *
     * @param refundPayDTO
     *         - 退款参数
     *
     * @return 返回订单金额
     */
    @Override
    public BigDecimal getPayOrderAmount(BusRefundPayDTO refundPayDTO) {
        JSONObject amount = refundPayDTO.getParams().getJSONObject("amount");
        return amount.getBigDecimal("total").divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    /**
     * 从退款参数中获取退款的金额
     *
     * @param refundPayDTO
     *         - 退款参数
     *
     * @return 返回退款的金额
     */
    @Override
    public BigDecimal getRefundAmount(BusRefundPayDTO refundPayDTO) {
        JSONObject amount = refundPayDTO.getParams().getJSONObject("amount");
        return amount.getBigDecimal("refund").divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }
}
