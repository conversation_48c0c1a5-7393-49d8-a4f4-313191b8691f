package com.puree.hospital.pay.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单退款记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/29 20:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("bus_order_refund_record")
public class BusOrderRefundRecord extends Entity {

    private static final long serialVersionUID = -3288429092579107574L;
    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 退款操作人
     */
    private String promoter;

    /**
     * 支付流水号
     */
    private String outTradeNo;

    /**
     * 退款流水号
     */
    private String outRefundNo;

    /**
     * 退款类型：FULL-全额退款，2.PARTIAL-部分退款
     */
    private String refundType;

    /**
     * 订单支付金额
     */
    private BigDecimal payAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款状态: PENDING_REFUND-待退款, REFUNDING-退款中, REFUND_SUCCESS退款成功, REFUND_FAILED-退款失败
     */
    private String refundStatus;

    /**
     * 退款成功时间
     */
    private Date refundSuccessTime;

    /**
     * 退款说明
     */
    private String refundRemark;

    /**
     * 退款id：支付渠道方相应的退款id
     */
    private String refundId;

    /**
     * 退款回调快照
     */
    private String notifySnapshot;

    /**
     * 退款方式：ONLINE-线上，OFFLINE-线下
     */
    private String refundMethod;

    /**
     * 售后单id
     */
    private Long afterSalesId;

}
