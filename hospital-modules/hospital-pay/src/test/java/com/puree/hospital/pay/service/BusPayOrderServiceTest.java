package com.puree.hospital.pay.service;

import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.core.enums.RefundReturnEnum;
import com.puree.hospital.pay.AbstractHospitalPayTest;
import com.puree.hospital.pay.api.model.dto.BusPayOrderQueryDTO;
import com.puree.hospital.pay.api.model.enums.RefundStatusEnum;
import com.puree.hospital.pay.api.model.vo.BusOrderRefundRecordVO;
import com.puree.hospital.pay.api.model.vo.BusPayOrderVO;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 支付
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/1 18:03
 */
public class BusPayOrderServiceTest extends AbstractHospitalPayTest {

    @Resource
    private IBusPayOrderService busPayOrderService;

    @Resource
    private IBusOrderRefundRecordService busOrderRefundRecordService;

    @Test
    public void testQueryDetail() {
        BusPayOrderVO detail = busPayOrderService.getDetail(806L);
        System.out.println(JSON.toJSONString(detail));
    }

    @Test
    public void testQueryList() {
        BusPayOrderQueryDTO queryDTO = new BusPayOrderQueryDTO();
        queryDTO.setHospitalId(2L);
        queryDTO.setFamilyName("陈森晋");
        List<BusPayOrderVO> list = busPayOrderService.queryList(queryDTO);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void testQueryPaidOrder() {
        BusOrderRefundRecordVO refundRecord = busOrderRefundRecordService.getByOutRefundNo("1743517407525a1391");
        Assert.assertTrue(RefundStatusEnum.isUnfinished(refundRecord.getRefundStatus()));
    }
}
