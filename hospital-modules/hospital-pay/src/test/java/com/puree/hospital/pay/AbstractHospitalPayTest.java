package com.puree.hospital.pay;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <p>
 * 抽象的app接口测试类
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/31 17:08
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HospitalPayApplication.class)
public abstract class AbstractHospitalPayTest {


}
