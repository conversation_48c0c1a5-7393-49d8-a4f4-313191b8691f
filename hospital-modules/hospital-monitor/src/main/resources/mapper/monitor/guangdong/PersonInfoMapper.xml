<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.guangdong.mapper.PersonInfoMapper">
    <select id="selectSyncDataList" resultType="com.puree.hospital.monitor.guangdong.domain.PersonInfo">
        SELECT f.id            AS familyId,
               f.id_number     AS ZJHM,
               f.name          AS XM,
               f.sex           AS XBDM,
               f.date_of_birth AS CSRQ,
               f.cell_phone_number  AS SJHM
        FROM bus_patient b
        INNER JOIN bus_patient_family f ON f.patient_id = b.id
        inner join bus_patient_hospital h on h.patient_id = b.id
        WHERE ISNULL(f.id_number) = 0
          AND LENGTH(TRIM(f.id_number)) &gt; 0
          AND ISNULL(f.date_of_birth) = 0
          AND LENGTH(TRIM(f.date_of_birth)) &gt; 0
          and h.hospital_id = #{hospitalId} and f.id_number is not null and f.id_number != ''
          and f.create_time &gt;= DATE_SUB(CURDATE(),INTERVAL 5 DAY)
          and f.create_time &lt;= date_add(curdate(),interval +1 day)
    </select>

</mapper> 