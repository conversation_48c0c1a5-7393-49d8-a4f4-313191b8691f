package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.app.api.RemoteDiagnosisService;
import com.puree.hospital.app.api.model.RemoteClinic;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.DoctorResource;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.PatientInfo;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnRemoteVisit;
import com.puree.hospital.monitor.common.domain.BusAssociateDepartment;
import com.puree.hospital.monitor.common.domain.BusBizDepartment;
import com.puree.hospital.monitor.common.domain.BusDepartment;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.common.repository.BusAssociateDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusBizDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorRepository;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 监管-远程门诊数据组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/5 16:35
 */
@Slf4j
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.REMOTE_VISIT + EventDataAssembler.SUFFIX)
public class YnRemoteVisitEventDataAssembler extends AbstractEventDataAssembler<YnRemoteVisit> {

    @Resource
    private BusAssociateDepartmentRepository busAssociateDepartmentRepository;

    @Resource
    private BusDepartmentRepository busDepartmentRepository;

    @Resource
    private BusBizDepartmentRepository busBizDepartmentRepository;

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Resource
    private RemoteDiagnosisService remoteDiagnosisService;

    @Resource
    private HospitalJgIdConfig hospitalJgIdConfig;

    @Override
    protected YnRemoteVisit newInstance() {
        return new YnRemoteVisit();
    }

    @Override
    protected void assembleOtherInfo(YnRemoteVisit regulatory, RegulatoryHospitalConfig config) {
        log.debug("远程门诊数据开始组装：{}", regulatory);
        //获取远程门诊记录详情
        RemoteClinic remoteClinic = remoteDiagnosisService.getInfoById(Integer.valueOf(regulatory.getBusinessId())).getData();
        if(Objects.isNull(remoteClinic) || CollectionUtils.isEmpty(remoteClinic.getDoctorInfoList())){
            throw new IllegalStateException(String.format("未找到远程门诊记录，门诊id：%s", regulatory.getBusinessId()));
        }
        regulatory.setBussID(remoteClinic.getRecordNum());
        regulatory.setHospitalName(config.getHospitalName());
        regulatory.setPrexHx(remoteClinic.getDiseaseDesc());
        regulatory.setApplyDatetime(remoteClinic.getCreateTime());
        regulatory.setStartDatetime(remoteClinic.getDiagTime());
        regulatory.setEndDatetime(remoteClinic.getFinishTime());
        regulatory.setThisDiagnosis(String.join("|", remoteClinic.getDiagnosis()));
        //目前远程门诊场景，挂号费一律设置为0元
        regulatory.setVisitPrice(new BigDecimal("0.00"));
        //获取远程门诊视频的完整url信息
        List<String> videoFullPathList = remoteClinic.getVideoUrl().stream()
                .filter(video -> StringUtils.isNotEmpty(video.getUrl()))
                .map(video -> String.format("%s%s", getFileAddressPrefix(), video.getUrl()))
                .collect(Collectors.toList());
        regulatory.setReportUrls(videoFullPathList);
        //获取患者病历资料完整url信息
        regulatory.setProcessDataUrls(remoteClinic.getFileList().stream().map(item -> String.format("%s%s", getFileAddressPrefix(), item)).collect(Collectors.toList()));

        //设置申请方医生信息
        DoctorResource applyDoctor = getDoctorInfo(remoteClinic.getDeptId(), remoteClinic.getApplyDoctorId(), regulatory.getHospitalId());
        applyDoctor.setUnifiedOrgCode(config.getUnifiedCreditCode());
        applyDoctor.setOrgName(config.getHospitalName());
        regulatory.setApplyDoctor(applyDoctor);

        //获取受邀方医生所属医院的统一代码、地址信息
        RemoteClinic.DoctorInfo remoteInvitedDoctor = remoteClinic.getDoctorInfoList().get(0);
        if (Objects.isNull(remoteInvitedDoctor)) {
            throw new IllegalArgumentException(String.format("未找到受邀方医生信息，门诊id:%s", regulatory.getBusinessId()));
        }

        String invitedHospitalCreditCode;
        String invitedHospitalName;
        if (remoteInvitedDoctor.getHospitalId().equals(regulatory.getHospitalId())) {
            invitedHospitalCreditCode = config.getUnifiedCreditCode();
            invitedHospitalName = config.getHospitalName();
        } else {
            RegulatoryHospitalConfig hospitalConfig = hospitalJgIdConfig.getHospitalConfig(remoteInvitedDoctor.getHospitalId());
            if (Objects.isNull(hospitalConfig)) {
                log.warn("根据hospitalId:{}未获取到医院配置信息,不做监管上报", remoteInvitedDoctor.getHospitalId());
                throw new IllegalArgumentException(String.format("未获取到医院配置信息,医院id：%s", remoteInvitedDoctor.getHospitalId()));
            }
            invitedHospitalCreditCode = hospitalConfig.getUnifiedCreditCode();
            invitedHospitalName = hospitalConfig.getHospitalName();
        }
        //设置受邀医生信息
        DoctorResource invitedDoctor = getDoctorInfo(remoteInvitedDoctor.getDeptId(), remoteInvitedDoctor.getDoctorId(), remoteInvitedDoctor.getHospitalId());
        invitedDoctor.setUnifiedOrgCode(invitedHospitalCreditCode);
        invitedDoctor.setOrgName(invitedHospitalName);
        regulatory.setInvitedDoctor(invitedDoctor);

        //设置患者信息
        PatientInfo patientInfo = new PatientInfo();
        RemoteClinic.PatientInfo remotePatientInfo = remoteClinic.getPatientInfo();
        patientInfo.setPatIdcardTypeCode(remotePatientInfo.getIdType());
        patientInfo.setPatIdcardNo(remotePatientInfo.getIdNumber());
        patientInfo.setPatName(remotePatientInfo.getPatientName());
        patientInfo.setAge(remotePatientInfo.getAge());
        YnRemoteVisit.GenderCodeEnum genderCode = YnRemoteVisit.GenderCodeEnum.getByName(remotePatientInfo.getSex());
        if (Objects.isNull(genderCode)) {
            throw new IllegalArgumentException(String.format("远程门诊患者性别不合法，性别：%s, 问诊id：%s", remotePatientInfo.getSex(), regulatory.getBusinessId()));
        }
        patientInfo.setGenderCode(genderCode.getCode());
        patientInfo.setMobile(remotePatientInfo.getCellPhoneNumber());
        regulatory.setPatient(patientInfo);

        log.debug("远程门诊数据组装完成：{}", regulatory);
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.REMOTE_VISIT;
    }

    /**
     * 获取医生所属业务科室、姓名等信息
     *
     * @param deptId 科室id
     * @param doctorId 医生id
     * @param hospitalId 医院id
     * @return 医生信息
     */
    private DoctorResource getDoctorInfo(Long deptId, Long doctorId, Long hospitalId){
        DoctorResource doctorInfo = new DoctorResource();
        //查询业务科室信息
        BusBizDepartment busBizDepartment = busBizDepartmentRepository.getById(deptId);
        if (Objects.isNull(busBizDepartment)) {
            throw new IllegalArgumentException(String.format("未找到关联的业务科室,业务科室id：%s", deptId));
        }
        //查询标准科室信息
        BusAssociateDepartment busAssociateDepartment = busAssociateDepartmentRepository.getByBizDepartmentIdAndHospitalId(busBizDepartment.getId(), hospitalId);
        if (Objects.isNull(busAssociateDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        BusDepartment busDepartment = busDepartmentRepository.getById(busAssociateDepartment.getDepartmentId());
        if (Objects.isNull(busDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        //查询医生信息
        BusDoctor busDoctor = busDoctorRepository.getById(doctorId);
        if (Objects.isNull(busDoctor)) {
            throw new IllegalArgumentException(String.format("未找到医生信息，医生id：%s", doctorId));
        }
        doctorInfo.setDeptClassCode(busDepartment.getDepartmentNumber());
        doctorInfo.setDeptClassName(busDepartment.getDepartmentName());
        doctorInfo.setDeptCode(busBizDepartment.getDepartmentNumber());
        doctorInfo.setDeptName(busBizDepartment.getDepartmentName());
        doctorInfo.setDoctIdcardNo(DESUtil.decrypt(busDoctor.getIdCardNo()));
        doctorInfo.setDoctName(busDoctor.getFullName());
        return doctorInfo;
    }

}
