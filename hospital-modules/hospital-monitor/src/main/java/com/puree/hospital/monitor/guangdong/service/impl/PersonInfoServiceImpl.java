package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.guangdong.domain.PersonInfo;
import com.puree.hospital.monitor.guangdong.infrastructure.IDCard;
import com.puree.hospital.monitor.guangdong.mapper.PersonInfoMapper;
import com.puree.hospital.monitor.guangdong.service.IPersonInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Service
public class PersonInfoServiceImpl implements IPersonInfoService {
    private final PersonInfoMapper personInfoMapper;
    private final HospitalJgIdConfig hospitalJgIdConfig;

    @Autowired
    public PersonInfoServiceImpl(PersonInfoMapper personInfoMapper,HospitalJgIdConfig hospitalJgIdConfig) {
        this.personInfoMapper = personInfoMapper;
        this.hospitalJgIdConfig = hospitalJgIdConfig;
    }

    @Override
    public List<PersonInfo> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<PersonInfo> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<PersonInfo>()
                    .eq(PersonInfo::getHospitalId, hospitalId)
                    .ge(PersonInfo::getCreateTime, LocalDate.now());
        }else{
            queryWrapper = new LambdaQueryWrapper<PersonInfo>()
                    .eq(PersonInfo::getHospitalId, hospitalId);
        }
        return personInfoMapper.selectList(queryWrapper);
    }

    @Override
    public int syncData(Long hospitalId) {
        List<PersonInfo> syncDataList = personInfoMapper.selectSyncDataList(hospitalId);
        syncDataList.forEach(i -> {
            i.setJgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setKh(String.valueOf(i.getFamilyId()));
            i.setKlx("3");//系统内部号
            i.setZjlbdm(IDCard.JMSFZ.getCode());
            String xbdm = i.getXbdm().equals("0") ? "2" : "1";
            String xbmc = i.getXbdm().equals("0") ? "女性" : "男性";
            i.setXbdm(xbdm);
            i.setXbmc(xbmc);
            i.setSjscsj(new Date());
            i.setCxbz("1");
            i.setSjscsj(new Date());
        });

        for (PersonInfo p : syncDataList) {
            QueryWrapper<PersonInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ZJHM", p.getZjhm());
            queryWrapper.eq("hospital_id",hospitalId);
            List<PersonInfo> plist = personInfoMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(plist)) {
                p.setHospitalId(hospitalId);
                personInfoMapper.insert(p);
            }
        }
        return 1;
    }
}
