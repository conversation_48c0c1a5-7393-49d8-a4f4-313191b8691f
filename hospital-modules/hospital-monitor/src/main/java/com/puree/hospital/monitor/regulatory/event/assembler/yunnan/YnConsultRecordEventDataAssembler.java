package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.enums.RegisterTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnConsultRecord;
import com.puree.hospital.monitor.common.domain.BusAssociateDepartment;
import com.puree.hospital.monitor.common.domain.BusBizDepartment;
import com.puree.hospital.monitor.common.domain.BusCommunicationMessage;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusDepartment;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.common.domain.BusDoctorPatientGroup;
import com.puree.hospital.monitor.common.domain.BusPatientFamily;
import com.puree.hospital.monitor.common.domain.BusQuickConsultation;
import com.puree.hospital.monitor.common.domain.BusSignature;
import com.puree.hospital.monitor.common.repository.BusAssociateDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusBizDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusCommunicationMessageRepository;
import com.puree.hospital.monitor.common.repository.BusDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorRepository;
import com.puree.hospital.monitor.common.repository.BusPatientFamilyRepository;
import com.puree.hospital.monitor.common.repository.BusQuickConsultationRepository;
import com.puree.hospital.monitor.common.repository.BusSignatureRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 监管-咨询记录数据组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/5 16:32
 */
@Slf4j
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.CONSULT_RECORD + EventDataAssembler.SUFFIX)
public class YnConsultRecordEventDataAssembler extends AbstractEventDataAssembler<YnConsultRecord> {

    @Resource
    private BusPatientFamilyRepository busPatientFamilyRepository;

    @Resource
    private BusAssociateDepartmentRepository busAssociateDepartmentRepository;

    @Resource
    private BusDepartmentRepository busDepartmentRepository;

    @Resource
    private BusBizDepartmentRepository busBizDepartmentRepository;

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Resource
    private BusSignatureRepository busSignatureRepository;

    @Resource
    private BusCommunicationMessageRepository busCommunicationMessageRepository;

    @Resource
    private BusQuickConsultationRepository busQuickConsultationRepository;

    @Override
    protected YnConsultRecord newInstance() {
        return new YnConsultRecord();
    }

    @Override
    protected void assembleOtherInfo(YnConsultRecord regulatory, RegulatoryHospitalConfig config) {
        BusConsultationOrder busConsultationOrder = super.getConsultationOrder(regulatory.getBusinessId());
        if (Objects.isNull(busConsultationOrder)) {
            throw new IllegalStateException(String.format("未找到问诊订单信息，问诊id：%s", regulatory.getBusinessId()));
        }
        if (RegisterTypeEnum.isVisit(busConsultationOrder.getRegisterType())) {
            throw new IllegalArgumentException(String.format("当前问诊订单类型为【复诊】不做咨询记录上报，问诊id：%s", regulatory.getBusinessId()));
        }
        regulatory.setVisitNo(busConsultationOrder.getOrderNo());
        BusPatientFamily busPatientFamily = busPatientFamilyRepository.getById(busConsultationOrder.getFamilyId());
        if (Objects.isNull(busPatientFamily)) {
            throw new IllegalArgumentException(String.format("未找到患者信息，患者id：%s", busConsultationOrder.getFamilyId()));
        }
        String idCardNo = busPatientFamily.getIdNumber();
        regulatory.setPatientIdCardTypeCode(busPatientFamily.getIdType());
        regulatory.setPatientIdCardNo(idCardNo);
        regulatory.setPatientName(busConsultationOrder.getFamilyName());
        regulatory.setPatientSex(busConsultationOrder.getFamilySex());
        regulatory.setPatientAge(Integer.parseInt(busConsultationOrder.getFamilyAge().replace("岁", "")));
        regulatory.setHospitalName(config.getHospitalName());

        //设置业务科室信息
        BusBizDepartment busBizDepartment = busBizDepartmentRepository.getById(busConsultationOrder.getDepartmentId());
        if (Objects.isNull(busBizDepartment)) {
            throw new IllegalArgumentException(String.format("未找到关联的业务科室,业务科室id：%s", busConsultationOrder.getDepartmentId()));
        }
        regulatory.setDepartmentName(busBizDepartment.getDepartmentName());
        regulatory.setDepartmentNumber(busBizDepartment.getDepartmentNumber());
        //设置标准科室信息
        BusAssociateDepartment busAssociateDepartment = busAssociateDepartmentRepository.getByBizDepartmentIdAndHospitalId(busBizDepartment.getId(), regulatory.getHospitalId());
        if (Objects.isNull(busAssociateDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        BusDepartment busDepartment = busDepartmentRepository.getById(busAssociateDepartment.getDepartmentId());
        if (Objects.isNull(busDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        regulatory.setDeptClassCode(busDepartment.getDepartmentNumber());
        regulatory.setDeptClassName(busDepartment.getDepartmentName());
        BusDoctor busDoctor = busDoctorRepository.getById(busConsultationOrder.getDoctorId());
        if (Objects.isNull(busDoctor)) {
            throw new IllegalArgumentException(String.format("未找到医生信息，医生id：%s", busConsultationOrder.getDoctorId()));
        }
        regulatory.setDoctorIdCardNo(DESUtil.decrypt(busDoctor.getIdCardNo()));
        regulatory.setDoctorName(busConsultationOrder.getDoctorName());
        //获取处方医生签名
        BusSignature signature = busSignatureRepository.getByObjectIdAndObjectType(busConsultationOrder.getDoctorId(), "0");
        if (Objects.nonNull(signature)) {
            String doctorSignUrl = signature.getCertSignature();
            regulatory.setDoctorSignUrl(getFileAddressPrefix() + doctorSignUrl);
        }
        regulatory.setDoctorCaSign(getDoctorCaSign());
        regulatory.setApplyTime(busConsultationOrder.getCreateTime());
        //咨询类别:默认是1
        String consultationAttribute = "1";
        if (Objects.equals(3, busConsultationOrder.getPayType())) {
            consultationAttribute = "2";
        }
        regulatory.setConsultationAttribute(consultationAttribute);
        //咨询内容
        regulatory.setContent(getSymptomDescription(busConsultationOrder.getConsultationId()));
        //咨询类型
        if (ConsultationOrderTypeEnum.IMAGETEXT.getCode().equals(busConsultationOrder.getOrderType())) {
            regulatory.setConsultationType("1");
        } else {
            regulatory.setConsultationType("3");
        }
        //问诊取消
        if (isCancel(busConsultationOrder)) {
            //医生取消
            if(isReturned(busConsultationOrder)){
                regulatory.setVisitStatus("-1");
                regulatory.setRefuseType("1");
                regulatory.setRefuseTime(regulatory.getEventTime());
                regulatory.setRefuseReason(busConsultationOrder.getReason());
            } else {
                //用户取消
                regulatory.setVisitStatus("0");
            }
        } else {
            regulatory.setVisitStatus("2");
            if (ConsultationOrderTypeEnum.IMAGETEXT.getCode().equals(busConsultationOrder.getOrderType())) {
                regulatory.setVisitStartTime(busConsultationOrder.getCreateTime());
            } else {
                regulatory.setVisitStartTime(busConsultationOrder.getStartTime());
            }
            regulatory.setVisitEndTime(Objects.nonNull(busConsultationOrder.getCompleteTime()) ? busConsultationOrder.getCompleteTime() : regulatory.getEventTime());
            //聊天组信息
            BusDoctorPatientGroup group = super.getGroup(busConsultationOrder.getDoctorId(),
                    busConsultationOrder.getPatientId(),
                    busConsultationOrder.getFamilyId(),
                    busConsultationOrder.getDepartmentId(),
                    busConsultationOrder.getHospitalId());
            //指导意见摘要信息
            regulatory.setInstruction(getInstruction(busConsultationOrder, group.getId()));
            regulatory.setVisitPrice(busConsultationOrder.getAmount());
            //咨询记录页面
            String reviewUrl = getConsultationRecordUrl(busConsultationOrder.getHospitalId(),
                    busConsultationOrder.getDoctorId(),
                    busConsultationOrder.getFamilyId(),
                    regulatory.getProvinceAbbreviation(),
                    busConsultationOrder.getId(),
                    group);
            regulatory.setReviewUrlList(Lists.newArrayList(reviewUrl));
        }
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.CONSULT_RECORD;
    }

    /**
     * 获取问诊咨询症状
     *
     * @param consultationId 问诊id
     * @return 症状信息
     */
    private String getSymptomDescription(Long consultationId) {
        String symptomDescription = null;
        if (Objects.nonNull(consultationId)) {
            BusQuickConsultation busQuickConsultation = busQuickConsultationRepository.getById(consultationId);

            if (Objects.nonNull(busQuickConsultation)) {
                symptomDescription = busQuickConsultation.getSymptomDescription();
            }
        }
        return StringUtils.isBlank(symptomDescription) ? "无" : symptomDescription;
    }

    /**
     * 获取问诊咨询 instruction
     *
     * @param busConsultationOrder 咨询订单
     * @param groupId              聊天组id
     * @return instruction
     */
    private String getInstruction(BusConsultationOrder busConsultationOrder, Long groupId) {
        if (Objects.nonNull(busConsultationOrder)) {
            BusCommunicationMessage message = busCommunicationMessageRepository.getLastDoctorMessage(groupId,
                    busConsultationOrder.getDoctorId(),
                    busConsultationOrder.getUpdateTime());
            if (Objects.nonNull(message)) {
                String payload = message.getPayload();
                if (StringUtils.isNotBlank(payload)) {
                    try {
                        JSONObject jsonObject = JSON.parseObject(payload);
                        String instruction = jsonObject.getString("text");
                        if (StringUtils.isNotBlank(instruction)) {
                            if (instruction.length() > 1000) {
                                instruction = instruction.substring(0, 1000);
                            }
                            return instruction;
                        }

                    } catch (Exception e) {
                        log.warn("获取IM聊天医生回复数据失败，问诊记录id：{}", groupId, e);
                    }
                }
            }
        }
        return "无";
    }

    /**
     * 判断是否取消
     *
     * @param busConsultationOrder 订单信息
     * @return boolean
     */
    private boolean isCancel(BusConsultationOrder busConsultationOrder) {
        if (ConsultationOrderTypeEnum.IMAGETEXT.getCode().equals(busConsultationOrder.getOrderType())) {
            //已取消，已退号，已退款
            return ConsultationOrderStatusEnum.CANCEL.getCode().equals(busConsultationOrder.getStatus()) ||
                    ConsultationOrderStatusEnum.RETURNED.getCode().equals(busConsultationOrder.getStatus())
                    || ConsultationOrderStatusEnum.REFUNDED.getCode().equals(busConsultationOrder.getStatus());
        }
        //已取消，已退号，已退款
        return ConsultationOrderStatusEnum.CANCEL.getCode().equals(busConsultationOrder.getVideoStatus()) ||
                ConsultationOrderStatusEnum.REFUNDED.getCode().equals(busConsultationOrder.getVideoStatus()) ||
                ConsultationOrderStatusEnum.RETURNED.getCode().equals(busConsultationOrder.getVideoStatus());
    }

    /**
     * 医生退号
     *
     * @param busConsultationOrder 订单信息
     * @return 问诊单退号
     */
    private boolean isReturned(BusConsultationOrder busConsultationOrder) {
        return ConsultationOrderStatusEnum.RETURNED.getCode().equals(busConsultationOrder.getVideoStatus()) ||
                ConsultationOrderStatusEnum.RETURNED.getCode().equals(busConsultationOrder.getStatus());
    }
}
