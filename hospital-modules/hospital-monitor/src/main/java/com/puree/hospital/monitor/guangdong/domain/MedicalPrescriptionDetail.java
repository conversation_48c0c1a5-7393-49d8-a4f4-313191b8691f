package com.puree.hospital.monitor.guangdong.domain;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MedicalPrescriptionDetail {
    private String detailId;
    private String yid;
    private String standardCommonName;
    private String drugsName;
    private BigDecimal sellingPrice;
    private int quantity;
    private String drugsSpecification;
    private String medicationFrequency;
    private int singleDose;
    private int medicationDays;
    private String unit;
    private String drugsUsageValue;
    private String frequencyCode;
    private String frequencyName;
    private String usageCode;
    private String usageName;
    private String weight;
}
