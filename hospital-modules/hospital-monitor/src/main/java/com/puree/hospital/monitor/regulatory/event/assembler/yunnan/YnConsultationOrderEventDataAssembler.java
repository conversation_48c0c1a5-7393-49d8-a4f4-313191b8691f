package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusAssociateDepartment;
import com.puree.hospital.monitor.common.domain.BusBizDepartment;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusDepartment;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.common.domain.BusDoctorHospital;
import com.puree.hospital.monitor.common.domain.BusPatientFamily;
import com.puree.hospital.monitor.common.repository.BusAssociateDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusBizDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorHospitalRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorRepository;
import com.puree.hospital.monitor.common.repository.BusPatientFamilyRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <p>
 * 云南问诊订单（挂号）
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:35
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.CONSULTATION_ORDER + EventDataAssembler.SUFFIX)
public class YnConsultationOrderEventDataAssembler extends AbstractEventDataAssembler<YnConsultationOrder> {

    @Resource
    private BusPatientFamilyRepository busPatientFamilyRepository;

    @Resource
    private BusAssociateDepartmentRepository busAssociateDepartmentRepository;

    @Resource
    private BusDepartmentRepository busDepartmentRepository;

    @Resource
    private BusBizDepartmentRepository busBizDepartmentRepository;

    @Resource
    private BusDoctorHospitalRepository busDoctorHospitalRepository;

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Override
    protected YnConsultationOrder newInstance() {
        return new YnConsultationOrder();
    }

    @Override
    protected void assembleOtherInfo(YnConsultationOrder regulatory, RegulatoryHospitalConfig config) {
        BusConsultationOrder busConsultationOrder = super.getConsultationOrder(regulatory.getBusinessId());
        if (Objects.isNull(busConsultationOrder)) {
            throw new IllegalStateException(String.format("未找到问诊订单信息，问诊id：%s", regulatory.getBusinessId()));
        }
        regulatory.setVisitNo(busConsultationOrder.getOrderNo());
        regulatory.setHospitalName(config.getHospitalName());
        BusPatientFamily busPatientFamily = busPatientFamilyRepository.getById(busConsultationOrder.getFamilyId());
        if (Objects.isNull(busPatientFamily)) {
            throw new IllegalArgumentException(String.format("未找到患者信息，患者id：%s, 问诊id：%s", busConsultationOrder.getFamilyId(), regulatory.getBusinessId()));
        }
        regulatory.setPatientIdCardTypeCode(busPatientFamily.getIdType());
        String idCardNo = busPatientFamily.getIdNumber();
        regulatory.setPatientIdCardNo(idCardNo);
        regulatory.setPatientName(busConsultationOrder.getFamilyName());
        regulatory.setPatientSex(busConsultationOrder.getFamilySex());
        regulatory.setPatientBirthday(getPatientFamilyBirthday(busPatientFamily));
        //设置业务科室信息
        BusBizDepartment busBizDepartment = busBizDepartmentRepository.getById(busConsultationOrder.getDepartmentId());
        if (Objects.isNull(busBizDepartment)) {
            throw new IllegalArgumentException(String.format("未找到关联的业务科室,业务科室id：%s,问诊id：%s", busConsultationOrder.getDepartmentId(), regulatory.getBusinessId()));
        }
        regulatory.setDepartmentName(busBizDepartment.getDepartmentName());
        regulatory.setDepartmentNumber(busBizDepartment.getDepartmentNumber());
        //设置标准科室信息
        BusAssociateDepartment busAssociateDepartment = busAssociateDepartmentRepository.getByBizDepartmentIdAndHospitalId(busBizDepartment.getId(), regulatory.getHospitalId());
        if (Objects.isNull(busAssociateDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        BusDepartment busDepartment = busDepartmentRepository.getById(busAssociateDepartment.getDepartmentId());
        if (Objects.isNull(busDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        regulatory.setDeptClassCode(busDepartment.getDepartmentNumber());
        regulatory.setDeptClassName(busDepartment.getDepartmentName());
        BusDoctorHospital doctorHospital = null;
        if (Objects.nonNull(busConsultationOrder.getDoctorId())) {
            doctorHospital = busDoctorHospitalRepository.getByDoctorIdAndHospitalId(busConsultationOrder.getDoctorId(), regulatory.getHospitalId());
        }
        if (Objects.isNull(doctorHospital)) {
            throw new IllegalArgumentException(String.format("未找到医生关联医院信息，问诊id：%s", regulatory.getBusinessId()));
        }
        //是否是特批专家
        if (Objects.equals(2, doctorHospital.getIsThisCourt())) {
            regulatory.setIfExpert(true);
        }
        BusDoctor busDoctor = busDoctorRepository.getById(busConsultationOrder.getDoctorId());
        if (Objects.isNull(busDoctor)) {
            throw new IllegalArgumentException(String.format("未找到医生信息，医生id：%s", busConsultationOrder.getDoctorId()));
        }
        regulatory.setDoctorIdCardNo(DESUtil.decrypt(busDoctor.getIdCardNo()));
        regulatory.setDoctorName(busConsultationOrder.getDoctorName());
        //图文文正按照当前eventTime进行解析
        if (ConsultationOrderTypeEnum.IMAGETEXT.getCode().equals(busConsultationOrder.getOrderType())) {
            regulatory.setVisitDate(DateUtil.format(regulatory.getEventTime(), DatePattern.PURE_DATE_FORMAT));
            regulatory.setVisitTime(DateUtil.format(regulatory.getEventTime(), DatePattern.PURE_TIME_FORMAT));
        } else if (ConsultationOrderTypeEnum.VIDEO.getCode().equals(busConsultationOrder.getOrderType())) {
            regulatory.setVisitDate(DateUtil.format(busConsultationOrder.getStartTime(), DatePattern.PURE_DATE_FORMAT));
            regulatory.setVisitTime(DateUtil.format(busConsultationOrder.getStartTime(), DatePattern.PURE_TIME_FORMAT));
        }
        BigDecimal amount = busConsultationOrder.getAmount();
        regulatory.setAmount(Objects.nonNull(amount) ? amount.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        regulatory.setCreateTime(busConsultationOrder.getOrderTime());
        regulatory.setOrderType(busConsultationOrder.getOrderType());
        String visitType = Objects.nonNull(busConsultationOrder.getRegisterType()) ? busConsultationOrder.getRegisterType()+ "" : "1";
        regulatory.setVisitType(visitType);
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.CONSULTATION_ORDER;
    }
}
