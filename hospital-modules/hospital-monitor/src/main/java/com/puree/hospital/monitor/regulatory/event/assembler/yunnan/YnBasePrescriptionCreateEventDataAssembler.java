package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.collection.CollectionUtil;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnBasePrescriptionCreate;
import com.puree.hospital.monitor.common.domain.BusAssociateDepartment;
import com.puree.hospital.monitor.common.domain.BusBizDepartment;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusDepartment;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.common.domain.BusDrugs;
import com.puree.hospital.monitor.common.domain.BusPatientFamily;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusPrescriptionDrugs;
import com.puree.hospital.monitor.common.domain.BusSignature;
import com.puree.hospital.monitor.common.repository.BusAssociateDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusBizDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorRepository;
import com.puree.hospital.monitor.common.repository.BusDrugsRepository;
import com.puree.hospital.monitor.common.repository.BusPatientFamilyRepository;
import com.puree.hospital.monitor.common.repository.BusPrescriptionDrugsRepository;
import com.puree.hospital.monitor.common.repository.BusPrescriptionRepository;
import com.puree.hospital.monitor.common.repository.BusSignatureRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 开立处方数据组装抽象
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 17:07
 */
public abstract class YnBasePrescriptionCreateEventDataAssembler<T extends YnBasePrescriptionCreate> extends AbstractEventDataAssembler<T> {

    @Resource
    private BusPrescriptionRepository busPrescriptionRepository;

    @Resource
    private BusAssociateDepartmentRepository busAssociateDepartmentRepository;

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Resource
    private BusSignatureRepository busSignatureRepository;

    @Resource
    private BusPrescriptionDrugsRepository busPrescriptionDrugsRepository;

    @Resource
    private BusDepartmentRepository busDepartmentRepository;

    @Resource
    private BusBizDepartmentRepository busBizDepartmentRepository;

    @Resource
    private BusDrugsRepository busDrugsRepository;

    @Resource
    private BusPatientFamilyRepository busPatientFamilyRepository;

    @Override
    protected void assembleOtherInfo(T regulatory, RegulatoryHospitalConfig config) {
        BusPrescription busPrescription = busPrescriptionRepository.getById(regulatory.getBusinessId());
        if (Objects.isNull(busPrescription)) {
            throw new IllegalStateException(String.format("处方信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        String patientIdCardNo = busPrescription.getFamilyIdcard();
        regulatory.setPatientIdCardNo(patientIdCardNo);
        regulatory.setPatientName(busPrescription.getFamilyName());
        regulatory.setPatientSex(busPrescription.getFamilySex());
        BusPatientFamily busPatientFamily = busPatientFamilyRepository.getById(busPrescription.getFamilyId());
        if (Objects.isNull(busPatientFamily)) {
            throw new IllegalArgumentException(String.format("未找到患者信息，患者id：%s", busPrescription.getFamilyId()));
        }
        regulatory.setPatientIdCardTypeCode(busPatientFamily.getIdType());
        regulatory.setPatientBirthday(getPatientFamilyBirthday(busPatientFamily));
        //如果有问诊信息则上传问诊单号
        BusConsultationOrder consultationOrder = getConsultationRecord(busPrescription);
        if (Objects.isNull(consultationOrder)) {
            throw new IllegalArgumentException(String.format("处方关联的问诊单不存在，处方id：%s", regulatory.getBusinessId()));
        }
        regulatory.setVisitNo(consultationOrder.getOrderNo());
        regulatory.setPrescriptionNumber(busPrescription.getPrescriptionNumber());
        regulatory.setPrescriptionType(busPrescription.getPrescriptionType());
        //1、处方分类代码: 默认都是普通处方
        regulatory.setRxTypeCode("1");
        regulatory.setRemark(busPrescription.getRemark());
        regulatory.setHospitalName(busPrescription.getHospitalName());
        //设置业务科室信息
        BusBizDepartment busBizDepartment = busBizDepartmentRepository.getById(busPrescription.getDepartmentId());
        if (Objects.isNull(busBizDepartment)) {
            throw new IllegalArgumentException(String.format("未找到关联的业务科室,业务科室id：%s", busPrescription.getDepartmentId()));
        }
        regulatory.setDepartmentName(busBizDepartment.getDepartmentName());
        regulatory.setDepartmentNumber(busBizDepartment.getDepartmentNumber());
        //设置标准科室信息
        BusAssociateDepartment busAssociateDepartment = busAssociateDepartmentRepository.getByBizDepartmentIdAndHospitalId(busBizDepartment.getId(), regulatory.getHospitalId());
        if (Objects.isNull(busAssociateDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        BusDepartment busDepartment = busDepartmentRepository.getById(busAssociateDepartment.getDepartmentId());
        if (Objects.isNull(busDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        regulatory.setDeptClassCode(busDepartment.getDepartmentNumber());
        regulatory.setDeptClassName(busDepartment.getDepartmentName());
        BusDoctor busDoctor = busDoctorRepository.getById(busPrescription.getDoctorId());
        if (Objects.isNull(busDoctor)) {
            throw new IllegalArgumentException(String.format("未找到处方医生信息，医生id：%s", busPrescription.getDoctorId()));
        }
        regulatory.setDoctorName(busDoctor.getFullName());
        regulatory.setDoctorIdCardNo(DESUtil.decrypt(busDoctor.getIdCardNo()));
        //获取处方医生签名
        BusSignature signature = busSignatureRepository.getByObjectIdAndObjectType(busPrescription.getDoctorId(), "0");
        if (Objects.nonNull(signature)) {
            String doctorSignUrl = signature.getCertSignature();
            regulatory.setDoctorSignUrl(getFileAddressPrefix() + doctorSignUrl);
        }
        regulatory.setDoctorCaSign(getDoctorCaSign());
        regulatory.setCreateTime(regulatory.getEventTime());
        regulatory.setDrugDirectoryType(busPrescription.getDrugDirectoryType());
        assembleExtra(regulatory, busPrescription, config);
    }

    /**
     * 额外信息组装
     *
     * @param regulatory      监管处方信息
     * @param busPrescription 处方基础信息
     * @param config          医院配置信息
     */
    protected abstract void assembleExtra(T regulatory, BusPrescription busPrescription, RegulatoryHospitalConfig config);

    /**
     * 获取处方药品信息
     *
     * @param prescriptionId 处方id
     * @return 处方药品信息
     */
    protected List<BusPrescriptionDrugs> getPrescriptionDrugs(Long prescriptionId) {
        List<BusPrescriptionDrugs> drugsList = busPrescriptionDrugsRepository.getByPrescriptionId(prescriptionId);
        if (CollectionUtil.isEmpty(drugsList)) {
            throw new IllegalArgumentException(String.format("处方药品列表为空，处方id：%s", prescriptionId));
        }
        return drugsList;
    }

    /**
     * 获取药品信息
     *
     * @param prescriptionDrugsList 处方药品信息
     * @return 药品信息
     */
    protected Map<Long, BusDrugs> getDrugsMap(List<BusPrescriptionDrugs> prescriptionDrugsList) {
        List<Long> drugsIdList = prescriptionDrugsList.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList());
        List<BusDrugs> drugsList = busDrugsRepository.listByIds(drugsIdList);
        if (CollectionUtil.isEmpty(drugsList)) {
            return new HashMap<>();
        }
        return drugsList.stream().collect(Collectors.toMap(BusDrugs::getId, Function.identity()));
    }
}
