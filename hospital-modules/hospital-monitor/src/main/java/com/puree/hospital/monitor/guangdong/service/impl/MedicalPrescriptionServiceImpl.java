package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.app.api.model.BusDictDrugsFrequencyDTO;
import com.puree.hospital.common.core.utils.AgeCalculationUtil;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.guangdong.domain.MedicalPrescription;
import com.puree.hospital.monitor.guangdong.domain.MedicalPrescriptionDetail;
import com.puree.hospital.monitor.guangdong.mapper.MedicalPrescriptionMapper;
import com.puree.hospital.monitor.guangdong.service.IMedicalPrescriptionService;
import com.puree.hospital.monitor.regulatory.event.helper.WmTotalDosageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MedicalPrescriptionServiceImpl implements IMedicalPrescriptionService {
    private final MedicalPrescriptionMapper medicalPrescriptionMapper;
    private final HospitalJgIdConfig hospitalJgIdConfig;
    private final WmTotalDosageHelper wmTotalDosageHelper;

    @Autowired
    public MedicalPrescriptionServiceImpl(MedicalPrescriptionMapper medicalPrescriptionMapper,HospitalJgIdConfig hospitalJgIdConfig, WmTotalDosageHelper wmTotalDosageHelper) {
        this.medicalPrescriptionMapper = medicalPrescriptionMapper;
        this.hospitalJgIdConfig = hospitalJgIdConfig;
        this.wmTotalDosageHelper = wmTotalDosageHelper;
    }

    @Override
    public List<MedicalPrescription> selectList(Long hospitalId,String isNewHos) {
        syncData(hospitalId);
        LambdaQueryWrapper<MedicalPrescription> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<MedicalPrescription>()
                    .eq(MedicalPrescription::getHospitalId, hospitalId)
                    .ge(MedicalPrescription::getCreateTime, LocalDate.now());
        }else{
            queryWrapper = new LambdaQueryWrapper<MedicalPrescription>()
                    .eq(MedicalPrescription::getHospitalId, hospitalId);
        }
        return medicalPrescriptionMapper.selectList(queryWrapper);
    }

    @Override
    public int syncData(Long hospitalId) {
        List<MedicalPrescription> syncDataList = medicalPrescriptionMapper.selectSyncDataList(hospitalId);

        // 获取用药频次
        List<BusDictDrugsFrequencyDTO> frequencyList = wmTotalDosageHelper.getFrequencyList(hospitalId);
        Map<String, BusDictDrugsFrequencyDTO> frequencyMap = frequencyList.stream().collect(Collectors.toMap(BusDictDrugsFrequencyDTO::getName, Function.identity(), (a, b) -> a));

        syncDataList.forEach(i -> {
            i.setJgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setFwwddm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setKh(String.valueOf(i.getFamilyId()));
            i.setKlx("3");//系统内部号
            i.setCfyxts(1);
            String xbdm = i.getXbdm().equals("0") ? "2" : "1";
            i.setXbdm(xbdm);
            int nls = AgeCalculationUtil.getAge(i.getCsrq());
            i.setNls(nls);
            if (nls == 0) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String csrq = sdf.format(i.getCsrq());
                String[] arrays = csrq.split("-");
                csrq = arrays[1] + " " + arrays[2] + "/30";
                i.setNly(csrq);
            }
            i.setYzxmlxdm("01");
            String ypcfsx = i.getPrescriptionType().equals("0") ? "7" : "1";
            i.setYpcfsx(ypcfsx);
            MedicalPrescriptionDetail detail = i.getList().get(0);
            i.setYpid(detail.getYid());
            i.setCfmxid(detail.getDetailId());
            i.setCfmxmc(detail.getStandardCommonName());
            i.setYwmc(detail.getDrugsName());
            i.setYpgg(detail.getDrugsSpecification());
            i.setYwjxdm("01");
            if (ypcfsx.equals("1")){ //西药
                i.setYwsycjl(new BigDecimal(detail.getSingleDose()));
                i.setYwsyjldw(detail.getUnit());
                i.setYytjdm(detail.getUsageCode());
                i.setYytjmc(detail.getUsageName());
                i.setFyjl(new BigDecimal(detail.getQuantity()));
                BusDictDrugsFrequencyDTO drugsFrequency = frequencyMap.get(detail.getMedicationFrequency());
                if (Objects.nonNull(drugsFrequency)) {
                    i.setYwsypcdm(drugsFrequency.getCode());
                    i.setYwsypcmc(drugsFrequency.getName());
                }
                // 药物使用总剂量 总剂量计算 = 单次剂量 * 用药频率几次 * 持续天数 / 天
                BigDecimal totalDosage = wmTotalDosageHelper.getTotalDosage(drugsFrequency, new BigDecimal(detail.getSingleDose()), new BigDecimal(detail.getMedicationDays()));
                i.setYwsyzjl(totalDosage);
            } else {
                //中药
                i.setFyjl(new BigDecimal(detail.getWeight()));
            }
            //限制10个字符以内
            i.setFyjldw(detail.getDrugsSpecification()
                    .substring(0, Math.min(detail.getDrugsSpecification().length(), 10)));
            i.setDj(detail.getSellingPrice());
            BigDecimal zje = detail.getSellingPrice().multiply(new BigDecimal(detail.getQuantity()));
            i.setZje(zje);
            i.setCxbz("1");
            i.setSjscsj(new Date());
        });

        for (MedicalPrescription p : syncDataList) {
            QueryWrapper<MedicalPrescription> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("CFBH", p.getCfbh());
            queryWrapper.eq("hospital_id",hospitalId);
            List<MedicalPrescription> plist = medicalPrescriptionMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(plist)) {
                p.setHospitalId(hospitalId);
                medicalPrescriptionMapper.insert(p);
            }
        }
        return 1;
    }

    @Override
    public int insert(MedicalPrescription medicalPrescription) {
        return medicalPrescriptionMapper.insert(medicalPrescription);
    }
}
