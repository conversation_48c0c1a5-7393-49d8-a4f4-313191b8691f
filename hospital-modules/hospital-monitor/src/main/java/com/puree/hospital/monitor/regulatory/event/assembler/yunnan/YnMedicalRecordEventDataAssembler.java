package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionStatusEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.RegisterTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnMedicalRecord;
import com.puree.hospital.monitor.common.domain.*;
import com.puree.hospital.monitor.common.repository.*;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.puree.hospital.monitor.regulatory.event.model.TcmDiagnosisModel;
import com.puree.hospital.monitor.regulatory.event.model.WmDiagnosisModel;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 云南--就诊记录 （需要再处方开立之前上传）
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:37
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.MEDICAL_RECORD + EventDataAssembler.SUFFIX)
public class YnMedicalRecordEventDataAssembler extends AbstractEventDataAssembler<YnMedicalRecord> {

    @Resource
    private BusPatientFamilyRepository busPatientFamilyRepository;

    @Resource
    private BusAssociateDepartmentRepository busAssociateDepartmentRepository;

    @Resource
    private BusDepartmentRepository busDepartmentRepository;

    @Resource
    private BusBizDepartmentRepository busBizDepartmentRepository;

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Resource
    private BusSignatureRepository busSignatureRepository;

    @Resource
    private BusQuickConsultationRepository busQuickConsultationRepository;

    @Resource
    private BusPrescriptionRepository busPrescriptionRepository;

    @Resource
    private BusHospitalFamilyRepository busHospitalFamilyRepository;

    @Override
    protected YnMedicalRecord newInstance() {
        return new YnMedicalRecord();
    }

    @Override
    protected void assembleOtherInfo(YnMedicalRecord regulatory, RegulatoryHospitalConfig config) {
        BusPrescription busPrescription = null;
        BusConsultationOrder busConsultationOrder;
        if (Boolean.TRUE.equals(regulatory.isPrescriptionCreateEvent())) {
            busPrescription = busPrescriptionRepository.getById(regulatory.getBusinessId());
            if (Objects.isNull(busPrescription)) {
                throw new IllegalArgumentException(String.format("处方信息不存在,处方id：%s", regulatory.getBusinessId()));
            }
            if (!Objects.equals("0", busPrescription.getIdentity())) {
                throw new IllegalArgumentException(String.format("当前不是普通开方，不做就诊记录上报，处方id：%s", regulatory.getBusinessId()));
            }
            if (Objects.isNull(busPrescription.getConsultationOrderId())) {
                throw new IllegalArgumentException(String.format("未找到关联的问诊订单信息，处方id：%s", regulatory.getBusinessId()));
            }
            busConsultationOrder = super.getConsultationOrder(busPrescription.getConsultationOrderId());
        } else {
            busConsultationOrder = super.getConsultationOrder(regulatory.getBusinessId());
        }
        if (Objects.isNull(busConsultationOrder)) {
            throw new IllegalStateException(String.format("未找到问诊订单信息，问诊id：%s", regulatory.getBusinessId()));
        }
        if (Objects.equals(RegisterTypeEnum.CONSULT.getCode(), busConsultationOrder.getRegisterType())) {
            throw new IllegalArgumentException(String.format("当前问诊订单类型为【咨询】不做就诊记录上报，问诊id：%s", regulatory.getBusinessId()));
        }
        regulatory.setVisitNo(busConsultationOrder.getOrderNo());
        BusPatientFamily busPatientFamily = busPatientFamilyRepository.getById(busConsultationOrder.getFamilyId());
        if (Objects.isNull(busPatientFamily)) {
            throw new IllegalArgumentException(String.format("未找到患者信息，患者id：%s", busConsultationOrder.getFamilyId()));
        }
        String idCardNo = busPatientFamily.getIdNumber();
        regulatory.setPatientIdCardTypeCode(busPatientFamily.getIdType());
        regulatory.setPatientIdCardNo(idCardNo);
        regulatory.setPatientName(busConsultationOrder.getFamilyName());
        regulatory.setPatientSex(busConsultationOrder.getFamilySex());
        regulatory.setPatientBirthday(getPatientFamilyBirthday(busPatientFamily));
        regulatory.setHospitalName(config.getHospitalName());

        //设置业务科室信息
        BusBizDepartment busBizDepartment = busBizDepartmentRepository.getById(busConsultationOrder.getDepartmentId());
        if (Objects.isNull(busBizDepartment)) {
            throw new IllegalArgumentException(String.format("未找到关联的业务科室,业务科室id：%s", busConsultationOrder.getDepartmentId()));
        }
        regulatory.setDepartmentName(busBizDepartment.getDepartmentName());
        regulatory.setDepartmentNumber(busBizDepartment.getDepartmentNumber());
        //设置标准科室信息
        BusAssociateDepartment busAssociateDepartment = busAssociateDepartmentRepository.getByBizDepartmentIdAndHospitalId(busBizDepartment.getId(), regulatory.getHospitalId());
        if (Objects.isNull(busAssociateDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        BusDepartment busDepartment = busDepartmentRepository.getById(busAssociateDepartment.getDepartmentId());
        if (Objects.isNull(busDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        regulatory.setDeptClassCode(busDepartment.getDepartmentNumber());
        regulatory.setDeptClassName(busDepartment.getDepartmentName());
        BusDoctor busDoctor = busDoctorRepository.getById(busConsultationOrder.getDoctorId());
        if (Objects.isNull(busDoctor)) {
            throw new IllegalArgumentException(String.format("未找到医生信息，医生id：%s", busConsultationOrder.getDoctorId()));
        }
        regulatory.setDoctorIdCardNo(DESUtil.decrypt(busDoctor.getIdCardNo()));
        regulatory.setDoctorName(busConsultationOrder.getDoctorName());
        //获取处方医生签名
        BusSignature signature = busSignatureRepository.getByObjectIdAndObjectType(busConsultationOrder.getDoctorId(), "0");
        if (Objects.nonNull(signature)) {
            String doctorSignUrl = signature.getCertSignature();
            regulatory.setDoctorSignUrl(getFileAddressPrefix() + doctorSignUrl);
        }
        regulatory.setDoctorCaSign(getDoctorCaSign());
        if (ConsultationOrderTypeEnum.IMAGETEXT.getCode().equals(busConsultationOrder.getOrderType())) {
            regulatory.setVisitStarTime(busConsultationOrder.getCreateTime());
        } else {
            regulatory.setVisitStarTime(busConsultationOrder.getStartTime());
        }
        regulatory.setVisitEndTime(Objects.nonNull(busConsultationOrder.getCompleteTime()) ? busConsultationOrder.getCompleteTime() : regulatory.getEventTime());
        String symptomDescription = "";
        List<String> firstVisitPictureUrl = Lists.newArrayList();
        if (Objects.nonNull(busConsultationOrder.getConsultationId())) {
            BusQuickConsultation busQuickConsultation = busQuickConsultationRepository.getById(busConsultationOrder.getConsultationId());
            if (Objects.nonNull(busQuickConsultation)) {
                symptomDescription = busQuickConsultation.getSymptomDescription();
                if (StringUtils.isNotBlank(busQuickConsultation.getPicture())) {
                    String[] pictureArray = busQuickConsultation.getPicture().split(",");
                    Arrays.stream(pictureArray).forEach(s -> {
                        firstVisitPictureUrl.add(getFileAddressPrefix() + s);
                    });
                }
            }
        }
        if (StringUtils.isBlank(symptomDescription)) {
            throw new IllegalArgumentException(String.format("未找到患者的病情描述, 订单id：%s", regulatory.getBusinessId()));
        }
        regulatory.setSubjComplaint(symptomDescription);

        //获取患者首诊病历图片
        if (CollectionUtil.isEmpty(firstVisitPictureUrl)) {
            BusHospitalFamily hospitalFamily = busHospitalFamilyRepository.getHospitalFamily(regulatory.getHospitalId(), busConsultationOrder.getPatientId(), busConsultationOrder.getFamilyId());
            if (Objects.nonNull(hospitalFamily) && StringUtils.isNotBlank(hospitalFamily.getDiagnosisArchives())) {
                String[] pictureArray = hospitalFamily.getDiagnosisArchives().split(",");
                Arrays.stream(pictureArray).forEach(s -> {
                    firstVisitPictureUrl.add(getFileAddressPrefix() + s);
                });
            }
        }
        if (CollectionUtil.isEmpty(firstVisitPictureUrl)) {
            throw new IllegalArgumentException(String.format("未找到患者首诊病历信息, 订单id：%s", regulatory.getBusinessId()));
        }
        regulatory.setFirstVisitPictureUrl(firstVisitPictureUrl);

        //获取有效的处方
        if (Objects.isNull(busPrescription)) {
            List<BusPrescription> busPrescriptionList = busPrescriptionRepository.getByConsultationOrderId(busConsultationOrder.getId());
            List<String> invalidStatus = Lists.newArrayList(PrescriptionStatusEnum.INVALID.getStatus(),
                    PrescriptionStatusEnum.NOT_PASS.getStatus(),
                    PrescriptionStatusEnum.CANCELLATION.getStatus());
            busPrescription = busPrescriptionList.stream().filter(Objects::nonNull).filter(s -> !invalidStatus.contains(s.getStatus())).findFirst().orElse(null);
        }
        if (Objects.isNull(busPrescription)) {
            throw new IllegalArgumentException(String.format("未找到问诊关联的处方信息，问诊订单id：%s", regulatory.getBusinessId()));
        }
        if (StringUtils.isBlank(busPrescription.getClinicalDiagnosis())) {
            throw new IllegalArgumentException(String.format("未找到问诊关联处方的诊断信息，问诊订单id：%s", regulatory.getBusinessId()));
        }
        if (PrescriptionTypeEnum.isMm(busPrescription.getPrescriptionType())) {
            List<WmDiagnosisModel> diagnosisList = JSON.parseArray(busPrescription.getClinicalDiagnosis(), WmDiagnosisModel.class);
            if (CollectionUtil.isNotEmpty(diagnosisList)) {
                WmDiagnosisModel wmDiagnosis = diagnosisList.stream().filter(Objects::nonNull).findFirst().orElse(null);
                if (Objects.isNull(wmDiagnosis)) {
                    throw new IllegalArgumentException(String.format("未找到问诊关联处方的西药诊断信息，问诊订单id：%s", regulatory.getBusinessId()));
                }
                regulatory.setMdDisCode(wmDiagnosis.getIcdCode());
                regulatory.setMdDisName(wmDiagnosis.getDiseaseName());
                regulatory.setDisDescription(wmDiagnosis.getDiseaseName());
            }
        } else {
            List<TcmDiagnosisModel> diagnosisList = JSON.parseArray(busPrescription.getClinicalDiagnosis(), TcmDiagnosisModel.class);
            if (CollectionUtil.isNotEmpty(diagnosisList)) {
                TcmDiagnosisModel tcmDiagnosis = diagnosisList.stream().filter(Objects::nonNull).findFirst().orElse(null);
                if (Objects.isNull(tcmDiagnosis)) {
                    throw new IllegalArgumentException(String.format("未找到问诊关联处方的中药诊断信息，问诊订单id：%s", regulatory.getBusinessId()));
                }
                regulatory.setTcmDdCode(tcmDiagnosis.getDiagnosisCode());
                regulatory.setTcmDdName(tcmDiagnosis.getTcmDiagnosis());
                if (Objects.isNull(tcmDiagnosis.getTcmSyndrome())) {
                    throw new IllegalArgumentException(String.format("未找到问诊关联处方的中药诊断的症状信息，问诊订单id：%s", regulatory.getBusinessId()));
                }
                regulatory.setTcmSdCode(tcmDiagnosis.getTcmSyndrome().getDiagnosisCode());
                regulatory.setTcmSdName(tcmDiagnosis.getTcmSyndrome().getTcmSyndrome());
                regulatory.setDisDescription(tcmDiagnosis.getTcmDiagnosis());
            }
        }
        String remark = StringUtils.isNotBlank(busPrescription.getRemark()) ? busPrescription.getRemark() : "无";
        regulatory.setTreatMeas(remark);
        //设置咨询记录链接
        regulatory.setReviewUrl(getConsultationRecordUrl(busConsultationOrder.getHospitalId(),
                busConsultationOrder.getDoctorId(),
                busConsultationOrder.getDepartmentId(),
                busConsultationOrder.getPatientId(),
                busConsultationOrder.getFamilyId(),
                regulatory.getProvinceAbbreviation(),
                busConsultationOrder.getId()));
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.MEDICAL_RECORD;
    }

    @Override
    protected void assembleExtraData(YnMedicalRecord regulatory, RegulatoryCollectEvent event) {
        if (Objects.equals(RegulatoryEventTypeEnum.PRESCRIPTION_CM_CREATE, event.getEventType())
                || Objects.equals(RegulatoryEventTypeEnum.PRESCRIPTION_WM_CREATE, event.getEventType())) {
            regulatory.setPrescriptionCreateEvent(true);
        }
    }
}
