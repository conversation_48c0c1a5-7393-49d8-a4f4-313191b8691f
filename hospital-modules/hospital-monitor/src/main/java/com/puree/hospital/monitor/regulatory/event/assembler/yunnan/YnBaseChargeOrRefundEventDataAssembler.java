package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnChargeOrRefund;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnChargeOrRefundItem;
import com.puree.hospital.monitor.common.domain.BusAssociateDepartment;
import com.puree.hospital.monitor.common.domain.BusBizDepartment;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusDepartment;
import com.puree.hospital.monitor.common.domain.BusDrugs;
import com.puree.hospital.monitor.common.domain.BusDrugsOrder;
import com.puree.hospital.monitor.common.domain.BusOrder;
import com.puree.hospital.monitor.common.domain.BusPatientFamily;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusPrescriptionDrugs;
import com.puree.hospital.monitor.common.domain.MiPayResult;
import com.puree.hospital.monitor.common.repository.BusAssociateDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusBizDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDrugsOrderRepository;
import com.puree.hospital.monitor.common.repository.BusDrugsRepository;
import com.puree.hospital.monitor.common.repository.BusOrderRepository;
import com.puree.hospital.monitor.common.repository.BusPatientFamilyRepository;
import com.puree.hospital.monitor.common.repository.BusPrescriptionDrugsRepository;
import com.puree.hospital.monitor.common.repository.BusPrescriptionRepository;
import com.puree.hospital.monitor.common.repository.MiPayResultRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.puree.hospital.pay.api.RemotePayOrderService;
import com.puree.hospital.pay.api.model.BusPayOrder;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 收费/退费数据组装抽象
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/2 09:49
 */
@Slf4j
public abstract class YnBaseChargeOrRefundEventDataAssembler extends AbstractEventDataAssembler<YnChargeOrRefund> {

    /**
     * 药品子订单类型
     */
    private static final String DRUG_SUB_ORDER_TYPE = "0";

    @Resource
    private BusPatientFamilyRepository busPatientFamilyRepository;

    @Resource
    private BusAssociateDepartmentRepository busAssociateDepartmentRepository;

    @Resource
    private BusDepartmentRepository busDepartmentRepository;

    @Resource
    private BusBizDepartmentRepository busBizDepartmentRepository;

    @Resource
    protected BusDrugsOrderRepository busDrugsOrderRepository;

    @Resource
    private BusPrescriptionRepository busPrescriptionRepository;

    @Resource
    private BusOrderRepository busOrderRepository;

    @Resource
    private MiPayResultRepository miPayResultRepository;

    @Resource
    private BusPrescriptionDrugsRepository busPrescriptionDrugsRepository;

    @Resource
    private BusDrugsRepository busDrugsRepository;

    @Resource
    private RemotePayOrderService remotePayOrderService;

    @Override
    protected YnChargeOrRefund newInstance() {
        return new YnChargeOrRefund();
    }

    @Override
    protected void assembleOtherInfo(YnChargeOrRefund regulatory, RegulatoryHospitalConfig config) {
        String orderType = regulatory.getBusinessId().substring(0, 2);
        List<BusPrescription> prescriptions = Lists.newArrayList();
        BusConsultationOrder busConsultationOrder = null;
        switch (orderType) {
            case OrderTypeConstant.CONSULATION_ORDER:
                busConsultationOrder = busConsultationOrderRepository.getByOrderNo(regulatory.getBusinessId());
                break;
            case OrderTypeConstant.DRUGS_ORDER:
               BusDrugsOrder drugsOrder = busDrugsOrderRepository.getByOrderNo(regulatory.getBusinessId());
               if (Objects.isNull(drugsOrder)) {
                   throw new IllegalArgumentException(String.format("未找到关联的药品订单信息，订单编号%s", regulatory.getBusinessId()));
               }
               //把处方加到处方集合中
               BusPrescription busPrescription = busPrescriptionRepository.getById(drugsOrder.getPrescriptionId());
               busConsultationOrder = getConsultationRecord(busPrescription);
               if (Objects.nonNull(busPrescription) || Objects.nonNull(busConsultationOrder)) {
                   prescriptions.add(busPrescription);
               }
               if (CollectionUtil.isEmpty(prescriptions)) {
                   throw new IllegalArgumentException(String.format("未找到关联的处方信息，订单编号%s", regulatory.getBusinessId()));
               }
               break;
            case OrderTypeConstant.TOTAL_ORDER:
                List<BusOrder> busOrders = busOrderRepository.getByOrderNo(regulatory.getBusinessId());
                List<Long> subOrderIds = busOrders.stream().filter(o -> DRUG_SUB_ORDER_TYPE.equals(o.getSubOrderType())).map(BusOrder::getSubOrderId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(subOrderIds)) {
                    List<BusDrugsOrder> busDrugsOrders = getDrugsOrderList(subOrderIds);
                    List<Long> prescriptionIds = busDrugsOrders.stream().map(BusDrugsOrder::getPrescriptionId).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(prescriptionIds)) {
                        List<BusPrescription> prescriptionsList = busPrescriptionRepository.listByIds(prescriptionIds);
                        for (BusPrescription prescription : prescriptionsList) {
                            BusConsultationOrder consultationRecord = getConsultationRecord(prescription);
                            if (consultationRecord != null) {
                                prescriptions.add(prescription);
                                busConsultationOrder = consultationRecord;
                            }
                        }
                    }
                    if (CollectionUtil.isEmpty(prescriptions)) {
                        throw new IllegalArgumentException(String.format("未找到关联的处方信息，订单编号%s", regulatory.getBusinessId()));
                    }
                }
                break;
            default:
                throw new IllegalArgumentException(String.format("订单类型%s不在监管上报类型内,订单编号：%s", orderType, regulatory.getBusinessId()));
        }
        if (Objects.isNull(busConsultationOrder)) {
            throw new IllegalArgumentException(String.format("未找到关联的问诊订单信息，订单编号：%s", regulatory.getBusinessId()));
        }
        //设置问诊信息
        assembleConsultationOrder(regulatory, busConsultationOrder, config);
        //交易时间
        regulatory.setRcdDatetime(regulatory.getEventTime());
        R<BusPayOrder> payOrderResult = remotePayOrderService.queryPayOrder(regulatory.getBusinessId());
        if (Objects.isNull(payOrderResult) || !payOrderResult.isSuccess() || Objects.isNull(payOrderResult.getData())) {
            throw new IllegalStateException(String.format("订单关联的支付流水信息查询异常，订单编号:%s", regulatory.getBusinessId()));
        }
        BusPayOrder busPayOrder = payOrderResult.getData();
        regulatory.setPayWay(busPayOrder.getPayWay());
        //支付方式编码
        regulatory.setPayTypeCode(getPayTypeCode(regulatory));
        regulatory.setAccountNo(getAccountNo(busPayOrder, regulatory));
        regulatory.setOriginalAccountNo(getOriginalAccountNo(busPayOrder));
        regulatory.setTradeNo(getOutTradeNo(busPayOrder, regulatory));
        //运费不属于监管范畴
        if (OrderTypeConstant.CONSULATION_ORDER.equals(orderType)) {
            regulatory.setItemList(getItemList(busConsultationOrder));
        } else {
            regulatory.setItemList(getItemList(prescriptions));
        }
        //总金额 = 明细之和
        BigDecimal totalAmount = regulatory.getItemList().stream().map(YnChargeOrRefundItem::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        if (ifNull(totalAmount).compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException(String.format("订单%s金额小于等于0，不做收退费上报", regulatory.getBusinessId()));
        }
        //订单总金额
        regulatory.setTotalAmount(totalAmount);
        //自付金额
        regulatory.setSelfPayAmount(getSelfPayAmount(regulatory));
    }

    /**
     * 获取收付款编码
     *
     * @return 收付款编码
     */
    protected abstract String getChargeRefundCode();

    /**
     * 获取结算单号
     *
     * @param busPayOrder 支付流水信息
     * @param regulatory  监管数据
     * @return 原始交易流水号
     */
    protected abstract String getAccountNo(BusPayOrder busPayOrder, YnChargeOrRefund regulatory);

    /**
     * 获取结算单号
     *
     * @param busPayOrder 支付流水信息
     * @param regulatory  监管数据
     * @return 结算单号
     */
    protected abstract String getOutTradeNo(BusPayOrder busPayOrder, YnChargeOrRefund regulatory);

    /**
     * 获取原始交易流水号
     *
     * @param busPayOrder 支付流水信息
     * @return 原始交易流水号
     */
    protected abstract String getOriginalAccountNo(BusPayOrder busPayOrder);

    /**
     * 获取药品订单列表
     *
     * @param subOrderIds 子订单ID
     * @return 药品订单列表
     */
    protected abstract List<BusDrugsOrder> getDrugsOrderList(List<Long> subOrderIds);

    /**
     * 设置问诊信息
     *
     * @param regulatory           收退费信息
     * @param config               医院配置
     */
    private void assembleConsultationOrder(YnChargeOrRefund regulatory,
                                           BusConsultationOrder consultationOrder,
                                           RegulatoryHospitalConfig config) {
        //问诊订单编号
        regulatory.setVisitNo(consultationOrder.getOrderNo());
        if (Objects.isNull(consultationOrder.getDoctorId())) {
            throw new IllegalArgumentException(String.format("当前问诊订单%s未关联医生，不做数据上报", regulatory.getBusinessId()));
        }
        BusPatientFamily busPatientFamily = busPatientFamilyRepository.getById(consultationOrder.getFamilyId());
        if (Objects.isNull(busPatientFamily)) {
            throw new IllegalArgumentException(String.format("未找到患者信息，患者id：%s", consultationOrder.getFamilyId()));
        }
        String idCardNo = busPatientFamily.getIdNumber();
        regulatory.setPatientIdCardTypeCode(busPatientFamily.getIdType());
        regulatory.setPatientIdCardNo(idCardNo);
        regulatory.setPatientName(consultationOrder.getFamilyName());
        regulatory.setPatientSex(consultationOrder.getFamilySex());
        regulatory.setPatientBirthday(getPatientFamilyBirthday(busPatientFamily));
        regulatory.setChargeRefundCode(getChargeRefundCode());
        regulatory.setHospitalName(config.getHospitalName());
        //设置业务科室信息
        BusBizDepartment busBizDepartment = busBizDepartmentRepository.getById(consultationOrder.getDepartmentId());
        if (Objects.isNull(busBizDepartment)) {
            throw new IllegalArgumentException(String.format("未找到关联的业务科室,业务科室id：%s", consultationOrder.getDepartmentId()));
        }
        regulatory.setDepartmentName(busBizDepartment.getDepartmentName());
        regulatory.setDepartmentNumber(busBizDepartment.getDepartmentNumber());
        //设置标准科室信息
        BusAssociateDepartment busAssociateDepartment = busAssociateDepartmentRepository.getByBizDepartmentIdAndHospitalId(busBizDepartment.getId(), regulatory.getHospitalId());
        if (Objects.isNull(busAssociateDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        BusDepartment busDepartment = busDepartmentRepository.getById(busAssociateDepartment.getDepartmentId());
        if (Objects.isNull(busDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        regulatory.setDeptClassCode(busDepartment.getDepartmentNumber());
        regulatory.setDeptClassName(busDepartment.getDepartmentName());
    }

    /**
     * <p>
     * 支付方式编码
     * 编码 名 称
     * 01 城镇职工基本医疗保险
     * 02 城镇居民基本医疗保险
     * 03 新型农村合作医疗
     * 04 贫困救助
     * 05 商业医疗保险
     * 06 全公费
     * 07 全自费
     * 08 其他社会保险
     * 99 其他
     * </p>
     *
     * @param regulatory 收退费信息
     * @return 支付方式编码
     */
    private String getPayTypeCode(YnChargeOrRefund regulatory) {
        if (PaysTypeEnum.WECHAT_PAY.getCode().equals(regulatory.getPayWay())
                || PaysTypeEnum.TONGLIAN_PAY.getCode().equals(regulatory.getPayWay())) {
            return "07";
        } else if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(regulatory.getPayWay())) {
            //未来可能需要根据险种进行获取
            return "01";
        }
        return "99";
    }

    /**
     * 获取收退费明细
     *
     * @param busConsultationOrder 问诊订单
     * @return 收退费明细
     */
    private List<YnChargeOrRefundItem> getItemList(BusConsultationOrder busConsultationOrder) {
        YnChargeOrRefundItem item = new YnChargeOrRefundItem();
        item.setItemNo(busConsultationOrder.getOrderNo());
        item.setItemName("问诊费");
        item.setChargeRefundCode(getChargeRefundCode());
        item.setPinCatCode("0301");
        item.setUnitPrice(busConsultationOrder.getAmount());
        item.setQuantity(1);
        item.setTotalAmount(busConsultationOrder.getAmount());
        return Lists.newArrayList(item);
    }

    /**
     * 获取收退费明细
     *
     * @param prescriptions 处方信息
     * @return 收退费明细
     */
    private List<YnChargeOrRefundItem> getItemList(List<BusPrescription> prescriptions) {
        List<YnChargeOrRefundItem> itemList = Lists.newArrayList();
        for (BusPrescription busPrescription : prescriptions) {
            List<BusPrescriptionDrugs> prescriptionDrugsList = busPrescriptionDrugsRepository.getByPrescriptionId(busPrescription.getId());
            if (CollectionUtil.isEmpty(prescriptionDrugsList)) {
                throw new IllegalArgumentException(String.format("未找到处方药品信息，处方id：%s", busPrescription.getId()));
            }
            Map<Long, BusDrugs> drugsMap = getDrugsMap(prescriptionDrugsList);
            String prescriptionType = busPrescription.getPrescriptionType();
            Map<Long, String> commodityCodeMap = getCommodityClassifyMap();
            for (BusPrescriptionDrugs prescriptionDrugs : prescriptionDrugsList) {
                BusDrugs busDrugs = drugsMap.get(prescriptionDrugs.getDrugsId());
                if (Objects.isNull(busDrugs)) {
                    throw new IllegalArgumentException(String.format("未找到药品信息，药品id：%s", prescriptionDrugs.getDrugsId()));
                }
                YnChargeOrRefundItem item = new YnChargeOrRefundItem();
                //避免重复前面增加一个D
                item.setItemNo("D" + prescriptionDrugs.getId());
                item.setItemName(prescriptionDrugs.getDrugsName());
                item.setChargeRefundCode(getChargeRefundCode());
                String pinCatCode = getDrugPinCatCode(prescriptionType, busDrugs.getDrugsType(), commodityCodeMap);
                item.setPinCatCode(pinCatCode);
                BigDecimal sellingPrice = Objects.nonNull(prescriptionDrugs.getSellingPrice()) ? prescriptionDrugs.getSellingPrice().setScale(4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                item.setUnitPrice(sellingPrice);
                Integer quantity = prescriptionDrugs.getQuantity();
                if (PrescriptionTypeEnum.isTcmOrZyxdf(prescriptionType)) {
                    //帖数
                    String[] usagesArr = busPrescription.getUsages().split(",");
                    int tcmPasteCnt = Integer.parseInt(usagesArr[1]);
                    //总数量
                    quantity = (int)Math.ceil((double) tcmPasteCnt * Double.parseDouble(prescriptionDrugs.getWeight()));
                }
                item.setQuantity(quantity);
                BigDecimal totalAmount = new BigDecimal(quantity).multiply(sellingPrice).setScale(2, RoundingMode.HALF_UP);
                item.setTotalAmount(totalAmount);
                itemList.add(item);
            }
            // 加工费
            BigDecimal processPrice = ifNull(busPrescription.getProcessPrice()).add(ifNull(busPrescription.getEnterpriseProcessPrice()));
            if (BigDecimal.ZERO.compareTo(processPrice) < 0) {
                YnChargeOrRefundItem item = new YnChargeOrRefundItem();
                //项目名称是加工费
                item.setItemNo("P" + busPrescription.getPrescriptionNumber());
                item.setItemName("药事服务费");
                item.setChargeRefundCode(getChargeRefundCode());
                item.setPinCatCode("0314");
                item.setUnitPrice(processPrice);
                item.setQuantity(1);
                item.setTotalAmount(processPrice);
                itemList.add(item);
            }
            BigDecimal examinationFee = ifNull(busPrescription.getExaminationFee());
            if (BigDecimal.ZERO.compareTo(examinationFee) < 0) {
                YnChargeOrRefundItem item = new YnChargeOrRefundItem();
                //诊查费
                item.setItemNo("E" + busPrescription.getPrescriptionNumber());
                item.setItemName(busPrescription.getExaminationName());
                item.setChargeRefundCode(getChargeRefundCode());
                item.setPinCatCode("0315");
                item.setUnitPrice(examinationFee);
                item.setQuantity(1);
                item.setTotalAmount(examinationFee);
                itemList.add(item);
            }
        }
        return itemList;
    }

    /**
     * 获取自付金额
     *
     * @param regulatory  收退费信息
     * @return 自付金额
     */
    private BigDecimal getSelfPayAmount(YnChargeOrRefund regulatory) {
        if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(regulatory.getPayWay())) {
            MiPayResult miPayResult = miPayResultRepository.getByOrderNo(regulatory.getBusinessId());
            if (Objects.isNull(miPayResult)) {
                throw new IllegalArgumentException(String.format("未找到医保支付关联信息，订单号：%s", regulatory.getBusinessId()));
            }
            // 自费金额 = 医保剩余现金支付部分 + 其他自费金额 - 运费
            BigDecimal ownPayAmt = Objects.nonNull(miPayResult.getOwnPayAmt()) ? miPayResult.getOwnPayAmt() : BigDecimal.ZERO;
            BigDecimal otherCashAmount = Objects.nonNull(miPayResult.getOtherCashAmount()) ? miPayResult.getOtherCashAmount() : BigDecimal.ZERO;
            BigDecimal freight = Objects.nonNull(miPayResult.getFreight()) ? miPayResult.getFreight() : BigDecimal.ZERO;
            return ownPayAmt.add(otherCashAmount).subtract(freight);
        }
        return regulatory.getTotalAmount();
    }

    private BigDecimal ifNull(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    /**
     * 获取药品信息
     *
     * @param prescriptionDrugsList 处方药品信息
     * @return 药品信息
     */
    private Map<Long, BusDrugs> getDrugsMap(List<BusPrescriptionDrugs> prescriptionDrugsList) {
        List<Long> drugsIdList = prescriptionDrugsList.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList());
        List<BusDrugs> drugsList = busDrugsRepository.listByIds(drugsIdList);
        if (CollectionUtil.isEmpty(drugsList)) {
            return Collections.emptyMap();
        }
        return drugsList.stream().collect(Collectors.toMap(BusDrugs::getId, Function.identity()));
    }

    /**
     * 获取药品财务费用代码
     *
     * @param prescriptionType     处方类型
     * @param drugsType            药品分类id
     * @param commodityClassifyMap 药品分类map
     * @return 药品品目编码
     */
    private String getDrugPinCatCode(String prescriptionType, Long drugsType, Map<Long, String> commodityClassifyMap) {
        //中药饮片和药协定方都是中药饮片
        if (PrescriptionTypeEnum.isTcmOrZyxdf(prescriptionType)) {
            return "0102";
        }
        //中成药
        String classifyName = commodityClassifyMap.get(drugsType);
        if (StringUtils.isNotBlank(classifyName) && classifyName.contains("中成药")) {
            return "0103";
        }
        //西药
        return "0101";
    }

}
