package com.puree.hospital.monitor.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/23 18:23
 */
@SpringBootTest
class FamilyIdCardConfigTest {

    @Autowired
    private FamilyIdCardConfig familyIdCardConfig;

    @Test
    void getFamilyIdCardMap() {
        Map<String, String> familyIdCardMap = familyIdCardConfig.getFamilyIdCardMap(1L);
        System.out.println(familyIdCardMap);
    }
}