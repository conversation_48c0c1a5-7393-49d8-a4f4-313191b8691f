package com.puree.hospital.hardware.domain.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CheckOrganizationEnum;
import com.puree.hospital.common.api.enums.EhrCheckItemEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SHA256Util;
import com.puree.hospital.common.security.entity.SignatureKeyEntity;
import com.puree.hospital.common.security.service.SignatureKeyService;
import com.puree.hospital.ehr.api.RemoteEhrCcrInspectionReportService;
import com.puree.hospital.followup.api.RemoteMedicalReportService;
import com.puree.hospital.followup.api.RemoteReportRegularRecordService;
import com.puree.hospital.followup.api.model.medical.constant.AdapterCheckItem;
import com.puree.hospital.followup.api.model.medical.constant.GenderEnum;
import com.puree.hospital.followup.api.model.medical.constant.UniversalAbnormalEnum;
import com.puree.hospital.followup.api.model.medical.upload.dto.BloodSugarDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamConclusionDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamConclusionItemDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamPatientInfoDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.InspectItemDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.InspectSummaryDTO;
import com.puree.hospital.followup.api.model.medical.upload.dto.RegularExamDTO;
import com.puree.hospital.hardware.domain.BusEhrCheckData;
import com.puree.hospital.hardware.domain.BusEhrCheckRecord;
import com.puree.hospital.hardware.domain.ehome.BusEhrCheck;
import com.puree.hospital.hardware.domain.ehome.BusEhrCheckItem;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName EHomeMedicalEvent
 * <AUTHOR>
 * @Description 体检新数据通知
 * @Date 2024/7/15 10:18
 * @Version 1.0
 */
@Slf4j
@Component
@AllArgsConstructor
public class EHomeMedicalEvent {

    private final RemoteEhrCcrInspectionReportService remoteEhrCcrInspectionReportService;
    private final RemoteReportRegularRecordService reportRegularRecordService;
    private final RemoteMedicalReportService remoteMedicalReportService;
    private final SignatureKeyService signatureKeyService;

    /**
     * @Param entity
     * @Return void
     * @Description 体检
     * <AUTHOR>
     * @Date 2024/7/15 14:38
     **/
    @Async
    @EventListener
    public void medicalEvent(EHomeMedicalEventEntity entity) {
        log.info("e家医护EHOME开始，DeviceNo：{}，hospitalId：{}", entity.getDeviceNo(), entity.getHospitalId());
        BusEhrCheckRecord params = entity.getParams();
        String deviceNo = entity.getDeviceNo();
        Long hospitalId = entity.getHospitalId();
        List<BusEhrCheckData> checkDataList = entity.getCheckDataList();
        try {
            dataConversion(params, checkDataList, deviceNo, hospitalId);
        } catch (Exception e) {
            log.error("EHOME 异常 medicalEvent :", e);
        }

    }

    /**
     * @Param record
     * @Param checkDataList
     * @Param deviceNo
     * @Param hospitalId
     * @Return void
     * @Description 数据转换并推送
     * <AUTHOR>
     * @Date 2024/7/12 15:26
     **/
    @Transactional
    public void dataConversion(BusEhrCheckRecord record, List<BusEhrCheckData> checkDataList, String deviceNo, Long hospitalId) {
        //分批的数据
        ExamReportInfoDTO examReportInfoDTO = buildExamReportInfoDTO(record, checkDataList, deviceNo, hospitalId);

        Map<String, Object> headerMap = new HashMap<>();

        SignatureKeyEntity entity = signatureKeyService.getEntity(hospitalId);
        if (ObjectUtil.isNull(entity)) {
            throw new ServiceException("未找到医院签名信息");
        }
        String appid = entity.getAppId();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String appSecret = entity.getAppSecret();
        String body = JSON.toJSONString(examReportInfoDTO);
        String plain = appid + appSecret + timestamp + body;
        // 签名
        String hexString = SHA256Util.toHexString(SHA256Util.getSHA(plain));
        headerMap.put("X-App-Id", appid);
        headerMap.put("X-Timestamp", timestamp);
        headerMap.put("X-Payload-Signature", hexString);


        AjaxResult<Boolean> upload = remoteMedicalReportService.upload(body, headerMap);


        log.info("EHOME upload :msg{},datta{}", upload.getMsg(), upload.getData());
    }

    /**
     * @Param params
     * @Param deviceNo
     * @Param hospitalId
     * @Return ExamReportInfoDTO
     * @Description 生成体检报告
     * <AUTHOR>
     * @Date 2024/7/4 14:57
     **/
    private ExamReportInfoDTO buildExamReportInfoDTO(BusEhrCheckRecord record, List<BusEhrCheckData> checkDataList, String deviceNo, Long hospitalId) {

        String idCardNo = record.getIdCard();
        Date measureTime = record.getMeasureTime();
        if (measureTime == null) {
            measureTime = record.getCreateTime();
        }
        long l = System.currentTimeMillis();

        ExamReportInfoDTO infoDTO = new ExamReportInfoDTO();
        //机构ID
        infoDTO.setOrgId(Long.parseLong(CheckOrganizationEnum.EHOME.getCode()));
        infoDTO.setHospitalId(hospitalId);
        //检测 ID
        infoDTO.setExamId(idCardNo + l);
        //设备编号
        infoDTO.setDeviceId(deviceNo);
        //检测日期,体检报告出具时间,格式 ：YYYY-MM-DD,示例 ：2023-06-01
        infoDTO.setExamDate(DateUtils.parse_YYYY_MM_DD_HH_MM_SS(measureTime));
        //体检报告 ID
        infoDTO.setReportId(idCardNo + l);
        //体检报告版本号，例如首次上传的报告为1，下次如有更新，则必须+1 （跟bizId关联）
        infoDTO.setReportVer(1L);
        //体检报告日期，格 式 ： YYYY-MM-DD,示例 ：2023-06-01
        infoDTO.setReportDate(DateUtils.dateTime(measureTime));
        infoDTO.setDataSource(Integer.parseInt(CheckOrganizationEnum.EHOME.getCode()));
        infoDTO.setDataSourceId(record.getId());

        //生成体检报告
        //生成体检报告
        R<String> r = remoteEhrCcrInspectionReportService.downloadReport(record.getId(), Integer.parseInt(CheckOrganizationEnum.EHOME.getCode()));
        String msg = "";
        if (r.getCode() == 200) {
            msg = r.getMsg();
        }
        infoDTO.setReportFile(msg);


        List<BusEhrCheck> busEhrChecks = packReportDetail(checkDataList);
        RegularExamDTO regularExamDTO = buildRegularExamDTO(busEhrChecks);
        ExamPatientInfoDTO examPatientInfoDTO = buildExamPatientInfoDTO(record);
        ExamConclusionDTO examConclusionDTO = buildExamConclusionDTO(busEhrChecks);
        InspectSummaryDTO[] inspectSummaryDTOS = buildInspectSummaryDTO(busEhrChecks);

        infoDTO.setRegularExam(regularExamDTO);
        infoDTO.setPatientInfo(examPatientInfoDTO);
        infoDTO.setConclusions(examConclusionDTO);
        infoDTO.setInspectSummaries(inspectSummaryDTOS);

        return infoDTO;
    }

    /**
     * @Param checks
     * @Return RegularExamDTO
     * @Description 常规检查
     * <AUTHOR>
     * @Date 2024/7/2 14:57
     **/
    private RegularExamDTO buildRegularExamDTO(List<BusEhrCheck> checks) {
        RegularExamDTO dto = new RegularExamDTO();
        Map<String, BusEhrCheckItem> checkMap = getCheckMap(checks);
        dto.setTall(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.HG.getKey())));
        dto.setWeight(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.WG.getKey())));
        dto.setBmi(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BMI.getKey())));
        dto.setSystolic(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BP_002.getKey())));
        dto.setDiastolic(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BP_001.getKey())));


        //血糖
        BusEhrCheckItem bsValue = getBsValue(checkMap);
        if (ObjectUtil.isNotNull(bsValue)) {
            BloodSugarDTO sugarDTO = new BloodSugarDTO();
            sugarDTO.setValue(NumberUtil.parseFloat(bsValue.getCheckValue()));
            sugarDTO.setTime(bsValue.getCheckTime());
            AdapterCheckItem itemType = bsValue.getItemType();
            int bsType = itemType.equals(AdapterCheckItem.BG_001) ? 2 : itemType.equals(AdapterCheckItem.BG_002) ? 3 : 1;
            sugarDTO.setType(bsType);
            dto.setBloodSugarDTO(ArrayUtils.toArray(sugarDTO));
        }

        //血氧
        dto.setBloodOxygen(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BO.getKey())));
        //体温
        dto.setBodyTemperature(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.TC.getKey())));
        //血脂
        dto.setTotalCholesterol(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BF_004.getKey())));
        dto.setTriglycerides(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BF_003.getKey())));
        dto.setHdl(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BF_002.getKey())));
        dto.setLdl(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.BF_001.getKey())));
        //尿酸
        dto.setUricAcid(NumberUtil.parseFloat(getValue(checkMap, AdapterCheckItem.UA.getKey())));
        //脉搏

        //心率
        int bo = NumberUtil.parseInt(getValue(checkMap, AdapterCheckItem.BO_001.getKey()));
        //心率
        dto.setPulseRate(bo);

        //脉搏
        int i = NumberUtil.parseInt(getValue(checkMap, AdapterCheckItem.RHR.getKey()));
        //脉搏
        dto.setHeartRate(i);

        return dto;
    }

    /**
     * @Param params
     * @Return ExamPatientInfoDTO
     * @Description 患者基本信息
     * <AUTHOR>
     * @Date 2024/7/2 14:16
     **/
    private ExamPatientInfoDTO buildExamPatientInfoDTO(BusEhrCheckRecord record) {

        ExamPatientInfoDTO dto = new ExamPatientInfoDTO();
        //患者姓名
        dto.setName(record.getFullName());
        String idCard = record.getIdCard();
        //身份证号
        dto.setIdCard(idCard);
        Integer sex = IdcardUtil.getGenderByIdCard(idCard);
        //性别
        dto.setGender(sex == 1 ? GenderEnum.MALE : sex == 2 ? GenderEnum.FEMALE : GenderEnum.UNKNOWN);
        //年龄
        dto.setAge(IdcardUtil.getAgeByIdCard(idCard));
        //电话
        dto.setPhoneNum(record.getPhone());
        return dto;
    }

    private final String NO_ABNORMAL = "未见明显异常";

    /**
     * @Param params
     * @Return ExamConclusionDTO
     * @Description 总检建议
     * <AUTHOR>
     * @Date 2024/7/2 14:16
     **/
    private ExamConclusionDTO buildExamConclusionDTO(List<BusEhrCheck> busEhrChecks) {

        List<ExamConclusionItemDTO> errorList = new ArrayList<>();
        List<String> suggestList = new ArrayList<>();
        busEhrChecks.stream().forEach(item -> {
            for (BusEhrCheckItem checkItem : item.getCheckItems()) {
                AdapterCheckItem itemType = checkItem.getItemType();
                String conclusion = checkItem.getConclusion();
                ExamConclusionItemDTO itemDTO = new ExamConclusionItemDTO();
                itemDTO.setName(itemType.getInfo());
                if (CharSequenceUtil.isEmpty(conclusion)) {
                    conclusion = NO_ABNORMAL;
                }
                itemDTO.setDesc(conclusion);
                errorList.add(itemDTO);
                String suggestion = checkItem.getSuggestion();
                if (CharSequenceUtil.isNotEmpty(suggestion)) {
                    suggestList.add(suggestion);
                }

            }
        });

        ExamConclusionDTO examConclusionDTO = new ExamConclusionDTO();
        examConclusionDTO.setItems(errorList.toArray(new ExamConclusionItemDTO[errorList.size()]));
        examConclusionDTO.setSuggest(suggestList.toArray(new String[suggestList.size()]));

        return examConclusionDTO;
    }

    /**
     * @Param params
     * @Return InspectSummaryDTO
     * @Description 检验综合-数值类
     * <AUTHOR>
     * @Date 2024/7/2 14:16
     **/
    private InspectSummaryDTO[] buildInspectSummaryDTO(List<BusEhrCheck> busEhrChecks) {

        List<InspectSummaryDTO> list = new ArrayList<>(busEhrChecks.size() * 2);

        for (BusEhrCheck item : busEhrChecks) {
            InspectSummaryDTO dto = new InspectSummaryDTO();
            dto.setSubject(item.getCode());
            dto.setExamDate(DateUtils.dateTime(new Date()));
            dto.setSummary(item.getExceptionProposal());

            List<BusEhrCheckItem> checkItems = item.getCheckItems();
            if (checkItems != null && checkItems.size() > 0) {
                InspectItemDTO[] items = new InspectItemDTO[checkItems.size()];
                for (int i = 0; i < checkItems.size(); i++) {
                    BusEhrCheckItem checkItem = checkItems.get(i);
                    InspectItemDTO itemDTO = new InspectItemDTO();
                    AdapterCheckItem itemType = checkItem.getItemType();
                    itemDTO.setName(itemType.getInfo());

                    String checkValue = checkItem.getCheckValue();
                    if (NumberUtil.isNumber(checkValue)) {
                        itemDTO.setResult(NumberUtil.parseDouble(checkValue));
                    }
                    itemDTO.setResultStr(checkValue);
                    itemDTO.setUnit(itemType.getUnit());
                    itemDTO.setAbnormal(checkItem.getIsHealth() == 0 ? UniversalAbnormalEnum.OTHER_ABNORMALITIES : UniversalAbnormalEnum.NORMAL);
                    items[i] = itemDTO;
                }
                dto.setItems(items);
            }
            list.add(dto);
        }

        return ArrayUtil.toArray(list, InspectSummaryDTO.class);
    }

    /**
     * @Param checkMap
     * @Param code
     * @Return String
     * @Description 获取属性
     * <AUTHOR>
     * @Date 2024/7/2 17:28
     **/
    private String getValue(Map<String, BusEhrCheckItem> checkMap, String code) {
        BusEhrCheckItem item = checkMap.get(code);
        if (ObjectUtil.isNull(item)) {
            return null;
        }
        return item.getCheckValue();
    }

    /**
     * @Param checkMap 包含血糖检查项的Map
     * @Return 最新的血糖检查项，如果没有则返回null
     * @Description 获取最新的血糖检查项
     * <AUTHOR>
     * @Date 2024/8/28 12:32
     **/
    private BusEhrCheckItem getBsValue(Map<String, BusEhrCheckItem> checkMap) {
        String BG001Key = AdapterCheckItem.BG_001.getKey();
        String BG002Key = AdapterCheckItem.BG_002.getKey();
        String BG003Key = AdapterCheckItem.BG_003.getKey();

        List<BusEhrCheckItem> items = new ArrayList<>();
        addItemIfNotNull(items, checkMap.get(BG001Key));
        addItemIfNotNull(items, checkMap.get(BG002Key));
        addItemIfNotNull(items, checkMap.get(BG003Key));

        if (items.isEmpty()) {
            return null; // 或者返回一个默认值，取决于你的需求
        }

        return items.stream()
                .max(Comparator.comparing(BusEhrCheckItem::getCheckTime))
                .orElse(null);
    }

    /**
     * @Param items
     * @Param item
     * @Return void
     * @Description
     * <AUTHOR>
     * @Date 2024/8/28 12:32
     **/
    private void addItemIfNotNull(List<BusEhrCheckItem> items, BusEhrCheckItem item) {
        if (ObjectUtil.isNotNull(item)) {
            items.add(item);
        }
    }


    /**
     * @Param checks
     * @Return Map<String, BusEhrCheckItem>
     * @Description 转换成 Map
     * <AUTHOR>
     * @Date 2024/7/2 16:32
     **/
    private Map<String, BusEhrCheckItem> getCheckMap(List<BusEhrCheck> checks) {
        Map<String, BusEhrCheckItem> map = new HashMap<>();
        for (BusEhrCheck check : checks) {
            List<BusEhrCheckItem> checkItems = check.getCheckItems();
            for (BusEhrCheckItem checkItem : checkItems) {
                map.put(checkItem.getSmallCode(), checkItem);
            }
        }
        return map;
    }


    /**
     * @Param suggests
     * @Param idCard
     * @Return List<BusEhrCheck>
     * @Description 数据处理
     * <AUTHOR>
     * @Date 2024/7/2 14:58
     **/
    public List<BusEhrCheck> packReportDetail(List<BusEhrCheckData> checkData) {
        Map<String, BusEhrCheck> map = new HashMap<>();

        Map<String, List<BusEhrCheckData>> group = checkData.stream().collect(Collectors.groupingBy(BusEhrCheckData::getCheckCode));
        for (Map.Entry<String, List<BusEhrCheckData>> entry : group.entrySet()) {
            for (BusEhrCheckData data : entry.getValue()) {
                if (data.getCheckItemCode().equals(EhrCheckItemEnum.ECG_002.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BF_005.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_003.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_006.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_008.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_016.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_018.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_019.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_020.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_021.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_022.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_023.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_024.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_025.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_027.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_029.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_030.getKey())
                        || data.getCheckItemCode().equals(EhrCheckItemEnum.BCP_031.getKey())) {
                    continue;
                }

                //获取枚举
                AdapterCheckItem checkItem = AdapterCheckItem.getItem(data.getCheckItemCode());
                BusEhrCheck busEhrCheck = map.get(checkItem.getBigKey());
                if (busEhrCheck == null) {
                    busEhrCheck = new BusEhrCheck(checkItem.getBigKey());
                    busEhrCheck.setCode(checkItem.getBigName());
                    map.put(checkItem.getBigKey(), busEhrCheck);
                }
                List<BusEhrCheckItem> itemList = busEhrCheck.getCheckItems();
                if (CollUtil.isEmpty(itemList)) {
                    itemList = new ArrayList<>();
                    busEhrCheck.setCheckItems(itemList);
                }

                BusEhrCheckItem item = new BusEhrCheckItem(data.getCheckItemCode());
                item.setItemType(checkItem);
                item.setItemName(checkItem.getInfo());

                item.setIcon(checkItem.getIcon());
                item.setCheckTime(data.getCheckTime());
                item.setCheckValue(data.getCheckValue());
                item.setCheckResult(data.getCheckResult());
                item.setUnit(data.getUnit());
                item.setIsHealth(data.getIsHealth());
                item.setReferenceScope(data.getReferenceRange());
                // 结论
                if (YesNoEnum.NO.getCode().equals(data.getIsHealth())) {
                    item.setConclusion(item.getItemName() + item.getCheckResult() +
                            " " + item.getCheckValue() + item.getUnit());
                    //写入建议
                    item.setSuggestion(data.getSuggest());
                    String proposal = busEhrCheck.getExceptionProposal();

                    StrBuilder builder = new StrBuilder(proposal);
                    if (CharSequenceUtil.isEmpty(proposal)) {
                        builder.append(StrPool.COMMA);
                    }
                    builder.append(item.getConclusion()).append(item.getSuggestion());
                }
                itemList.add(item);
            }
        }

        return map.values().stream().collect(Collectors.toList());
    }
}
