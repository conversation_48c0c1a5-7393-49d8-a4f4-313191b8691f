package com.puree.hospital.business.domain.vo;

import com.puree.hospital.business.domain.BusOfficinaPharmacist;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 订单发货vo
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/22 16:46
 */
@Data
public class OrderSendOutResultVO implements Serializable {

    private static final long serialVersionUID = 7547369204785636798L;

    /**
     * 发货药品信息
     */
    private BusOfficinaPharmacist pharmacist;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 总订单号
     */
    private String totalOrderNo;

    /**
     * 是否需要上传追溯码
     */
    private Boolean needUploadTraceCode = Boolean.FALSE;

}
