package com.puree.hospital.business.service.refund.impl;

import com.puree.hospital.app.api.RemoteWxPayService;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.service.refund.IRefundService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.PayWayConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/***
 *@title WeChatRefundServiceImpl
 *@description
 *<AUTHOR>
 *@version 1.0
 *@create 2024/12/16 11:37
 */
@Service(PayWayConstant.WECHAT_PAY + IRefundService.REFUND_SERVICE)
@Slf4j
public class WeChatRefundServiceImpl implements IRefundService {
    @Resource
    private RemoteWxPayService remoteWxPayService;

    @Override
    public void refund(BusOrder busOrder, BigDecimal refundAmount, OrderTypeEnum orderTypeEnum) {
        String pCode = busOrder.getPartnersCode();
        R refundOrder = remoteWxPayService.refundOrder(busOrder.getOrderNo(), orderTypeEnum.getCode(), refundAmount.doubleValue(), "2", "",
                StringUtils.isNotEmpty(pCode) ? CodeEnum.YES.getCode() : null, null);
        if (Constants.FAIL.equals(refundOrder.getCode())) {
            log.error("微信支付退款失败 refundOrder={}", refundOrder);
            throw new ServiceException("调用微信支付退款失败");
        }
        log.info("微信支付退款成功 refundOrderNo={}", busOrder.getOrderNo());
    }
}
