package com.puree.hospital.business.service;

import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.dto.BusDrugsOrderDto;
import com.puree.hospital.business.domain.dto.DXRefundCallDto;
import com.puree.hospital.business.domain.dto.DeliveryDTO;
import com.puree.hospital.business.domain.dto.ZXRefundCallDto;
import com.puree.hospital.business.domain.vo.BusDrugsOrderDetailVo;
import com.puree.hospital.business.domain.vo.BusDrugsOrderVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface IBusDrugsOrderService {
    List<BusDrugsOrderVo> selectListPrescriptionVo(BusDrugsOrderDto dto);
    List<BusDrugsOrderVo> selectListBySearch(BusDrugsOrderDto dto);
    BusDrugsOrderDetailVo selectDetail(BusDrugsOrderDto dto);
    int delivery(DeliveryDTO dto);
    int regenerateOrder(BusDrugsOrder drugsOrder);
    List<String> selectAfterSaleList(BusDrugsOrderDto dto);
    int updateStatus(String orderNo);
    int updateStatusByPrescriptionNumber(DXRefundCallDto dxRefundCallDto);
    int updateOrderState(DXRefundCallDto dxRefundCallDto);
    void updateOrderStatus(List<String> orderNos);
    int updateZXOrderState(ZXRefundCallDto zxRefundCallDto);
    /**
     * 导出药品订单Excel
     * @param response
     * @throws IOException
     */
    void exportExcel(HttpServletResponse response, BusDrugsOrderDto busDrugsOrderDto);

    /**
     * 释放药品库存
     * @param drugsOrder
     */
    void releaseStock(BusDrugsOrder drugsOrder);

    BusDrugsOrder queryDrugsOrderByNo(String orderNo);

    /**
     * 根据子订单号查询药品订单列表
     * @param subOrderList 子订单号列表
     * @return 药品订单列表
     */
    List<BusDrugsOrderDetailVo> selectDrugOrderListWithDetail(List<String> subOrderList, Long hospitalId);
}
