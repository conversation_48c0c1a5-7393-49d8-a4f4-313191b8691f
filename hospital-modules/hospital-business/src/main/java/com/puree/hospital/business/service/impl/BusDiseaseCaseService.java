package com.puree.hospital.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.business.domain.BusConsultationOrder;
import com.puree.hospital.business.domain.BusEhrCaseDiagnosisHistory;
import com.puree.hospital.business.domain.BusHospital;
import com.puree.hospital.business.domain.BusHospitalPa;
import com.puree.hospital.business.domain.BusHospitalPaDrugs;
import com.puree.hospital.business.domain.BusHospitalPreorderDoctor;
import com.puree.hospital.business.domain.BusHospitalWechatConfig;
import com.puree.hospital.business.domain.BusPatientFamily;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusSignature;
import com.puree.hospital.business.domain.DiseaseCaseDO;
import com.puree.hospital.business.domain.query.DiseaseCaseQuery;
import com.puree.hospital.business.domain.vo.BusHospitalPaDrugsVo;
import com.puree.hospital.business.domain.vo.BusShanDeMedicalHistoryVo;
import com.puree.hospital.business.domain.vo.DiseaseCaseVO;
import com.puree.hospital.business.domain.vo.DrugsVo;
import com.puree.hospital.business.mapper.BusConsultationOrderMapper;
import com.puree.hospital.business.mapper.BusEhrCaseDiagnosisHistoryMapper;
import com.puree.hospital.business.mapper.BusHospitalMapper;
import com.puree.hospital.business.mapper.BusHospitalPaDrugsMapper;
import com.puree.hospital.business.mapper.BusHospitalPaMapper;
import com.puree.hospital.business.mapper.BusHospitalWechatConfigMapper;
import com.puree.hospital.business.mapper.BusPatientFamilyMapper;
import com.puree.hospital.business.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.mapper.BusSignatureMapper;
import com.puree.hospital.business.mapper.BusThymeleafTemplatesMapper;
import com.puree.hospital.business.service.IBusHospitalPreorderDoctorService;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.enums.ConsultationOrderStatusEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.ProcessingMethodEnum;
import com.puree.hospital.common.core.enums.SexTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName: BusDiseaseCaseService
 * @Date 2024/3/21 15:27
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Service
@RefreshScope
public class BusDiseaseCaseService {

    @Value("${aliyun.oss.fileAddressPrefix}")
    private String ossUrl;

    @Autowired
    private BusThymeleafTemplatesMapper busThymeleafTemplatesMapper;
    @Autowired
    private BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    @Autowired
    private IBusHospitalPreorderDoctorService iBusHospitalPreorderDoctorService;
    @Autowired
    private BusSignatureMapper busSignatureMapper;
    @Autowired
    private BusHospitalWechatConfigMapper hospitalWechatConfigMapper ;
    @Autowired
    private BusPrescriptionMapper prescriptionMapper ;
    @Autowired
    private BusEhrCaseDiagnosisHistoryMapper ehrCaseDiagnosisHistoryMapper ;
    @Autowired
    private BusConsultationOrderMapper consultationOrderMapper ;
    @Autowired
    private BusPatientFamilyMapper busPatientFamilyMapper ;
    @Autowired
    private BusHospitalPaDrugsMapper hospitalPaDrugsMapper ;
    @Autowired
    private BusHospitalPaMapper hospitalPaMapper ;
    @Autowired
    private BusHospitalMapper busHospitalMapper;


    /**
     * 医院-新病例
     *
     */
    public DiseaseCaseVO getDiseaseCaseInfo(String orderNo) {

        BusConsultationOrder consultationOrder = consultationOrderMapper.selectOne(new LambdaQueryWrapper<BusConsultationOrder>().eq(BusConsultationOrder::getOrderNo, orderNo)) ;
        if (null==consultationOrder) {
            throw new ServiceException("没有该问诊订单 : " + orderNo) ;
        }

        Boolean isComplete = false ;

        // 0:图文
        if ("0".equals(consultationOrder.getOrderType())) {
            isComplete = ConsultationOrderStatusEnum.COMPLETE.getCode().equals(consultationOrder.getStatus()) ;
        }

        // 1:视频
        if ("1".equals(consultationOrder.getOrderType())) {
            isComplete = ConsultationOrderStatusEnum.COMPLETE.getCode().equals(consultationOrder.getVideoStatus()) ;
        }

        if (!isComplete) {
            return null ;
        }

        DiseaseCaseVO diseaseCaseVO = new DiseaseCaseVO();

        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusPrescription::getConsultationOrderId,consultationOrder.getId());
        // 2-完成 4-使用中
        queryWrapper.in(BusPrescription::getStatus, "2", "4");
        List<BusPrescription> prescriptionList = prescriptionMapper.selectList(queryWrapper);

        BusEhrCaseDiagnosisHistory ehrCaseDiagnosisHistory = ehrCaseDiagnosisHistoryMapper.selectOne(new LambdaQueryWrapper<BusEhrCaseDiagnosisHistory>()
                .eq(BusEhrCaseDiagnosisHistory::getConsultationId, consultationOrder.getId())
                .orderByAsc(BusEhrCaseDiagnosisHistory::getCreateTime).last("limit 1"));

        if (null==ehrCaseDiagnosisHistory) {
            ehrCaseDiagnosisHistory = this.syncEhrInfo(consultationOrder, prescriptionList) ;
        }

        BusShanDeMedicalHistoryVo oldDiseaseCaseInfo = this.findBasicCaseInfo(consultationOrder.getId(), consultationOrder.getHospitalId());
        if (null!=oldDiseaseCaseInfo) {
            BeanUtils.copyProperties(oldDiseaseCaseInfo, diseaseCaseVO);
        }

        BusHospital busHospital = busHospitalMapper.selectById(consultationOrder.getHospitalId());
        if (null!=busHospital) {
            diseaseCaseVO.setHospitalName(busHospital.getHospitalName());
        }

        diseaseCaseVO.setChiefComplaint(ehrCaseDiagnosisHistory.getChiefComplaint());
        diseaseCaseVO.setHistoryOfPresentIllness(ehrCaseDiagnosisHistory.getHistoryOfPresentIllness());
        diseaseCaseVO.setPastHistory(ehrCaseDiagnosisHistory.getPastHistory());
        diseaseCaseVO.setAllergicDrugs(ehrCaseDiagnosisHistory.getAllergicDrugs());
        diseaseCaseVO.setDiagnosis(ehrCaseDiagnosisHistory.getDiagnosis());
        diseaseCaseVO.setAdvice(ehrCaseDiagnosisHistory.getAdvice());
        diseaseCaseVO.setManagement(ehrCaseDiagnosisHistory.getManagement());
        diseaseCaseVO.setTcmDiagnosis(ehrCaseDiagnosisHistory.getTcmDiagnosis());
        diseaseCaseVO.setCustomFields(ehrCaseDiagnosisHistory.getCustomFields());
        return diseaseCaseVO;

    }

    /**
     * 获取CA签名
     * @param vo
     * @param hospitalId
     * @return
     */
    private String getCASign(BusShanDeMedicalHistoryVo vo,Long hospitalId){
        String sign="";
        Long doctorId=null;
        if(StringUtils.isNotNull(vo.getExpertId())){
            BusHospitalPreorderDoctor preorderDoctor = iBusHospitalPreorderDoctorService.getByhospitalId(hospitalId);
            doctorId= Objects.isNull(preorderDoctor)?null:preorderDoctor.getDoctorId();
        }else{
            doctorId=vo.getDoctorId();
        }
        //开方医生签名
        QueryWrapper queryWrapper = new QueryWrapper<BusSignature>();
        queryWrapper.eq("object_id", doctorId);
        queryWrapper.eq("object_type", 0);
        BusSignature result = busSignatureMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(result)) {
            sign=result.getCertSignature();
        }
        return sign;
    }

    private BusShanDeMedicalHistoryVo findBasicCaseInfo(Long id, Long hospitalId) {
        BusShanDeMedicalHistoryVo templates = busThymeleafTemplatesMapper.findBasicCaseInfo(id, hospitalId);
        if ( null==templates ) {
            return templates ;
        }

        BusHospitalWechatConfig hospitalWechatConfig = hospitalWechatConfigMapper.selectOne(new LambdaQueryWrapper<BusHospitalWechatConfig>()
                .eq(BusHospitalWechatConfig::getHospitalId, hospitalId).eq(BusHospitalWechatConfig::getAppType, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType()));
        if (StrUtil.isNotBlank(hospitalWechatConfig.getLogo())) {
            templates.setPath(ossUrl+hospitalWechatConfig.getLogo());
        }
        if (StrUtil.isNotBlank(hospitalWechatConfig.getQrCodeUrl())) {
            templates.setQrCodeUrl(ossUrl+hospitalWechatConfig.getQrCodeUrl());
        }

        if (SexTypeEnum.MAN.getCode().equals(templates.getFamilySex())){
            templates.setFamilySex("男");
        }else if(SexTypeEnum.WOMAN.getCode().equals(templates.getFamilySex())){
            templates.setFamilySex("女");
        }

        //获取签名
        String caSign = getCASign(templates, hospitalId);
        if (StrUtil.isNotBlank(caSign)) {
            templates.setCertSignature(ossUrl + caSign);
        }

        String hospitalCode = busThymeleafTemplatesMapper.selcetHospitalCode(hospitalId);
        templates.setHospitalCode(hospitalCode);

        if (StrUtil.isNotBlank(templates.getDiagnosticsTime1())) {
            templates.setDiagnosticsTime(templates.getDiagnosticsTime1()) ;
        }
        if (StrUtil.isNotBlank(templates.getDiagnosticsTime2())) {
            templates.setDiagnosticsTime(templates.getDiagnosticsTime2()) ;
        }
        if (StrUtil.isNotBlank(templates.getDiagnosticsTime())) {
            String yearStr = templates.getDiagnosticsTime().substring(0, 4) + "年" ;
            String monthStr = templates.getDiagnosticsTime().substring(5, 7) + "月" ;
            String dayStr = templates.getDiagnosticsTime().substring(8, 10) + "日" ;
            templates.setDiagnosticsTime(yearStr + monthStr + dayStr);
        }

        templates.setLocation( null==templates.getLocation() ?  "" : templates.getLocation() );
        templates.setDetailAddress( null==templates.getDetailAddress() ? "" : templates.getDetailAddress() );
        templates.setDetailAddress(templates.getLocation() + templates.getDetailAddress());

        // 优先级 ：check time > payment time > create time
        if (StrUtil.isNotBlank(templates.getCreateTime())) {
            templates.setConsultationTime(templates.getCreateTime());
        }
        if (StrUtil.isNotBlank(templates.getPaymentTime())) {
            templates.setConsultationTime(templates.getPaymentTime());
        }
        if (StrUtil.isNotBlank(templates.getCheckTime())) {
            templates.setConsultationTime(templates.getCheckTime());
        }

        return templates ;

    }

    public BusEhrCaseDiagnosisHistory syncEhrInfo(BusConsultationOrder consultationOrder, List<BusPrescription> prescriptionList) {

        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(consultationOrder.getFamilyId());

        BusEhrCaseDiagnosisHistory ehrCaseDiagnosisHistory = new BusEhrCaseDiagnosisHistory() ;
        ehrCaseDiagnosisHistory.setConsultationId(consultationOrder.getId());
        ehrCaseDiagnosisHistory.setFullName(consultationOrder.getFamilyName());
        ehrCaseDiagnosisHistory.setFamilyId(consultationOrder.getFamilyId());
        ehrCaseDiagnosisHistory.setAge(consultationOrder.getFamilyAge());
        ehrCaseDiagnosisHistory.setConsultationTime(consultationOrder.getCreateTime());
        ehrCaseDiagnosisHistory.setIdCardNo(busPatientFamily.getIdNumber());
        ehrCaseDiagnosisHistory.setDoctorId(consultationOrder.getDoctorId());
        ehrCaseDiagnosisHistory.setHospitalId(busPatientFamily.getHospitalId());
        ehrCaseDiagnosisHistory.setSex(Integer.valueOf(consultationOrder.getFamilySex()));
        ehrCaseDiagnosisHistory.setFamilyPhone(busPatientFamily.getCellPhoneNumber());
        ehrCaseDiagnosisHistory.setSource(YesNoEnum.NO.getCode());

        // 医嘱, 包含所有处方
        List<String> adviceList = new ArrayList<>() ;

        // 处置， 里面是所有处方的药品信息
        List<String> managementList = new ArrayList<>() ;

        // 诊断, 包含所有处方
        List<String> diagnosisList = new ArrayList<>() ;

        List<String> tcmDiagnosisList = new ArrayList<>() ;

        this.handlePrescriptionInfo(consultationOrder.getHospitalId(),prescriptionList, adviceList, managementList, diagnosisList, tcmDiagnosisList ) ;

        StringBuilder adviceStringBuilder = new StringBuilder();
        for ( int i=0 ; i<adviceList.size() ; i++ ) {
            adviceStringBuilder.append(adviceList.get(i)).append("\n") ;
        }
        ehrCaseDiagnosisHistory.setAdvice(adviceStringBuilder.toString());

        StringBuilder managementStringBuilder = new StringBuilder();
        for ( int i=0 ; i<managementList.size() ; i++ ) {
            managementStringBuilder.append(managementList.get(i)).append("\n") ;
        }
        ehrCaseDiagnosisHistory.setManagement(managementStringBuilder.toString());

        StringBuilder diagnosisStringBuilder = new StringBuilder();
        for ( int i=0 ; i<diagnosisList.size() ; i++ ) {
            diagnosisStringBuilder.append(diagnosisList.get(i)).append("\n") ;
        }
        ehrCaseDiagnosisHistory.setDiagnosis(diagnosisStringBuilder.toString());

        StringBuilder tcmDiagnosisStringBuilder = new StringBuilder();
        for ( int i=0 ; i<tcmDiagnosisList.size() ; i++ ) {
            tcmDiagnosisStringBuilder.append(tcmDiagnosisList.get(i)).append("\n") ;
        }
        ehrCaseDiagnosisHistory.setTcmDiagnosis(tcmDiagnosisStringBuilder.toString());

        // insert
        ehrCaseDiagnosisHistoryMapper.insert(ehrCaseDiagnosisHistory) ;

        return ehrCaseDiagnosisHistory;

    }

    public void handlePrescriptionInfo( Long hospitalId, List<BusPrescription> prescriptionList, List<String> adviceList, List<String> managementList, List<String> diagnosisList, List<String> tcmDiagnosisList ) {

        if ( null==prescriptionList || prescriptionList.isEmpty() ) {
            return ;
        }

        for (BusPrescription busPrescription : prescriptionList) {

            // 医嘱
            if (StrUtil.isNotBlank(busPrescription.getRemark())) {
                adviceList.add(busPrescription.getRemark()) ;
            }

            List<DrugsVo> drugsVos = this.findPrescriptionDrugInfo(hospitalId, busPrescription);

            // 中药
            if (PrescriptionTypeEnum.isTcmOrZyxdf(busPrescription.getPrescriptionType())){

                this.handleChinaDrug(busPrescription, drugsVos, managementList, tcmDiagnosisList);

            }

            // 西药
            if(PrescriptionTypeEnum.isMm(busPrescription.getPrescriptionType())){

                StringBuilder stringBuilder = new StringBuilder();

                for (int i = 0; i < drugsVos.size(); i++) {
                    DrugsVo vo = drugsVos.get(i);
                    stringBuilder.append(vo.getStandardCommonName()+"，\n规格："+vo.getDrugsSpecification()+"×"+vo.getQuantity()+"\n用法用量："+vo.getDrugsUsageValue()+"，"+vo.getMedicationFrequencyRemarks()+"，"+vo.getSingleDose()+vo.getUnit()+"。\n");
                }

                // 处置
                managementList.add(stringBuilder.toString()) ;

                String clinicalDiagnosis = busPrescription.getClinicalDiagnosis();
                JSONArray objects = JSONObject.parseArray(clinicalDiagnosis);

                String diagnosis = "";
                for (int i = 0; i < objects.size(); i++){
                    JSONObject jsonObject = objects.getJSONObject(i);
                    String diseaseName = jsonObject.getString("diseaseName");
                    if(i == objects.size()-1){
                        diagnosis = diagnosis+ diseaseName;
                    }else{
                        diagnosis = diagnosis+ diseaseName+"\n";
                    }
                }
                // 诊断
                diagnosisList.add(diagnosis) ;

            }

        }

    }

    public List<DrugsVo> findPrescriptionDrugInfo(Long hospitalId, BusPrescription busPrescription) {

        List<DrugsVo> drugsVos;
        if (!PrescriptionTypeEnum.isZyxdf(busPrescription.getPrescriptionType())){
            drugsVos = busPrescriptionDrugsMapper.selectTemplateDrugsInfo(hospitalId, busPrescription.getId());
        } else {
            BusHospitalPaDrugs busHospitalPaDrugs = new BusHospitalPaDrugs();
            busHospitalPaDrugs.setHospitalId(busPrescription.getHospitalId());
            busHospitalPaDrugs.setPaId(busPrescription.getPaId());
            List<BusHospitalPaDrugsVo> busHospitalPaDrugsVos = hospitalPaDrugsMapper.selectPaDrugsListV2(busHospitalPaDrugs);
            drugsVos = new ArrayList<>();
            busHospitalPaDrugsVos.forEach(item ->{
                DrugsVo drugsVo = new DrugsVo();
                drugsVo.setDrugsName(item.getDrugsName());
                drugsVo.setDrugsSpecification(item.getDrugsSpecification());
                drugsVo.setWeight(item.getWeight());
                drugsVo.setSellingPrice(String.valueOf(item.getReferenceSellingPrice()));
                drugsVo.setStandardCommonName(item.getStandardCommonName());
                drugsVos.add(drugsVo);
            });
        }
        return drugsVos ;

    }

    public void handleChinaDrug(BusPrescription busPrescription,  List<DrugsVo> drugsVos , List<String> managementList, List<String> tcmDiagnosisList) {

        StringBuilder stringBuilder = new StringBuilder() ;

        /*中药方剂类型0 协定方 1经典方剂*/
        Integer zyfjType = null;
        if (PrescriptionTypeEnum.isZyxdf(busPrescription.getPrescriptionType())){
            BusHospitalPa busHospitalPa = hospitalPaMapper.selectById(busPrescription.getPaId());

            /*如果是协定方*/
            if (busHospitalPa.getType().equals(YesNoEnum.NO.getCode())){
                stringBuilder.append(busPrescription.getPaName()+"\n");
            }else {
                /*如果是经典方*/
                zyfjType=busHospitalPa.getType();
            }
        }

        if (null != zyfjType || PrescriptionTypeEnum.isTcm(busPrescription.getPrescriptionType())){
            for (int i = 0; i < drugsVos.size(); i++) {
                DrugsVo vo = drugsVos.get(i);
                if ("g".equals(vo.getDrugsSpecification())){
                    if (i  == drugsVos.size()-1){
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getWeight()+vo.getDrugsSpecification()+"。\n");
                    }else{
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getWeight()+vo.getDrugsSpecification()+"，\n");
                    }
                }else{
                    if (i  == drugsVos.size()-1){
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getDrugsSpecification()+"×"+vo.getWeight()+"。\n");
                    }else{
                        stringBuilder.append(vo.getDrugsName()+"："+vo.getDrugsSpecification()+"×"+vo.getWeight()+"，\n");
                    }
                }
            }
        }

        //用法（0内服 1外用）逗号分隔（共几贴，每日几贴，每贴分几次服用）',
        String usages = busPrescription.getUsages();
        if (StrUtil.isNotBlank(usages)) {
            String[] split = usages.split(",");
            String use = "";
            String sumSize = "";
            String daySize = "";
            String timeSize = "";
            for (int i = 0; i < split.length; i++) {
                if ("0".equals(split[i]) && i == 0) {
                    use = "内服";
                } else if ("1".equals(split[i]) && i == 0) {
                    use = "外用";
                } else if (i == 1) {
                    sumSize = "共" + split[i] + "剂";
                } else if (i == 2) {
                    daySize = "每日" + split[i] + "剂";
                } else if (i == 3) {
                    timeSize = "每剂分" + split[i] + "次服用";
                }
            }

            busPrescription.setProcessingMethod(ProcessingMethodEnum.getInfo(busPrescription.getProcessingMethod())) ;

            stringBuilder.append("用法与加工方法："+use + "，" + sumSize + "，" + daySize + "，" + timeSize  + busPrescription.getProcessingMethod()+"。\n");
        }

        /*处置*/
        managementList.add(stringBuilder.toString()) ;


        String tcmSyndrome = "";
        String tcmDiagnosis;
        String diagnosis ="";

        String clinicalDiagnosis = busPrescription.getClinicalDiagnosis();
        JSONArray objects = JSONObject.parseArray(clinicalDiagnosis);

        for (int i = 0; i < objects.size(); i++){
            JSONObject jsonObject = objects.getJSONObject(i);
            tcmDiagnosis = jsonObject.getString("tcmDiagnosis");
            JSONObject obj = jsonObject.getJSONObject("tcmSyndrome");
            if (StringUtils.isNotNull(obj)) {
                tcmSyndrome = "("+obj.getString("tcmSyndrome")+ ")";
            }
            if(i == objects.size()-1){
                diagnosis = diagnosis+ tcmDiagnosis + tcmSyndrome;
            }else{
                diagnosis = diagnosis + tcmDiagnosis + tcmSyndrome+ "\n";
            }
        }

        // 诊断
        if (StrUtil.isNotBlank(diagnosis)) {
            tcmDiagnosisList.add(diagnosis) ;
        }

    }


    public List<DiseaseCaseDO> getDiseaseCaseList(DiseaseCaseQuery query) {
        return ehrCaseDiagnosisHistoryMapper.getDiseaseCaseList(query);
    }
}
