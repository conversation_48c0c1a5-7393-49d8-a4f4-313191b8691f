package com.puree.hospital.business.service.refund;

import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.common.core.enums.OrderTypeEnum;

import java.math.BigDecimal;

/**
 * 退款接口
 */
public interface IRefundService {
    String REFUND_SERVICE = "REFUND_SERVICE";
    /**
     * 退款 - 根据order类型不同，调用退款
     * @param busOrder - 订单
     * @param refundAmount - 退款金额
     * @param orderType - 订单类型
     */
    void refund(BusOrder busOrder, BigDecimal refundAmount, OrderTypeEnum orderType);
}
