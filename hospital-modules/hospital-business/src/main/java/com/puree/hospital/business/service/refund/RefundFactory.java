package com.puree.hospital.business.service.refund;

import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.common.core.constant.PayWayConstant;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/***
 *@title RefundProcessor
 *@description
 *<AUTHOR>
 *@version 1.0
 *@create 2024/12/16 14:17
 */
@Service
@Slf4j
public class RefundFactory {
    /**
     * 退款处理
     * @param busOrder 订单信息 - 必须包含支付类型（PayWay）
     * @param refundAmount - 退款金额
     * @param orderType - 订单类型
     * 备注：医保订单不需要传退款金额和订单类型，退款目前是全额退款
     */
    public void processRefund(BusOrder busOrder, BigDecimal refundAmount, OrderTypeEnum orderType) {
        String payWay = busOrder.getPayWay();
        PaysTypeEnum anEnum = PaysTypeEnum.getEnum(payWay);
        IRefundService refundService = SpringUtils.getBean(anEnum.getConstantName() + IRefundService.REFUND_SERVICE);
        refundService.refund(busOrder, refundAmount, orderType);
        log.info("订单{}{}退款成功", busOrder.getOrderNo(), anEnum.getInfo());
    }
}
