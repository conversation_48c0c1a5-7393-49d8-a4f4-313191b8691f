package com.puree.hospital.business.channel.commission.service.state.impl;

import com.puree.hospital.business.channel.commission.service.state.ChannelOrderAction;
import com.puree.hospital.business.channel.commission.service.state.ChannelOrderContext;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.pay.api.RemotePayOrderService;
import com.puree.hospital.pay.api.constants.BusPayOrderStatusEnum;
import com.puree.hospital.pay.api.model.BusPayOrder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


@Component
public class VerifyPaymentSuccessAction implements ChannelOrderAction<ChannelOrderContext> {

    @Resource
    private RemotePayOrderService remotePayOrderService;
    /**
     * 执行动作
     *
     * @param context 渠道订单上下文对象
     */
    @Override
    public void execute(ChannelOrderContext context) {
        R<BusPayOrder> busPayOrderR = remotePayOrderService.queryPayOrder(context.getOrderNo());
        if (!busPayOrderR.isSuccess() || Objects.isNull(busPayOrderR.getData())) {
            throw new RuntimeException(String.format("查询支付订单失败，订单号：%s", context.getOrderNo()));
        }
        BusPayOrder busPayOrder = busPayOrderR.getData();
        if (BusPayOrderStatusEnum.PAYING.getValue().equalsIgnoreCase(busPayOrder.getStatus())) {
            throw new RuntimeException(String.format("支付订单状态异常，订单号：%s", context.getOrderNo()));
        }
        context.setPaySuccessTime(busPayOrder.getPaySuccessTime());
    }
}
