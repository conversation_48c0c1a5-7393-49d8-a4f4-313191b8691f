package com.puree.hospital.business.service;

import com.puree.hospital.business.api.model.dto.BusHospitalOrderDTO;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.dto.DeliveryInfoDTO;
import com.puree.hospital.business.domain.vo.DeliveryInfoVO;
import com.puree.hospital.business.domain.vo.GoodsVO;
import com.puree.hospital.business.domain.vo.OrderSendOutResultVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 16:53
 */
public interface IBusDrugOrderPackageService {
    /**
     * 查询包裹信息
     * @param orderNo
     * @return
     */
    List<BusDrugOrderPackage> selectPackageList(String orderNo, Integer barterOrNot);

    /**
     * 商品发货
     * @param list
     * @return
     */
    OrderSendOutResultVO insertSendOutGoods(List<BusHospitalOrderDTO> list);

    /**
     * 更新发货
     * @param list
     * @return
     */
    int updateDeliveryNo(List<DeliveryInfoDTO> list);

    /**
     * 查询包裹信息
     * @param orderNo
     * @return
     */
    List<DeliveryInfoVO> getDeliveryInfo(String orderNo);

    /**
     * 查询西药处方包裹信息
     * @param goodsVO
     * @return
     */
    List<BusDrugOrderPackage> selectPrescriptionPackageList(GoodsVO goodsVO);

    /**
     * 查询otc药包裹信息
     * @param goodsVO
     * @return
     */
    List<BusDrugOrderPackage> selectOtcPackageList(GoodsVO goodsVO);

    /**
     * 查询商品包裹信息
     * @param goodsVO
     * @return
     */
    List<BusDrugOrderPackage> selectGoodsPackageList(GoodsVO goodsVO);

    /**
     * 查询订单包裹信息
     * @param order
     * @return
     */
    List<BusDrugOrderPackage> selectOrderPackageList(BusOrder order);

    /**
     * 判定订单是否完全发货
     *
     * @param subOrderId    子订单号
     * @param type（0药品      1商品）
     * @param afterSaleType (0 非售后  1售后)
     * @return 0 未全部发货 1-全部发货
     */
    Integer isAllSendGoods(Long subOrderId,String type,Integer afterSaleType);

    /**
     * 查询处方生成的包裹集合
     * @param goodsVO
     * @return
     */
    List<BusDrugOrderPackage> selectPackageStatusList(GoodsVO goodsVO);

    /**
     * 保存物流信息
     * @param drugsOrder
     * @param order
     */
     void saveOrderLogistics(BusDrugsOrder drugsOrder, BusOrder order);

    /**
     * 按订单 ID 查询
     *
     * @param orderId 订单 ID
     * @param packageType 包裹类型 0-药品包裹 1-商品包裹
     * @return {@link BusDrugOrderPackage }
     */
    List<BusDrugOrderPackage> queryByOrderId(Long orderId, Integer packageType);

    /**
     * 按订单 ID 查询
     *
     * @param orderIds 订单 ID 列表
     * @param packageType 包裹类型 0-药品包裹 1-商品包裹
     * @return {@link BusDrugOrderPackage }
     */
    List<BusDrugOrderPackage> queryByOrderIds(List<Long> orderIds, Integer packageType);

}
