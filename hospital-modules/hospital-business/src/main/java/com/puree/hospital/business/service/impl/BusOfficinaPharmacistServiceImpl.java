package com.puree.hospital.business.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.business.api.model.enums.DispensingStatusEnum;
import com.puree.hospital.business.domain.BusDoctorPushSetup;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusFiveGroupMember;
import com.puree.hospital.business.domain.BusFiveWorkGroup;
import com.puree.hospital.business.domain.BusImGroupMember;
import com.puree.hospital.business.domain.BusOfficinaPharmacist;
import com.puree.hospital.business.domain.BusPharmacistSchedule;
import com.puree.hospital.business.domain.BusPharmacyConfig;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusSignature;
import com.puree.hospital.business.domain.dto.BusOfficinaPharmacistDto;
import com.puree.hospital.business.domain.vo.BusOfficinaAllNameVo;
import com.puree.hospital.business.domain.vo.PharmacistInfoVo;
import com.puree.hospital.business.infrastructure.utils.SysNumberGenerator;
import com.puree.hospital.business.listener.ImportScheduleListener;
import com.puree.hospital.business.mapper.BusDoctorPushSetupMapper;
import com.puree.hospital.business.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.business.mapper.BusFiveGroupMemberMapper;
import com.puree.hospital.business.mapper.BusFiveWorkGroupMapper;
import com.puree.hospital.business.mapper.BusImGroupMemberMapper;
import com.puree.hospital.business.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.business.mapper.BusPharmacistScheduleMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.mapper.BusSignatureMapper;
import com.puree.hospital.business.service.IBusOfficinaPharmacistService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.SysNumConstants;
import com.puree.hospital.common.core.constant.TencentyunImConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AppRoleEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.FiveRoleEnum;
import com.puree.hospital.common.core.enums.OfficinaPharmacistEnum;
import com.puree.hospital.common.core.enums.PharmacistCategoryEnum;
import com.puree.hospital.common.core.enums.SignatureRole;
import com.puree.hospital.common.core.enums.SignatureStatusEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.ImPortraitRequest;
import com.puree.hospital.im.api.model.ProfileItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOfficinaPharmacistServiceImpl implements IBusOfficinaPharmacistService {
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;
    private final SysNumberGenerator sysNumberGenerator;
    private final BusDoctorPushSetupMapper busDoctorPushSetupMapper;
    private final BusFiveGroupMemberMapper busFiveGroupMemberMapper;
    private final RemoteImService remoteImService;
    private final BusFiveWorkGroupMapper busFiveWorkGroupMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusImGroupMemberMapper busImGroupMemberMapper;
    private final BusSignatureMapper busSignatureMapper;
    private final BusPharmacistScheduleMapper pharmacistScheduleMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    /**
     * 事务状态
     */
    private TransactionStatus transactionStatus;
    /**
     * 事务管理器
     */
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    /**
     * 事务定义
     */
    @Autowired
    private DefaultTransactionDefinition transactionDefinition;

    @Value("${templatePath}")
    private String templatePath;

    /**
     * 查询所有药师的姓名
     *
     * @param busPharmacyConfig
     * @return
     */
    @Override
    public List<BusOfficinaAllNameVo> getOfficinaNameAll(BusPharmacyConfig busPharmacyConfig) {
        return busOfficinaPharmacistMapper.getOfficinaNameAll(busPharmacyConfig);
    }

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    @Override
    public List<BusOfficinaPharmacist> listAll(BusOfficinaPharmacist busOfficinaPharmacist) {
        List<BusOfficinaPharmacist> busOfficinaPharmacists = busOfficinaPharmacistMapper.listAll(busOfficinaPharmacist);
        for (BusOfficinaPharmacist officinaPharmacist : busOfficinaPharmacists) {
            String pharmacistCategory = officinaPharmacist.getPharmacistCategory();
            LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusSignature::getObjectId, officinaPharmacist.getId());
            if (OfficinaPharmacistEnum.DELIVER.getCode().equals(pharmacistCategory)) {
                officinaPharmacist.setPharmacistCategory("发货药师");
                lambdaQuery.eq(BusSignature::getObjectType, SignatureRole.DELIVERY_PHARMACIST.getValue());
            } else if (OfficinaPharmacistEnum.ADJUST.getCode().equals(pharmacistCategory)) {
                officinaPharmacist.setPharmacistCategory("调剂药师");
                lambdaQuery.eq(BusSignature::getObjectType, SignatureRole.CHEMIST.getValue());
            }
            // 查询发货药师签名
            BusSignature busSignature = busSignatureMapper.selectOne(lambdaQuery);
            if (null != busSignature) {
                officinaPharmacist.setPharmacistSignature(busSignature.getCertSignature());
                if (null == busSignature.getEndDate()) {
                    officinaPharmacist.setSignatureStatus(SignatureStatusEnum.NOT_APPLIED_FOR.getCode());
                    officinaPharmacist.setSignatureStatusValue(SignatureStatusEnum.NOT_APPLIED_FOR.getInfo());
                } else if (DateUtils.getNowDate().compareTo(busSignature.getEndDate()) >= 0) {
                    officinaPharmacist.setSignatureStatus(SignatureStatusEnum.EXPIRED.getCode());
                    officinaPharmacist.setSignatureStatusValue(SignatureStatusEnum.EXPIRED.getInfo());
                    if (StringUtils.isNotEmpty(busSignature.getCertId())) {
                        busSignatureMapper.updateSignature(busSignature.getId());
                    }
                } else {
                    officinaPharmacist.setSignatureStatus(SignatureStatusEnum.NORMAL.getCode());
                    officinaPharmacist.setSignatureStatusValue(SignatureStatusEnum.NORMAL.getInfo());
                }
            } else {
                officinaPharmacist.setSignatureStatus(SignatureStatusEnum.NOT_APPLIED_FOR.getCode());
                officinaPharmacist.setSignatureStatusValue(SignatureStatusEnum.NOT_APPLIED_FOR.getInfo());
            }
            officinaPharmacist.setIdNumber(DESUtil.decrypt(officinaPharmacist.getIdNumber()));
            officinaPharmacist.setPhoneNumber(DESUtil.decrypt(officinaPharmacist.getPhoneNumber()));
        }
        return busOfficinaPharmacists;
    }

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    @Override
    public BusOfficinaPharmacist getById(Long id) {
        BusOfficinaPharmacist officinaPharmacist = busOfficinaPharmacistMapper.selectById(id);
        officinaPharmacist.setPhoneNumber(DESUtil.decrypt(officinaPharmacist.getPhoneNumber()));
        officinaPharmacist.setIdNumber(DESUtil.decrypt(officinaPharmacist.getIdNumber()));
        return officinaPharmacist;
    }

    /**
     * 添加药师
     *
     * @param busOfficinaPharmacist 药师信息
     * @return 返回影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(BusOfficinaPharmacist busOfficinaPharmacist) {
        busOfficinaPharmacist.setCreateTime(DateUtils.getNowDate());
        String number = sysNumberGenerator.get(SysNumConstants.YS_NUM_KEY);
        busOfficinaPharmacist.setPharmacistNumber(number);
        busOfficinaPharmacist.setIdNumber(DESUtil.encrypt(busOfficinaPharmacist.getIdNumber()));
        busOfficinaPharmacist.setPhoneNumber(DESUtil.encrypt(busOfficinaPharmacist.getPhoneNumber()));
        busOfficinaPharmacistMapper.insert(busOfficinaPharmacist);
        //插入推送设置
        BusDoctorPushSetup busDoctorPushSetup = new BusDoctorPushSetup();
        busDoctorPushSetup.setPusherId(busOfficinaPharmacist.getId());
        busDoctorPushSetup.setIsPush(YesNoEnum.YES.getCode());
        busDoctorPushSetup.setHospitalId(busOfficinaPharmacist.getHospitalId());
        busDoctorPushSetup.setPushType(2);
        busDoctorPushSetup.setType("1");
        busDoctorPushSetup.setCreateTime(DateUtils.getNowDate());
        busDoctorPushSetupMapper.insert(busDoctorPushSetup);
        //导入im账号
        R<Boolean> returnData = remoteImService.accountCheck(TencentyunImConstants.PHARMACIST_IM_ACCOUNT + busOfficinaPharmacist.getId());
        if (Constants.SUCCESS.equals(returnData.getCode()) && !(boolean) returnData.getData()) {
            remoteImService.accountImport(TencentyunImConstants.PHARMACIST_IM_ACCOUNT + busOfficinaPharmacist.getId(),
                    busOfficinaPharmacist.getPharmacistName(), "");
        }
        return 1;
    }

    /**
     * 修改，忽略null字段
     *
     * @param busOfficinaPharmacist 修改的记录
     * @return 返回影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateIgnoreNull(BusOfficinaPharmacist busOfficinaPharmacist) {
        // 查询药师角色类型
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectById(busOfficinaPharmacist.getId());
        busOfficinaPharmacist.setCreateTime(DateUtils.getNowDate());
        busOfficinaPharmacist.setIdNumber(DESUtil.encrypt(busOfficinaPharmacist.getIdNumber()));
        busOfficinaPharmacist.setPhoneNumber(DESUtil.encrypt(busOfficinaPharmacist.getPhoneNumber()));
        int i = busOfficinaPharmacistMapper.updateById(busOfficinaPharmacist);
        if (!pharmacist.getPharmacistCategory().equals(busOfficinaPharmacist.getPharmacistCategory())) {
            log.info("修改药师类型={}，查询药师类型={}", busOfficinaPharmacist.getPharmacistCategory(), pharmacist.getPharmacistCategory());
            LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusSignature::getObjectId, pharmacist.getId());
            lambdaQuery.eq(BusSignature::getObjectType, herbalistTypeConversion(pharmacist.getPharmacistCategory()));
            // 查询药师是否认证签名
            BusSignature signature = busSignatureMapper.selectOne(lambdaQuery);
            if (StringUtils.isNotNull(signature)) {
                // 修改药师签名角色类型
                BusSignature busSignature = new BusSignature();
                busSignature.setObjectType(herbalistTypeConversion(busOfficinaPharmacist.getPharmacistCategory()));
                busSignatureMapper.update(busSignature, lambdaQuery);
            }
        }
        //修改im药师信息
        if (StringUtils.isNotEmpty(busOfficinaPharmacist.getPharmacistName())) {
            portraitSet(busOfficinaPharmacist);
        }
        return i;
    }

    private String herbalistTypeConversion(String pharmacistCategory) {
        String objectType;
        if (PharmacistCategoryEnum.DELIVERY_PHARMACIST.getValue().equals(pharmacistCategory)) {
            objectType = SignatureRole.DELIVERY_PHARMACIST.getValue();
        } else if (PharmacistCategoryEnum.PHARMACIST.getValue().equals(pharmacistCategory)) {
            objectType = SignatureRole.PHARMACIST.getValue();
        } else {
            objectType = SignatureRole.CHEMIST.getValue();
        }
        return objectType;
    }

    /**
     * 修改im信息
     *
     * @param busOfficinaPharmacist
     */
    private void portraitSet(BusOfficinaPharmacist busOfficinaPharmacist) {
        //修改im资料
        ImPortraitRequest imPortraitRequest = new ImPortraitRequest();
        imPortraitRequest.setFrom_Account(TencentyunImConstants.PHARMACIST_IM_ACCOUNT + busOfficinaPharmacist.getId());
        List<ProfileItem> profileItemList = new ArrayList<>();
        ProfileItem profileItem = new ProfileItem();
        profileItem.setTag("Tag_Profile_IM_Nick");
        profileItem.setValue(busOfficinaPharmacist.getPharmacistName());
        profileItemList.add(profileItem);
        imPortraitRequest.setProfileItemList(profileItemList);
        R<Boolean> objectR = remoteImService.portraitSet(imPortraitRequest);
        if (Constants.FAIL.equals(objectR.getCode()) || !(boolean) objectR.getData()) {
            throw new ServiceException("更新im账号信息失败");
        }
    }

    /**
     * 删除记录
     *
     * @param id 待删除的记录
     * @return 返回影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectById(id);
        // 校验药师是否有关联信息
        checkRelationInfo(pharmacist);
        // 删除排班信息
        pharmacistScheduleMapper.delete(new LambdaQueryWrapper<BusPharmacistSchedule>()
                .eq(BusPharmacistSchedule::getPharmacistId,id)
                .gt(BusPharmacistSchedule::getScheduleDate,new Date()));
        // 删除药师签名
        String roleType;
        if (CodeEnum.NO.getCode().equals(pharmacist.getPharmacistCategory())) {
            roleType = SignatureRole.DELIVERY_PHARMACIST.getValue();
        } else {
            roleType = SignatureRole.CHEMIST.getValue();
        }
        LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusSignature::getObjectType, roleType);
        lambdaQuery.eq(BusSignature::getObjectId, pharmacist.getId());
        busSignatureMapper.delete(lambdaQuery);
        // 软删除药师
        BusOfficinaPharmacist officinaPharmacist = new BusOfficinaPharmacist();
        officinaPharmacist.setId(pharmacist.getId());
        officinaPharmacist.setDelStatus(CodeEnum.YES.getCode());
        return busOfficinaPharmacistMapper.updateById(officinaPharmacist);
    }

    private void checkRelationInfo(BusOfficinaPharmacist pharmacist) {
        // 查询该药师是否审核过处方
        LambdaQueryWrapper<BusPrescription> queryWrapper = new LambdaQueryWrapper<BusPrescription>()
                .eq(BusPrescription::getHospitalId, pharmacist.getHospitalId())
                .eq(BusPrescription::getReviewPharmacistId, pharmacist.getId());
        BusPrescription busPrescription = busPrescriptionMapper.selectOne(queryWrapper.last(" limit 1"));
        if (StringUtils.isNotNull(busPrescription)) {
            throw new ServiceException("该药师有审核记录，不能删除！");
        }
        // 查询该药师是否加入过工作组
        checkWorkGroupInfo(pharmacist);
        // 查询医生是否服务包群组
        LambdaQueryWrapper<BusImGroupMember> lambdaQueryWrapper = new LambdaQueryWrapper<BusImGroupMember>()
                .eq(BusImGroupMember::getPersonnelId, pharmacist.getId())
                .eq(BusImGroupMember::getRole, AppRoleEnum.PHARMACIST.getCode());
        BusImGroupMember busImGroupMember = busImGroupMemberMapper.selectOne(lambdaQueryWrapper.last(" limit 1"));
        if (StringUtils.isNotNull(busImGroupMember)) {
            throw new ServiceException("该药师有加入服务包群组，不能删除！");
        }
    }

    /**
     * 药师停用启用操作
     *
     * @param busOfficinaPharmacist
     * @return
     */
    @Override
    public int lock(BusOfficinaPharmacist busOfficinaPharmacist) {
//        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectById(busOfficinaPharmacist.getId());
//        // 校验药师关联的工作组信息
//        checkWorkGroupInfo(pharmacist);
        busOfficinaPharmacist.setUpdateTime(DateUtils.getNowDate());
        return busOfficinaPharmacistMapper.updateById(busOfficinaPharmacist);
    }

    /**
     * 校验药师身份证号是否重复
     *
     * @param id
     * @param idNumber
     * @return
     */
    @Override
    public boolean checkIdNumber(Long id, String idNumber) {
        LambdaQueryWrapper<BusOfficinaPharmacist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOfficinaPharmacist::getIdNumber, idNumber);
        queryWrapper.eq(BusOfficinaPharmacist::getDelStatus, "0");
        BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectOne(queryWrapper);
        // 新增操作
        if (Objects.isNull(id) && Objects.nonNull(busOfficinaPharmacist)) {
            return true;
        }
        // 修改操作
        if (Objects.nonNull(id) && Objects.nonNull(busOfficinaPharmacist)) {
            return !id.equals(busOfficinaPharmacist.getId());
        }
        return false;
    }

    /**
     * 校验药师手机号是否重复
     *
     * @param id
     * @param phoneNumber
     * @return
     */
    @Override
    public boolean checkPhoneNumber(Long id, String phoneNumber) {
        LambdaQueryWrapper<BusOfficinaPharmacist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOfficinaPharmacist::getPhoneNumber, DESUtil.encrypt(phoneNumber));
        queryWrapper.eq(BusOfficinaPharmacist::getDelStatus, "0");
        BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectOne(queryWrapper);
        // 新增操作
        if (Objects.isNull(id) && Objects.nonNull(busOfficinaPharmacist)) {
            return true;
        }
        // 修改操作
        if (Objects.nonNull(id) && Objects.nonNull(busOfficinaPharmacist)) {
            return !id.equals(busOfficinaPharmacist.getId());
        }
        return false;
    }

    @Override
    public PharmacistInfoVo getPharmacistInfo(String userName, String roleType) {
        return busOfficinaPharmacistMapper.getPharmacistInfo(userName, roleType);
    }

    /**
     * 查询药师信息
     *
     * @param username
     * @return
     */
    @Override
    public PharmacistInfoVo getPharmacist(String username) {
        return busOfficinaPharmacistMapper.getPharmacist(username);
    }

    /**
     * 查询药师角色
     *
     * @param phonenumber
     * @return
     */
    @Override
    public String queryOfficinaPharmacist(String phonenumber) {
        LambdaQueryWrapper<BusOfficinaPharmacist> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOfficinaPharmacist::getPhoneNumber, DESUtil.encrypt(phonenumber));
        lambdaQuery.eq(BusOfficinaPharmacist::getDelStatus, "0");
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectOne(lambdaQuery);
        if (Objects.nonNull(pharmacist)) {
            return pharmacist.getPharmacistCategory();
        } else {
            return null;
        }
    }

    /**
     * 根据身份证号查询药师信息
     *
     * @param idNumber
     * @return
     */
    @Override
    public BusOfficinaPharmacist selectPharmacistByNo(String idNumber) {
        LambdaQueryWrapper<BusOfficinaPharmacist> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOfficinaPharmacist::getIdNumber, idNumber);
        lambdaQuery.eq(BusOfficinaPharmacist::getDelStatus, "0");
        return busOfficinaPharmacistMapper.selectOne(lambdaQuery);
    }

    private void checkWorkGroupInfo(BusOfficinaPharmacist pharmacist) {
        LambdaQueryWrapper<BusFiveGroupMember> qw = new LambdaQueryWrapper<BusFiveGroupMember>()
                .eq(BusFiveGroupMember::getMemberId, pharmacist.getId())
                .eq(BusFiveGroupMember::getType, FiveRoleEnum.PSYCHOLOGY.getCode())
                .eq(BusFiveGroupMember::getHospitalId, pharmacist.getHospitalId())
                .eq(BusFiveGroupMember::getStatus, YesNoEnum.YES.getCode());
        List<BusFiveGroupMember> groupMemberList = busFiveGroupMemberMapper.selectList(qw);
        if (StringUtils.isNotEmpty(groupMemberList)) {
            /*获取药师所绑定工作组的工作组id集合*/
            List<Long> workGroupIds =
                    groupMemberList.stream().map(BusFiveGroupMember::getWorkGroupId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(workGroupIds)) {
                workGroupIds.forEach(w -> {
                    // 查询药师所关联的工作组信息
                    BusFiveWorkGroup busFiveWorkGroup = busFiveWorkGroupMapper.selectById(w);
                    if (StringUtils.isNotNull(busFiveWorkGroup) && CodeEnum.YES.getCode().equals(busFiveWorkGroup.getStatus())) {
                        throw new ServiceException("该药师账号有加入工作组！");
                    }
                });
            }
        }
    }

    @Override
    public void downloadScheduleTemplate(String scheduleDate, Long hospitalId, HttpServletResponse response) throws IOException {
        // 查询医院启用药师
        List<BusOfficinaPharmacist> pharmacistList = busOfficinaPharmacistMapper.selectList(new LambdaQueryWrapper<BusOfficinaPharmacist>()
                .eq(BusOfficinaPharmacist::getHospitalId, hospitalId)
                .eq(BusOfficinaPharmacist::getStatus, YesNoEnum.YES.getCode())
                .orderByAsc(BusOfficinaPharmacist::getPharmacistCategory));
        pharmacistList.forEach(item -> item.setPharmacistCategory(PharmacistCategoryEnum.getNameByCode(item.getPharmacistCategory())));
        // 填充月份天数
        Date date = DateUtils.dateTime(DateUtils.YYYY_MM, scheduleDate);
        int days = DateUtils.getMonthTotalDays(date);
        List<Map<String,String>> dayArr = new ArrayList<>();
        for (int i = 1; i <= days; i++) {
            HashMap<String, String> map = new HashMap<>(1);
            map.put("scheduleDate",date.getMonth()+1 + "." + i);
            dayArr.add(map);
        }
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templatePath + "药师排班模板表.xlsx").build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillHorizontal = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
            FillConfig fillVertical = FillConfig.builder().direction(WriteDirectionEnum.VERTICAL).build();
            excelWriter.fill(dayArr, fillHorizontal, writeSheet);
            excelWriter.fill(pharmacistList, fillVertical, writeSheet);
        }
    }

    @Override
    public AjaxResult importSchedule(MultipartFile file, Long hospitalId,String scheduleDate) {
        try {
            Date date = DateUtils.dateTime(DateUtils.YYYY_MM, scheduleDate);
            // 开启事务
            this.transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
            EasyExcel.read(file.getInputStream(),
                    new ImportScheduleListener(pharmacistScheduleMapper,busOfficinaPharmacistMapper,date,
                            platformTransactionManager,hospitalId, transactionStatus)).sheet().doRead();
        }catch (Exception ex){
            log.error("导入药师排班异常",ex);
            // 回滚事务
            platformTransactionManager.rollback(transactionStatus);
            return AjaxResult.error(ex.getMessage());
        }
        return AjaxResult.success();
    }

    @Override
    public List<BusPharmacistSchedule> listSchedule(BusPharmacistSchedule dto) {
        return pharmacistScheduleMapper.listSchedule(dto);
    }

    /**
     * 更新药师处方签
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BusOfficinaPharmacist updatePrescriptionLabel(BusOfficinaPharmacistDto dto) {
        BusOfficinaPharmacist officinaPharmacist = new BusOfficinaPharmacist();
        String endDate = dto.getEndDate();
        Calendar instance = Calendar.getInstance();
        instance.setTime(DateUtils.parseDate(endDate));
        instance.add(Calendar.MONTH, -1);
        String time = DateUtils.parse_yyyyMMdd(instance.getTime());
        if (time.compareTo(dto.getStartDate()) > 0) {
            throw new ServiceException("时间跨度超过一个月！");
        }
        // 查询该时间段内已发货的处方
        LambdaQueryWrapper<BusPrescription> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusPrescription::getStatus, "4");
        lambdaQuery.between(BusPrescription::getCreateTime, dto.getStartDate() + " 00:00:00", dto.getEndDate() + " 23:59:59");
        List<BusPrescription> prescriptionList = busPrescriptionMapper.selectList(lambdaQuery);
        if (StringUtils.isEmpty(prescriptionList)) {
            throw new ServiceException("该时间段内没有已发货的处方！");
        }
        // 查询处方发货时间
        List<Long> ids = prescriptionList.stream().map(BusPrescription::getId).collect(Collectors.toList());
        Map<Long, BusPrescription> prescriptionMap = prescriptionList.stream().collect(Collectors.toMap(BusPrescription::getId, prescription -> prescription));
        LambdaQueryWrapper<BusDrugOrderPackage> wrapper = Wrappers.lambdaQuery();
        wrapper.and(w -> w.isNotNull(BusDrugOrderPackage::getDeliveryNo).or().isNotNull(BusDrugOrderPackage::getCode));
        wrapper.in(BusDrugOrderPackage::getPrescriptionId, ids);
        List<BusDrugOrderPackage> packageList = busDrugOrderPackageMapper.selectList(wrapper);
        if (StringUtils.isEmpty(packageList)) {
            throw new ServiceException("该处方药品还未发货");
        }
        for (BusDrugOrderPackage orderPackage : packageList) {
            // 查询当天药师排班
            LambdaQueryWrapper<BusPharmacistSchedule> query = Wrappers.lambdaQuery();
            query.eq(BusPharmacistSchedule::getHospitalId, dto.getHospitalId());
            String scheduleTime = DateUtils.parse_yyyyMMdd(orderPackage.getDeliveryTime());
            query.eq(BusPharmacistSchedule::getScheduleDate, scheduleTime);
            query.eq(BusPharmacistSchedule::getScheduleStatus, CodeEnum.YES.getCode());
            List<BusPharmacistSchedule> scheduleList = pharmacistScheduleMapper.selectList(query);
            if (StringUtils.isEmpty(scheduleList)) {
                throw new ServiceException(scheduleTime + " 无药师排班，无法更新处方签");
            }
            BusPrescription prescription = new BusPrescription();
            prescription.setId(orderPackage.getPrescriptionId());
            for (BusPharmacistSchedule schedule : scheduleList) {
                BusOfficinaPharmacist pharmacist = busOfficinaPharmacistMapper.selectById(schedule.getPharmacistId());
                LambdaQueryWrapper<BusSignature> lambdaQueryWrapper = Wrappers.lambdaQuery();
                if (CodeEnum.NO.getCode().equals(schedule.getPharmacistType())) {
                    prescription.setDeliveryPharmacistId(schedule.getPharmacistId());
                    prescription.setDeliveryPharmacistName(pharmacist.getPharmacistName());
                    lambdaQueryWrapper.eq(BusSignature::getObjectType, SignatureRole.DELIVERY_PHARMACIST.getValue());
                } else {
                    // 处方已复核不再允许签名
                    BusPrescription busPrescription = prescriptionMap.get(prescription.getId());
                    if (DispensingStatusEnum.DISPENSING_PENDING.getCode().equals(busPrescription.getDispensingStatus())) {
                        prescription.setDispensingPharmacistId(schedule.getPharmacistId());
                        prescription.setDispensingPharmacistName(pharmacist.getPharmacistName());
                    } else {
                        pharmacist.setId(busPrescription.getDispensingPharmacistId());
                    }
                    lambdaQueryWrapper.eq(BusSignature::getObjectType, SignatureRole.CHEMIST.getValue());
                }
                // 校验药师签名认证是否过期
                lambdaQueryWrapper.eq(BusSignature::getObjectId, pharmacist.getId());
                BusSignature signature = busSignatureMapper.selectOne(lambdaQueryWrapper);
                if (null == signature || null == signature.getEndDate() || DateUtils.getNowDate().compareTo(signature.getEndDate()) >= 0) {
                    officinaPharmacist.setSignatureStatus(SignatureStatusEnum.EXPIRED.getCode());
                    officinaPharmacist.setPharmacistName(pharmacist.getPharmacistName());
                    officinaPharmacist.setIdNumber(DESUtil.decrypt(pharmacist.getIdNumber()));
                    return officinaPharmacist;
                }
            }
            int update = busPrescriptionMapper.updateById(prescription);
            if (update > 0) {
                officinaPharmacist.setSignatureStatus(SignatureStatusEnum.NORMAL.getCode());
            }
        }
        return officinaPharmacist;
    }
}
