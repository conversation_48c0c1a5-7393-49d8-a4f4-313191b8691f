package com.puree.hospital.business.domain.vo;

import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusPrescriptionDrugs;
import com.puree.hospital.common.api.enums.YesNoEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BusPrescriptionDetailVo extends BusPrescription {

    private static final long serialVersionUID = -626452033232255212L;

    private String doctorSignature;
    private String reviewerSignature;
    private String deliverGoodsSignature;
    private String dispensingPharmacistSignature;
    private List<BusPrescriptionDrugs> prescriptionDrugsList;
    private String expertName;
    private Integer showBoilWay = YesNoEnum.YES.getCode() ;

}
