package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.model.event.order.DrugsOrderShippedEvent;
import com.puree.hospital.app.api.model.event.order.ShopOrderShippedEvent;
import com.puree.hospital.business.api.model.dto.BusDrugTraceCodeDTO;
import com.puree.hospital.business.api.model.dto.BusHospitalOrderDTO;
import com.puree.hospital.business.api.model.enums.DispensingStatusEnum;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusOfficinaPharmacist;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOrderAfterSales;
import com.puree.hospital.business.domain.BusOrderDrugTraceCode;
import com.puree.hospital.business.domain.BusOtcDrugs;
import com.puree.hospital.business.domain.BusPharmacistSchedule;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusPrescriptionDrugs;
import com.puree.hospital.business.domain.BusSignature;
import com.puree.hospital.business.domain.dto.DeliveryInfoDTO;
import com.puree.hospital.business.domain.vo.DeliveryInfoVO;
import com.puree.hospital.business.domain.vo.GoodsVO;
import com.puree.hospital.business.domain.vo.OrderSendOutResultVO;
import com.puree.hospital.business.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.business.mapper.BusOfficinaPharmacistMapper;
import com.puree.hospital.business.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.business.mapper.BusOrderDrugTraceCodeMapper;
import com.puree.hospital.business.mapper.BusOrderMapper;
import com.puree.hospital.business.mapper.BusOtcDrugsMapper;
import com.puree.hospital.business.mapper.BusPharmacistScheduleMapper;
import com.puree.hospital.business.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.mapper.BusSignatureMapper;
import com.puree.hospital.business.service.IBusDrugOrderPackageService;
import com.puree.hospital.business.service.IBusOrderService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.DeliveryTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DrugTraceCodeStatusEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.NemberEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.SignatureRole;
import com.puree.hospital.common.core.enums.SignatureStatusEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.order.api.RemoteBusOrderService;
import com.puree.hospital.order.api.RemoteBusOrderShopService;
import com.puree.hospital.order.api.model.BusOrderShop;
import com.puree.hospital.order.api.model.BusShopOrder;
import com.puree.hospital.order.api.model.vo.BusDrugsOrderVO;
import com.puree.hospital.order.api.model.vo.BusOrderVO;
import com.puree.hospital.tool.api.RemoteKdnService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/20 16:54
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDrugOrderPackageServiceImpl implements IBusDrugOrderPackageService {
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final RemoteKdnService remoteKdnService;
    private final RemoteBusOrderService remoteBusOrderService;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final BusOtcDrugsMapper busOtcDrugsMapper;
    private final RemoteBusOrderShopService remoteBusOrderShopService;
    private final BusOrderAfterSalesMapper busOrderAfterSalesMapper;
    private final BusOrderMapper busOrderMapper;
    private final BusPharmacistScheduleMapper busPharmacistScheduleMapper;
    private final BusOfficinaPharmacistMapper busOfficinaPharmacistMapper;

    @Lazy
    @Resource
    private IBusOrderService iBusOrderService;
    private final BusSignatureMapper signatureMapper;

    private final BusOrderDrugTraceCodeMapper busOrderDrugTraceCodeMapper;

    private final ApplicationEventPublisher publisher;

    /**
     * 查询包裹信息
     *
     * @param orderNo
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> selectPackageList(String orderNo, Integer barterOrNot) {
        return busDrugOrderPackageMapper.selectPackageList(orderNo, barterOrNot);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderSendOutResultVO insertSendOutGoods(List<BusHospitalOrderDTO> list) {
        log.info("发货入参list={}", JSONObject.toJSONString(list));
        OrderSendOutResultVO resultVO = new OrderSendOutResultVO();
        BusOfficinaPharmacist pharmacist = new BusOfficinaPharmacist();
        BusOrder busOrder = null;
        // 无法区分是商品订单还是药品订单，是否有多个，采取Set去重
        Set<Long> drugOrderIds = new HashSet<>();
        Set<Long> shopOrderIds = new HashSet<>();
        Date nowDate = DateUtils.getNowDate();
        String selfPickupCode = String.valueOf(RandomUtils.nextInt(100000, 999999));
        for (BusHospitalOrderDTO item : list) {
            // 子订单编号
            String orderNo = item.getOrderNo();
            BusDrugOrderPackage busDrugOrderPackageDTO = new BusDrugOrderPackage();
            if (OrderTypeConstant.DRUGS_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
                BusDrugsOrderVO drugsOrder = iBusOrderService.getDrugsOrdeByOrderNo(orderNo);
                // 添加到待推送事件集合
                drugOrderIds.add(drugsOrder.getId());
                String prescriptionType = "";
                if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
                    BusPrescription busPrescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());
                    prescriptionType = busPrescription.getPrescriptionType();
                    // 检查当天发货药师或调剂药师有无排班
                    List<BusPharmacistSchedule> busPharmacistScheduleList = busPharmacistScheduleMapper.selectList(new LambdaQueryWrapper<BusPharmacistSchedule>()
                            .ge(BusPharmacistSchedule::getScheduleDate, DateUtils.getDate() + " 00:00:00")
                            .le(BusPharmacistSchedule::getScheduleDate, DateUtils.getDate() + " 23:59:59")
                            .eq(BusPharmacistSchedule::getScheduleStatus, "1"));
                    log.info("查询是否当天有排班：{}", JSON.toJSONString(busPharmacistScheduleList));
                    for (BusPharmacistSchedule busPharmacistSchedule : busPharmacistScheduleList) {
                        long deliveryPharmacistId = busPharmacistSchedule.getPharmacistId();
                        BusOfficinaPharmacist busOfficinaPharmacist = busOfficinaPharmacistMapper.selectById(deliveryPharmacistId);
                        if (Objects.isNull(busOfficinaPharmacist)) {
                            throw new ServiceException("药师不存在！");
                        }
                        LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
                        if ("0".equals(busPharmacistSchedule.getPharmacistType())) {
                            lambdaQuery.eq(BusSignature::getObjectType, SignatureRole.DELIVERY_PHARMACIST.getValue());
                            // 修改处方发货药师
                            busPrescription.setDeliveryPharmacistId(busOfficinaPharmacist.getId());
                            busPrescription.setDeliveryPharmacistName(busOfficinaPharmacist.getPharmacistName());
                            busPrescriptionMapper.updateById(busPrescription);
                            log.info("修改处方发货药师：{}", JSON.toJSONString(busPrescription));
                        } else if ("2".equals(busPharmacistSchedule.getPharmacistType())) {
                            lambdaQuery.eq(BusSignature::getObjectType, SignatureRole.CHEMIST.getValue());
                            // 处方已复核不再允许签名
                            if (DispensingStatusEnum.DISPENSING_PENDING.getCode().equals(busPrescription.getDispensingStatus()) && Objects.isNull(busPrescription.getDispensingPharmacistId())) {
                                // 修改处方调剂药师
                                busPrescription.setDispensingPharmacistId(busOfficinaPharmacist.getId());
                                busPrescription.setDispensingPharmacistName(busOfficinaPharmacist.getPharmacistName());
                                busPrescriptionMapper.updateById(busPrescription);
                                log.info("修改处方调剂药师：{}", JSON.toJSONString(busPrescription));
                                lambdaQuery.eq(BusSignature::getObjectId, busOfficinaPharmacist.getId());
                            } else {
                                lambdaQuery.eq(BusSignature::getObjectId, busPrescription.getDispensingPharmacistId());
                            }
                        }
                        BusSignature signature = signatureMapper.selectOne(lambdaQuery);
                        if (null == signature || null == signature.getEndDate() || DateUtils.getNowDate().compareTo(signature.getEndDate()) >= 0) {
                            pharmacist.setSignatureStatus(SignatureStatusEnum.EXPIRED.getCode());
                            pharmacist.setPharmacistName(busOfficinaPharmacist.getPharmacistName());
                            pharmacist.setIdNumber(DESUtil.decrypt(busOfficinaPharmacist.getIdNumber()));
                            resultVO.setPharmacist(pharmacist);
                            return resultVO;
                        }
                    }
                }
                // 根据子订单ID查询总订单信息
                busOrder = selectOrderInfo(CodeEnum.NO.getCode(), drugsOrder.getId());
                log.info("发货busOrder:{}", JSON.toJSONString(busOrder));
                if (Objects.isNull(busOrder)) {
                    throw new ServiceException("订单信息不存在");
                }
                // 如果是医保支付订单，而且是西药处方
                if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay())
                        && PrescriptionTypeEnum.isMm(prescriptionType)) {
                    String traceOrderNo = busOrder.getOrderNo();
                    //如果订单是售后退货退款，重新发货,此时需要用历史的订单号进行溯源码上传
                    if (Objects.nonNull(busOrder.getAfterSaleId())) {
                        BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectById(busOrder.getAfterSaleId());
                        if (Objects.nonNull(busOrderAfterSales)) {
                            traceOrderNo = busOrderAfterSales.getOrderNo();
                        }
                    }
                    List<BusDrugTraceCodeDTO> drugTraceCodeList = item.getDrugTraceCodeList();
                    if (CollectionUtil.isNotEmpty(drugTraceCodeList)) {
                        //将溯源码信息保存到数据库中
                        for (BusDrugTraceCodeDTO input : drugTraceCodeList) {
                            if (Objects.isNull(input)
                                    || StringUtils.isBlank(input.getProductionBatchNo())
                                    || StringUtils.isBlank(input.getProductionDate())
                                    || StringUtils.isBlank(input.getDrugTraceCode())) {
                                throw new ServiceException("请填写完整的溯源相关信息");
                            }
                            BusOrderDrugTraceCode busOrderDrugTraceCode = new BusOrderDrugTraceCode();
                            busOrderDrugTraceCode.setHospitalId(busOrder.getHospitalId());
                            busOrderDrugTraceCode.setOrderNo(traceOrderNo);
                            busOrderDrugTraceCode.setPrescriptionId(drugsOrder.getPrescriptionId());
                            busOrderDrugTraceCode.setDrugId(item.getDrugsGoodsId());
                            busOrderDrugTraceCode.setProductionBatchNo(input.getProductionBatchNo());
                            busOrderDrugTraceCode.setProductionDate(input.getProductionDate());
                            busOrderDrugTraceCode.setDrugTraceCode(input.getDrugTraceCode());
                            busOrderDrugTraceCode.setStatus(DrugTraceCodeStatusEnum.PENDING_UPLOAD.getStatus());
                            busOrderDrugTraceCodeMapper.insert(busOrderDrugTraceCode);
                        }
                        resultVO.setNeedUploadTraceCode(true);
                    }
                    resultVO.setTotalOrderNo(busOrder.getOrderNo());
                    resultVO.setHospitalId(busOrder.getHospitalId());
                }
                /*查询包裹是否发货*/
                LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode());
                lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, item.getBarterOrNot());
                lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrder.getId());
                if (Objects.nonNull(item.getEnterpriseId())) {
                    lambdaQuery.eq(BusDrugOrderPackage::getEnterpriseId, item.getEnterpriseId());
                } else {
                    lambdaQuery.eq(BusDrugOrderPackage::getDrugsGoodsId, item.getDrugsGoodsId());
                }
                lambdaQuery.last("limit 1");
                BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(lambdaQuery);
                log.info("包裹查出信息：{}", JSON.toJSONString(drugOrderPackage));
                if (Objects.nonNull(drugOrderPackage)) {
                    if (Objects.nonNull(drugOrderPackage.getDeliveryTime())) {
                        throw new ServiceException("该订单商品已经发货，请刷新页面");
                    }
                    /*配送企业订单，自提发货需要做更新操作*/
                    if (DeliveryTypeEnum.isPickup(busOrder.getDeliveryType())) {
                        /*设置提货码*/
                        busDrugOrderPackageDTO.setCode(selfPickupCode);
                        busDrugOrderPackageDTO.setOrderNo(busOrder.getOrderNo());
                        busDrugOrderPackageDTO.setDeliveryTime(nowDate);
                        busDrugOrderPackageDTO.setId(drugOrderPackage.getId());
                        busDrugOrderPackageDTO.setDrugsGoodsId(item.getDrugsGoodsId());
                        busDrugOrderPackageDTO.setCreateTime(DateUtils.getNowDate());
                        busDrugOrderPackageDTO.setCreateBy(SecurityUtils.getUsername());
                        log.info("自提发货相关参数：{}", busDrugOrderPackageDTO);
                        busDrugOrderPackageMapper.updateById(busDrugOrderPackageDTO);
                        // 更新订单状态
                        this.updateBusOrderStatus(busOrder);
                        continue;
                    }
                }
                busDrugOrderPackageDTO.setHospitalId(busOrder.getHospitalId());
                busDrugOrderPackageDTO.setPackageDrugType(YesNoEnum.YES.getCode());
                busDrugOrderPackageDTO.setPrescriptionId(drugsOrder.getPrescriptionId());
                busDrugOrderPackageDTO.setDrugsOrderId(drugsOrder.getId());
                busDrugOrderPackageDTO.setDrugsGoodsId(item.getDrugsGoodsId());
                busDrugOrderPackageDTO.setPackageType(YesNoEnum.NO.getCode());
                busDrugOrderPackageDTO.setDeliveryType(Integer.valueOf(busOrder.getDeliveryType()));
                busDrugOrderPackageDTO.setOrderNo(busOrder.getOrderNo());
                busDrugOrderPackageDTO.setEnterpriseName("药房");
                if (Objects.nonNull(busOrder.getAfterSaleId())) {
                    busDrugOrderPackageDTO.setBarterOrNot(YesNoEnum.YES.getCode());
                }

            } else if (OrderTypeConstant.GOODS_ORDER.equals(orderNo.substring(0, NemberEnum.TWO.getCode()))) {
                BusShopOrder shopOrder = iBusOrderService.getShopOrdeByOrderNo(orderNo);
                // 添加到待推送事件集合
                shopOrderIds.add(shopOrder.getId());
                // 查询总订单信息
                busOrder = selectOrderInfo(CodeEnum.YES.getCode(), shopOrder.getId());
                /*查询包裹是否发货*/
                BusDrugOrderPackage shopOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                        .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.YES.getCode())
                        .eq(BusDrugOrderPackage::getDrugsOrderId, shopOrder.getId())
                        .eq(BusDrugOrderPackage::getDrugsGoodsId, item.getDrugsGoodsId())
                        .last("limit 1")
                );
                if (Objects.nonNull(shopOrderPackage) && Objects.nonNull(shopOrderPackage.getDeliveryTime())) {
                    throw new ServiceException("该订单已经发货，请刷新页面");
                }
                if (Objects.nonNull(busOrder.getAfterSaleId())) {
                    busDrugOrderPackageDTO.setBarterOrNot(YesNoEnum.YES.getCode());
                }
                busDrugOrderPackageDTO.setHospitalId(busOrder.getHospitalId());
                busDrugOrderPackageDTO.setDrugsOrderId(shopOrder.getId());
                busDrugOrderPackageDTO.setDrugsGoodsId(item.getDrugsGoodsId());
                busDrugOrderPackageDTO.setPackageType(YesNoEnum.YES.getCode());
                busDrugOrderPackageDTO.setDeliveryType(Integer.valueOf(busOrder.getDeliveryType()));
                busDrugOrderPackageDTO.setEnterpriseName("药房");
                busDrugOrderPackageDTO.setOrderNo(busOrder.getOrderNo());
                if (null != item.getEnterpriseId()) {
                    busDrugOrderPackageDTO.setEnterpriseId(item.getEnterpriseId());
                    busDrugOrderPackageDTO.setEnterpriseName(item.getEnterpriseName());
                }
            }
            if (DeliveryTypeEnum.isExpressDelivery(busDrugOrderPackageDTO.getDeliveryType().toString())) {
                /*查询快递鸟*/
                JSONArray shippers = this.getKDNDelivery(item.getDeliveryNo());
                if (CollectionUtil.isNotEmpty(shippers)) {
                    JSONObject shipper = (JSONObject) shippers.get(0);
                    busDrugOrderPackageDTO.setLogisticsCompany(shipper.getString("ShipperName"));
                    busDrugOrderPackageDTO.setExpressCode(shipper.getString("ShipperCode"));
                } else {
                    throw new ServiceException("请输入正确的快递单号");
                }
            } else if (DeliveryTypeEnum.isPickup(busDrugOrderPackageDTO.getDeliveryType().toString())) {
                /*设置提货码*/
                busDrugOrderPackageDTO.setCode(selfPickupCode);
            } else {
                // 设置商品无需物流
                busDrugOrderPackageDTO.setCode("2");
            }
            busDrugOrderPackageDTO.setDeliveryNo(item.getDeliveryNo());
            busDrugOrderPackageDTO.setDeliveryTime(nowDate);
            busDrugOrderPackageDTO.setCreateTime(DateUtils.getNowDate());
            busDrugOrderPackageDTO.setCreateBy("系统");
            log.info("发货新增包裹:{}", JSON.toJSONString(busDrugOrderPackageDTO));

            // 保存非配送企业发货包裹信息
            busDrugOrderPackageDTO.setId(null);
            busDrugOrderPackageMapper.insert(busDrugOrderPackageDTO);

            // 修改订单状态
            int result = this.updateBusOrderStatus(busOrder);
            if (result > 0) {
                pharmacist.setSignatureStatus(SignatureStatusEnum.NORMAL.getCode());
            }
        }
        // 发送发货事件通知
        sendOrderShippedEventNotification(drugOrderIds, shopOrderIds);
        resultVO.setPharmacist(pharmacist);
        return resultVO;
    }

    /**
     * 发送发货事件通知
     * @param drugOrderIds  药品订单ID
     * @param shopOrderIds  商品订单ID
     */
    private void sendOrderShippedEventNotification(Set<Long> drugOrderIds, Set<Long> shopOrderIds) {
        // 发送药品订单发货事件
        if (CollUtil.isNotEmpty(drugOrderIds)) {
            drugOrderIds.forEach(item -> {
                DrugsOrderShippedEvent drugsOrderShippedEvent = new DrugsOrderShippedEvent();
                drugsOrderShippedEvent.setSubOrderId(item);
                publisher.publishEvent(drugsOrderShippedEvent);
            });
        }
        // 发送商品订单发货事件
        if (CollUtil.isNotEmpty(shopOrderIds)) {
            shopOrderIds.forEach(item -> {
                ShopOrderShippedEvent shopOrderShippedEvent = new ShopOrderShippedEvent();
                shopOrderShippedEvent.setSubOrderId(item);
                publisher.publishEvent(shopOrderShippedEvent);
            });
        }
    }

    /**
     * 查询总订单信息
     * @param code 0药品 1商品
     * @param id 子订单ID
     * @return
     */
    private BusOrder selectOrderInfo(String code, Long id) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getSubOrderType, code);
        lambdaQuery.eq(BusOrder::getSubOrderId, id);
        return busOrderMapper.selectOne(lambdaQuery);
    }

    @Override
    public Integer isAllSendGoods(Long subOrderId, String type, Integer afterSaleType) {
        R<BusOrderVO> busOrderBySubOrderId = remoteBusOrderService.getBusOrderBySubOrderId(subOrderId, type);
        if (busOrderBySubOrderId.getCode() == Constants.FAIL) {
            throw new ServiceException(busOrderBySubOrderId.getMsg());
        }
        BusOrderVO busOrder = busOrderBySubOrderId.getData();
        List<BusOrder> orderList = iBusOrderService.getOrderList(busOrder.getOrderNo());
        for (BusOrder item : orderList) {
            /*如果是药品订单*/
            if (YesNoEnum.NO.getCode().equals(Integer.valueOf(item.getSubOrderType()))) {
                BusDrugsOrder drugsOrderById = iBusOrderService.getDrugsOrdeById(item.getSubOrderId());
                log.info("drugsOrderById:",drugsOrderById);
                if (Objects.nonNull(drugsOrderById)){
                    /*如果是处方药*/
                    if(Objects.nonNull(drugsOrderById.getPrescriptionId())){
                        BusPrescription busPrescription = busPrescriptionMapper.selectById(drugsOrderById.getPrescriptionId());

                        List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                                .eq(BusPrescriptionDrugs::getPrescriptionId, busPrescription.getId())
                        );
                        /*如果是中药方剂*/
                        if (Objects.nonNull(busPrescription.getPaId())){
                            BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                    .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                                    .eq(BusDrugOrderPackage::getBarterOrNot, afterSaleType)
                                    .eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrderById.getId())
                                    .eq(BusDrugOrderPackage::getPrescriptionId, drugsOrderById.getPrescriptionId())
                                    .eq(BusDrugOrderPackage::getHospitalId,drugsOrderById.getHospitalId())
                                    .last("limit 1")
                            );
                            if (Objects.isNull(drugOrderPackage)){
                                return 0;
                            }
                        } else {
                            /*如果是中药处方*/
                            if (CodeEnum.NO.getCode().equals(busPrescription.getPrescriptionType())) {
                                BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                                        .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode())
                                        .eq(BusDrugOrderPackage::getBarterOrNot, afterSaleType)
                                        .eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrderById.getId())
                                        .eq(BusDrugOrderPackage::getPrescriptionId, drugsOrderById.getPrescriptionId())
                                        .eq(BusDrugOrderPackage::getHospitalId,drugsOrderById.getHospitalId())
                                        .last("limit 1")
                                );
                                if (Objects.isNull(drugOrderPackage)){
                                    return 0;
                                }
                            } else {
                                /*如果是西药*/
                                for (BusPrescriptionDrugs item2 : busPrescriptionDrugs) {
                                    LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                                    lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, drugsOrderById.getPrescriptionId());
                                    lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, drugsOrderById.getHospitalId());
                                    lambdaQuery.eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode());
                                    if (Objects.isNull(item2.getEnterpriseId())) {
                                        lambdaQuery.eq(BusDrugOrderPackage::getDrugsGoodsId, item2.getDrugsId());
                                        lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrderById.getId());
                                    } else {
                                        lambdaQuery.eq(BusDrugOrderPackage::getEnterpriseId, item2.getEnterpriseId());
                                    }
                                    lambdaQuery.last("limit 1");
                                    BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(lambdaQuery);
                                    if (Objects.isNull(drugOrderPackage)){
                                        return 0;
                                    }
                                }
                            }
                        }
                    } else {
                        /*如果是OTC*/
                        List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectList(new LambdaQueryWrapper<BusOtcDrugs>()
                                .eq(BusOtcDrugs::getDrugsOrderId, drugsOrderById.getId())
                        );
                        for (BusOtcDrugs item3 : busOtcDrugs) {
                            LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                            lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, drugsOrderById.getHospitalId());
                            lambdaQuery.eq(BusDrugOrderPackage::getPackageType, YesNoEnum.NO.getCode());
                            if (Objects.isNull(item3.getEnterpriseId())) {
                                lambdaQuery.eq(BusDrugOrderPackage::getDrugsGoodsId, item3.getDrugsId());
                                lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrderById.getId());
                            } else {
                                lambdaQuery.eq(BusDrugOrderPackage::getEnterpriseId, item3.getEnterpriseId());
                            }
                            lambdaQuery.last("limit 1");
                            BusDrugOrderPackage drugOrderPackage = busDrugOrderPackageMapper.selectOne(lambdaQuery);
                            if (Objects.isNull(drugOrderPackage)){
                                return 0;
                            }
                        }
                    }
                }
            } else {
                /*如果是商品*/
                R<List<BusOrderShop>> listR = remoteBusOrderShopService.listBusOrderShopByOrderId(item.getSubOrderId());
                List<BusOrderShop> data = listR.getData();
                for (BusOrderShop date : data) {
                    BusDrugOrderPackage shopOrderPackage = busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                            .eq(BusDrugOrderPackage::getPackageType, YesNoEnum.YES.getCode())
                            .eq(BusDrugOrderPackage::getBarterOrNot, afterSaleType)
                            .eq(BusDrugOrderPackage::getDrugsOrderId, item.getSubOrderId())
                            .eq(BusDrugOrderPackage::getDrugsGoodsId, date.getShopId())
                            .last("limit 1")
                    );
                    if (Objects.isNull(shopOrderPackage)){
                        return 0;
                    }
                }
            }
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDeliveryNo(List<DeliveryInfoDTO> list) {
        int update = 0;
        for (DeliveryInfoDTO item : list) {
            BusDrugOrderPackage busDrugOrderPackage = new BusDrugOrderPackage();
            JSONArray shippers = this.getKDNDelivery(item.getDeliveryNo());
            if (shippers.size() > 0) {
                JSONObject shipper = (JSONObject) shippers.get(0);
                busDrugOrderPackage.setDeliveryNo(item.getDeliveryNo());
                busDrugOrderPackage.setLogisticsCompany(shipper.getString("ShipperName"));
                busDrugOrderPackage.setExpressCode(shipper.getString("ShipperCode"));
            } else {
                throw new ServiceException("请输入正确的快递单号");
            }
            for (Long id : item.getIds()) {
                busDrugOrderPackage.setId(id);
                update = busDrugOrderPackageMapper.updateById(busDrugOrderPackage);
            }
        }
        return update;
    }


    @Override
    public List<DeliveryInfoVO> getDeliveryInfo(String orderNo) {
        List<BusOrder> orderList = iBusOrderService.getOrderList(orderNo);
        BusOrder busOrder = orderList.get(0);
        List<DeliveryInfoVO> result = new ArrayList<>();
        List<DeliveryInfoVO> list = busDrugOrderPackageMapper.getDeliveryInfo(busOrder.getOrderNo());
        Map<String, List<DeliveryInfoVO>> collect = list.stream()
                .collect(Collectors.groupingBy(vo -> vo.getDeliveryTime() + "-" + vo.getDeliveryNo()));
        for (String item : collect.keySet()) {
            List<DeliveryInfoVO> deliveryInfoVOS = collect.get(item);
            if (deliveryInfoVOS.size() == 1) {
                DeliveryInfoVO deliveryInfoVO = deliveryInfoVOS.get(0);
                deliveryInfoVO.setCount(deliveryInfoVOS.size());
                List<Long> ids = new ArrayList<>();
                ids.add(deliveryInfoVO.getId());
                deliveryInfoVO.setIds(ids);
                result.addAll(deliveryInfoVOS);
            } else {
                DeliveryInfoVO deliveryInfoVO = deliveryInfoVOS.get(0);
                deliveryInfoVO.setCount(deliveryInfoVOS.size());
                List<Long> ids = deliveryInfoVOS.stream().map(DeliveryInfoVO::getId).collect(Collectors.toList());
                deliveryInfoVO.setIds(ids);
                result.add(deliveryInfoVO);
            }
        }
        result = result.stream()
                .sorted(Comparator.comparing(DeliveryInfoVO::getDeliveryTime)).collect(Collectors.toList());
        return result;
    }

    /**
     * 查询西药处方包裹信息
     *
     * @param goodsVO
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> selectPrescriptionPackageList(GoodsVO goodsVO) {
        return busDrugOrderPackageMapper.selectPrescriptionPackageList(goodsVO);
    }

    /**
     * 查询otc药包裹信息
     *
     * @param goodsVO
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> selectOtcPackageList(GoodsVO goodsVO) {
        return busDrugOrderPackageMapper.selectOtcPackageList(goodsVO);
    }

    /**
     * 查询商品包裹信息
     *
     * @param goodsVO
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> selectGoodsPackageList(GoodsVO goodsVO) {
        return busDrugOrderPackageMapper.selectGoodsPackageList(goodsVO);
    }

    /**
     * 查询订单包裹信息
     *
     * @param order
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> selectOrderPackageList(BusOrder order) {
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getPackageType, Integer.valueOf(order.getSubOrderType()));
        lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, order.getSubOrderId());
        return busDrugOrderPackageMapper.selectList(lambdaQuery);
    }

    /**
     * 查询处方生成的包裹集合
     *
     * @param goodsVO
     * @return
     */
    @Override
    public List<BusDrugOrderPackage> selectPackageStatusList(GoodsVO goodsVO) {
        return busDrugOrderPackageMapper.selectPackageStatusList(goodsVO);
    }

    /**
     * 更新总订单表状态
     */
    public int updateBusOrderStatus(BusOrder busOrder) {
        BusOrder order = new BusOrder();
        order.setDeliveryTime(DateUtils.getNowDate());
        if (CodeEnum.NO.getCode().equals(busOrder.getDeliveryType())) {
            order.setOrderStatus(DrugsOrderEnum.TOBEPICKEDUP.getCode());
        } else {
            order.setOrderStatus(DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode());
        }
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getOrderNo, busOrder.getOrderNo());
        return busOrderMapper.update(order, lambdaQuery);
    }

    /**
     * 更新售后订单表状态
     * @param type 订单类型  0药品 1商品
     * @param id  订单id
     */
    public int updateBusOrderAfterSalesStatus(String type, Long id) {
        R<BusOrderVO> busOrderBySubOrderId = remoteBusOrderService.getBusOrderBySubOrderId(id, type);
        if (busOrderBySubOrderId.getCode() == Constants.FAIL) {
            throw new ServiceException(busOrderBySubOrderId.getMsg());
        }
        BusOrderVO busOrder = busOrderBySubOrderId.getData();
        BusOrderAfterSales busOrderAfterSales = new BusOrderAfterSales();
        busOrderAfterSales.setId(busOrder.getAfterSaleId());
        busOrderAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.RESHIP_BUYER_RECEIVES_GOODS.getCode());
        return busOrderAfterSalesMapper.updateById(busOrderAfterSales);
    }

    /**
     * 快递鸟查询
     */
    public JSONArray getKDNDelivery(String deliveryNo) {
        /*查询快递鸟*/
        if (deliveryNo.contains("JT")) {
            throw new ServiceException("暂不支持极兔快递");
        }
        R<String> objectR = remoteKdnService.searchKdInfo(deliveryNo);
        if (!Constants.SUCCESS.equals(objectR.getCode())) {
            throw new ServiceException(objectR.getMsg());
        }
        log.info("发货快递鸟信息objectR ={}",objectR.getData());
        JSONObject jsonObject = JSONObject.parseObject(objectR.getData());
        JSONArray shippers = jsonObject.getJSONArray("Shippers");
        if (null == shippers || shippers.isEmpty()) {
            throw new ServiceException("您的快递单号无物流信息，请检查单号是否有误");
        }
        return shippers;
    }

    /**
     * 保存物流信息
     *
     * @param drugsOrder
     * @param order
     */
    @Override
    public void saveOrderLogistics(BusDrugsOrder drugsOrder, BusOrder order) {
        BusDrugOrderPackage orderPackage = new BusDrugOrderPackage();
        // 患者选择快递发货，保存物流信息
        if (CodeEnum.YES.getCode().equals(drugsOrder.getDeliveryType())) {
            orderPackage.setDeliveryTime(drugsOrder.getDeliveryTime());
            orderPackage.setExpressCode(drugsOrder.getExpressCode());
            orderPackage.setLogisticsCompany(drugsOrder.getLogisticsCompany());
            orderPackage.setDeliveryNo(drugsOrder.getDeliveryNo());
        }
        orderPackage.setOrderNo(order.getOrderNo());
        orderPackage.setDrugsOrderId(drugsOrder.getId());
        LambdaUpdateWrapper<BusDrugOrderPackage> lambdaUpdate = Wrappers.lambdaUpdate();
        lambdaUpdate.eq(BusDrugOrderPackage::getEnterpriseId, drugsOrder.getEnterpriseId());
        if (Objects.nonNull(drugsOrder.getPrescriptionId())) {
            lambdaUpdate.eq(BusDrugOrderPackage::getPrescriptionId, drugsOrder.getPrescriptionId());
        } else {
            lambdaUpdate.eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrder.getId());
        }
        // 订单金额为零且医院后台未修改订单价格的是换货单
        if (order.getRelPrice().equals(0d) && YesNoEnum.NO.getCode().equals(order.getChangePrice())) {
            lambdaUpdate.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
        } else {
            lambdaUpdate.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
        }
        busDrugOrderPackageMapper.update(orderPackage, lambdaUpdate);
    }

    @Override
    public List<BusDrugOrderPackage> queryByOrderId(Long orderId, Integer packageType) {
        return busDrugOrderPackageMapper.selectList(new LambdaQueryWrapper<BusDrugOrderPackage>()
                .eq(BusDrugOrderPackage::getDrugsOrderId, orderId)
                .eq(BusDrugOrderPackage::getPackageType, packageType)
        );
    }

    @Override
    public List<BusDrugOrderPackage> queryByOrderIds(List<Long> orderIds, Integer packageType) {
        if (orderIds.isEmpty()) {
            return Collections.emptyList();
        }
        return busDrugOrderPackageMapper.selectList(new LambdaQueryWrapper<BusDrugOrderPackage>()
                .in(BusDrugOrderPackage::getDrugsOrderId, orderIds)
                .eq(BusDrugOrderPackage::getPackageType, packageType)
        );
    }
}
