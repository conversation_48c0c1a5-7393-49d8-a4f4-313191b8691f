package com.puree.hospital.business.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.model.event.order.DrugsOrderRefuseRefundExchangeEvent;
import com.puree.hospital.app.api.model.event.order.DrugsOrderRefuseRefundReturnEvent;
import com.puree.hospital.app.api.model.event.order.DrugsOrderShippedEvent;
import com.puree.hospital.business.api.RemoteHospitalEnterpriseService;
import com.puree.hospital.business.domain.BusAfterSale;
import com.puree.hospital.business.domain.BusBizDepartment;
import com.puree.hospital.business.domain.BusDoctor;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusDrugs;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusEnterpriseDrugs;
import com.puree.hospital.business.domain.BusEnterpriseDrugsPrice;
import com.puree.hospital.business.domain.BusHospitalEnterprise;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOtcDrugs;
import com.puree.hospital.business.domain.BusPatientFamily;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusPrescriptionDrugs;
import com.puree.hospital.business.domain.dto.BusDrugsOrderDto;
import com.puree.hospital.business.domain.dto.DXRefundCallDto;
import com.puree.hospital.business.domain.dto.DeliveryDTO;
import com.puree.hospital.business.domain.dto.ZXRefundCallDto;
import com.puree.hospital.business.domain.vo.BusDrugsOrderDetailVo;
import com.puree.hospital.business.domain.vo.BusDrugsOrderVo;
import com.puree.hospital.business.domain.vo.DrugsVo;
import com.puree.hospital.business.domain.vo.HospitalVo;
import com.puree.hospital.business.domain.vo.PharmacistInfoVo;
import com.puree.hospital.business.mapper.BusAfterSaleMapper;
import com.puree.hospital.business.mapper.BusBizDepartmentMapper;
import com.puree.hospital.business.mapper.BusDoctorMapper;
import com.puree.hospital.business.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.business.mapper.BusDrugsMapper;
import com.puree.hospital.business.mapper.BusDrugsOrderMapper;
import com.puree.hospital.business.mapper.BusEnterpriseDrugsMapper;
import com.puree.hospital.business.mapper.BusEnterpriseDrugsPriceMapper;
import com.puree.hospital.business.mapper.BusHospitalMapper;
import com.puree.hospital.business.mapper.BusOrderMapper;
import com.puree.hospital.business.mapper.BusOtcDrugsMapper;
import com.puree.hospital.business.mapper.BusPatientFamilyMapper;
import com.puree.hospital.business.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.queue.producer.event.order.DrugsOrderShippedEventProducer;
import com.puree.hospital.business.service.IBusDrugOrderPackageService;
import com.puree.hospital.business.service.IBusDrugsOrderService;
import com.puree.hospital.business.service.IBusHospitalEnterpriseService;
import com.puree.hospital.business.service.IBusOfficinaPharmacistService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.DeliveryTypeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.EnterpriseEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.SexTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.AgeCalculationUtil;
import com.puree.hospital.common.core.utils.BirthUtils;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.DoubleUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.utils.kdn.KDNUtil;
import com.puree.hospital.supplier.api.RemoteDFHService;
import com.puree.hospital.supplier.api.RemoteDXService;
import com.puree.hospital.supplier.api.RemoteGYService;
import com.puree.hospital.supplier.api.RemoteHRService;
import com.puree.hospital.supplier.api.RemoteXXService;
import com.puree.hospital.supplier.api.model.dfh.DFHOrder;
import com.puree.hospital.supplier.api.model.dfh.Item;
import com.puree.hospital.supplier.api.model.dfh.ReturnGoodsOrderQueryDto;
import com.puree.hospital.supplier.api.model.dx.Drug;
import com.puree.hospital.supplier.api.model.dx.Prescription;
import com.puree.hospital.supplier.api.model.dx.PrescriptionDrugInfo;
import com.puree.hospital.supplier.api.model.gy.dto.RecipeDTO;
import com.puree.hospital.supplier.api.model.gy.dto.RecipeItemDTO;
import com.puree.hospital.supplier.api.model.gy.vo.GYResponseVo;
import com.puree.hospital.supplier.api.model.hr.HrDetails;
import com.puree.hospital.supplier.api.model.hr.HrDrug;
import com.puree.hospital.supplier.api.model.hr.HrDrugInfoDTO;
import com.puree.hospital.supplier.api.model.hr.HrOrderDetails;
import com.puree.hospital.supplier.api.model.hr.HrPatients;
import com.puree.hospital.supplier.api.model.hr.HrPrescrOrder;
import com.puree.hospital.supplier.api.model.hr.HrPrescrs;
import com.puree.hospital.supplier.api.model.hr.HrReceiveAddress;
import com.puree.hospital.supplier.api.model.xx.XXDrug;
import com.puree.hospital.supplier.api.model.xx.XXOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeSet;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDrugsOrderServiceImpl implements IBusDrugsOrderService {
    private final BusDrugsOrderMapper busDrugsOrderMapper;
    private final BusOrderMapper busOrderMapper;
    private final BusAfterSaleMapper afterSaleMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusAfterSaleMapper busAfterSaleMapper;
    private final RemoteDFHService remoteDFHService;
    private final RemoteGYService remoteGYService;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final BusHospitalMapper busHospitalMapper;
    private final KDNUtil kdnUtil;
    public final RedisTemplate<String, String> redisTemplate;
    private final BusOtcDrugsMapper busOtcDrugsMapper;
    private final BusDoctorMapper busDoctorMapper;
    private final BusPatientFamilyMapper busPatientFamilyMapper;
    private final BusBizDepartmentMapper busBizDepartmentMapper;
    private final BusEnterpriseDrugsMapper busEnterpriseDrugsMapper;
    private final BusEnterpriseDrugsPriceMapper busEnterpriseDrugsPriceMapper;
    private final BusDrugsMapper busDrugsMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    @Resource
    private RemoteDXService remoteDXService;
    @Resource
    private RemoteXXService remoteXXService;
    private final IBusOfficinaPharmacistService busOfficinaPharmacistService;
    private final IBusDrugOrderPackageService busDrugOrderPackageService;
    private final IBusHospitalEnterpriseService hospitalEnterpriseService;
    private final RemoteHospitalEnterpriseService remoteHosEntService;
    private final RemoteHRService remoteHRService;
    private final ApplicationEventPublisher publisher;

    @Resource
    @Lazy
    private DrugsOrderShippedEventProducer drugsOrderShippedEventProducer;

    @Override
    public List<BusDrugsOrderVo> selectListPrescriptionVo(BusDrugsOrderDto dto) {
        return busDrugsOrderMapper.selectListPrescriptionVo(dto);
    }

    @Override
    public List<BusDrugsOrderVo> selectListBySearch(BusDrugsOrderDto dto) {
        List<BusDrugsOrderVo> busDrugsOrderVos = busDrugsOrderMapper.selectListBySearch(dto);
        if (StringUtils.isNotEmpty(busDrugsOrderVos)) {
            busDrugsOrderVos.forEach(d -> {
                LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, dto.getHospitalId());
                if (StringUtils.isNotNull(d.getPrescriptionId())) {
                    lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, d.getPrescriptionId());
                } else {
                    lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, d.getId());
                }
                if ("0.00".equals(d.getAmount())) {
                    lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
                } else {
                    lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
                }
                // 查询配送企业名称
                List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectList(lambdaQuery);
                StringBuilder enterpriseName = new StringBuilder();
                StringBuilder logisticsCompany = new StringBuilder();
                TreeSet<String> logisticsCompanySet = new TreeSet<>();
                for (BusDrugOrderPackage orderPackage : busDrugOrderPackages) {
                    if (enterpriseName.length() > 0) {
                        enterpriseName.append(",").append(orderPackage.getEnterpriseName());
                    } else {
                        enterpriseName.append(orderPackage.getEnterpriseName());
                    }
                    if (StringUtils.isNotEmpty(orderPackage.getLogisticsCompany())) {
                        logisticsCompanySet.add(orderPackage.getLogisticsCompany());
                    }
                }
                for (String s : logisticsCompanySet) {
                    if (logisticsCompany.length() > 0) {
                        logisticsCompany.append(",").append(s);
                    } else {
                        logisticsCompany.append(s);
                    }
                }
                d.setEnterpriseName(enterpriseName.toString());
                d.setLogisticsCompany(logisticsCompany.toString());
            });
        }
        return busDrugsOrderVos;
    }

    @Override
    public BusDrugsOrderDetailVo selectDetail(BusDrugsOrderDto dto) {
        BusDrugsOrderDetailVo busDrugsOrderDetailVo = busDrugsOrderMapper.selectDetail(dto);
        List<BusDrugOrderPackage> packages;
        LambdaQueryWrapper<BusDrugOrderPackage> query = Wrappers.lambdaQuery();
        query.eq(BusDrugOrderPackage::getHospitalId, dto.getHospitalId());
        if (StringUtils.isNotNull(busDrugsOrderDetailVo.getPrescriptionId())) {
            // 查询处方药品信息
            List<BusPrescriptionDrugs> busPrescriptionDrugs =
                    busPrescriptionDrugsMapper.selectPdList(busDrugsOrderDetailVo.getPrescriptionId());
            busDrugsOrderDetailVo.setPrescriptionDrugsList(busPrescriptionDrugs);
            query.eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrderDetailVo.getPrescriptionId());
        } else {
            // 查询非处方药品信息
            LambdaQueryWrapper<BusOtcDrugs> queryWrapper = new LambdaQueryWrapper<BusOtcDrugs>()
                    .eq(BusOtcDrugs::getDrugsOrderId, busDrugsOrderDetailVo.getId());
            List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectList(queryWrapper);
            List<BusPrescriptionDrugs> drugsList = OrikaUtils.converts(busOtcDrugs, BusPrescriptionDrugs.class);
            busDrugsOrderDetailVo.setPrescriptionDrugsList(drugsList);
            query.eq(BusDrugOrderPackage::getDrugsOrderId, dto.getId());
        }
        // 换货生成的订单
        if (busDrugsOrderDetailVo.getAmount().compareTo(new BigDecimal(0)) == 0) {
            query.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
        } else {
            query.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
        }
        query.orderByAsc(BusDrugOrderPackage::getDeliveryTime);
        packages = busDrugOrderPackageMapper.selectList(query);
        boolean flag = true;
        StringBuilder enterpriseName = new StringBuilder();
        for (BusDrugOrderPackage p : packages) {
            if (flag) {
                // 药房未发货设置标识返回
                if (StringUtils.isNull(p.getEnterpriseId()) && StringUtils.isEmpty(p.getDeliveryNo())) {
                    busDrugsOrderDetailVo.setPharmacyDeliverStatus(YesNoEnum.NO.getCode());
                    flag = false;
                }
            }
            // 设置配送企业名称
            if (enterpriseName.length() > 0) {
                enterpriseName.append(",").append(p.getEnterpriseName());
            } else {
                enterpriseName.append(p.getEnterpriseName());
            }
        }
        busDrugsOrderDetailVo.setEnterpriseName(enterpriseName.toString());
        List<BusDrugOrderPackage> deliverPackages = packages.stream().filter(f -> f.getDeliveryNo() != null).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(deliverPackages)) {
            busDrugsOrderDetailVo.setDeliveryTime(deliverPackages.get(0).getDeliveryTime());
        }
        // 查询医院信息
        HospitalVo hospitalVo = busHospitalMapper.selectHospitalTel(busDrugsOrderDetailVo.getHospitalId());
        busDrugsOrderDetailVo.setProvince(hospitalVo.getProvince());
        busDrugsOrderDetailVo.setCity(hospitalVo.getCity());
        busDrugsOrderDetailVo.setArea(hospitalVo.getArea());
        busDrugsOrderDetailVo.setPackages(deliverPackages);
        return busDrugsOrderDetailVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delivery(DeliveryDTO dto) {
        BusDrugsOrder drugsOrder = OrikaUtils.convert(dto, BusDrugsOrder.class);
        //加入消息队列
        addQueue(drugsOrder.getId(), drugsOrder.getHospitalId());
        // 查询药品订单信息
        BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(drugsOrder.getId());
        // 发送药品订单发货事件
        sendDrugsOrderShippedEventNotification(busDrugsOrder);
        // 查询包裹信息
        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, busDrugsOrder.getHospitalId());
        if (StringUtils.isNotNull(busDrugsOrder.getPrescriptionId())) {
            lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, busDrugsOrder.getPrescriptionId());
        } else {
            lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, busDrugsOrder.getId());
        }
        if ("0.00".equals(busDrugsOrder.getAmount())) {
            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
        } else {
            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
        }
        List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectList(lambdaQuery);
        if (busDrugOrderPackages.size() > 1) {
            List<String> deliverNos =
                    busDrugOrderPackages.stream().filter(f -> f.getDeliveryNo() != null).map(BusDrugOrderPackage::getDeliveryNo).collect(Collectors.toList());
            if (busDrugOrderPackages.size() - deliverNos.size() == 1) {
                drugsOrder.setStatus(DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode());
            } else {
                drugsOrder.setStatus(DrugsOrderEnum.PART_DELIVERY.getCode());
            }
        } else {
            drugsOrder.setStatus(DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode());
        }
        if (StringUtils.isNotNull(busDrugsOrder.getPrescriptionId())) {
            String username = SecurityUtils.getUsername();
            PharmacistInfoVo pharmacistInfo = busOfficinaPharmacistService.getPharmacistInfo(username, dto.getRoleType());
            if (StringUtils.isNotNull(pharmacistInfo)) {
                // 修改处方发货药师
                BusPrescription busPrescription = new BusPrescription();
                busPrescription.setId(busDrugsOrder.getPrescriptionId());
                busPrescription.setDeliveryPharmacistId(pharmacistInfo.getPharmacistId());
                busPrescription.setDeliveryPharmacistName(pharmacistInfo.getPharmacistName());
                busPrescriptionMapper.updateById(busPrescription);
            }
        }
        // 保存物流信息
        drugsOrder.setPrescriptionId(busDrugsOrder.getPrescriptionId());
        saveOrderLogistics(drugsOrder);
        return busDrugsOrderMapper.delivery(drugsOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int regenerateOrder(BusDrugsOrder drugsOrder) {
        // 修改旧药品订单为售后完成
        BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
        busDrugsOrder.setUpdateBy(SecurityUtils.getUsername());
        busDrugsOrder.setUpdateTime(DateUtils.getNowDate());
        busDrugsOrder.setCompleteTime(DateUtils.getNowDate());
        busDrugsOrder.setStatus(DrugsOrderEnum.AFTERSALES_COMPLETION.getCode());
        UpdateWrapper<BusDrugsOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_no", drugsOrder.getOrderNo());
        busDrugsOrderMapper.update(busDrugsOrder, updateWrapper);
        // 查询订单信息
        BusDrugsOrder oldOrder = busDrugsOrderMapper.selectOne(updateWrapper);

        //更新退换信息
        BusAfterSale sale = new BusAfterSale();
        //退换成功
        sale.setStatus("4");
        sale.setUpdateTime(DateUtils.getNowDate());
        UpdateWrapper<BusAfterSale> saleUpdateWrapper = new UpdateWrapper<>();
        saleUpdateWrapper.eq("order_no", drugsOrder.getOrderNo());
        afterSaleMapper.update(sale, saleUpdateWrapper);
        // 新增新药品订单
        if (CodeEnum.NO.getCode().equals(drugsOrder.getDeliveryType())) {
            drugsOrder.setStatus(DrugsOrderEnum.TOBEPICKEDUP.getCode());
        } else {
            drugsOrder.setStatus(DrugsOrderEnum.TO_BE_SHIPPED.getCode());
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        drugsOrder.setOrderNo(OrderTypeConstant.DRUGS_ORDER + simpleDateFormat.format(new Date()));
        drugsOrder.setAmount("0");
        drugsOrder.setOrderTime(DateUtils.getNowDate());
        drugsOrder.setCreateBy(SecurityUtils.getUsername());
        drugsOrder.setCreateTime(DateUtils.getNowDate());
        drugsOrder.setOrderClassify(oldOrder.getOrderClassify());
        drugsOrder.setProvince(oldOrder.getProvince());
        drugsOrder.setCity(oldOrder.getCity());
        drugsOrder.setArea(oldOrder.getArea());
        drugsOrder.setDetailedAddress(oldOrder.getDetailedAddress());
        drugsOrder.setLogisticsCompany(null);
        drugsOrder.setDeliveryNo(null);
        drugsOrder.setDeliveryTime(null);
        drugsOrder.setExpressCode(null);
        busDrugsOrderMapper.insert(drugsOrder);

        // 西药
        if (CodeEnum.YES.getCode().equals(oldOrder.getOrderDrugsType())) {
            // 查询药品信息
            LambdaQueryWrapper<BusOtcDrugs> wrapper = new LambdaQueryWrapper<BusOtcDrugs>()
                    .eq(BusOtcDrugs::getHospitalId, oldOrder.getHospitalId())
                    .eq(BusOtcDrugs::getDrugsOrderId, oldOrder.getId());
            List<BusOtcDrugs> busOtcDrugs = busOtcDrugsMapper.selectList(wrapper);
            if (StringUtils.isNotEmpty(busOtcDrugs)) {
                busOtcDrugs.forEach(d -> {
                    d.setDrugsOrderId(drugsOrder.getId());
                    d.setCreateTime(DateUtils.getNowDate());
                });
            }
        }
        // 保存包裹信息
        List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectPdList(oldOrder);
        if (StringUtils.isNotEmpty(busDrugOrderPackages)) {
            for (BusDrugOrderPackage orderPackage : busDrugOrderPackages) {
                orderPackage.setCreateTime(DateUtils.getNowDate());
                orderPackage.setBarterOrNot(YesNoEnum.YES.getCode());
                orderPackage.setDeliveryNo(null);
                orderPackage.setLogisticsCompany(null);
                orderPackage.setExpressCode(null);
                orderPackage.setDeliveryTime(null);
                busDrugOrderPackageMapper.insert(orderPackage);
                String identifying = orderPackage.getIdentifying();
                if (StringUtils.isNotEmpty(identifying)) {
                    BusPrescription busPrescription;
                    Long prescriptionId = orderPackage.getPrescriptionId();
                    if (StringUtils.isNotNull(prescriptionId)) {
                        // 查询处方信息
                        busPrescription = busPrescriptionMapper.selectById(prescriptionId);
                        // 处方药品信息
                        LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusPrescriptionDrugs::getHospitalId, drugsOrder.getHospitalId());
                        lambdaQuery.eq(BusPrescriptionDrugs::getEnterpriseId, orderPackage.getEnterpriseId());
                        lambdaQuery.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
                        List<BusPrescriptionDrugs> pdDrugsList = busPrescriptionDrugsMapper.selectList(lambdaQuery);
                        busPrescription.setPdDrugsList(pdDrugsList);
                    } else {
                        busPrescription = new BusPrescription();
                        busPrescription.setPrescriptionNumber(drugsOrder.getOrderNo());
                        busPrescription.setPrescriptionAmount(new BigDecimal(drugsOrder.getAmount()));
                        // 非处方药品信息
                        LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusOtcDrugs::getHospitalId, drugsOrder.getHospitalId());
                        lambdaQuery.eq(BusOtcDrugs::getEnterpriseId, orderPackage.getEnterpriseId());
                        lambdaQuery.eq(BusOtcDrugs::getDrugsOrderId, drugsOrder.getId());
                        lambdaQuery.eq(BusOtcDrugs::getOtcOrNot, YesNoEnum.YES.getCode());
                        List<BusOtcDrugs> busOtcDrugsList = busOtcDrugsMapper.selectList(lambdaQuery);
                        busPrescription.setOtcDrugsList(busOtcDrugsList);
                    }
                    busPrescription.setEnterpriseId(orderPackage.getEnterpriseId());
                    if (EnterpriseEnum.GYJT.getInfo().equals(identifying)) {
                        // 推送订单到广药集团
                        saveGYJTOrder(drugsOrder, busPrescription);
                    } else if (EnterpriseEnum.SST.getInfo().equals(identifying)) {
                        // 推送订单到盛世通
                        saveSSTOrder(drugsOrder);
                    } else if (EnterpriseEnum.DX.getInfo().equals(identifying)) {
                        // 推送订单到大翔
                        saveDXOrder(drugsOrder, busPrescription);
                    } else if (EnterpriseEnum.XX.getInfo().equals(identifying)) {
                        // 推送订单到香雪
                        saveXXOrder(drugsOrder, busPrescription);
                    } else if (EnterpriseEnum.HR.getInfo().equals(identifying)) {
                        // 推送订单到华润
                        saveHROrder(drugsOrder, busPrescription);
                    }
                }
            }
        }

        // 将药品订单插入全部订单
        BusOrder order = new BusOrder();
        order.setOrderTime(drugsOrder.getOrderTime());
        order.setSubOrderId(drugsOrder.getId());
        order.setSubOrderType(OrderTypeEnum.DRUGS.getCode());
        order.setCreateBy(drugsOrder.getCreateBy());
        order.setCreateTime(drugsOrder.getCreateTime());
        return busOrderMapper.insert(order);
    }

    private void saveHROrder(BusDrugsOrder order, BusPrescription prescription) {
        List<BusPrescriptionDrugs> rxDrugs;
        R<Map<String, String>> entConfigResult = remoteHosEntService.getByHosIdAndEntCode(order.getHospitalId(), EnterpriseEnum.HR.getInfo());
        log.info("远程调用查询华润配送企业配置信息返回：" + JSON.toJSONString(entConfigResult));
        if (!Constants.SUCCESS.equals(entConfigResult.getCode())) {
            log.error("华润处方推送失败：" + entConfigResult.getMsg());
        }
        HrPrescrOrder prescrOrder = new HrPrescrOrder();
        HrReceiveAddress receiveAddress = new HrReceiveAddress();
        List<HrPatients> patients = new ArrayList<>();
        List<HrPrescrs> prescrs = new ArrayList<>();
        HrPrescrs prescr = new HrPrescrs();
        HrPatients patient = new HrPatients();
        Map<String, String> entConfig;
        prescrs.add(prescr);
        if (StringUtils.isNotNull(entConfigResult.getData())) {
            entConfig = entConfigResult.getData();
            prescrOrder.setUserName(entConfig.get("userName"));
            prescrOrder.setPassword(entConfig.get("password"));
            prescrOrder.setStoreId(entConfig.get("storeId"));
        } else {
            log.error("华润处方推送失败：读取配置信息为空");
            throw new ServiceException("配送企业信息缺失");
        }
        String orderDate = DateUtils.parseDateToStr(DateUtils.T_DATE, new Date());
        prescrOrder.setOrderDate(orderDate);
        prescrOrder.setOrderId(order.getOrderNo());
        // 处方单
        if (StringUtils.isNotNull(prescription.getId())) {
            // 患者信息
            patient.setPatientId(prescription.getFamilyId() + "");
            patient.setBirthday(BirthUtils.calculateBirthday(prescription.getFamilyIdcard()));
            patient.setIdentityNo(prescription.getFamilyIdcard());
            patient.setName(prescription.getFamilyName());
            patient.setSex(prescription.getFamilySex().equals(SexTypeEnum.WOMAN.getCode()) ? 1 : 0);
            patients.add(patient);
            prescrOrder.setPatients(patients);
            // 处方详情
            String prescDate = DateUtils.parseDateToStr(DateUtils.T_DATE, prescription.getPrescriptionTime());
            prescr.setBuyerMobile(order.getReceivingTel());
            prescr.setDepartmentName(prescription.getDepartmentName());
            prescr.setHospitalId(entConfig.get("hospitalId"));
            prescr.setHospitalName(entConfig.get("hospitalName"));
            prescr.setPatientId(prescription.getFamilyId() + "");
            prescr.setPrescrDate(prescDate);
            prescr.setPrescrNo(prescription.getPrescriptionNumber());
            prescr.setReviewerName(prescription.getReviewPharmacistName());
            rxDrugs = prescription.getPdDrugsList();
        } else {
            // 患者信息
            patient.setPatientId("0");
            patient.setBirthday("1999-09-09");
            patient.setIdentityNo("无");
            patient.setIdentityType(0);
            patient.setName(order.getReceivingUser());
            patient.setSex(2);
            patients.add(patient);
            prescrOrder.setPatients(patients);
            // 处方详情
            prescr.setBuyerMobile(order.getReceivingTel());
            prescr.setHospitalId(entConfig.get("hospitalId"));
            prescr.setHospitalName(entConfig.get("hospitalName"));
            prescr.setPatientId("0");
            prescr.setPrescrDate(orderDate);
            prescr.setPrescrNo(prescription.getPrescriptionNumber());
            rxDrugs = OrikaUtils.converts(prescription.getOtcDrugsList(), BusPrescriptionDrugs.class);
        }
        // 收货地址
        receiveAddress.setReceiver(order.getReceivingUser());
        receiveAddress.setPhone(order.getReceivingTel());
        receiveAddress.setProvince(order.getProvince());
        receiveAddress.setCity(order.getCity());
        receiveAddress.setDistrict(order.getArea());
        receiveAddress.setDetail(order.getDetailedAddress());
        prescrOrder.setReceiveAddress(receiveAddress);
        // 查询运营的配送方药品信息
        List<Long> rxDrugsId = rxDrugs.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList());
        List<BusEnterpriseDrugs> entDrugs = busEnterpriseDrugsMapper.selectList(new LambdaQueryWrapper<BusEnterpriseDrugs>()
                .eq(BusEnterpriseDrugs::getEnterpriseId, prescription.getEnterpriseId())
                .in(BusEnterpriseDrugs::getDrugsId, rxDrugsId));
        List<String> entProductIds = entDrugs.stream().map(BusEnterpriseDrugs::getEnterpriseDrugsId).collect(Collectors.toList());
        if (rxDrugsId.size() != entProductIds.size()) {
            log.error("处方内有配送企业药品缺失");
        }
        // 实时查价格
        HrDrugInfoDTO dto = new HrDrugInfoDTO();
        dto.setHospitalId(entConfig.get("hospitalId"));
        dto.setStoreId(entConfig.get("storeId"));
        dto.setPassword(entConfig.get("password"));
        dto.setUserName(entConfig.get("userName"));
        dto.setProductId(entProductIds);
        dto.setBusHospitalId(order.getHospitalId());
        R<List<HrDrug>> drugInfoResult = remoteHRService.getDrugInfo(dto);
        log.info("远程调用查询华润药品目录返回：" + JSON.toJSONString(drugInfoResult));
        if (!Constants.SUCCESS.equals(drugInfoResult.getCode())
                || drugInfoResult.getData().isEmpty()) {
            log.error("华润处方推送失败：查询华润药品目录失败");
        }
        List<HrDrug> entProducts = drugInfoResult.getData();
        // 处方药品信息
        List<HrDetails> hrDetails = new ArrayList<>();
        List<HrOrderDetails> hrOrderDetails = new ArrayList<>();
        BigDecimal totalAmount = new BigDecimal(0);
        for (BusPrescriptionDrugs rxDrug : rxDrugs) {
            HrDetails details = new HrDetails();
            HrOrderDetails orderDetails = new HrOrderDetails();
            if (StringUtils.isNotEmpty(rxDrug.getUnit()) && rxDrug.getUnit().contains("/次")) {
                rxDrug.setUnit(rxDrug.getUnit().substring(0, rxDrug.getUnit().indexOf("/次")));
            }
            details.setCommonName(rxDrug.getStandardCommonName());
            details.setMedicineName(rxDrug.getDrugsName());
            details.setProducer(rxDrug.getDrugsManufacturer());
            if (StringUtils.isNotNull(rxDrug.getMedicationDays())) {
                details.setDays(rxDrug.getMedicationDays());
            }
            if (StringUtils.isNotEmpty(rxDrug.getMedicationFrequency())) {
                details.setFrequency(rxDrug.getMedicationFrequency());
            }
            if (StringUtils.isNotEmpty(rxDrug.getMedicationFrequencyRemarks())) {
                details.setFrequencyDescription(rxDrug.getMedicationFrequencyRemarks());
            }
            if (StringUtils.isNotNull(rxDrug.getSeveralTimesDay())) {
                details.setFrequencyQuantity(rxDrug.getSeveralTimesDay());
            }
            if (StringUtils.isNotNull(rxDrug.getSingleDose())) {
                details.setPerDosageQuantity(rxDrug.getSingleDose());
            }
            if (StringUtils.isNotEmpty(rxDrug.getUnit())) {
                details.setPerDosageUnit(rxDrug.getUnit());
            }
            if (StringUtils.isNotEmpty(rxDrug.getUsages())) {
                details.setPerDosageUnit(rxDrug.getUsages());
            }
            if (StringUtils.isNotEmpty(rxDrug.getUnit())) {
                details.setPerDosageUnit(rxDrug.getUnit());
            }
            details.setQuantity(rxDrug.getQuantity());
            details.setSpecs(rxDrug.getDrugsSpecification());
            orderDetails.setCommonName(rxDrug.getStandardCommonName());
            orderDetails.setMedicineName(rxDrug.getDrugsName());
            orderDetails.setProducer(rxDrug.getDrugsManufacturer());
            orderDetails.setQuantity(rxDrug.getQuantity());
            orderDetails.setSpecs(rxDrug.getDrugsSpecification());
            for (BusEnterpriseDrugs entDrug : entDrugs) {
                if (entDrug.getDrugsId().equals(rxDrug.getDrugsId())) {
                    for (HrDrug entProduct : entProducts) {
                        if (entProduct.getProductId().equals(entDrug.getEnterpriseDrugsId())) {
                            details.setProductId(entDrug.getEnterpriseDrugsId());
                            details.setPrice(entProduct.getPrice());
                            details.setUnit(entProduct.getUnit());
                            orderDetails.setProductId(entDrug.getEnterpriseDrugsId());
                            orderDetails.setPrice(entProduct.getPrice());
                            orderDetails.setUnit(entProduct.getUnit());
                        }
                    }
                }
            }
            orderDetails.setAmount(rxDrug.getQuantity() * orderDetails.getPrice());
            totalAmount = totalAmount.add(BigDecimal.valueOf(orderDetails.getAmount()));
            hrDetails.add(details);
            hrOrderDetails.add(orderDetails);
        }
        prescrOrder.setAmount(totalAmount.doubleValue());
        prescr.setPrescrAmount(totalAmount.doubleValue());
        prescr.setDetails(hrDetails);
        prescrOrder.setPrescrs(prescrs);
        prescrOrder.setDetails(hrOrderDetails);
        R<JSONObject> pushResult = remoteHRService.uploadRecipe(prescrOrder);
        log.info("远程调用华润处方推单返回：{}", JSON.toJSONString(pushResult));
        if (!Constants.SUCCESS.equals(pushResult.getCode())) {
            log.error("远程调用华润处方推单失败：" + pushResult.getMsg());
        }
    }

    private void saveXXOrder(BusDrugsOrder drugsOrder, BusPrescription prescription) {
        XXOrder xxOrder = new XXOrder();
        xxOrder.setDelnum(drugsOrder.getOrderNo());
        xxOrder.setOrderType("1");
        xxOrder.setConsultcost(0);
        xxOrder.setName(prescription.getFamilyName());
        // 查询就诊人信息
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(prescription.getFamilyId());
        xxOrder.setAge(AgeCalculationUtil.getAge(busPatientFamily.getDateOfBirth()));
        xxOrder.setGender(prescription.getFamilySex().equals("1") ? 1 : 2);
        xxOrder.setPhone(prescription.getFamilyTel());
        xxOrder.setAddress(drugsOrder.getReceivingAddress());
        xxOrder.setShrtel(drugsOrder.getReceivingTel());
        xxOrder.setShrname(drugsOrder.getReceivingUser());
        xxOrder.setLogisticscost(new BigDecimal(0));
        StringBuilder tcmDiagnosis = new StringBuilder();
        String jsonString = prescription.getClinicalDiagnosis();
        JSONArray jsonArray = JSONObject.parseArray(jsonString);
        for (int i = 0; i < jsonArray.size(); i++) {
            tcmDiagnosis.append(jsonArray.getJSONObject(i).getString("tcmDiagnosis"));
        }
        xxOrder.setDiagresult(tcmDiagnosis.toString());
        String[] split = prescription.getUsages().split(",");
        xxOrder.setDose(Integer.parseInt(split[1]));
        xxOrder.setTakenum(Integer.parseInt(split[2]) * Integer.parseInt(split[3]));
        xxOrder.setYizhu(prescription.getRemark());
        xxOrder.setDecmothed(0);
        xxOrder.setDrugmakeff(getDrugmakeff(Integer.valueOf(prescription.getProcessingMethod())));
        xxOrder.setTakeway(4);
        xxOrder.setTakemethod(split[0].equals("0") ? "内服" : "外用");
        xxOrder.setDoctorName(prescription.getDoctorName());
        // 查询医生电话
        BusDoctor doctor = busDoctorMapper.selectById(prescription.getDoctorId());
        xxOrder.setDoctorTel(DESUtil.decrypt(doctor.getPhoneNumber()));
//        xxOrder.setHospitalName(prescription.getHospitalName());
        xxOrder.setHospitalName("广东省网络医院");
        xxOrder.setYgOrderCost(new BigDecimal(0));
        xxOrder.setPackagenum(50);
        // 药品信息
        List<BusPrescriptionDrugs> busPrescriptionDrugs = prescription.getPdDrugsList();
        for (BusPrescriptionDrugs drug : busPrescriptionDrugs) {
            BusDrugs busDrugs = busDrugsMapper.selectById(drug.getDrugsId());
            drug.setDrugsDosageForm(busDrugs.getDrugsDosageForm());
        }

        List<XXDrug> drugList = new ArrayList<>();
        busPrescriptionDrugs.forEach(pd -> {
            // 查询配送方药品信息
            BusEnterpriseDrugsPrice drugsInfo = busEnterpriseDrugsPriceMapper.getDrugsInfoByDrugsId(drugsOrder.getHospitalId(), pd.getEnterpriseId(), pd.getDrugsId());
            if (Objects.isNull(drugsInfo)) {
                throw new ServiceException("未录入该药材！");
            }
            XXDrug xiangXueDrug = new XXDrug();
            xiangXueDrug.setId(drugsInfo.getEnterpriseDrugsId());
            xiangXueDrug.setCode(drugsInfo.getEnterpriseDrugsId());
            xiangXueDrug.setName(pd.getDrugsName());
            xiangXueDrug.setWeight(new BigDecimal(pd.getWeight()));
            xiangXueDrug.setSpecialmake("");
            xiangXueDrug.setUnit("g");
            xiangXueDrug.setYgprice(drugsInfo.getReferencePurchasePrice());
            drugList.add(xiangXueDrug);
        });
        xxOrder.setDrugList(drugList);
        R<JSONObject> r = remoteXXService.insertOrder(xxOrder);
        log.info("香雪创建订单远程调用结果：{}", JSON.toJSONString(r));
        // 保存返回的订单号，用于查询物流信息
        if (r.getCode() != 200) {
            throw new ServiceException("远程调用香雪新增订单失败！");
        }
        JSONObject data = r.getData();
        if (StringUtils.isNotNull(data)) {
            int code = data.getIntValue("code");
            if (YesNoEnum.NO.getCode() == code) {
                JSONObject jsonObject = data.getJSONObject("data");
                String orderNumbr = jsonObject.getString("orderNumber");
                log.info("香雪单号=" + orderNumbr);
                BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
                busDrugsOrder.setId(drugsOrder.getId());
                busDrugsOrder.setXxOrderNumber(orderNumbr);
                int i = busDrugsOrderMapper.updateById(busDrugsOrder);
                log.info("修改订单是否成功=" + i);
            } else {
                throw new ServiceException("调用香雪新增订单失败！");
            }
        }
    }

    private String getDrugmakeff(Integer type) {
        switch (type) {
            case 0:
                return "0";
            case 1:
                return "1";
            case 9:
                return "2";
            case 10:
                return "4";
            case 3:
                return "5";
            case 4:
                return "31";
            case 6:
                return "33";
            default:
                return "-1";
        }
    }

    private void saveDXOrder(BusDrugsOrder drugsOrder, BusPrescription busPrescription) {
        // 获取药品信息
        List<BusPrescriptionDrugs> prescriptionDrugs = busPrescription.getPdDrugsList();
        // 调用大翔传入处方接口
        PrescriptionDrugInfo prescriptionDrugInfo = new PrescriptionDrugInfo();
        List<Prescription> prescriptions = new ArrayList<>();
        Prescription prescription = new Prescription();
        BusHospitalEnterprise hosEntConfig = hospitalEnterpriseService.getByHosIdAndEntId(drugsOrder.getHospitalId(), busPrescription.getEnterpriseId());
        log.info("查询配送企业配置信息返回：" + JSON.toJSONString(hosEntConfig));
        if (StringUtils.isNotNull(hosEntConfig) && StringUtils.isNotEmpty(hosEntConfig.getConfig())) {
            List<JSONObject> configs = JSONObject.parseArray(hosEntConfig.getConfig(), JSONObject.class);
            for (JSONObject config : configs) {
                if ("hospitalName".equals(config.get("msgKey"))) {
                    prescription.setHospital_name(config.getString("msgValue"));
                    break;
                }
            }
        } else {
            log.error("大翔推单失败，读取配置信息为空");
            throw new ServiceException("大翔推单失败，读取配置信息为空");
        }
        prescription.setPspnum(drugsOrder.getOrderNo());
        prescription.setName(busPrescription.getFamilyName());
        if (busPrescription.getFamilySex().equals(CodeEnum.NO.getCode())) {
            prescription.setSex(2);
        } else {
            prescription.setSex(1);
        }
        prescription.setPhone(busPrescription.getFamilyTel());
        prescription.setAddress(drugsOrder.getDetailedAddress());
        prescription.setDepartment(busPrescription.getDepartmentName());
        StringBuilder tcmDiagnosis = new StringBuilder();
        String jsonString = busPrescription.getClinicalDiagnosis();
        JSONArray jsonArray = JSONObject.parseArray(jsonString);
        for (int i = 0; i < jsonArray.size(); i++) {
            tcmDiagnosis.append(jsonArray.getJSONObject(i).getString("tcmDiagnosis"));
        }
        prescription.setDiagresult(tcmDiagnosis.toString());
        String[] split = busPrescription.getUsages().split(",");
        prescription.setDose(Integer.parseInt(split[1]));
        prescription.setTakenum(Integer.parseInt(split[2]) * Integer.parseInt(split[3]));
        prescription.setGetdrugtime("dhySYSTEM");
        prescription.setDecscheme(3);
        prescription.setOncetime("dhySYSTEM");
        prescription.setTwicetime("dhySYSTEM");
        prescription.setDoctor(busPrescription.getDoctorName());
        // 查询就诊人出生日期
        QueryWrapper<BusPatientFamily> queryWrapper4 = new QueryWrapper<>();
        queryWrapper4
                .eq("patient_id", busPrescription.getPatientId())
                .eq("id", busPrescription.getFamilyId());
        BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectOne(queryWrapper4);
        int age = AgeCalculationUtil.getAge(busPatientFamily.getDateOfBirth());
        if (age > 16) {
            prescription.setPackagenum(200);
        } else if (age > 12) {
            prescription.setPackagenum(150);
        } else if (age > 3) {
            prescription.setPackagenum(100);
        } else {
            prescription.setPackagenum(50);
        }
        prescription.setAge(String.valueOf(age));
        prescription.setDotime("dhySYSTEM");
        // 自提
        if (CodeEnum.NO.getCode().equals(drugsOrder.getDeliveryType())) {
            prescription.setDoperson(busPrescription.getHospitalName());
            prescription.setDtbcompany(busPrescription.getHospitalName());
            prescription.setDtbaddress(busPrescription.getHospitalAddress());
            HospitalVo hospitalVo = busDrugsOrderMapper.selectHospitalTel(drugsOrder.getHospitalId());
            prescription.setDtbphone(DESUtil.decrypt(hospitalVo.getHospitalPhone()));
        } else { // 快递
            prescription.setDoperson(drugsOrder.getReceivingUser());
            prescription.setDtbcompany(drugsOrder.getReceivingUser());
            prescription.setDtbaddress(drugsOrder.getReceivingAddress());
            prescription.setDtbphone(drugsOrder.getReceivingTel());
        }
        prescription.setDtbtype(1);
        prescription.setSoakwater("dhySYSTEM");
        prescription.setSoaktime(30);
        prescription.setOrdertime("dhySYSTEM");
        prescription.setDecmothed("dhySYSTEM");
        prescription.setTakeway("dhySYSTEM");
        prescription.setTakemethod(split[0].equals("0") ? "内服" : "外用");
        prescription.setDrug_count(prescriptionDrugs.size());
        if ("0".equals(busPrescription.getProcessingMethod())) {
            prescription.setIsDaijian("0"); // 不代煎
            int abs = Math.abs(prescription.getDose() - 2);
            prescription.setLabelnum(String.valueOf(abs));
        } else if ("3".equals(busPrescription.getProcessingMethod())) {
            prescription.setIsDaijian("5"); // 膏方
            prescription.setLabelnum(String.valueOf(prescription.getTakenum() * prescription.getDose()));
        } else {
            prescription.setIsDaijian("dhySYSTEM"); // 代煎
            prescription.setLabelnum(String.valueOf(prescription.getTakenum() * prescription.getDose()));
        }
        prescription.setPayment(CodeEnum.YES.getCode().equals(drugsOrder.getPayWay()) ? "微信支付" : "通联支付");
        prescription.setYizhu(busPrescription.getRemark());
        prescription.setMoney(drugsOrder.getAmount());
        prescription.setPtype("dhySYSTEM");
        prescriptions.add(prescription);
        prescriptionDrugInfo.setPrescription(prescriptions);
        List<Drug> drugs = new ArrayList<>();
        for (BusPrescriptionDrugs p : prescriptionDrugs) {
            QueryWrapper<BusEnterpriseDrugs> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2
                    .eq("enterprise_id", busPrescription.getEnterpriseId())
                    .eq("drugs_id", p.getDrugsId());
            BusEnterpriseDrugs busEnterpriseDrugs = busEnterpriseDrugsMapper.selectOne(queryWrapper2);
            Drug drug = new Drug();
            drug.setDrugnum(busEnterpriseDrugs.getEnterpriseDrugsId());
            drug.setDrugname(p.getDrugsName());
            drug.setDrugposition("g");
            drug.setDrugallnum(p.getWeight());
            drug.setTienum(split[1]);
            drug.setDrugweight(DoubleUtils.mul(Double.valueOf(split[1]), Double.valueOf(p.getWeight())).toString());
            drug.setUnit("g");
            drugs.add(drug);
        }
        prescriptionDrugInfo.setDrug(drugs);
        R<Boolean> objectR = remoteDXService.addPrescription(prescriptionDrugInfo);
        //Boolean data = (Boolean) objectR.getData();
        Boolean data = objectR.getData();
        log.info("处方是否推送成功:{}", data);
        if (!data) {
            throw new ServiceException("调用大翔推送处方失败！");
        }
    }

    private void saveGYJTOrder(BusDrugsOrder drugsOrder, BusPrescription prescription) {
        List<BusPrescriptionDrugs> busPrescriptionDrugs;
        RecipeDTO dto = new RecipeDTO();
        dto.setRecipeno(drugsOrder.getOrderNo());
        dto.setDoctorAnswerNo(drugsOrder.getOrderNo());
        BusHospitalEnterprise hosEntConfig = hospitalEnterpriseService.getByHosIdAndEntId(drugsOrder.getHospitalId(), prescription.getEnterpriseId());
        log.info("查询配送企业配置信息返回：" + JSON.toJSONString(hosEntConfig));
        if (StringUtils.isNotNull(hosEntConfig) && StringUtils.isNotEmpty(hosEntConfig.getConfig())) {
            List<JSONObject> configs = JSONObject.parseArray(hosEntConfig.getConfig(), JSONObject.class);
            for (JSONObject config : configs) {
                if ("hospitalId".equals(config.get("msgKey"))) {
                    dto.setHospitalid(config.getString("msgValue"));
                } else if ("storeId".equals(config.get("msgKey"))) {
                    dto.setStoreid(config.getString("msgValue"));
                } else if ("platformType".equals(config.get("msgKey"))) {
                    dto.setPlatformtype(config.getString("msgValue"));
                } else if ("appId".equals(config.get("msgKey"))) {
                    dto.setAppId(config.getString("msgValue"));
                } else if ("appKey".equals(config.get("msgKey"))) {
                    dto.setAppKey(config.getString("msgValue"));
                } else if ("privateKey".equals(config.get("msgKey"))) {
                    dto.setPrivateKey(config.getString("msgValue"));
                } else if ("publicKey".equals(config.get("msgKey"))) {
                    dto.setPublicKey(config.getString("msgValue"));
                }
            }
        } else {
            log.error("广药推单失败，读取配置信息为空");
            throw new ServiceException("广药推单失败，读取配置信息为空");
        }
        if (StringUtils.isNotNull(prescription.getId())) {
            dto.setPatientname(prescription.getFamilyName());
            dto.setIdnumber(prescription.getFamilyIdcard());
            dto.setMobile(prescription.getFamilyTel());
            dto.setEmpsex(prescription.getFamilySex().equals(CodeEnum.NO.getCode()) ? "女" : "男");
            // 查询就诊人信息
            BusPatientFamily busPatientFamily = busPatientFamilyMapper.selectById(prescription.getFamilyId());
            dto.setAge(AgeCalculationUtil.getAge(busPatientFamily.getDateOfBirth()) + "");
            dto.setBirthdate(DateUtils.parse_YYYY_MM_DD_HH_MM_SS(busPatientFamily.getDateOfBirth()));
            // 查询业务科室信息
            BusBizDepartment busBizDepartment = busBizDepartmentMapper.selectById(prescription.getDepartmentId());
            dto.setRegistdeptcode(busBizDepartment.getDepartmentNumber());
            dto.setRegistdeptname(busBizDepartment.getDepartmentName());
            dto.setRecipestatus("2");
            dto.setHospital(prescription.getHospitalName());
            // 查询医生信息
            BusDoctor doctor = busDoctorMapper.selectById(prescription.getDoctorId());
            dto.setRegistdrcode(doctor.getDoctorNumber());
            dto.setRegistdrname(doctor.getFullName());

            dto.setDrugfeeamount(prescription.getPrescriptionAmount() + "");
            JSONArray array = JSONObject.parseArray(prescription.getClinicalDiagnosis());
            JSONObject jsonObject = array.getJSONObject(0);
            dto.setDiagcode(jsonObject.getString("icdCode"));
            dto.setDiagname(jsonObject.getString("diseaseName"));
            // 查询药品信息
            busPrescriptionDrugs = prescription.getPdDrugsList();
        } else {
            dto.setPatientname(drugsOrder.getReceivingUser());
            dto.setAge("");
            busPrescriptionDrugs = OrikaUtils.converts(prescription.getOtcDrugsList(), BusPrescriptionDrugs.class);
        }

        // 设置收货信息
        dto.setContactname(drugsOrder.getReceivingUser());
        dto.setContactaddr(drugsOrder.getReceivingAddress());
        dto.setContactphone(drugsOrder.getReceivingTel());
        dto.setCountry("中国");
        dto.setProvince(drugsOrder.getProvince());
        dto.setCity(drugsOrder.getCity());
        dto.setAddress(drugsOrder.getReceivingAddress());
        dto.setPayamount(new BigDecimal(drugsOrder.getAmount()));
        dto.setPaytype(2);
        dto.setPaystatus("2");

        List<RecipeItemDTO> list = new ArrayList<>();
        for (BusPrescriptionDrugs drug : busPrescriptionDrugs) {
            // 查询配送方药品信息
            BusEnterpriseDrugsPrice drugsInfo = busEnterpriseDrugsPriceMapper.getDrugsInfoByDrugsId(drugsOrder.getHospitalId(), drug.getEnterpriseId(), drug.getDrugsId());
            RecipeItemDTO itemDTO = new RecipeItemDTO();
            itemDTO.setDrugcode(drugsInfo.getEnterpriseDrugsId());
            itemDTO.setDrugname(drug.getDrugsName());
            itemDTO.setFactoryname(drug.getDrugsManufacturer());
            itemDTO.setDrugspec(drug.getDrugsSpecification());
            itemDTO.setUnitprice(drugsInfo.getReferencePurchasePrice());
            itemDTO.setUsagename(drug.getDrugsUsageValue());
            itemDTO.setQuantity(new BigDecimal(drug.getQuantity()));
            itemDTO.setDrugunit("盒");
            if (StringUtils.isNotNull(drugsOrder.getPrescriptionId())) {
                itemDTO.setFreqname(drug.getMedicationFrequencyRemarks());
                itemDTO.setSustaineddays(drug.getMedicationDays() + "");
//                itemDTO.setDrugunit(drug.getUnit());
                itemDTO.setDosage(drug.getSingleDose() + "");
                itemDTO.setDosageunit(drug.getSingleDose() + drug.getUnit());
            }
            list.add(itemDTO);
        }
        dto.setDetaillist(list);
        log.info("推单到广药入参=" + dto.toString());
        R<GYResponseVo> gyResponseVoR = remoteGYService.uploadRecipe(dto);
        int code = gyResponseVoR.getCode();
        String s = JSON.toJSONString(gyResponseVoR);
        log.info("远程调用广药新增code={},接口返回值：{}", code, s);
        if (HttpStatus.SUCCESS != code) {
            throw new ServiceException("调用广药新增订单失败！");
        }
    }

    private void saveSSTOrder(BusDrugsOrder drugsOrder) {
        List<DrugsVo> drugsVos;
        // 调用东方红新增订单接口
        if (StringUtils.isNotNull(drugsOrder.getPrescriptionId())) {
            // 查询处方药品信息
            drugsVos = busPrescriptionDrugsMapper.selectDrugsInfo(drugsOrder.getHospitalId(), drugsOrder.getPrescriptionId());
        } else {
            // 查询非处方药品信息
            drugsVos = busDrugsOrderMapper.selectDrugsInfo(drugsOrder.getId());
        }
        log.info("新增订单药品信息:{}" + drugsVos.toString());
        // 将药品订单新增到东方红
        DFHOrder dfhOrder = new DFHOrder();
        dfhOrder.setPlatformCode(drugsOrder.getOrderNo()); // 平台单号
        dfhOrder.setOrderTypeCode("Sales"); // 订单类型
        dfhOrder.setDealDatetime(drugsOrder.getOrderTime()); // 拍单时间
        dfhOrder.setExpressCode("SF"); // 物流公司
        List<Item> items = new ArrayList<>();
        drugsVos.forEach(d -> {
            Item item = new Item();
            item.setItemCode(d.getEnterpriseDrugsId());// 商品代码
            item.setPrice(d.getSellingPrice()); // 实际单价
            item.setQty(d.getQuantity()); // 商品数量
            items.add(item);
        });
        dfhOrder.setDetails(items);
        if (CodeEnum.NO.getCode().equals(drugsOrder.getDeliveryType())) { // 自提
            // 查询医院信息
            HospitalVo hospital = busHospitalMapper.selectHospitalTel(drugsOrder.getHospitalId());
            dfhOrder.setReceiverName(hospital.getHospitalName());
            dfhOrder.setReceiverMobile(DESUtil.decrypt(hospital.getHospitalPhone()));
            dfhOrder.setReceiverProvince(hospital.getProvince());
            dfhOrder.setReceiverCity(hospital.getCity());
            dfhOrder.setReceiverDistrict(hospital.getArea());
            dfhOrder.setReceiverAddress(hospital.getDetailAddress());
        } else {
            dfhOrder.setReceiverName(drugsOrder.getReceivingUser());
            dfhOrder.setReceiverMobile(drugsOrder.getReceivingTel());
            dfhOrder.setReceiverProvince(drugsOrder.getProvince());
            dfhOrder.setReceiverCity(drugsOrder.getCity());
            dfhOrder.setReceiverDistrict(drugsOrder.getArea());
            dfhOrder.setReceiverAddress(drugsOrder.getDetailedAddress());
        }
        log.info("调用东方红新增订单参数：" + dfhOrder);
        HashMap<String, Object> addOrder = remoteDFHService.addOrder(dfhOrder);
        log.info("调用东方红返回结果：" + addOrder);
        HashMap<String, Object> data = (HashMap<String, Object>) addOrder.get("data");
        Boolean b = (Boolean) data.get("success");
        if (!b) {
            throw new ServiceException("调用东方红新增订单失败！");
        }
    }

    private void saveOrderLogistics(BusDrugsOrder drugsOrder) {
        BusDrugOrderPackage busDrugOrderPackage = new BusDrugOrderPackage();
        busDrugOrderPackage.setDeliveryTime(DateUtils.getNowDate());
        busDrugOrderPackage.setExpressCode(drugsOrder.getExpressCode());
        busDrugOrderPackage.setLogisticsCompany(drugsOrder.getLogisticsCompany());
        busDrugOrderPackage.setDeliveryNo(drugsOrder.getDeliveryNo());
        UpdateWrapper<BusDrugOrderPackage> updateWrapper = Wrappers.update();
        updateWrapper.isNull("enterprise_id");
        if (StringUtils.isNotNull(drugsOrder.getPrescriptionId())) {
            updateWrapper.eq("prescription_id", drugsOrder.getPrescriptionId());
        } else {
            updateWrapper.eq("drugs_order_id", drugsOrder.getId());
        }
        busDrugOrderPackageMapper.update(busDrugOrderPackage, updateWrapper);
    }

    @Override
    public List<String> selectAfterSaleList(BusDrugsOrderDto dto) {
        QueryWrapper<BusDrugsOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("hospital_id", dto.getHospitalId())
                .eq("status", dto.getStatus());
        List<BusDrugsOrder> busDrugsOrders = busDrugsOrderMapper.selectList(queryWrapper);
        return busDrugsOrders.stream().map(BusDrugsOrder::getOrderNo).collect(Collectors.toList());
    }

    @Override
    public int updateStatus(String orderNo) {
        BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
        busDrugsOrder.setStatus(DrugsOrderEnum.RECEIVED.getCode());
        QueryWrapper<BusDrugsOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        return busDrugsOrderMapper.update(busDrugsOrder, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusByPrescriptionNumber(DXRefundCallDto dxRefundCallDto) {
        QueryWrapper<BusPrescription> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prescription_number", dxRefundCallDto.getPspnum());
        queryWrapper.orderByDesc("create_time");
        BusPrescription busPrescription = busPrescriptionMapper.selectOne(queryWrapper.last("LIMIT 1"));
        if (StringUtils.isNull(busPrescription)) {
            throw new ServiceException("未查询到处方");
        }
        QueryWrapper<BusDrugsOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.eq("prescription_id", busPrescription.getId());
        orderQueryWrapper.orderByDesc("create_time");
        BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectOne(orderQueryWrapper.last("LIMIT 1"));
        if (StringUtils.isNull(busDrugsOrder)) {
            throw new ServiceException("未查询到订单");
        }
        QueryWrapper<BusAfterSale> saleQueryWrapper = new QueryWrapper<>();
        saleQueryWrapper.eq("order_no", busDrugsOrder.getOrderNo());
        saleQueryWrapper.orderByDesc("create_time");
        BusAfterSale busAfterSale = busAfterSaleMapper.selectOne(saleQueryWrapper.last("LIMIT 1"));
        busAfterSale.setStatus(dxRefundCallDto.getRefundCode() ? "4" : "1");
        if (!dxRefundCallDto.getRefundCode()) {
            busAfterSale.setReason(dxRefundCallDto.getRefuseReason());
        }
        busAfterSale.setUpdateTime(DateUtils.getNowDate());
        busAfterSaleMapper.updateById(busAfterSale);
        UpdateWrapper<BusDrugsOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", busDrugsOrder.getId());
        BusDrugsOrder updateOrder = new BusDrugsOrder();
        updateOrder.setStatus(busAfterSale.getOrderStatus());
        updateOrder.setUpdateTime(DateUtils.getNowDate());
        busDrugsOrderMapper.update(updateOrder, updateWrapper);
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateOrderStatus(List<String> orderNos) {
        for (String o : orderNos) {
            ReturnGoodsOrderQueryDto queryDto = new ReturnGoodsOrderQueryDto();
            queryDto.setPageNo(1);
            queryDto.setPageSize(10);
            queryDto.setPlatformCode(o);
            R<JSONObject> objectR = remoteDFHService.getTradeReturns(queryDto);
            JSONObject orderInfo = objectR.getData();
            Boolean success = orderInfo.getBoolean("success");
            log.info("调用东方红查询订单是否成功：" + success);
            if (success) {
                JSONArray tradeReturns = orderInfo.getJSONArray("tradeReturns");
                if (tradeReturns.size() > 0) {
                    JSONObject o1 = tradeReturns.getJSONObject(tradeReturns.size() - 1);
                    Integer approve = o1.getInteger("approve");
                    Integer receive = o1.getInteger("receive");
                    String note = o1.getString("note");
                    if (YesNoEnum.YES.getCode().equals(approve) && YesNoEnum.YES.getCode().equals(receive)) { // 已审核，已入库
                        // 查询售后信息
                        QueryWrapper<BusAfterSale> queryWrapper1 = new QueryWrapper<>();
                        queryWrapper1.eq("order_no", o);
                        BusAfterSale busAfterSale = busAfterSaleMapper.selectOne(queryWrapper1);
                        if ("换货".equals(busAfterSale.getRefundType())) {
                            // 查询药品订单信息
                            QueryWrapper<BusDrugsOrder> queryWrapper2 = new QueryWrapper<>();
                            queryWrapper2.eq("order_no", o);
                            BusDrugsOrder busDrugsOrder1 = busDrugsOrderMapper.selectOne(queryWrapper2);
                            BusDrugsOrder busDrugsOrder2 = OrikaUtils.convert(busDrugsOrder1, BusDrugsOrder.class);
                            busDrugsOrder2.setId(null);
                            // 重新生成药品订单
                            regenerateOrder(busDrugsOrder2);
                        } else {
                            // 修改药品订单状态为换货完成
                            BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
                            busDrugsOrder.setUpdateTime(DateUtils.getNowDate());
                            QueryWrapper<BusDrugsOrder> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("order_no", o);
                            busDrugsOrder.setStatus(DrugsOrderEnum.RECEIVED.getCode());
                            busDrugsOrderMapper.update(busDrugsOrder, queryWrapper);
                            // 修改售后信息
                            BusAfterSale afterSale = new BusAfterSale();
                            afterSale.setUpdateTime(DateUtils.getNowDate());
                            UpdateWrapper<BusAfterSale> afterSaleUpdateWrapper = new UpdateWrapper<>();
                            afterSaleUpdateWrapper.eq("order_no", o);
                            afterSale.setStatus("3"); // 已寄回
                            busAfterSaleMapper.update(afterSale, afterSaleUpdateWrapper);
                        }
                    } else if ((YesNoEnum.NO.getCode().equals(approve) || YesNoEnum.YES.getCode().equals(approve)) &&
                            YesNoEnum.NO.getCode().equals(receive) && StringUtils.isNotEmpty(note)) {
                        // 未审核/已审核，未入库，备注不为空
                        // 查询售后信息
                        QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("order_no", o);
                        queryWrapper.orderByDesc("create_time");
                        BusAfterSale selafterSale = busAfterSaleMapper.selectOne(queryWrapper.last("limit 1"));
                        if (StringUtils.isNotNull(selafterSale) && selafterSale.getStatus().equals("3")) {
                            // 修改售后信息为不同意
                            BusAfterSale afterSale = new BusAfterSale();
                            afterSale.setStatus(CodeEnum.REFUSE.getCode()); // 不同意退货/换货
                            afterSale.setUpdateTime(DateUtils.getNowDate());
                            afterSale.setReason(note);
                            UpdateWrapper<BusAfterSale> afterSaleUpdateWrapper = new UpdateWrapper<>();
                            afterSaleUpdateWrapper.eq("order_no", o);
                            busAfterSaleMapper.update(afterSale, afterSaleUpdateWrapper);
                            // 修改药品订单状态为退款前状态
                            BusDrugsOrder busDrugsOrder = new BusDrugsOrder();
                            busDrugsOrder.setStatus(selafterSale.getOrderStatus());
                            busDrugsOrder.setUpdateTime(DateUtils.getNowDate());
                            UpdateWrapper<BusDrugsOrder> orderUpdateWrapper = new UpdateWrapper<>();
                            orderUpdateWrapper.eq("order_no", o);
                            busDrugsOrderMapper.update(busDrugsOrder, orderUpdateWrapper);
                            // 消息通知
                            LambdaQueryWrapper<BusDrugsOrder> busDrugsOrderQW = Wrappers.<BusDrugsOrder>lambdaQuery().eq(BusDrugsOrder::getOrderNo, o);
                            BusDrugsOrder busDrugsOrder1 = busDrugsOrderMapper.selectOne(busDrugsOrderQW);

                            // 发送药品订单拒绝退换货或拒绝退款退货事件通知
                            sendDrugsOrderRefuseEventNotification(selafterSale, busDrugsOrder1);
                        }
                    }
                }
            }
        }
    }

    /**
     *  发送药品订单拒绝退换货或拒绝退款退货事件通知
     * @param selafterSale  订单售后信息
     * @param busDrugsOrder 药品订单信息
     */
    private void sendDrugsOrderRefuseEventNotification(BusAfterSale selafterSale, BusDrugsOrder busDrugsOrder) {
        Map<String, Object> attachment = new HashMap<>();
        attachment.put("orderId", busDrugsOrder.getId());
        attachment.put("status", "5");
        if ("换货".equals(selafterSale.getRefundType())) {
            // 组装拒绝退换货事件通知
            DrugsOrderRefuseRefundExchangeEvent orderRefuseRefundExchangeEvent = new DrugsOrderRefuseRefundExchangeEvent();
            orderRefuseRefundExchangeEvent.setSubOrderId(busDrugsOrder.getId());
            orderRefuseRefundExchangeEvent.setAttachment(attachment);
            publisher.publishEvent(orderRefuseRefundExchangeEvent);
        } else {
            // 组装拒绝退款退货事件通知
            DrugsOrderRefuseRefundReturnEvent orderRefuseRefundReturnEvent = new DrugsOrderRefuseRefundReturnEvent();
            orderRefuseRefundReturnEvent.setSubOrderId(busDrugsOrder.getId());
            orderRefuseRefundReturnEvent.setAttachment(attachment);
            publisher.publishEvent(orderRefuseRefundReturnEvent);
        }
    }

    @Override
    public void exportExcel(HttpServletResponse response, BusDrugsOrderDto busDrugsOrderDto) {
        long beforeTime = System.currentTimeMillis();
        OutputStream outputStream;
        try {
            outputStream = response.getOutputStream();

            String time = new SimpleDateFormat("yyyy-MM-dd-hh-mm-ss").format(new Date());
            //添加响应头信息
            response.setHeader("Content-disposition", "attachment; filename=" + "contract" + time + ".xlsx");
            //设置类型
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            //设置头
            response.setHeader("Pragma", "No-cache");
            //设置头
            response.setHeader("Cache-Control", "no-cache");
            //设置日期头
            response.setDateHeader("Expires", 0);
            Integer count = busDrugsOrderMapper.getCount(busDrugsOrderDto);
            writeExcel(busDrugsOrderMapper, busDrugsOrderDto, count, outputStream);
            outputStream.flush();
            response.getOutputStream().close();
            long afterTime = System.currentTimeMillis();
            log.info("耗时:{}", afterTime - beforeTime);
        } catch (Exception e) {
            log.error("导出失败：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void releaseStock(BusDrugsOrder drugsOrder) {

    }

    @Override
    public BusDrugsOrder queryDrugsOrderByNo(String orderNo) {
        LambdaQueryWrapper<BusDrugsOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugsOrder::getOrderNo, orderNo);
        return busDrugsOrderMapper.selectOne(lambdaQuery);
    }
    /**
     * 根据子订单号查询药品订单列表
     * @param subOrderList 子订单号列表
     * @return 药品订单列表
     */
    @Override
    public List<BusDrugsOrderDetailVo> selectDrugOrderListWithDetail(List<String> subOrderList, Long hospitalId) {
        if (CollectionUtils.isEmpty(subOrderList)){
            return Collections.emptyList();
        }
        return busDrugsOrderMapper.selectDrugOrderListWithDetail(subOrderList, hospitalId);
    }

    public void writeExcel(BusDrugsOrderMapper busDrugsOrderMapper, BusDrugsOrderDto queryCondition, int exifInfoCount, OutputStream outputStream) {
        //每个sheet保存的数据量
        int num = 10000;
        ExcelWriter excelWriter = null;
        int corePoolSize = 10;
        int maximumPoolSize = 20;
        //用线程池管理多线程
        ThreadPoolExecutor exector = (ThreadPoolExecutor) Executors.newFixedThreadPool(corePoolSize);
        exector.setCorePoolSize(corePoolSize);
        exector.setMaximumPoolSize(maximumPoolSize);
        List<DrugsOrderThread> tasks = Lists.newCopyOnWriteArrayList();
        excelWriter = EasyExcel.write(outputStream, BusDrugsOrderVo.class).build();
        //exifInfoCount 写入excel数据总量
        //pageCount 要写入sheet页数量。同分页
        int pageCount = exifInfoCount % num == 0 ? (exifInfoCount / num) : (exifInfoCount / num + 1);
        for (int i = 0; i < pageCount; i++) {
            DrugsOrderThread readExifInfoThread = new DrugsOrderThread(queryCondition, busDrugsOrderMapper, i, num);
            tasks.add(readExifInfoThread);
        }
        try {
            //用invokeAll方法提交任务，返回数据的顺序和tasks中的任务顺序一致，如果第一个线程查0-10000行数据，第二个线程查10000-10001行数据，
            //第二个线程大概率比第一个线程先执行完，但是futures中第一位数据是0-10000的数据。
            List<Future<List<BusDrugsOrderVo>>> futures = exector.invokeAll(tasks);
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "药品订单信息" + (1)).build();
            for (int i = 0; i < pageCount; i++) {
                List<BusDrugsOrderVo> exifInfoList = futures.get(i).get();

                excelWriter.write(exifInfoList, writeSheet);
            }
        } catch (Exception e) {
            log.error("问诊订单导出数据失败", e);
        }
        exector.shutdown();
        excelWriter.finish();

    }

    /**
     * 多线程查询数据内部类
     */
    public class DrugsOrderThread implements Callable<List<BusDrugsOrderVo>> {
        @Autowired
        private BusDrugsOrderMapper busDrugsOrderMapper;
        private int startNum;
        private int pageZize;

        /**
         * 医院id
         */
        private final Long hospitalId;
        /**
         * 订单编码
         */
        private final String orderNo;
        /**
         * 订单类型
         */
        private final String source;
        /**
         * 医生名称
         */
        private final String doctorName;
        /**
         * 状态
         */
        private final String status;
        /**
         * 订单药品类型
         */
        private final String orderDrugsType;
        /**
         * 经纪人id
         */
        private final Long agentId;

        /**
         * 合作渠道id
         */
        private final Long partnerId;

        public DrugsOrderThread(BusDrugsOrderDto busDrugsOrderDto, BusDrugsOrderMapper busDrugsOrderMapper, int startNum, int pageZize) {
            this.hospitalId = busDrugsOrderDto.getHospitalId();
            this.orderNo = busDrugsOrderDto.getOrderNo();
            this.source = busDrugsOrderDto.getSource();
            this.doctorName = busDrugsOrderDto.getDoctorName();
            this.orderDrugsType = busDrugsOrderDto.getOrderDrugsType();
            this.status = busDrugsOrderDto.getStatus();
            this.agentId = busDrugsOrderDto.getAgentId();
            this.partnerId = busDrugsOrderDto.getPartnerId();
            this.startNum = startNum;
            this.pageZize = pageZize;
            this.busDrugsOrderMapper = busDrugsOrderMapper;
        }

        @Override
        public List<BusDrugsOrderVo> call() {
            BusDrugsOrderDto queryCondition = new BusDrugsOrderDto();
            queryCondition.setStartNum(startNum * pageZize);
            queryCondition.setPageSize(pageZize);
            queryCondition.setOrderNo(orderNo);
            queryCondition.setSource(source);
            queryCondition.setDoctorName(doctorName);
            queryCondition.setOrderDrugsType(orderDrugsType);
            queryCondition.setStatus(status);
            queryCondition.setAgentId(agentId);
            queryCondition.setPartnerId(partnerId);
            queryCondition.setHospitalId(hospitalId);
            long startTime = System.currentTimeMillis();
            List<BusDrugsOrderVo> exifInfoList = null;
            try {
                //从数据库查询要写入excle的数据
                exifInfoList = busDrugsOrderMapper.exportListBySearch(queryCondition);
                if (StringUtils.isNotEmpty(exifInfoList)) {
                    exifInfoList.forEach(d -> {
                        LambdaQueryWrapper<BusDrugOrderPackage> lambdaQuery = Wrappers.lambdaQuery();
                        lambdaQuery.eq(BusDrugOrderPackage::getHospitalId, queryCondition.getHospitalId());
                        if (StringUtils.isNotNull(d.getPrescriptionId())) {
                            lambdaQuery.eq(BusDrugOrderPackage::getPrescriptionId, d.getPrescriptionId());
                        } else {
                            lambdaQuery.eq(BusDrugOrderPackage::getDrugsOrderId, d.getId());
                        }
                        if ("0.00".equals(d.getAmount())) {
                            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
                        } else {
                            lambdaQuery.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
                        }
                        // 查询配送企业名称
                        List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectList(lambdaQuery);
                        StringBuilder enterpriseName = new StringBuilder();
                        StringBuilder logisticsCompany = new StringBuilder();
                        for (BusDrugOrderPackage orderPackage : busDrugOrderPackages) {
                            if (enterpriseName.length() > 0) {
                                enterpriseName.append(",").append(orderPackage.getEnterpriseName());
                            } else {
                                enterpriseName.append(orderPackage.getEnterpriseName());
                            }
                            if (StringUtils.isNotEmpty(orderPackage.getLogisticsCompany())) {
                                if (logisticsCompany.length() > 0) {
                                    logisticsCompany.append(",").append(orderPackage.getLogisticsCompany());
                                } else {
                                    logisticsCompany.append(orderPackage.getLogisticsCompany());
                                }
                            }
                        }
                        d.setEnterpriseName(enterpriseName.toString());
                        d.setLogisticsCompany(logisticsCompany.toString());
                    });
                }
                long endTime = System.currentTimeMillis();
                long spendTime = endTime - startTime;
                log.info(Thread.currentThread().getName() + "查询耗时：" + spendTime + "；分页是从【" + queryCondition.getStartNum() + "】开始");
            } catch (Exception e) {
                log.error("多线程查询导出数据失败", e);
            }
            return exifInfoList;
        }
    }

    @Override
    public int updateOrderState(DXRefundCallDto dxRefundCallDto) {
        try {
            QueryWrapper<BusPrescription> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("prescription_number", dxRefundCallDto.getPspnum());
            queryWrapper.orderByDesc("create_time");
            BusPrescription busPrescription = busPrescriptionMapper.selectOne(queryWrapper.last("LIMIT 1"));
            if (StringUtils.isNull(busPrescription)) {
                throw new ServiceException("未查询到处方");
            }
            QueryWrapper<BusDrugsOrder> orderQueryWrapper = new QueryWrapper<>();
            orderQueryWrapper.eq("prescription_id", busPrescription.getId());
            orderQueryWrapper.orderByDesc("create_time");
            BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectOne(orderQueryWrapper.last("LIMIT 1"));
            if (StringUtils.isNull(busDrugsOrder)) {
                throw new ServiceException("未查询到订单");
            }

            // 校验总订单是否全部发货
            LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOrder::getSubOrderType, CodeEnum.NO.getCode());
            lambdaQuery.eq(BusOrder::getSubOrderId, busDrugsOrder.getId());
            BusOrder busOrder = busOrderMapper.selectOne(lambdaQuery);
            if (DeliveryTypeEnum.isExpressDelivery(busOrder.getDeliveryType())) {
                String amount = busDrugsOrder.getAmount();
                Integer afterSaleType;
                if ("0.00".equals(amount)) {
                    afterSaleType = YesNoEnum.YES.getCode();
                } else {
                    afterSaleType = YesNoEnum.NO.getCode();
                }
                Integer allSendGoods = busDrugOrderPackageService.isAllSendGoods(busDrugsOrder.getId(), CodeEnum.NO.getCode(), afterSaleType);
                if (YesNoEnum.YES.getCode().equals(allSendGoods)) {
                    LambdaQueryWrapper<BusOrder> wrapper = Wrappers.lambdaQuery();
                    wrapper.eq(BusOrder::getOrderNo, busOrder.getOrderNo());
                    BusOrder order = new BusOrder();
                    order.setOrderStatus("3");
                    busOrderMapper.update(order, wrapper);
                }
            }

            String data = kdnUtil.numberIdentification(dxRefundCallDto.getDeliveryNo());
            JSONObject jsonObject = JSONObject.parseObject(data);
            JSONArray shippers = jsonObject.getJSONArray("Shippers");
            if (StringUtils.isEmpty(shippers)) {
                throw new ServiceException("运单号格式错误");
            }
            JSONObject object = shippers.getJSONObject(0);
            String shipperName = object.getString("ShipperName");
            String shipperCode = object.getString("ShipperCode");
            // 保存物流信息
            BusDrugOrderPackage busDrugOrderPackage = new BusDrugOrderPackage();
            busDrugOrderPackage.setDeliveryTime(DateUtils.getNowDate());
            busDrugOrderPackage.setExpressCode(shipperCode);
            busDrugOrderPackage.setLogisticsCompany(shipperName);
            busDrugOrderPackage.setDeliveryNo(dxRefundCallDto.getDeliveryNo());
            LambdaUpdateWrapper<BusDrugOrderPackage> lambdaUpdate = Wrappers.lambdaUpdate();
            lambdaUpdate.eq(BusDrugOrderPackage::getEnterpriseId, busPrescription.getEnterpriseId());
            lambdaUpdate.eq(BusDrugOrderPackage::getPrescriptionId, busPrescription.getId());
            if ("0.00".equals(busDrugsOrder.getAmount())) {
                lambdaUpdate.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
            } else {
                lambdaUpdate.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
            }
            int ret = busDrugOrderPackageMapper.update(busDrugOrderPackage, lambdaUpdate);
            // 发送药品订单发货消息
            DrugsOrderShippedEvent drugsOrderShippedEvent = new DrugsOrderShippedEvent();
            drugsOrderShippedEvent.setSubOrderId(busDrugsOrder.getId());
            drugsOrderShippedEventProducer.send(drugsOrderShippedEvent);
            return ret;
        } catch (Exception e) {
            throw new ServiceException("操作失败");
        }
    }

    @Override
    public int updateZXOrderState(ZXRefundCallDto zxRefundCallDto) {
        log.info("至信发货入参:{}", JSON.toJSONString(zxRefundCallDto));
        try {
            QueryWrapper<BusPrescription> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("prescription_number", zxRefundCallDto.getPspnum());
            queryWrapper.orderByDesc("create_time");
            BusPrescription busPrescription = busPrescriptionMapper.selectOne(queryWrapper.last("LIMIT 1"));
            log.info("至信发货-处方BusPrescription：{}", busPrescription);
            if (StringUtils.isNull(busPrescription)) {
                throw new ServiceException("未查询到处方");
            }
            QueryWrapper<BusDrugsOrder> orderQueryWrapper = new QueryWrapper<>();
            orderQueryWrapper.eq("prescription_id", busPrescription.getId());
            orderQueryWrapper.orderByDesc("create_time");
//            List<BusDrugsOrder> busDrugsOrderList = busDrugsOrderMapper.selectList(orderQueryWrapper);
            BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectOne(orderQueryWrapper.last("limit 1"));
            int ret = 0;
//            for(BusDrugsOrder busDrugsOrder : busDrugsOrderList){
                log.info("至信发货-订单BusDrugsOrder：{}",busDrugsOrder);
                if (StringUtils.isNull(busDrugsOrder)) {
                    throw new ServiceException("未查询到订单");
                }
                // 校验总订单是否全部发货
                LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusOrder::getSubOrderType, CodeEnum.NO.getCode());
                lambdaQuery.eq(BusOrder::getSubOrderId, busDrugsOrder.getId());
                BusOrder busOrder = busOrderMapper.selectOne(lambdaQuery);
                if (DeliveryTypeEnum.isExpressDelivery(busOrder.getDeliveryType())) {
                    String amount = busDrugsOrder.getAmount();
                    Integer afterSaleType;
                if ("0.00".equals(amount)) {
                    afterSaleType = YesNoEnum.YES.getCode();
                } else {
                    afterSaleType = YesNoEnum.NO.getCode();
                }
                Integer allSendGoods = busDrugOrderPackageService.isAllSendGoods(busDrugsOrder.getId(), CodeEnum.NO.getCode(), afterSaleType);
                if (YesNoEnum.YES.getCode().equals(allSendGoods)) {
                    LambdaQueryWrapper<BusOrder> wrapper = Wrappers.lambdaQuery();
                    wrapper.eq(BusOrder::getOrderNo, busOrder.getOrderNo());
                    BusOrder order = new BusOrder();
                    order.setOrderStatus("3");
                    busOrderMapper.update(order, wrapper);
                }
            } else {
                // 自提免处理
                log.info("至信自提免处理:{}", JSON.toJSONString(busOrder));
                return 1;
            }

                String data = kdnUtil.numberIdentification(zxRefundCallDto.getDeliveryNo());
                JSONObject jsonObject = JSONObject.parseObject(data);
                JSONArray shippers = jsonObject.getJSONArray("Shippers");
                if (StringUtils.isEmpty(shippers)) {
                    throw new ServiceException("运单号格式错误");
                }
                JSONObject object = shippers.getJSONObject(0);
                String shipperName = object.getString("ShipperName");
                String shipperCode = object.getString("ShipperCode");
                // 保存物流信息
                BusDrugOrderPackage busDrugOrderPackage = new BusDrugOrderPackage();
                busDrugOrderPackage.setDeliveryTime(DateUtils.getNowDate());
                busDrugOrderPackage.setExpressCode(shipperCode);
                busDrugOrderPackage.setLogisticsCompany(shipperName);
                busDrugOrderPackage.setDeliveryNo(zxRefundCallDto.getDeliveryNo());
                busDrugOrderPackage.setOrderNo(busOrder.getOrderNo());
                LambdaUpdateWrapper<BusDrugOrderPackage> lambdaUpdate = Wrappers.lambdaUpdate();
                lambdaUpdate.eq(BusDrugOrderPackage::getEnterpriseId, busPrescription.getEnterpriseId());
                lambdaUpdate.eq(BusDrugOrderPackage::getPrescriptionId, busPrescription.getId());
                if ("0.00".equals(busDrugsOrder.getAmount())) {
                    lambdaUpdate.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.YES.getCode());
                } else {
                    lambdaUpdate.eq(BusDrugOrderPackage::getBarterOrNot, YesNoEnum.NO.getCode());
                }
                ret = busDrugOrderPackageMapper.update(busDrugOrderPackage, lambdaUpdate);
                // 发送药品订单发货消息
                DrugsOrderShippedEvent drugsOrderShippedEvent = new DrugsOrderShippedEvent();
                drugsOrderShippedEvent.setSubOrderId(busDrugsOrder.getId());
                drugsOrderShippedEventProducer.send(drugsOrderShippedEvent);
//            }
            return ret;
        } catch (Exception e) {
            log.info("至信发货操作失败：{}", e);
            throw new ServiceException("操作失败:" + e);
        }
    }

    //    //加入消息队列
    private void addQueue(long id, long hospitalId) {
        ConcurrentMap map = new ConcurrentHashMap();
        map.put("id", id);
        map.put("hospitalId", hospitalId);
        long currTime = System.currentTimeMillis();
        long fireTime = 7 * 24 * 60 * 60 * 1000 + currTime;
        redisTemplate.opsForZSet().add("queue_drugs_order_auto_complete", JSON.toJSONString(map), fireTime);
        log.info("药品订单自动完成加入消息队列成功！");
    }

    /**
     *  发送药品订单发货事件通知
     * @param busDrugsOrder 药品订单通知
     */
    private void sendDrugsOrderShippedEventNotification(BusDrugsOrder busDrugsOrder) {
        log.info("发送药品订单发货事件 orderNo: {}", busDrugsOrder.getOrderNo());
        // 组装发货消息事件通知
        DrugsOrderShippedEvent drugsOrderShippedEvent = new DrugsOrderShippedEvent();
        drugsOrderShippedEvent.setSubOrderId(busDrugsOrder.getId());
        Map<String, Object> attachment = new HashMap<>();
        attachment.put("orderId", busDrugsOrder.getId());
        attachment.put("status", "5");
        drugsOrderShippedEvent.setAttachment(attachment);
        publisher.publishEvent(drugsOrderShippedEvent);
    }
}
