package com.puree.hospital.business.controller;

import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.dto.BusPrescriptionDto;
import com.puree.hospital.business.domain.dto.BusPrescriptionRuleDTO;
import com.puree.hospital.business.domain.vo.DiagnosisPatientInfoVo;
import com.puree.hospital.business.service.IBusPrescriptionService;
import com.puree.hospital.common.api.annotation.RsaEncryptedOperate;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.RsaOperateType;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("prescription")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPrescriptionController extends BaseController {
    private final IBusPrescriptionService busPrescriptionService;

    /**
     * 查询处方列表
     *
     * @return
     */
    @RsaEncryptedOperate(RsaOperateType.ENCRYPT)
    @PreAuthorize(hasPermi = "business:prescription:list")
    @GetMapping("list")
    @Log(title = "查询处方列表", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public TableDataInfo list(BusPrescriptionDto dto) {
        startPage();
        List<BusPrescription> list = busPrescriptionService.selectListBySearch(dto);
        return getDataTable(list);
    }

    /**
     * 查询清单处方详情
     *
     * @return
     */
    @PreAuthorize(hasPermi = "business:prescription:detail")
    @GetMapping("detail")
    @Log(title = "查询清单处方详情", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public AjaxResult detail(@RequestParam("id") Long id,
                             @RequestParam("hospitalId") Long hospitalId) {
        BusPrescriptionDto dto = new BusPrescriptionDto();
        dto.setId(id);
        dto.setHospitalId(hospitalId);
        return AjaxResult.success(busPrescriptionService.selectDetail(dto));
    }

    /**
     * 查询处方详情
     *
     * @return
     */
    @PreAuthorize(hasPermi = "business:prescription:detail")
    @GetMapping("info")
    @Log(title = "查询处方详情", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public AjaxResult prescriptionInfo(@RequestParam("id") Long id,
                                       @RequestParam("hospitalId") Long hospitalId) {
        BusPrescriptionDto dto = new BusPrescriptionDto();
        dto.setId(id);
        dto.setHospitalId(hospitalId);
        return AjaxResult.success(busPrescriptionService.selectPrescriptionDetail(dto));
    }

    /**
     * 查询处方下载链接
     *
     * @return
     */
    @PreAuthorize(hasPermi = "business:prescription:url")
    @GetMapping("infoByUrl")
    @Log(title = "处方下载", businessType = BusinessType.EXPORT, operatorType = OperatorType.MANAGE)
    public AjaxResult prescriptionInfoByUrl(@RequestParam("id") Long id,
                                            @RequestParam("hospitalId") Long hospitalId) {
        BusPrescriptionDto dto = new BusPrescriptionDto();
        dto.setId(id);
        dto.setHospitalId(hospitalId);
        return AjaxResult.success(busPrescriptionService.selectPrescriptionDetailByUrl(dto));
    }

    @PostMapping("/import")
    @PreAuthorize(hasPermi = "business:prescription:import")
    @Log(title = "导出处方", businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)
    public void importList(BusPrescriptionDto dto, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyExcel没有关系
            String fileName = URLEncoder.encode("处方导出", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            busPrescriptionService.importList(dto, response);
        } catch (Exception ex) {
            logger.error("处方导出异常", ex);
            throw new ServiceException("导出处方列表失败");
        }
    }

    /**
     * 打印用药指导单
     * @param id
     * @param hospitalId
     */
    @GetMapping("/print/{id}/{hospitalId}")
    //@PreAuthorize(hasPermi = "business:prescription:print")
    @Log(title = "用药指导单", businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)
    public AjaxResult print(@PathVariable("id") Long id, @PathVariable("hospitalId") Long hospitalId) {
        BusPrescriptionDto dto = new BusPrescriptionDto();
        dto.setId(id);
        dto.setHospitalId(hospitalId);
        dto.setPrintType(CodeEnum.YES.getCode());
        return AjaxResult.success(busPrescriptionService.selectPrescriptionDetailByUrl(dto));
    }


    /**
     * 随访-处方诊断-过滤患者列表(远程调用)
     * @param dto 诊断列表
     * @return 就诊人列表
     */
    @PostMapping("/follow-up/rule/users")
    @Log(title = "随访-处方诊断-过滤患者列表", businessType = BusinessType.QUERY)
    public R<List<DiagnosisPatientInfoVo>> getPatientsByDiagnosisIds(@RequestBody BusPrescriptionRuleDTO dto) {
        return R.ok(busPrescriptionService.getPatientsByDiagnosisIds(dto));
    }

    /**
     * 根据处方id查询处方详情(Feign调用)
     * @param id 处方id
     * @return 处方详情
     */
    @GetMapping("/feign/info")
    @Log(title = "查询处方详情", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public R<BusPrescription> selectDrugsOrderById(@RequestParam("id") Long id) {
        return R.ok(busPrescriptionService.selectPrescriptionById(id));
    }

}
