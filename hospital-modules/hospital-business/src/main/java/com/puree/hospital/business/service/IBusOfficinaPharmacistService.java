package com.puree.hospital.business.service;

import com.puree.hospital.business.domain.BusOfficinaPharmacist;
import com.puree.hospital.business.domain.BusPharmacistSchedule;
import com.puree.hospital.business.domain.BusPharmacyConfig;
import com.puree.hospital.business.domain.dto.BusOfficinaPharmacistDto;
import com.puree.hospital.business.domain.vo.BusOfficinaAllNameVo;
import com.puree.hospital.business.domain.vo.PharmacistInfoVo;
import com.puree.hospital.common.api.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface IBusOfficinaPharmacistService {
    /**
     * 查询所有药师的姓名
     * @param busPharmacyConfig
     * @return
     */
    List<BusOfficinaAllNameVo> getOfficinaNameAll(BusPharmacyConfig busPharmacyConfig);

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    List<BusOfficinaPharmacist> listAll(BusOfficinaPharmacist busOfficinaPharmacist);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    BusOfficinaPharmacist getById(Long id);

    /**
     * 添加药师
     *
     * @param pharmacist 药师信息
     * @return 返回影响行数
     */
    int insert(BusOfficinaPharmacist pharmacist);

    /**
     * 修改，忽略null字段
     *
     * @param busOfficinaPharmacist 修改的记录
     * @return 返回影响行数
     */
    int updateIgnoreNull(BusOfficinaPharmacist busOfficinaPharmacist);

    /**
     * 删除记录
     *
     * @param id 待删除的记录
     * @return 返回影响行数
     */
    int delete(Long id);

    /**
     * 药师停用启用操作
     * @param busOfficinaPharmacist
     * @return
     */
    int lock(BusOfficinaPharmacist busOfficinaPharmacist);

    /**
     * 校验药师身份证号是否重复
     * @param id
     * @param idNumber
     * @return
     */
    boolean checkIdNumber(Long id, String idNumber);

    /**
     * 校验药师手机号是否重复
     * @param id
     * @param phoneNumber
     * @return
     */
    boolean checkPhoneNumber(Long id, String phoneNumber);

    /**
     * 获取登录用户药师的信息
     * @param userName
     * @return
     */
    PharmacistInfoVo getPharmacistInfo(String userName, String roleType);

    /**
     * 查询药师信息
     * @param username
     * @return
     */
    PharmacistInfoVo getPharmacist(String username);

    /**
     * 查询药师角色
     * @param phonenumber
     * @return
     */
    String queryOfficinaPharmacist(String phonenumber);

    /**
     * 根据身份证号查询药师信息
     * @param idNumber
     * @return
     */
    BusOfficinaPharmacist selectPharmacistByNo(String idNumber);

    /**
     * 下载药房药师排班
     *
     * @param scheduleDate 排班时间
     * @param hospitalId 医院id
     * @param response 请求返回头
     * @throws IOException IO异常
     */
    void downloadScheduleTemplate(String scheduleDate, Long hospitalId, HttpServletResponse response) throws IOException;

    /**
     * 导入药师排班表
     *
     * @param file excel文件
     * @param hospitalId 医院id
     * @param scheduleDate 排班日期
     * @return 导入结果
     */
    AjaxResult importSchedule(MultipartFile file, Long hospitalId, String scheduleDate);

    /**
     * 查询药师排班
     *
     * @param dto 筛选信息
     * @return 列表
     */
    List<BusPharmacistSchedule> listSchedule(BusPharmacistSchedule dto);

    /**
     * 更新药师处方签
     * @param dto
     * @return
     */
    BusOfficinaPharmacist updatePrescriptionLabel(BusOfficinaPharmacistDto dto);
}
