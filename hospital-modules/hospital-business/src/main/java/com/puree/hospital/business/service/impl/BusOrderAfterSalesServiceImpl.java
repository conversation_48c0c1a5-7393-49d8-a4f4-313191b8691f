package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.puree.hospital.app.api.RemoteTongLianPayService;
import com.puree.hospital.app.api.RemoteWxPayService;
import com.puree.hospital.app.api.model.BusRefundPayDTO;
import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundExchangeEvent;
import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundExchangeReceivedEvent;
import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundExchangeRefuseReceivedEvent;
import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundReturnEvent;
import com.puree.hospital.app.api.model.event.order.OrderAgreeRefundReturnRefuseReceivedEvent;
import com.puree.hospital.app.api.model.event.order.OrderRefuseRefundEvent;
import com.puree.hospital.app.api.model.event.order.OrderRefuseRefundExchangeEvent;
import com.puree.hospital.app.api.model.event.order.OrderRefuseRefundReturnEvent;
import com.puree.hospital.business.api.RemoteHospitalEnterpriseService;
import com.puree.hospital.business.api.model.BusHospitalEnterprise;
import com.puree.hospital.business.domain.BusChannelOrder;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusDrugs;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusEnterprise;
import com.puree.hospital.business.domain.BusHospitalPa;
import com.puree.hospital.business.domain.BusNegotiationRecord;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOrderAfterSales;
import com.puree.hospital.business.domain.BusOrderDrugTraceCode;
import com.puree.hospital.business.domain.BusOtcDrugs;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusPrescriptionDrugs;
import com.puree.hospital.business.domain.dto.BusOrderAfterSalesDTO;
import com.puree.hospital.business.domain.dto.BusOrderAfterSalesListDTO;
import com.puree.hospital.business.domain.dto.ChangingRefundNewOrderInDTO;
import com.puree.hospital.business.domain.vo.AfterSalesReceiveGoodsResultVO;
import com.puree.hospital.business.domain.vo.AfterSalesRefundVO;
import com.puree.hospital.business.domain.vo.BusAfterSaleDetailVO;
import com.puree.hospital.business.domain.vo.BusAfterSaleGood;
import com.puree.hospital.business.domain.vo.BusOrderAfterSalesListVO;
import com.puree.hospital.business.domain.vo.GoodsItemVO;
import com.puree.hospital.business.helper.BusOrderAfterSalesHelper;
import com.puree.hospital.business.channel.mapper.BusChannelOrderMapper;
import com.puree.hospital.business.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.business.mapper.BusDrugsMapper;
import com.puree.hospital.business.mapper.BusDrugsOrderMapper;
import com.puree.hospital.business.mapper.BusEnterpriseMapper;
import com.puree.hospital.business.mapper.BusHospitalPaMapper;
import com.puree.hospital.business.mapper.BusInvoiceMapper;
import com.puree.hospital.business.mapper.BusNegotiationRecordMapper;
import com.puree.hospital.business.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.business.mapper.BusOrderDrugTraceCodeMapper;
import com.puree.hospital.business.mapper.BusOrderMapper;
import com.puree.hospital.business.mapper.BusOtcDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.queue.producer.event.order.OrderAgreeRefundExchangeReceivedEventProducer;
import com.puree.hospital.business.queue.producer.event.order.OrderAgreeRefundExchangeRefuseReceivedEventProducer;
import com.puree.hospital.business.service.IBusNegotiationRecordService;
import com.puree.hospital.business.service.IBusOrderAfterSalesService;
import com.puree.hospital.business.service.IBusOrderService;
import com.puree.hospital.common.core.calculator.price.IPriceCalculator;
import com.puree.hospital.common.core.calculator.price.drug.CmPiecesDrugPriceCalculator;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.OrderAfterSalesConstants;
import com.puree.hospital.common.api.constant.OrderTypeConstant;
import com.puree.hospital.common.core.domain.BusInvoice;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AfterSaleRefundTypeEnum;
import com.puree.hospital.common.core.enums.ChannelOrderEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.DeliveryTypeEnum;
import com.puree.hospital.common.core.enums.DrugTraceCodeStatusEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.EnterpriseEnum;
import com.puree.hospital.common.core.enums.HrOrderStatusEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesChangingRefundEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesRefundCauseEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesReturnCauseEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesReturnRefundCauseEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.OrderDrugsTypeEnum;
import com.puree.hospital.common.core.enums.OrderListTypeEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.RefundOnlyEnum;
import com.puree.hospital.common.core.enums.RefundReturnEnum;
import com.puree.hospital.common.core.enums.RefundsExchangesEnum;
import com.puree.hospital.common.core.enums.ShopOrderEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.insurance.api.RemotePaymentService;
import com.puree.hospital.insurance.api.RemotePreprocessorService;
import com.puree.hospital.insurance.api.model.MiPayResult;
import com.puree.hospital.insurance.api.model.UniformRequest;
import com.puree.hospital.order.api.RemoteBusOrderShopService;
import com.puree.hospital.order.api.RemoteBusShopOrderService;
import com.puree.hospital.order.api.model.BusOrderShop;
import com.puree.hospital.order.api.model.BusShopOrder;
import com.puree.hospital.order.api.model.BusShopOrderDTO;
import com.puree.hospital.supplier.api.RemoteDXService;
import com.puree.hospital.supplier.api.RemoteZXService;
import com.puree.hospital.supplier.api.model.zx.dto.CannelOrderDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 订单售后信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOrderAfterSalesServiceImpl implements IBusOrderAfterSalesService {
    private final BusOrderAfterSalesMapper orderAfterSalesMapper;
    private final BusNegotiationRecordMapper negotiationRecordMapper;
    private final BusDrugsOrderMapper drugsOrderMapper;
    private final BusOrderMapper orderMapper;
    private final BusPrescriptionMapper rxMapper;
    private final BusOtcDrugsMapper otcDrugsMapper;
    private final BusDrugsMapper drugsMapper;
    private final BusPrescriptionDrugsMapper rxDrugsMapper;
    private final BusHospitalPaMapper paMapper;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final BusOtcDrugsMapper busOtcDrugsMapper;
    private final BusHospitalPaMapper busHospitalPaMapper;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final BusEnterpriseMapper busEnterpriseMapper;
    private final RemoteBusOrderShopService remoteBusOrderShopService;
    private final BusDrugsOrderMapper busDrugsOrderMapper;

    // 远程调用商品服务
    private final RemoteBusShopOrderService remoteBusShopOrderService;
    // 远程调用通联支付服务
    private final RemoteTongLianPayService remoteTongLianPayService;
    // 远程调用微信支付服务
    private final RemoteWxPayService remoteWxPayService;
    // 远程调用微信服务
    private final IBusNegotiationRecordService negotiationService;
    private final RemoteDXService remoteDXService;
    private final RemoteZXService remoteZXService;
    private final RemoteHospitalEnterpriseService remoteHosEntService;
    private final RemotePaymentService remotePaymentService;
    private final RemotePreprocessorService remotePreprocessorService;
    private final IBusOrderService orderService;

    // 订单类型常量 药品订单
    private final String ORDER_TYPE_DRUG_ORDER = "0";
    // 订单类型常量 商品订单
    private final String ORDER_TYPE_SHOP_ORDER = "1";

    private final BusInvoiceMapper invoiceMapper ;
    private final BusChannelOrderMapper channelOrderMapper ;

    private final BusOrderDrugTraceCodeMapper busOrderDrugTraceCodeMapper;

    private final BusOrderAfterSalesHelper busOrderAfterSalesHelper;

    private final IPriceCalculator<BusPrescriptionDrugs> cmPiecesDrugPriceCalculator = new CmPiecesDrugPriceCalculator<>();

    @Resource @Lazy
    private ApplicationEventPublisher publisher;

    @Resource @Lazy
    private OrderAgreeRefundExchangeReceivedEventProducer orderAgreeRefundExchangeReceivedEventProducer;

    @Resource @Lazy
    private OrderAgreeRefundExchangeRefuseReceivedEventProducer orderAgreeRefundExchangeRefuseReceivedEventProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject isRefund(BusOrderAfterSalesDTO dto) {
        log.info("退款前查询配送企业入参：{}", dto);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("flag", true);
        // 查询售后信息
        BusOrderAfterSales afterSales = orderAfterSalesMapper.selectById(dto.getId());
        String refundType;
        List<Long> subOrderIds = new ArrayList<>();
        if(Objects.nonNull(afterSales)) {
            // 商品不判断配送企业
            if (YesNoEnum.YES.getCode().equals(afterSales.getAfterSalesType())) {
                return jsonObject;
            }
            refundType = afterSales.getRefundType();
            subOrderIds.add(afterSales.getOrderId());
        } else {
            LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(BusOrder::getOrderNo, dto.getOrderNo())
                    .eq(BusOrder::getSubOrderType, CodeEnum.NO.getCode());
            List<BusOrder> orderList = orderMapper.selectList(lambdaQuery);
            if (CollUtil.isEmpty(orderList)) {
                return jsonObject;
            }
            refundType = AfterSaleRefundTypeEnum.REFUND_ONLY.getCode();
            List<Long> subOrderId = orderList.stream().map(BusOrder::getSubOrderId).collect(Collectors.toList());
            subOrderIds.addAll(subOrderId);
        }
        for (Long orderId : subOrderIds) {
            // 查询药品订单信息
            BusDrugsOrder drugsOrder = busDrugsOrderMapper.selectById(orderId);
            if (Objects.isNull(drugsOrder)) {
                throw new ServiceException("订单不存在！");
            }
            // 判断华润是否同意推送售后单
            if (HrOrderStatusEnum.HR_30.getCode().equals(drugsOrder.getHrStatus())) {
                jsonObject.put("flag", false);
                jsonObject.put("msg", "配送企业药品已复核，确认要强制退款吗?");
                return jsonObject;
            }
            // 判断中药配送企业是否同意推送退款单
            List<BusPrescriptionDrugs> prescriptionDrugsList = busPrescriptionDrugsMapper.selectPdList(drugsOrder.getPrescriptionId());
            if (CollectionUtil.isNotEmpty(prescriptionDrugsList)) {
                BusPrescriptionDrugs prescriptionDrugs = prescriptionDrugsList.get(0);
                String identifying = prescriptionDrugs.getIdentifying();
                Long enterpriseId = prescriptionDrugs.getEnterpriseId();
                log.info("配送企业标识：{}", identifying);
                if (StringUtils.isNotEmpty(identifying)) {
                    if (!AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(refundType)) {
                        if (EnterpriseEnum.DX.getInfo().equals(identifying)) {
                            R<JSONObject> objectR = remoteDXService.presRefund(drugsOrder.getHospitalId(), enterpriseId, drugsOrder.getOrderNo());
                            JSONObject data = objectR.getData();
                            log.info("退款大翔返回结果:{}", data);
                            String code = data.getString("dhy_error_code");
                            String msg = data.getString("dhy_error_msg");
                            if (!code.equals(CodeEnum.NO.getCode())) {
                                if ("该处方已经进入调配".equals(msg)) {
                                    jsonObject.put("flag", false);
                                    jsonObject.put("msg", "配送企业处方已经开始煎煮，确认要强制退款吗?");
                                    return jsonObject;
                                }
                            }
                        } else if (EnterpriseEnum.ZX.getInfo().equals(identifying)) {
                            CannelOrderDTO cannelOrderDTO = new CannelOrderDTO();
                            cannelOrderDTO.setHospitalOrderCode(drugsOrder.getZxOrderNumber());
                            log.info("进入至信退单cancelOrderDTO：{}", cannelOrderDTO);
                            R<BusHospitalEnterprise> result =
                                    remoteHosEntService.getByHosIdAndEntId(drugsOrder.getHospitalId(), enterpriseId);
                            log.info("远程调用查询配送企业配置信息返回：{}", result);
                            if (!Constants.SUCCESS.equals(result.getCode())) {
                                log.error("至信退单失败：{}", result.getMsg());
                            }
                            if (Objects.nonNull(result.getData()) && StringUtils.isNotEmpty(result.getData().getConfig())) {
                                List<JSONObject> configs = JSONObject.parseArray(result.getData().getConfig(), JSONObject.class);
                                for (JSONObject config : configs) {
                                    if ("cusCode".equals(config.get("msgKey"))) {
                                        cannelOrderDTO.setCusCode(config.getString("msgValue"));
                                    }
                                }
                            } else {
                                log.error("至信退单失败，读取配置信息为空");
                            }
                            R<String> objectR = remoteZXService.cannelOrder(cannelOrderDTO);
                            log.info("退单至信返回结果1:{}", objectR);
                            if (Objects.isNull(objectR) || !objectR.isSuccess()) {
                                throw new ServiceException("至信退单失败！");
                            }
                            JSONObject data = JSONObject.parseObject(objectR.getData());
                            log.info("退单至信返回结果2:{}", data);
                            String code = data.getString("code");
                            String msg = data.getString("msg");
                            if ("400".equals(code)) {
                                if ("该处方已经配药煎煮,无法取消!".equals(msg)) {
                                    jsonObject.put("flag", false);
                                    jsonObject.put("msg", "配送企业处方已经开始煎煮，确认要强制退款吗?");
                                    return jsonObject;
                                }
                            }
                            if (Constants.SUCCESS.toString().equals(code)) {
                                log.info("至信已成功取消订单，订单号：{}", drugsOrder.getOrderNo());
                            }
                        }
                    }
                }
            }
        }
        return jsonObject;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AfterSalesRefundVO refund(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        log.info("退款调用开始：{}", orderAfterSalesDTO);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询售后信息，并插入协商记录");
        boolean refundFlat = false;
        Long id = orderAfterSalesDTO.getId();
        BusOrderAfterSales orderAfterSales = orderAfterSalesMapper.selectById(id);
        if (!OrderAfterSalesStatusEnum.APPLY_AFTER_SALES_SERVICE.getCode().equals(orderAfterSales.getAfterSalesStatus())) {
            throw new ServiceException("退款操作失败,售后单已被操作");
        }
        //插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        OrderAfterSalesStatusEnum salesStatusEnum = OrderAfterSalesStatusEnum.getTypeByCode(orderAfterSalesDTO.getAfterSalesStatus());
        //同意或拒绝原因
        OrderAfterSalesRefundCauseEnum refundCauseEnum = OrderAfterSalesRefundCauseEnum.getTypeByCode(orderAfterSalesDTO.getRefusalCause());
        negotiationRecord.setCause(refundCauseEnum.getInfo());
        negotiationRecord.setAfterSalesStatusCode(salesStatusEnum.getCode());
        //判断是否同意退款
        switch (salesStatusEnum) {
            case DISAPPROVED_AFTER_SALE:
                negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.JJTK);
                break;
            case AFTER_SALE:
                negotiationRecord.setRefundAmount(orderAfterSalesDTO.getRefundAmount());
                negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.TYTK);
                refundFlat = true;
                break;
            default:
                break;
        }
        //执行插入
        negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getReplenishVoucher());
        negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getReplenishDescription());
        negotiationRecord.setRefundType(AfterSaleRefundTypeEnum.REFUND_ONLY.getCode());
        negotiationRecord.setOrderAfterSalesId(orderAfterSales.getId());
        negotiationRecord.setRoleType(CodeEnum.YES.getCode());
        negotiationRecord.setHospitalId(orderAfterSales.getHospitalId());
        negotiationRecord.setRefundType(orderAfterSales.getRefundType());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecordMapper.insert(negotiationRecord);
        AfterSalesRefundVO afterSalesRefundVO = new AfterSalesRefundVO();
        afterSalesRefundVO.setHospitalId(orderAfterSales.getHospitalId());
        afterSalesRefundVO.setOrderNo(orderAfterSales.getOrderNo());
        stopWatch.stop();
        //判断是否退款
        if (refundFlat) {
            stopWatch.start("第三方配送企业取消配送");
            if (YesNoEnum.NO.getCode().equals(orderAfterSales.getAfterSalesType())) {
                BusDrugsOrder busDrugsOrder = busDrugsOrderMapper.selectById(orderAfterSales.getOrderId());
                if (OrderDrugsTypeEnum.ENTERPRISE.getCode().equals(busDrugsOrder.getOrderDrugsType())) {
                    // 判断是否走配送企业
                    orderService.splitOrder(orderAfterSalesDTO.getOrderNo(), orderAfterSales);
                }
                orderService.pushOrderToYF(busDrugsOrder.getOrderNo(), orderAfterSales.getHospitalId());
            }
            stopWatch.stop();
            stopWatch.start("售后直接退款");
            // 退款
            BusOrder busOrder = this.payRefund(orderAfterSalesDTO, orderAfterSales);
            stopWatch.stop();
            stopWatch.start("溯源码退回状态更新");
            //判断是否退回溯源码
            afterSalesRefundVO.setNeedReturnTraceCode(needReturnDrugTraceCode(busOrder, orderAfterSales, false));
            stopWatch.stop();
        } else {
            stopWatch.start("更新售后订单-拒绝退款");
            //更新售后
            BusOrderAfterSales updateOrderAfterSales = new BusOrderAfterSales(orderAfterSalesDTO.getId(), orderAfterSalesDTO.getAfterSalesStatus());
            orderAfterSalesMapper.updateById(updateOrderAfterSales);
            // 发送订单拒绝退款事件通知
            OrderRefuseRefundEvent orderRefuseRefundEvent = new OrderRefuseRefundEvent();
            orderRefuseRefundEvent.setTotalOrderNo(orderAfterSales.getOrderNo());
            orderRefuseRefundEvent.setOrderType(orderAfterSales.getAfterSalesType().toString());
            publisher.publishEvent(orderRefuseRefundEvent);
            stopWatch.stop();
        }
        log.debug("退款执行时间统计：{}", stopWatch.prettyPrint());
        return afterSalesRefundVO;
    }

    /**
     * 退货或退换货售后申请
     *
     * @param orderAfterSalesDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int returnRefund(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        // 退货退款
        boolean pushReturnRefundFlat = false;
        // 退换货
        boolean pushChangingRefundFlat = false;
        Long id = orderAfterSalesDTO.getId();
        BusOrderAfterSales orderAfterSales = orderAfterSalesMapper.selectById(id);
        //插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        OrderAfterSalesStatusEnum salesStatusEnum = OrderAfterSalesStatusEnum.getTypeByCode(orderAfterSalesDTO.getAfterSalesStatus());
        //同意或拒绝原因
        OrderAfterSalesReturnCauseEnum returnCauseEnum = OrderAfterSalesReturnCauseEnum.getTypeByCode(orderAfterSalesDTO.getRefusalCause());
        negotiationRecord.setCause(returnCauseEnum.getInfo());
        negotiationRecord.setAfterSalesStatusCode(salesStatusEnum.getCode());
        //判断是否同意退货
        switch (salesStatusEnum) {
            case AGREE_TO_RETURN_AFTER_SALE:
                negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getReplenishVoucher());
                negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getReplenishDescription());
                negotiationRecord.setReturnAddress(orderAfterSalesDTO.getReturnAddress());
                if(AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(orderAfterSales.getRefundType())){
                    negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.TYTKDDSH);
                }else{
                    negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.HHTH);
                }
                pushReturnRefundFlat = true;
                pushChangingRefundFlat = true;
                break;
            case DISAPPROVED_AFTER_SALE:
                negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getReplenishVoucher());
                negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getReplenishDescription());
                if(AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(orderAfterSales.getRefundType())){
                    negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.JJTKTH);
                }else{
                    negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.JJTHHH);
                }
                break;
            default:
                break;
        }
        //更新售后
        BusOrderAfterSales updateOrderAfterSales = new BusOrderAfterSales(orderAfterSalesDTO.getId(), orderAfterSalesDTO.getAfterSalesStatus());
        updateOrderAfterSales.setReturnAddress(orderAfterSalesDTO.getReturnAddress());
        updateOrderAfterSales.setReturnContactPhone(orderAfterSalesDTO.getReturnContactPhone());
        updateOrderAfterSales.setReturnContact(orderAfterSalesDTO.getReturnContact());
        orderAfterSalesMapper.updateById(updateOrderAfterSales);
        //执行插入
        negotiationRecord.setRefundType(orderAfterSales.getRefundType());
        negotiationRecord.setOrderAfterSalesId(orderAfterSales.getId());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecord.setRoleType(CodeEnum.YES.getCode());
        negotiationRecord.setHospitalId(orderAfterSales.getHospitalId());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecordMapper.insert(negotiationRecord);
        // 获取售后方式枚举
        AfterSaleRefundTypeEnum afterSaleRefundTypeEnum = AfterSaleRefundTypeEnum.getTypeByCode(orderAfterSales.getRefundType());
        // 根据售后方式判断推送文案
        switch (afterSaleRefundTypeEnum) {
            // 退货退款
            case REFUND_RETURN:
                if (pushReturnRefundFlat) {
                    // 发送订单同意退款退货事件通知
                    OrderAgreeRefundReturnEvent orderAgreeRefundReturnEvent = new OrderAgreeRefundReturnEvent();
                    orderAgreeRefundReturnEvent.setTotalOrderNo(orderAfterSales.getOrderNo());
                    orderAgreeRefundReturnEvent.setOrderType(orderAfterSales.getAfterSalesType().toString());
                    publisher.publishEvent(orderAgreeRefundReturnEvent);
                } else {
                    // 发送订单拒绝退款退货事件通知
                    OrderRefuseRefundReturnEvent orderRefuseRefundReturnEvent = new OrderRefuseRefundReturnEvent();
                    orderRefuseRefundReturnEvent.setTotalOrderNo(orderAfterSales.getOrderNo());
                    orderRefuseRefundReturnEvent.setOrderType(orderAfterSales.getAfterSalesType().toString());
                    publisher.publishEvent(orderRefuseRefundReturnEvent);
                }
                break;
            // 退换货
            case REFUNDS_EXCHANGES:
                if (pushChangingRefundFlat) {
                    // 发送订单同意退换货事件通知
                    OrderAgreeRefundExchangeEvent orderAgreeRefundExchangeEvent = new OrderAgreeRefundExchangeEvent();
                    orderAgreeRefundExchangeEvent.setTotalOrderNo(orderAfterSales.getOrderNo());
                    orderAgreeRefundExchangeEvent.setOrderType(orderAfterSales.getAfterSalesType().toString());
                    publisher.publishEvent(orderAgreeRefundExchangeEvent);
                } else {
                    // 发送订单拒绝退换货事件通知
                    OrderRefuseRefundExchangeEvent orderRefuseRefundExchangeEvent = new OrderRefuseRefundExchangeEvent();
                    orderRefuseRefundExchangeEvent.setTotalOrderNo(orderAfterSales.getOrderNo());
                    orderRefuseRefundExchangeEvent.setOrderType(orderAfterSales.getAfterSalesType().toString());
                    publisher.publishEvent(orderRefuseRefundExchangeEvent);
                }
                break;
            default:
                break;
        }
        return 1;
    }

    @Override
    public int changingRefundConfirmReceipt(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        boolean pushFlat = false;
        Long id = orderAfterSalesDTO.getId();
        BusOrderAfterSales orderAfterSales = orderAfterSalesMapper.selectById(id);
        if (OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(orderAfterSales.getAfterSalesStatus())) {
            throw new ServiceException("退换货确认收货操作失败,售后单已被操作");
        }
        // 插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        OrderAfterSalesStatusEnum salesStatusEnum = OrderAfterSalesStatusEnum.getTypeByCode(orderAfterSalesDTO.getAfterSalesStatus());
        // 退换货拒绝原因
        OrderAfterSalesChangingRefundEnum returnCauseEnum = OrderAfterSalesChangingRefundEnum.getTypeByCode(orderAfterSalesDTO.getRefusalCause());
        if(StringUtils.isNotEmpty(orderAfterSalesDTO.getRefusalCause())){
            negotiationRecord.setCause(returnCauseEnum.getInfo());
        }else{
            negotiationRecord.setCause("退换货确认收货！");
        }
        // 判断退换货是否确认收货
        negotiationRecord.setAfterSalesStatusCode(salesStatusEnum.getCode());
      switch (salesStatusEnum) {
            // 已拒绝收货，待买家处理
            case REFUSE_RECEIVE_GOODS:
                negotiationRecord.setReturnAddress(orderAfterSalesDTO.getReturnAddress());
                negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getReplenishDescription());
                negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.JJSH);
                break;
            // 已确认收货，待重新发货
            case CONFIRM_RECEIPT_RESEND_GOODS:
                negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getReplenishVoucher());
                negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.YQRSH);
                pushFlat = true;
                break;
            default:
                break;
        }
        //更新售后
        BusOrderAfterSales updateOrderAfterSales = new BusOrderAfterSales(orderAfterSalesDTO.getId(), orderAfterSalesDTO.getAfterSalesStatus());
        orderAfterSalesMapper.updateById(updateOrderAfterSales);
        //执行插入
        negotiationRecord.setRefundType(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode());
        negotiationRecord.setOrderAfterSalesId(orderAfterSales.getId());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecord.setRoleType(CodeEnum.YES.getCode());
        negotiationRecord.setHospitalId(orderAfterSales.getHospitalId());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecordMapper.insert(negotiationRecord);
        if (!pushFlat) {
            // 发送订单同意退换货后拒绝收货事件通知  同意收货事件迁移到生成新订单里面去了
            OrderAgreeRefundExchangeRefuseReceivedEvent orderAgreeRefundExchangeRefuseReceivedEvent = new OrderAgreeRefundExchangeRefuseReceivedEvent();
            orderAgreeRefundExchangeRefuseReceivedEvent.setTotalOrderNo(orderAfterSales.getOrderNo());
            orderAgreeRefundExchangeRefuseReceivedEvent.setOrderType(orderAfterSales.getAfterSalesType().toString());
            orderAgreeRefundExchangeRefuseReceivedEventProducer.send(orderAgreeRefundExchangeRefuseReceivedEvent);
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AfterSalesReceiveGoodsResultVO receiveGoods(BusOrderAfterSalesDTO orderAfterSalesDTO) {
        log.info("医院确认到货请求参数:{}", orderAfterSalesDTO);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询售后当信息，并插入协商记录");
        boolean refundFlat = false;
        Long id = orderAfterSalesDTO.getId();
        BusOrderAfterSales orderAfterSales = orderAfterSalesMapper.selectById(id);
        //插入协商记录
        BusNegotiationRecord negotiationRecord = new BusNegotiationRecord();
        OrderAfterSalesStatusEnum salesStatusEnum = OrderAfterSalesStatusEnum.getTypeByCode(orderAfterSalesDTO.getAfterSalesStatus());
        negotiationRecord.setAfterSalesStatusCode(salesStatusEnum.getCode());
        AfterSalesReceiveGoodsResultVO resultVO = new AfterSalesReceiveGoodsResultVO();
        resultVO.setHospitalId(orderAfterSales.getHospitalId());
        resultVO.setOrderNo(orderAfterSales.getOrderNo());
        //判断是否同意退货
        switch (salesStatusEnum) {
            case AFTER_SALE:
                negotiationRecord.setRefundAmount(orderAfterSalesDTO.getRefundAmount());
                negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.QRSH);
                refundFlat = true;
                break;
            case REFUSE_RECEIVE_GOODS:
                //同意或拒绝原因
                OrderAfterSalesReturnRefundCauseEnum returnRefundCauseEnum = OrderAfterSalesReturnRefundCauseEnum.getTypeByCode(orderAfterSalesDTO.getRefusalCause());
                negotiationRecord.setCause(returnRefundCauseEnum.getInfo());
                negotiationRecord.setReplenishVoucher(orderAfterSalesDTO.getReplenishVoucher());
                negotiationRecord.setReplenishDescription(orderAfterSalesDTO.getReplenishDescription());
                negotiationRecord.setAfterSalesStatus(OrderAfterSalesConstants.JJSH);
                break;
            default:
                break;
        }
        //执行插入
        negotiationRecord.setRefundType(AfterSaleRefundTypeEnum.REFUND_RETURN.getCode());
        negotiationRecord.setOrderAfterSalesId(orderAfterSalesDTO.getId());
        negotiationRecord.setCreateTime(DateUtils.getNowDate());
        negotiationRecord.setRoleType(CodeEnum.YES.getCode());
        negotiationRecord.setHospitalId(orderAfterSales.getHospitalId());
        negotiationRecord.setCreateBy(SecurityUtils.getUsername());
        negotiationRecordMapper.insert(negotiationRecord);
        stopWatch.stop();
        if (refundFlat) {
            stopWatch.start("售后退货退款");
            //退款
            BusOrder busOrder = this.payRefund(orderAfterSalesDTO, orderAfterSales);
            stopWatch.stop();
            stopWatch.start("溯源码退回状态更新");
            //设置是否退回医保溯源码
            resultVO.setNeedReturnTraceCode(needReturnDrugTraceCode(busOrder, orderAfterSales, true));
            stopWatch.stop();
        } else {
            stopWatch.start("更新售后订单-拒绝退货");
            //更新售后
            BusOrderAfterSales updateOrderAfterSales = new BusOrderAfterSales(orderAfterSalesDTO.getId(), orderAfterSalesDTO.getAfterSalesStatus());
            orderAfterSalesMapper.updateById(updateOrderAfterSales);
            //发送订单同意退款退货后拒绝收货事件通知
            OrderAgreeRefundReturnRefuseReceivedEvent orderAgreeRefundReturnRefuseReceivedEvent = new OrderAgreeRefundReturnRefuseReceivedEvent();
            orderAgreeRefundReturnRefuseReceivedEvent.setTotalOrderNo(orderAfterSales.getOrderNo());
            orderAgreeRefundReturnRefuseReceivedEvent.setOrderType(orderAfterSales.getAfterSalesType().toString());
            publisher.publishEvent(orderAgreeRefundReturnRefuseReceivedEvent);
            stopWatch.stop();
        }
        log.debug("医院确认到货事件统计：{}", stopWatch.prettyPrint());
        return resultVO;
    }

    /**
     * 退换货重新生成订单实现
     *
     * @param changingRefundNewOrderInDTO
     * @return
     */
    @Override
    public String changingRefundNewOrder(ChangingRefundNewOrderInDTO changingRefundNewOrderInDTO) {
        log.info("退换货重新生成新订单，入参：{}", changingRefundNewOrderInDTO);
        log.info("退换货重新生成新订单，入参原订单号：{}", changingRefundNewOrderInDTO.getOrderNo());
        // 新总订单对象
        BusOrder busNewOrder;
        // 原售后订单
        BusOrderAfterSales busOrderAfterSales;
        // OTC标识
        final int OTC_IDENTIFYING = 1;
        // 获取换货商品/药品/处方药
        List<BusAfterSaleGood> busAfterSaleGoodList = changingRefundNewOrderInDTO.getBusAfterSaleGoodList();
        if (busAfterSaleGoodList == null || busAfterSaleGoodList.size() == 0) {
            throw new ServiceException("退换货请传入相应商品或药品！");
        }
        // 日期-生成订单使用
        Date nowDate = DateUtils.getNowDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        // 售后详情
        busOrderAfterSales = orderAfterSalesMapper.selectById(changingRefundNewOrderInDTO.getAfterSaleId());
        // 查询原订单信息
        BusOrder busOrder = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                .eq(BusOrder::getOrderNo, changingRefundNewOrderInDTO.getOrderNo())
                .eq(BusOrder::getHospitalId, changingRefundNewOrderInDTO.getHospitalId())
                .eq(BusOrder::getSubOrderId, busOrderAfterSales.getOrderId())
                .eq(BusOrder::getSubOrderType, busOrderAfterSales.getAfterSalesType()));

        String newOrderNo = "" ;
        // 生成商品、药品新订单
        if(null != busOrder){
            busOrder.setId(null);
            busOrder.setFreight("0");
            busOrder.setRelPrice(0d);
            busOrder.setAfterSaleId(null);
            // 新总订单编号-方法内公用
            newOrderNo = OrderTypeConstant.TOTAL_ORDER + simpleDateFormat.format(nowDate);
            busOrder.setOrderTime(DateUtils.getNowDate());
            switch (busOrder.getSubOrderType()) {
                // 处方药与非处方药子订单处理
                case ORDER_TYPE_DRUG_ORDER:
                    // 药品订单处理
                    createDrugsOrder(changingRefundNewOrderInDTO, OTC_IDENTIFYING, busAfterSaleGoodList,
                            nowDate, simpleDateFormat, busOrder, newOrderNo);

                    // 售后ID
                    busOrder.setAfterSaleId(changingRefundNewOrderInDTO.getAfterSaleId());
                    busOrder.setOrderStatus(DrugsOrderEnum.TO_BE_SHIPPED.getCode());
                    busOrder.setPaymentTime(null);
                    // 处方药或otc药品-总订单新增数据
                    orderMapper.insert(busOrder);
                    // 原售后订单改为售后关闭
                    busOrderAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.AFTER_SALE.getCode());
                    log.info("药品otc或处方修改原售后对象:{}", busOrderAfterSales);
                    orderAfterSalesMapper.updateById(busOrderAfterSales);
                    break;
                // 商品子订单处理
                case ORDER_TYPE_SHOP_ORDER:
                    //商品订单生成
                    BusShopOrder shopOrderJsonObj = getBusShopOrder(changingRefundNewOrderInDTO, busAfterSaleGoodList, nowDate, simpleDateFormat, busOrder);
                    // 总订单公共属性赋值
                    busOrder.setOrderNo(newOrderNo);
                    // 商品订单id
                    busOrder.setSubOrderId(shopOrderJsonObj.getId());
                    // 售后ID
                    busOrder.setAfterSaleId(changingRefundNewOrderInDTO.getAfterSaleId());
                    busOrder.setOrderStatus(DrugsOrderEnum.TO_BE_SHIPPED.getCode());
                    busOrder.setPaymentTime(null);
                    // 商品-总订单新增数据
                    orderMapper.insert(busOrder);
                    // 原售后订单改为售后关闭
                    busOrderAfterSales.setAfterSalesStatus(OrderAfterSalesStatusEnum.AFTER_SALE.getCode());
                    log.info("商品修改原售后对象:{}", busOrderAfterSales);
                    orderAfterSalesMapper.updateById(busOrderAfterSales);
                    break;
                default:
                    break;
            }
        }
        // 发送订单同意退换货后收货事件通知
        if(StrUtil.isNotBlank(newOrderNo)) {

            log.info("changingRefundNewOrder-oldOrderNo is {}, changingRefundNewOrder-newOrderNo is {}", changingRefundNewOrderInDTO.getOrderNo(), newOrderNo);

            this.copyChannelOrder(changingRefundNewOrderInDTO.getOrderNo(), newOrderNo);

            List<BusInvoice> invoiceList = invoiceMapper.selectList(new LambdaQueryWrapper<BusInvoice>()
                    .eq(BusInvoice::getOrderNo, changingRefundNewOrderInDTO.getOrderNo())
                    .isNotNull(BusInvoice::getInvoiceCode));

            log.info("changingRefundNewOrder-invoiceList is {}", invoiceList);

            if ( null!=invoiceList && !invoiceList.isEmpty() ) {
                Date now = new Date();

                for (BusInvoice newInvoice : invoiceList) {
                    newInvoice.setId(null) ;
                    newInvoice.setOrderNo(newOrderNo);
                    newInvoice.setCreateTime(now) ;
                    newInvoice.setUpdateTime(now) ;
                    invoiceMapper.insert(newInvoice) ;
                }
            }

        }

        // 发送订单同意退换货后收货事件通知
        OrderAgreeRefundExchangeReceivedEvent orderAgreeRefundExchangeReceivedEvent = new OrderAgreeRefundExchangeReceivedEvent();
        orderAgreeRefundExchangeReceivedEvent.setHospitalId(busOrderAfterSales.getHospitalId());
        orderAgreeRefundExchangeReceivedEvent.setTotalOrderNo(changingRefundNewOrderInDTO.getOrderNo());
        orderAgreeRefundExchangeReceivedEvent.setOrderType(busOrderAfterSales.getAfterSalesType().toString());
        orderAgreeRefundExchangeReceivedEventProducer.send(orderAgreeRefundExchangeReceivedEvent);

        return "操作成功";
    }

    private void copyChannelOrder(String oldOrderNo, String newOrderNo){

        BusOrder orderReq = new BusOrder();
        orderReq.setOrderNo(oldOrderNo);
        BusOrder oldOrder = orderMapper.findTotalOrderInfo(orderReq).get(0) ;

        BusChannelOrder channelOrderReq = new BusChannelOrder();
        channelOrderReq.setOrderId(oldOrder.getId());
        channelOrderReq.setType(ChannelOrderEnum.TO.getCode());
        List<BusChannelOrder> channelOrderList = channelOrderMapper.findChannelOrderByCondition(channelOrderReq);
        if ( null==channelOrderList || channelOrderList.isEmpty() ) {
            return ;
        }

        BusChannelOrder oldChannelOrder = channelOrderList.get(0) ;

        orderReq.setOrderNo(newOrderNo);
        BusOrder newOrder = orderMapper.findTotalOrderInfo(orderReq).get(0) ;

        Date now = new Date();
        oldChannelOrder.setId(null) ;
        oldChannelOrder.setOrderId(newOrder.getId()) ;
        oldChannelOrder.setCreateTime(now) ;
        oldChannelOrder.setUpdateTime(now) ;
        oldChannelOrder.setVersion(null);
        oldChannelOrder.setCommissionStatus(null);
        channelOrderMapper.insert(oldChannelOrder) ;
    }

    /**
     * 换货生成对应的商品订单
     * @param changingRefundNewOrderInDTO
     * @param busAfterSaleGoodList
     * @param nowDate
     * @param simpleDateFormat
     * @param busOrder
     * @return
     */
    public BusShopOrder getBusShopOrder(ChangingRefundNewOrderInDTO changingRefundNewOrderInDTO, List<BusAfterSaleGood> busAfterSaleGoodList, Date nowDate, SimpleDateFormat simpleDateFormat, BusOrder busOrder) {
        // 原商品订单
        R<BusShopOrder> busShopOrder;
        // 远程查询商品订单
        busShopOrder = remoteBusShopOrderService.selectShopOrderById(busOrder.getSubOrderId());
        // 商品订单改为0元订单,并把不需要的数据设置为空
        BusShopOrder shopOrderJsonObj = busShopOrder.getData();
        BusShopOrderDTO busShopOrderDTO = new BusShopOrderDTO();
        String shopOrderNo = OrderTypeConstant.GOODS_ORDER + simpleDateFormat.format(nowDate);
        busShopOrderDTO.setOrderNo(shopOrderNo);
        busShopOrderDTO.setReceivingTel(shopOrderJsonObj.getReceivingTel());
        busShopOrderDTO.setDeliveryType(shopOrderJsonObj.getDeliveryType());
        busShopOrderDTO.setReceivingUser(shopOrderJsonObj.getReceivingUser());
        busShopOrderDTO.setReceivingAddress(shopOrderJsonObj.getReceivingAddress());
        busShopOrderDTO.setStatus(ShopOrderEnum.TO_BE_SHIPPED.getCode());
        busShopOrderDTO.setProvince(shopOrderJsonObj.getProvince());
        busShopOrderDTO.setArea(shopOrderJsonObj.getArea());
        busShopOrderDTO.setCity(shopOrderJsonObj.getCity());
        busShopOrderDTO.setDetailedAddress(shopOrderJsonObj.getDetailedAddress());
        busShopOrderDTO.setHospitalId(shopOrderJsonObj.getHospitalId());
        busShopOrderDTO.setHospitalName(shopOrderJsonObj.getHospitalName());
        busShopOrderDTO.setDelStatus(shopOrderJsonObj.getDelStatus());
        busShopOrderDTO.setPatientId(shopOrderJsonObj.getPatientId());
        busShopOrderDTO.setFreight(null);
        busShopOrderDTO.setAmount(new BigDecimal(0));
        busShopOrderDTO.setCreateTime(DateUtils.getNowDate());
        busShopOrderDTO.setReferrer(shopOrderJsonObj.getReferrer());
        //fix-修复缺少订单时间（2024-6-25）
        busShopOrderDTO.setOrderTime(nowDate);

        busShopOrderDTO.setFamilyId(shopOrderJsonObj.getFamilyId());
        busShopOrderDTO.setFamilyName(shopOrderJsonObj.getFamilyName());
        busShopOrderDTO.setFamilySex(shopOrderJsonObj.getFamilySex());
        busShopOrderDTO.setFamilyAge(shopOrderJsonObj.getFamilyAge());

        // 远程调用-生成新的商品订单
        remoteBusShopOrderService.addShopOrder(busShopOrderDTO);
        // 查询商品订单插入的主键
        busShopOrder = remoteBusShopOrderService.selectShopOrderByNo(shopOrderNo);
        shopOrderJsonObj =busShopOrder.getData();
        // 补充商品订单详情信息
        for (BusAfterSaleGood busAfterSaleGood : busAfterSaleGoodList) {
            // 插入商品信息
            BusOrderShop busOrderShop = new BusOrderShop();
            busOrderShop.setOrderId(shopOrderJsonObj.getId());
            busOrderShop.setShopId(busAfterSaleGood.getId());
            busOrderShop.setShopImg(busAfterSaleGood.getImg());
            busOrderShop.setSellingPrice(busAfterSaleGood.getPrice());
            busOrderShop.setQuantity(busAfterSaleGood.getQuantity());
            busOrderShop.setShopTitle(busAfterSaleGood.getTitle());
            busOrderShop.setShopName(busAfterSaleGood.getName());
            busOrderShop.setShopSpecification(busAfterSaleGood.getSpec());
            busOrderShop.setHospitalId(changingRefundNewOrderInDTO.getHospitalId());
            busOrderShop.setCreateTime(DateUtils.getNowDate());
            busOrderShop.setReferencePurchasePrice(busAfterSaleGood.getReferencePurchasePrice());
            busOrderShop.setEnterpriseId(busAfterSaleGood.getEnterpriseId());
            remoteBusOrderShopService.addOrderShop(busOrderShop);
        }
        return shopOrderJsonObj;
    }

    /**
     * 换货生成对应的药品订单 + 添加包裹记录
     * @param changingRefundNewOrderInDTO
     * @param OTC_IDENTIFYING
     * @param busAfterSaleGoodList
     * @param nowDate
     * @param simpleDateFormat
     * @param busOrder
     * @param newOrderNo
     */
    public void createDrugsOrder(ChangingRefundNewOrderInDTO changingRefundNewOrderInDTO, int OTC_IDENTIFYING, List<BusAfterSaleGood> busAfterSaleGoodList, Date nowDate, SimpleDateFormat simpleDateFormat, BusOrder busOrder, String newOrderNo) {
        // 原药品订单
        BusDrugsOrder busDrugsOrder;
        // 查old药品订单
        busDrugsOrder = drugsOrderMapper.selectOne(new LambdaQueryWrapper<BusDrugsOrder>()
                .eq(BusDrugsOrder::getId, busOrder.getSubOrderId()));
        // 1.创建新药品订单信息 金额改为0
        busDrugsOrder.setId(null);
        // 运费为0
        busDrugsOrder.setFreight("0");
        // 支付方式为空
        busDrugsOrder.setPayWay(null);
        // 金额制空
        busDrugsOrder.setAmount("0.00");
        // 完成时间设置为空
        busDrugsOrder.setCompleteTime(null);
        // 创建时间
        busDrugsOrder.setCreateTime(new Date());
        // 订单状态待发货
        busDrugsOrder.setStatus(DrugsOrderEnum.TO_BE_SHIPPED.getCode());
        // 生成新药品订单号
        busDrugsOrder.setOrderNo(OrderTypeConstant.DRUGS_ORDER + simpleDateFormat.format(nowDate));
        //fix-修复缺少订单时间（2024-6-25）
        busDrugsOrder.setOrderTime(nowDate);
        // 插入新药品子订单
        drugsOrderMapper.insert(busDrugsOrder);
        // 获取新药品子订单ID
        long drugOrderId = drugsOrderMapper.selectOne(new LambdaQueryWrapper<BusDrugsOrder>()
                .eq(BusDrugsOrder::getOrderNo, busDrugsOrder.getOrderNo())).getId();
        // 总订单公共属性赋值
        busOrder.setOrderNo(newOrderNo);
        // 两种情况 插入药品订单id 处方或OTC药
        busOrder.setSubOrderId(drugOrderId);
        // 判断退换货 OTC药品
        if (busDrugsOrder.getPrescriptionId() == null) {
            // 插入药品详情信息
            for (BusAfterSaleGood busAfterSaleGood : busAfterSaleGoodList) {
                // 判断是否为OTC药
                if (busAfterSaleGood.getIsOtc() == OTC_IDENTIFYING) {
                    // 插入OTC药品
                    BusOtcDrugs busOtcDrugs = new BusOtcDrugs();
                    busOtcDrugs.setDrugsOrderId(drugOrderId);
                    busOtcDrugs.setDrugsId(busAfterSaleGood.getId());
                    busOtcDrugs.setDrugsImg(busAfterSaleGood.getImg());
                    busOtcDrugs.setSellingPrice(busAfterSaleGood.getPrice());
                    busOtcDrugs.setQuantity(busAfterSaleGood.getQuantity());
                    busOtcDrugs.setDrugsUsageValue(busAfterSaleGood.getDrugsUsageValue());
                    busOtcDrugs.setDrugsManufacturer(busAfterSaleGood.getDrugsManufacturer());
                    busOtcDrugs.setStandardCommonName(busAfterSaleGood.getStandardCommonName());
                    busOtcDrugs.setDrugsName(busAfterSaleGood.getName());
                    busOtcDrugs.setDrugsSpecification(busAfterSaleGood.getSpec());
                    busOtcDrugs.setHospitalId(changingRefundNewOrderInDTO.getHospitalId());
                    busOtcDrugs.setCreateTime(DateUtils.getNowDate());
                    busOtcDrugs.setOtcOrNot(OTC_IDENTIFYING);
                    busOtcDrugs.setReferencePurchasePrice(busAfterSaleGood.getReferencePurchasePrice());
                    busOtcDrugs.setEnterpriseId(busAfterSaleGood.getEnterpriseId());
                    busOtcDrugsMapper.insert(busOtcDrugs);
                }
            }
        }
        log.info("busAfterSaleGoodList:{}",JSON.toJSONString(busAfterSaleGoodList) );
        if(busAfterSaleGoodList.size() > 0){
            // 插入包裹表
            BusDrugOrderPackage busDrugOrderPackage = new BusDrugOrderPackage();
            busDrugOrderPackage.setHospitalId(busOrder.getHospitalId());
            busDrugOrderPackage.setPrescriptionId(busDrugsOrder.getPrescriptionId()==null?null:busDrugsOrder.getPrescriptionId());
            busDrugOrderPackage.setDrugsOrderId(drugOrderId);
            busDrugOrderPackage.setOrderNo(newOrderNo);
            busDrugOrderPackage.setCreateTime(DateUtils.getNowDate());
            //包裹为药品订单
            busDrugOrderPackage.setPackageType(0);
            // 有配送企业的插入包裹
            if(busAfterSaleGoodList.get(0).getEnterpriseId()!=null){
                busDrugOrderPackage.setEnterpriseId(busAfterSaleGoodList.get(0).getEnterpriseId());
                BusEnterprise busEnterprise = busEnterpriseMapper.selectById(busAfterSaleGoodList.get(0).getEnterpriseId());
                busDrugOrderPackage.setEnterpriseName(busEnterprise.getEnterpriseName());
                busDrugOrderPackage.setBarterOrNot(1);
            }

            log.info("busDrugOrderPackage:{}",busDrugOrderPackage);
            busDrugOrderPackageMapper.insert(busDrugOrderPackage);
        }
    }

    /**
     * 支付退款
     *
     * @param orderAfterSalesDTO
     * @param orderAfterSales
     * @return
     */
    private BusOrder payRefund(BusOrderAfterSalesDTO orderAfterSalesDTO, BusOrderAfterSales orderAfterSales) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询订单信息并更新售后订单状态");
        //查询总订单数据
        BusOrder order = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                .eq(BusOrder::getSubOrderId, orderAfterSales.getOrderId())
                .eq(BusOrder::getOrderNo, orderAfterSales.getOrderNo()).last("limit 1"));
        if (Objects.isNull(order)) {
            throw new ServiceException("查询总订单失败");
        }
        busOrderAfterSalesHelper.orderRefunding(orderAfterSales.getId());
        stopWatch.stop();
        //判断支付类型退款
        if (PaysTypeEnum.WECHAT_PAY.getCode().equals(order.getPayWay())) {
            stopWatch.start("调用微信退款接口");
            R refundOrder = remoteWxPayService.refundOrder(order.getOrderNo(), "3",
                    orderAfterSalesDTO.getRefundAmount().doubleValue(), "2", "",
                    StringUtils.isNotEmpty(order.getPartnersCode()) ? CodeEnum.YES.getCode() : null, orderAfterSales.getId());
            if (Constants.FAIL.equals(refundOrder.getCode())) {
                log.error("微信支付退款失败 refundOrder={}", JSON.toJSON(refundOrder));
                busOrderAfterSalesHelper.orderRefundFailed(orderAfterSales.getId(), orderAfterSales.getAfterSalesStatus());
                /*如果返回refundCode,则前端更新为退款失败*/
                throw new ServiceException("调用微信支付退款失败");
            }
            stopWatch.stop();
        } else if (PaysTypeEnum.TONGLIAN_PAY.getCode().equals(order.getPayWay())) {
            stopWatch.start("调用通联退款接口");
            BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
            busRefundPayDTO.setOrderNo(orderAfterSales.getOrderNo());
            busRefundPayDTO.setOrderOtherNo(orderAfterSales.getAfterSalesNumber());
            busRefundPayDTO.setOrderAfterSaleId(orderAfterSales.getId());
            busRefundPayDTO.setRefundAmount(orderAfterSalesDTO.getRefundAmount().doubleValue());
            busRefundPayDTO.setOrderType("3");
            R<JSONObject> tongLianRefund = remoteTongLianPayService.tongLianRefund(busRefundPayDTO);
            if (Constants.FAIL.equals(tongLianRefund.getCode())) {
                log.error("通联支付退款失败 refundOrder={}", JSON.toJSON(tongLianRefund));
                busOrderAfterSalesHelper.orderRefundFailed(orderAfterSales.getId(), orderAfterSales.getAfterSalesStatus());
                /*如果返回refundCode,则前端更新为退款失败*/
                throw new ServiceException("调用通联支付退款失败");
            }
            stopWatch.stop();
        } else {
            // 微信医保退款
            wxInsuranceRefund(orderAfterSales, order);
        }
        log.debug("退款耗时={}", stopWatch.prettyPrint());
        return order;
    }

    /**
     * 微信医保退款
     * @param orderAfterSales - 售后订单
     * @param order - 总订单
     */
    public void wxInsuranceRefund(BusOrderAfterSales orderAfterSales, BusOrder order) {
        StopWatch stopWatch = new StopWatch();
        // 查询是否是微信医保支付
        R<MiPayResult> result = remotePaymentService.queryPayResult(orderAfterSales.getOrderNo());
        if (Constants.SUCCESS != result.getCode()) {
            throw new ServiceException(result.getMsg());
        }
        if (ObjectUtil.isNull(result.getData())) {
            return;
        }
        stopWatch.start("调用医保退款接口");
        MiPayResult miPayResult = result.getData();
        // 医保退费
        UniformRequest request = new UniformRequest();
        request.setHospitalId(order.getHospitalId());
        request.setReqKey("6203");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("payAuthNo", miPayResult.getPayAuthNo());
        jsonObject.put("uldLatlnt", "0,0");
        jsonObject.put("totalOrderNo", miPayResult.getOrderNo());
        request.setParams(jsonObject);
        AjaxResult ajaxResult = remotePreprocessorService.uniformRequest(request);
        if (Objects.isNull(ajaxResult) || !ajaxResult.isSuccess()) {
            log.error("医保退款失败 ajaxResult={}", JSON.toJSON(ajaxResult));
            busOrderAfterSalesHelper.orderRefundFailed(orderAfterSales.getId(), orderAfterSales.getAfterSalesStatus());
            throw new ServiceException(String.format("医保退款失败,原因：%s", ajaxResult.getMsg()));
        }
        stopWatch.stop();
        // 运费退款
        boolean flag = true;
        BigDecimal orderFreight = Objects.nonNull(order.getFreight()) ? new BigDecimal(order.getFreight()) : BigDecimal.ZERO;
        //判断逻辑：1.是否为配送订单 2.运费是否大于0 3.订单运费是否与医保支付运费一致（兼容历史订单退款）
        if (CodeEnum.YES.getCode().equals(order.getDeliveryType())
                && orderFreight.compareTo(BigDecimal.ZERO) > 0
                //如果运费一致，则证明运费已跟着微信医保支付一起去结算过了，则不在单独走运费退款
                && orderFreight.compareTo(miPayResult.getFreight()) != 0) {
            stopWatch.start("调用微信运费退款接口");
            R refundOrder =
                    remoteWxPayService.refundOrder(order.getOrderNo(), OrderTypeEnum.GOODS.getCode(),
                            Double.valueOf(order.getFreight()), "2", "",
                    StringUtils.isNotEmpty(order.getPartnersCode()) ? CodeEnum.YES.getCode() : null,
                            orderAfterSales.getId());
            if (Constants.FAIL.equals(refundOrder.getCode())) {
                log.error("微信退款医保运费失败 refundOrder={}", refundOrder);
                flag = false;
            }
            stopWatch.stop();
        }
        // 修改订单状态
        if (flag) {
            stopWatch.start("订单状态流转");
            // 流转订单状态
            R r = remoteWxPayService.updateOrderStatus(orderAfterSales.getId(), orderAfterSales.getOrderNo());
            log.info("调用app服务修改订单状态={}", r.getCode());
            stopWatch.stop();
        }
        log.debug("退款耗时={}", stopWatch.prettyPrint());
    }

    /**
     * 售后列表
     *
     * @param busOrderAfterSalesListDTO - 售后列表DTO
     * @return - 售后列表VO
     */
    @Override
    public List<BusOrderAfterSalesListVO> listOrderAfterSales(BusOrderAfterSalesListDTO busOrderAfterSalesListDTO) {
        // 修改售后单为旧订单 刷新页面红点
        LambdaUpdateWrapper<BusOrderAfterSales> lambdaUpdate = Wrappers.lambdaUpdate();
        lambdaUpdate.eq(BusOrderAfterSales::getHospitalId, busOrderAfterSalesListDTO.getHospitalId());
        BusOrderAfterSales afterSales = new BusOrderAfterSales();
        afterSales.setNewOrderOrNot(YesNoEnum.NO.getCode());
        orderAfterSalesMapper.update(afterSales, lambdaUpdate);

        List<BusOrderAfterSalesListVO> busOrderAfterSalesListVOS = orderAfterSalesMapper.listOrderAfterSales(busOrderAfterSalesListDTO);
        /*药品*/
        for (BusOrderAfterSalesListVO topItem : busOrderAfterSalesListVOS) {
            topItem.setOverTime(DateUtils.addDays(topItem.getApplyTime(), 7));
            if (YesNoEnum.NO.getCode().equals(topItem.getAfterSalesType())) {
                /*如果是处方药*/
                // 提取为方法
                afterSalePrescriptionDrugsList(topItem);
                /*如果是商品*/
            } else {
                // 提取为方法
                afterSaleGoodsList(topItem);
            }
        }
        return busOrderAfterSalesListVOS;
    }

    /**
     * 药品售后列表
     * @param topItem - 售后列表VO
     */
    private void afterSalePrescriptionDrugsList(BusOrderAfterSalesListVO topItem) {
        if (topItem.getPrescriptionId() == null) {
            /*非处方*/
            topItem.setListType(OrderListTypeEnum.OTC.getCode());
            List<GoodsItemVO> goodsList = new ArrayList<>();
            BusOtcDrugs item = busOtcDrugsMapper.selectOne(new LambdaQueryWrapper<BusOtcDrugs>()
                    .eq(BusOtcDrugs::getDrugsId, topItem.getGoodsId())
                    .eq(BusOtcDrugs::getDrugsOrderId, topItem.getOrderId())
            );
            if (item != null){
                GoodsItemVO goodsItem = new GoodsItemVO();
                /*查询是否发货*/
                BusDrugOrderPackage busDrugOrderPackage = queryOtcSendGoodStatus(topItem.getOrderId(), YesNoEnum.NO.getCode(), topItem.getGoodsId(),item.getEnterpriseId());
                if (busDrugOrderPackage != null && busDrugOrderPackage.getDeliveryTime() != null){
                    goodsItem.setSendOutgoodsStatus(YesNoEnum.YES.getCode());
                } else {
                    goodsItem.setSendOutgoodsStatus(YesNoEnum.NO.getCode());
                }
                goodsItem.setItemName(item.getDrugsName());
                goodsItem.setItemImage(item.getDrugsImg());
                goodsItem.setItemQuantity(topItem.getQuantity());
                goodsItem.setItemSpec(item.getDrugsSpecification());
                goodsItem.setItemPrice(item.getSellingPrice());
                goodsList.add(goodsItem);
                topItem.setItemList(goodsList);
            }
            return;
        }
        // 处方药品
        BusPrescription busPrescription = busPrescriptionMapper.selectById(topItem.getPrescriptionId());
        List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
            .eq(BusPrescriptionDrugs::getPrescriptionId, topItem.getPrescriptionId())
        );
        topItem.setProcessPrice(busPrescription.getProcessPrice());
        topItem.setDecoct(busPrescription.getDecoct());
        topItem.setProcessingMethod(busPrescription.getProcessingMethod());
        topItem.setPrNo(busPrescription.getPrescriptionNumber());
        /*如果是中药方剂或者协定方*/
        if (busPrescription.getPaId() != null) {
            String usages = busPrescription.getUsages();
            String[] split = usages.split(",");
            BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(busPrescription.getPaId());
            if (YesNoEnum.YES.getCode().equals(busHospitalPa.getType())) {
                topItem.setListType(OrderListTypeEnum.CLASSICAL_FORMULA.getCode());
            } else {
                topItem.setListType(OrderListTypeEnum.PARTIES.getCode());
            }
            List<GoodsItemVO> goodsList = new ArrayList<>();
            GoodsItemVO goodsItem = new GoodsItemVO();
            BusDrugOrderPackage busDrugOrderPackage = this.queryChineseMedicinePrescriptionSendGoodStatus(busPrescription, topItem.getOrderId(), YesNoEnum.NO.getCode());
            if (busDrugOrderPackage != null && busDrugOrderPackage.getDeliveryTime() != null) {
                goodsItem.setSendOutgoodsStatus(YesNoEnum.YES.getCode());
            } else {
                goodsItem.setSendOutgoodsStatus(YesNoEnum.NO.getCode());
            }
            StringBuffer stringBuffer = new StringBuffer();
            busPrescriptionDrugs.forEach(item -> {
                stringBuffer.append(item.getDrugsName())
                        .append(item.getDrugsSpecification())
                        .append("×")
                        .append(item.getWeight())
                        .append("  ");
            });
            goodsItem.setItemName(busHospitalPa.getPaName());
            goodsItem.setNumber(busPrescriptionDrugs.size());
            goodsItem.setItemListStr(stringBuffer.toString());
            goodsItem.setItemQuantity(Integer.valueOf(split[1]));
            //因为数据库中 中药方剂的单价可能改变
            //中药方剂单价应为 （金额-加工费）/数量，而不是直接获取数据库中该中药方剂的单价

            //对加工费的非空校验
            BigDecimal processPrice=ObjectUtil.isNull(busPrescription.getProcessPrice())? BigDecimal.ZERO :busPrescription.getProcessPrice();
            //计算单价
            BigDecimal amount =busPrescription.getPrescriptionAmount().subtract(processPrice).
                    divide(BigDecimal.valueOf(goodsItem.getItemQuantity()), RoundingMode.CEILING) ;
            goodsItem.setItemPrice(amount);
            goodsList.add(goodsItem);
            topItem.setItemList(goodsList);
        } else {
            /*如果是中药处方*/
            if (PrescriptionTypeEnum.TCM.getCode().equals(busPrescription.getPrescriptionType())) {
                String usages = busPrescription.getUsages();
                String[] split = usages.split(",");
                topItem.setListType(OrderListTypeEnum.GENERAL_PRESCRIPTION.getCode());
                List<GoodsItemVO> goodsList = new ArrayList<>();
                GoodsItemVO goodsItem = new GoodsItemVO();
                BusDrugOrderPackage drugOrderPackage = this.queryChineseMedicinePrescriptionSendGoodStatus(busPrescription, topItem.getOrderId(), YesNoEnum.NO.getCode());
                if (drugOrderPackage != null && drugOrderPackage.getDeliveryTime() != null) {
                    goodsItem.setSendOutgoodsStatus(YesNoEnum.YES.getCode());
                } else {
                    goodsItem.setSendOutgoodsStatus(YesNoEnum.NO.getCode());
                }
                for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                    //BigDecimal subtotalPrice = new BigDecimal(0);
                    GoodsItemVO vo = new GoodsItemVO();
                    BeanUtils.copyProperties(item, vo);
                    // g/袋  g/瓶  规格*规格数量
                    String specification = String.format("%s × %s", item.getDrugsSpecification(), item.getWeight());
                    //subtotalPrice = totalPriceService.getSubtotalPriceWithChinaDrug(item.getSellingPrice(), new BigDecimal(item.getWeight()),new BigDecimal(split[1]));
                    // 剂量
                    vo.setItemQuantity(Integer.valueOf(split[1]));
                    // 前端说兼容之前的设置，中药饮片剂量放在number里
                    vo.setNumber(Integer.valueOf(split[1]));
                    // 前端说兼容之前的设置，放在itemListStr里
                    vo.setItemListStr(item.getDrugsName());
                    vo.setItemName(item.getDrugsName());
                    vo.setItemSpec(specification);
                    vo.setItemPrice(item.getSellingPrice());
                    //vo.setItemListStr(stringBuffer.toString());
                    goodsList.add(vo);
                }
                topItem.setItemList(goodsList);
                /*如果是西药处方*/
            } else {
                topItem.setListType(OrderListTypeEnum.WESTERN_MEDICINE_PRESCRIPTION.getCode());
                List<GoodsItemVO> goodsList = new ArrayList<>();
                busPrescriptionDrugs.forEach(item -> {
                    GoodsItemVO goodsItem = new GoodsItemVO();
                    BusDrugOrderPackage busDrugOrderPackage = queryWesternMedicinePrescriptionSendGoodStatus(busPrescription, topItem.getOrderId(), YesNoEnum.NO.getCode(), item.getDrugsId());
                    if (busDrugOrderPackage != null&&  busDrugOrderPackage.getDeliveryTime() != null) {
                        goodsItem.setSendOutgoodsStatus(YesNoEnum.YES.getCode());
                    } else {
                        goodsItem.setSendOutgoodsStatus(YesNoEnum.NO.getCode());
                    }
                    goodsItem.setItemName(item.getDrugsName());
                    goodsItem.setItemSpec(item.getDrugsSpecification());
                    goodsItem.setItemQuantity(item.getQuantity());
                    goodsItem.setItemPrice(item.getSellingPrice());
                    goodsItem.setItemImage(item.getDrugsImg());
                    goodsList.add(goodsItem);
                });
                topItem.setItemList(goodsList);
            }
        }
    }

    /**
     * 商品售后列表
     * @param topItem - 售后列表VO
     */
    private void afterSaleGoodsList(BusOrderAfterSalesListVO topItem) {
        List<GoodsItemVO> goodsList = new ArrayList<>();
        topItem.setListType(OrderListTypeEnum.GOODS.getCode());
        R<List<BusOrderShop>> listR = remoteBusOrderShopService.listBusOrderShopByOrderId(topItem.getOrderId());
        if (Constants.FAIL.equals(listR.getCode())){
            throw new ServiceException("查询商品远程调用失败");
        }
        List<BusOrderShop> data = listR.getData();
        List<BusOrderShop> collect = data.stream().filter(item -> item.getShopId().equals(topItem.getGoodsId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            BusOrderShop item = collect.get(0);
            GoodsItemVO goodsItem = new GoodsItemVO();
            /*查询是否发货*/
            BusDrugOrderPackage shopPackage = queryShopSendGoodStatus(topItem.getOrderId(), YesNoEnum.YES.getCode(), topItem.getGoodsId());
            if (shopPackage != null  && shopPackage.getDeliveryTime() != null) {
                goodsItem.setSendOutgoodsStatus(YesNoEnum.YES.getCode());
            } else {
                goodsItem.setSendOutgoodsStatus(YesNoEnum.NO.getCode());
            }
            goodsItem.setItemName(item.getShopName());
            goodsItem.setItemImage(item.getShopImg());
            goodsItem.setItemQuantity(topItem.getQuantity());
            goodsItem.setItemSpec(item.getShopSpecification());
            goodsItem.setItemPrice(item.getSellingPrice());
            goodsList.add(goodsItem);
            topItem.setItemList(goodsList);
        }
    }

    /**
     * 查询中药处方的售后发货状态
     *
     * @param busPrescription
     * @param orderId
     * @param orderType
     */
    private BusDrugOrderPackage queryChineseMedicinePrescriptionSendGoodStatus(BusPrescription busPrescription, Long orderId, Integer orderType) {
        return busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                .eq(BusDrugOrderPackage::getDrugsOrderId, orderId)
                .eq(BusDrugOrderPackage::getPrescriptionId, busPrescription.getId())
                .eq(BusDrugOrderPackage::getPackageType, orderType)
                .orderByAsc(BusDrugOrderPackage::getId)
                .last("limit 1")
        );
    }

    /**
     * 查询西药处方的售后发货状态
     *
     * @param busPrescription
     * @param orderId
     * @param orderType(商品类型 0药品 1商品)
     */
    private BusDrugOrderPackage queryWesternMedicinePrescriptionSendGoodStatus(BusPrescription busPrescription, Long orderId, Integer orderType, Long drugsId) {
        return busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
            .eq(BusDrugOrderPackage::getDrugsOrderId, orderId)
            .eq(Objects.isNull(busPrescription.getEnterpriseId()),BusDrugOrderPackage::getDrugsGoodsId, drugsId)
            .eq(Objects.nonNull(busPrescription.getEnterpriseId()),BusDrugOrderPackage::getEnterpriseId,busPrescription.getEnterpriseId())
            .eq(BusDrugOrderPackage::getPrescriptionId, busPrescription.getId())
            .eq(BusDrugOrderPackage::getPackageType, orderType)
            .last("limit 1")
        );
    }

    /**
     * 查询发货快递公司信息
     * @param orderNo
     * @param drugsOrderId
     * @param barterOrNot
     * @param orderType(商品类型 0药品 1商品)
     * @return
     */
    private BusDrugOrderPackage querySendGoodStatus(String orderNo, Long drugsOrderId,Integer barterOrNot, Integer orderType) {
        return busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                .eq(BusDrugOrderPackage::getOrderNo, orderNo)
                .eq(BusDrugOrderPackage::getDrugsOrderId, drugsOrderId)
                .eq(BusDrugOrderPackage::getBarterOrNot, barterOrNot)
                .eq(BusDrugOrderPackage::getPackageType, orderType)
                .last("limit 1")
        );
    }

    /**
     * 查询发货快递公司信息
     * @param orderNo
     * @param barterOrNot
     * @param orderType(商品类型 0药品 1商品)
     * @return
     */
    private BusDrugOrderPackage querySendGoodStatus(String orderNo, Integer barterOrNot, Integer orderType) {
        return busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
                .eq(BusDrugOrderPackage::getOrderNo, orderNo)
                .eq(BusDrugOrderPackage::getBarterOrNot, barterOrNot)
                .eq(BusDrugOrderPackage::getPackageType, orderType)
                .last("limit 1")
        );
    }

    /**
     * 查询OTC药品的售后发货状态
     *
     * @param orderId
     * @param orderId
     * @param orderType(商品类型 0药品 1商品)
     */
    private BusDrugOrderPackage queryOtcSendGoodStatus(Long orderId, Integer orderType, Long drugsId,Long enterpriseId) {
        return busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
            .eq(BusDrugOrderPackage::getDrugsOrderId, orderId)
            .eq(Objects.isNull(enterpriseId),BusDrugOrderPackage::getDrugsGoodsId, drugsId)
            .eq(BusDrugOrderPackage::getPackageType, orderType)
            .eq(Objects.nonNull(enterpriseId),BusDrugOrderPackage::getEnterpriseId,enterpriseId)
            .last("limit 1")
        );
    }

    /**
     * 查询商品的售后发货状态
     *
     * @param orderId
     * @param orderId
     * @param orderType(商品类型 0药品 1商品)
     */
    private BusDrugOrderPackage queryShopSendGoodStatus(Long orderId, Integer orderType, Long shopId) {
        return busDrugOrderPackageMapper.selectOne(new LambdaQueryWrapper<BusDrugOrderPackage>()
            .eq(BusDrugOrderPackage::getDrugsOrderId, orderId)
            .eq(BusDrugOrderPackage::getDrugsGoodsId, shopId)
            .eq(BusDrugOrderPackage::getPackageType, orderType)
            .last("limit 1")
        );
    }

    @Override
    public BusAfterSaleDetailVO getAfterSaleDetail(Long afterSaleId) {
        BusAfterSaleDetailVO afterSaleDetail = new BusAfterSaleDetailVO();
        // 查询售后单
        BusOrderAfterSales afterSale = orderAfterSalesMapper.selectById(afterSaleId);
        StringUtils.isNullThrowExp(afterSale, "售后单已删除！");
        // 查询订单信息(子订单类型+子订单id+总订单编号)
        BusOrder order = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                .eq(BusOrder::getSubOrderType, afterSale.getAfterSalesType())
                .eq(BusOrder::getSubOrderId, afterSale.getOrderId())
                .eq(BusOrder::getOrderNo, afterSale.getOrderNo()));
        StringUtils.isNullThrowExp(order, "申请售后的订单已删除！");
        BeanUtils.copyProperties(afterSale, afterSaleDetail);
        // 订单信息
        afterSaleDetail.setOrderId(order.getId());
        afterSaleDetail.setOrderNo(order.getOrderNo());
        afterSaleDetail.setOrderStatus(Integer.valueOf(order.getOrderStatus()));
        afterSaleDetail.setDeliveryType(Integer.valueOf(order.getDeliveryType()));
        afterSaleDetail.setOrderTime(order.getOrderTime());
        // 收货信息
        afterSaleDetail.setReceiveName(order.getReceiver());
        afterSaleDetail.setReceivePhone(order.getReceivePhone());
        afterSaleDetail.setReceiveAddress(order.getReceiveAddress());
        // 售后商品/药品信息
        List<BusAfterSaleGood> saleGoods = new ArrayList<>();
        if (YesNoEnum.YES.getCode().equals(afterSale.getAfterSalesType())) {
            // 商品类型
            R<BusOrderShop> r = remoteBusShopOrderService.getOrderShop(afterSale.getOrderId(), afterSale.getGoodsId());
            log.info("远程调用商品订单查询商品返回结果：" + r);
            if (Constants.SUCCESS.equals(r.getCode())) {
                BusOrderShop good = r.getData();
                BusAfterSaleGood saleGood = new BusAfterSaleGood(afterSale.getGoodsId(), good.getShopImg(),
                        good.getShopName(), good.getSellingPrice(), afterSale.getQuantity(), good.getShopSpecification());
                saleGood.setTitle(good.getShopTitle());
                // 药品类型-商品
                saleGood.setDrugsType("1");
                // saleGood.setOrderNo(String.valueOf(good.getOrderId()));
                getGoodsSendStatus(afterSale, saleGoods, saleGood);
            }
        } else {
            if (Objects.nonNull(afterSale.getPrescriptionId())) {
                getRxDrugs(afterSale, afterSaleDetail, saleGoods);
            } else {
                // 查询OTC药品(子订单id+药品id)
                BusOtcDrugs otcDrug = otcDrugsMapper.selectOne(new LambdaQueryWrapper<BusOtcDrugs>()
                        .eq(BusOtcDrugs::getDrugsId, afterSale.getGoodsId())
                        .eq(BusOtcDrugs::getDrugsOrderId, afterSale.getOrderId()));
                BusAfterSaleGood saleGood = new BusAfterSaleGood(afterSale.getGoodsId(), otcDrug.getDrugsImg(),
                        otcDrug.getDrugsName(), otcDrug.getSellingPrice(), afterSale.getQuantity(), otcDrug.getDrugsSpecification());
                saleGood.setDrugsManufacturer(otcDrug.getDrugsManufacturer());
                saleGood.setStandardCommonName(otcDrug.getStandardCommonName());
                saleGood.setDrugsUsageValue(otcDrug.getDrugsUsageValue());
                saleGood.setEnterpriseId(otcDrug.getEnterpriseId());
                saleGood.setReferencePurchasePrice(otcDrug.getReferencePurchasePrice());
                saleGood.setIsOtc(YesNoEnum.YES.getCode());
                // 药品类型-西药
                saleGood.setDrugsType("1");
                getGoodsSendStatus(afterSale, saleGoods, saleGood);
            }
        }
        afterSaleDetail.setSaleItemList(saleGoods);
        // 查询是否有换货订单
        BusOrder replaceOrder = orderMapper.selectOne(
                new LambdaQueryWrapper<BusOrder>().eq(BusOrder::getAfterSaleId, afterSaleId));
        if (Objects.nonNull(replaceOrder)){
            afterSaleDetail.setReplaceOrderNo(replaceOrder.getOrderNo());
        }
        // 查询售后协商记录
        List<BusNegotiationRecord> negotiationList = negotiationService.listNegotiationRecord(afterSaleId);
        afterSaleDetail.setNegotiationList(negotiationList);
        // 倒计时
        if (afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode())) {
            afterSaleDetail.setCloseTime(negotiationList.get(0).getCreateTime());
        } else if (afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.DISAPPROVED_AFTER_SALE.getCode())
                || afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.AGREE_TO_RETURN_AFTER_SALE.getCode())
                || afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.RESHIP_BUYER_RECEIVES_GOODS.getCode())
                || afterSale.getAfterSalesStatus().equals(OrderAfterSalesStatusEnum.REFUSE_RECEIVE_GOODS.getCode())) {
            if (CollUtil.isNotEmpty(negotiationList)) {
                long delayTime = negotiationList.get(0).getCreateTime().getTime() + (1000 * 60 * 60 * 24 * 7);
                afterSaleDetail.setCountDownTime(delayTime);
            }
        }
        log.info("afterSaleDetail:{}",afterSaleDetail);
        return afterSaleDetail;
    }

    /**
     * 查询售后信息
     *
     * @param prescriptionId
     * @param orderId 子订单ID
     * @return
     */
    @Override
    public BusOrderAfterSales selectPrescriptionAfterSales(Long prescriptionId, Long orderId) {
        LambdaQueryWrapper<BusOrderAfterSales> query = Wrappers.lambdaQuery();
        query.eq(BusOrderAfterSales::getPrescriptionId, prescriptionId);
        query.eq(BusOrderAfterSales::getOrderId, orderId);
        return orderAfterSalesMapper.selectOne(query);
    }

    /**
     * 查询otc或商品信息
     *
     * @param code （0药品 1商品）
     * @param orderId 订单ID
     * @param id 药品/商品ID
     * @return
     */
    @Override
    public BusOrderAfterSales selectOtcOrGoodsAfterSales(String code, Long orderId, Long id) {
        LambdaQueryWrapper<BusOrderAfterSales> query = Wrappers.lambdaQuery();
        query.eq(BusOrderAfterSales::getAfterSalesType, code);
        query.eq(BusOrderAfterSales::getOrderId, orderId);
        query.eq(BusOrderAfterSales::getGoodsId, id);
        return orderAfterSalesMapper.selectOne(query);
    }

    @Override
    public List<BusOrderAfterSalesListVO> listOrderAfterSalesExport(BusOrderAfterSalesListDTO busOrderAfterSalesListDTO) {
        List<BusOrderAfterSalesListVO> busOrderAfterSalesListVOS = orderAfterSalesMapper.listOrderAfterSales(busOrderAfterSalesListDTO);
        return busOrderAfterSalesListVOS;
    }

    @Override
    public int updateRefundStatus(String afterSalesNumber) {
        BusOrderAfterSales busOrderAfterSales = new BusOrderAfterSales();
        busOrderAfterSales.setUpdateTime(DateUtils.getNowDate());
        busOrderAfterSales.setUpdateBy(SecurityUtils.getUsername());
        busOrderAfterSales.setRefundStatus(CodeEnum.YES.getCode());
        return orderAfterSalesMapper.update(busOrderAfterSales,new LambdaQueryWrapper<BusOrderAfterSales>()
                  .eq(BusOrderAfterSales::getAfterSalesNumber,afterSalesNumber));
    }

    /**
     * 查询是否有新订单
     *
     * @param dto
     * @return
     */
    @Override
    public int queryNewOrder(BusOrderAfterSalesListDTO dto) {
        List<BusOrderAfterSalesListVO> orderAfterSalesList = orderAfterSalesMapper.listOrderAfterSales(dto);
        List<String> afterSalesStatus = Arrays.asList(OrderAfterSalesStatusEnum.APPLY_AFTER_SALES_SERVICE.getCode(),
                OrderAfterSalesStatusEnum.RETURNED_GOODS_TO_BE_CONFIRMED.getCode(),
                OrderAfterSalesStatusEnum.CONFIRM_RECEIPT_RESEND_GOODS.getCode());
        orderAfterSalesList = orderAfterSalesList.stream()
                .filter(o -> afterSalesStatus.contains(o.getAfterSalesStatus())).collect(Collectors.toList());
        return orderAfterSalesList.size();
    }

    @Override
    public void exportExcel(HttpServletResponse response, BusOrderAfterSalesListDTO busOrderAfterSalesListDTO) {
        List<BusOrderAfterSalesListVO> busOrderAfterSalesListVOS = orderAfterSalesMapper.listOrderAfterSales(busOrderAfterSalesListDTO);
        long beforeTime = System.currentTimeMillis();
        OutputStream outputStream;
        Integer count =0;
        try {
            outputStream = response.getOutputStream();
            String time = new SimpleDateFormat("yyyy-MM-dd-hh-mm-ss").format(new Date());
            //添加响应头信息
            response.setHeader("Content-disposition", "attachment; filename=" + "contract" + time + ".xlsx");
            //设置类型
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            //设置头
            response.setHeader("Pragma", "No-cache");
            //设置头
            response.setHeader("Cache-Control", "no-cache");
            //设置日期头
            response.setDateHeader("Expires", 0);
            if(CollUtil.isNotEmpty(busOrderAfterSalesListVOS)){
                count = busOrderAfterSalesListVOS.size();
            }
            writeExcel(orderAfterSalesMapper,busOrderAfterSalesListDTO,count,outputStream);
            outputStream.flush();
            response.getOutputStream().close();
            long afterTime = System.currentTimeMillis();
            log.info("耗时:{}", afterTime - beforeTime);
        } catch (Exception e) {
            log.error("导出失败：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public List<BusOrderAfterSales> listByOrderNo(String orderNo) {
        LambdaQueryWrapper<BusOrderAfterSales> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOrderAfterSales::getOrderNo, orderNo);
        return orderAfterSalesMapper.selectList(queryWrapper);
    }

    public void writeExcel(BusOrderAfterSalesMapper busOrderAfterSalesMapper,BusOrderAfterSalesListDTO dto, int exifInfoCount, OutputStream outputStream) {
        //每个sheet保存的数据量
        int num = 10000;
        ExcelWriter excelWriter = null;
        int corePoolSize = 10;
        int maximumPoolSize = 20;
        //用线程池管理多线程
        ThreadPoolExecutor exector = (ThreadPoolExecutor) Executors.newFixedThreadPool(corePoolSize);
        exector.setCorePoolSize(corePoolSize);
        exector.setMaximumPoolSize(maximumPoolSize);
        List<OrderAfterSalesThread> tasks = Lists.newCopyOnWriteArrayList();
        excelWriter = EasyExcel.write(outputStream, BusOrderAfterSalesListVO.class).build();
        //exifInfoCount 写入excel数据总量
        //pageCount 要写入sheet页数量。同分页
        int pageCount = exifInfoCount % num == 0 ? (exifInfoCount / num) : (exifInfoCount / num + 1);
        for (int i = 0; i < pageCount; i++) {
            OrderAfterSalesThread readExifInfoThread = new OrderAfterSalesThread(dto, busOrderAfterSalesMapper, i, num);
            tasks.add(readExifInfoThread);
        }
        try {
            //用invokeAll方法提交任务，返回数据的顺序和tasks中的任务顺序一致，如果第一个线程查0-10000行数据，第二个线程查10000-10001行数据，
            //第二个线程大概率比第一个线程先执行完，但是futures中第一位数据是0-10000的数据。
            List<Future<List<BusOrderAfterSalesListVO>>> futures = exector.invokeAll(tasks);
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "售后订单信息" + (1)).build();
            for (int i = 0; i < pageCount; i++) {
                List<BusOrderAfterSalesListVO> exifInfoList = futures.get(i).get();
                excelWriter.write(exifInfoList, writeSheet);
            }
        } catch (Exception e) {
            log.error("问诊订单导出数据失败", e);
        }
        exector.shutdown();
        excelWriter.finish();

    }

    /**
     * 多线程查询数据内部类
     */
    public class OrderAfterSalesThread implements Callable<List<BusOrderAfterSalesListVO>> {
        @Autowired
        private  BusOrderAfterSalesMapper orderAfterSalesMapper;
        private Integer startNum;
        private Integer pageZize;

        /**
         * 医院ID
         */
        private Long hospitalId;
        /**
         * 订单编号
         */
        private String orderNo;
        /**
         * 售后编号
         */
        private String afterSalesNumber;

        /**
         * 申请开始时间
         */
        private String applyStartTime;

        /**
         * 申请结束时间
         */
        private String applyEndTime;

        /**
         * 售后方式（0仅退款 1退货退款 2退换货）
         */
        private String refundType;
        /**
         * 售后状态（0买家申请售后 1不同意售后，待买家处理 2已同意售后，待买家退货 3买家已退货，待确认收货 4已拒绝收货，待买家处理 5已确认收货，待重新发货 6已重新发货，待买家收货 7售后结束 8售后关闭）
         */
        private String afterSalesStatus;

        /**
         * 商品状态(0-退款中  1-退款失败  2-退款成功)
         */
        private Integer status;
        /**
         * 商品状态(0-待商家处理  1-待患者处理  2-退款失败)
         */
        private Integer handleType;


        public OrderAfterSalesThread(BusOrderAfterSalesListDTO dto, BusOrderAfterSalesMapper orderAfterSalesMapper, int startNum, int pageZize) {
            this.hospitalId=dto.getHospitalId();
            this.orderNo=dto.getOrderNo();
            this.afterSalesNumber=dto.getAfterSalesNumber();
            this.applyStartTime=dto.getApplyStartTime();
            this.applyEndTime=dto.getApplyEndTime();
            this.refundType=dto.getRefundType();
            this.afterSalesStatus=dto.getAfterSalesStatus();
            this.status=dto.getStatus();
            this.startNum = startNum;
            this.pageZize = pageZize;
            this.orderAfterSalesMapper=orderAfterSalesMapper;
            this.handleType=dto.getHandleType();
        }

        @Override
        public List<BusOrderAfterSalesListVO> call() {
            BusOrderAfterSalesListDTO queryCondition = new BusOrderAfterSalesListDTO();
            queryCondition.setStartNum(startNum * pageZize);
            queryCondition.setPageSize(pageZize);
            queryCondition.setOrderNo(orderNo);
            queryCondition.setAfterSalesNumber(afterSalesNumber);
            queryCondition.setAfterSalesStatus(afterSalesStatus);
            queryCondition.setRefundType(refundType);
            queryCondition.setApplyStartTime(applyStartTime);
            queryCondition.setApplyEndTime(applyEndTime);
            queryCondition.setStatus(status);
            queryCondition.setHospitalId(hospitalId);
            queryCondition.setHandleType(handleType);
            long startTime = System.currentTimeMillis();
            List<BusOrderAfterSalesListVO> exifInfoList = null;
            try {
                //从数据库查询要写入excle的数据
                exifInfoList = orderAfterSalesMapper.listOrderAfterSales(queryCondition);
                /*药品*/
                for (BusOrderAfterSalesListVO topItem : exifInfoList) {
                    topItem.setOverTime(DateUtils.addDays(topItem.getApplyTime(), 7));
                   // 查询订单信息(子订单类型+子订单id+总订单编号)
                    BusOrder order = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                            .eq(BusOrder::getSubOrderType, topItem.getAfterSalesType())
                            .eq(BusOrder::getSubOrderId, topItem.getOrderId())
                            .eq(BusOrder::getOrderNo, topItem.getOrderNo()));
                    StringUtils.isNullThrowExp(order, "申请售后的订单已删除！");
                    //付款时间
                    topItem.setOrderTime(order.getOrderTime());
                    //交易完成时间
                    topItem.setCompletionTime(order.getCompleteTime());
                    //订单金额
                    topItem.setAmount(order.getRelPrice());
                    //如果状态等于售后结束,这获取最后一次修改状态的时间
                    if(OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(topItem.getAfterSalesStatus())){
                        //退款完成时间
                        topItem.setRefundTime(topItem.getUpdateTime());
                    }

                    if (CodeEnum.NO.getCode().equals(topItem.getRefundStatus())){
                        topItem.setRefundState("退款中");
                    }else if (CodeEnum.YES.getCode().equals(topItem.getRefundStatus())){
                        topItem.setRefundState("退款失败");
                    }else if (CodeEnum.DELETE.getCode().equals(topItem.getRefundStatus())){
                        topItem.setRefundState("退款成功");
                    }

                    if (YesNoEnum.NO.getCode().equals(topItem.getAfterSalesType())) {
                        if(Objects.nonNull(topItem.getOrderNo()) ) {
                            BusDrugOrderPackage busDeliveryOrderPackage = querySendGoodStatus(topItem.getOrderNo(), topItem.getOrderId(), 0, YesNoEnum.NO.getCode());
                            if (Objects.nonNull(busDeliveryOrderPackage)) {
                                //发货快递公司
                                topItem.setOrderLogisticsCompany(busDeliveryOrderPackage.getLogisticsCompany());
                                //发货快递单号
                                topItem.setOrderDeliveryNo(busDeliveryOrderPackage.getDeliveryNo());
                            }
                            if (Objects.nonNull(busDeliveryOrderPackage) && Objects.nonNull(busDeliveryOrderPackage.getDeliveryTime())) {
                                topItem.setDeliveryStatus("已发货");
                            } else {
                                topItem.setDeliveryStatus("未发货");
                            }
                        }

                        /*如果是处方药*/
                        if (Objects.nonNull(topItem.getPrescriptionId())) {
                            BusPrescription busPrescription = busPrescriptionMapper.selectById(topItem.getPrescriptionId());
                            List<BusPrescriptionDrugs> busPrescriptionDrugs = busPrescriptionDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                                    .eq(BusPrescriptionDrugs::getPrescriptionId, topItem.getPrescriptionId())
                            );
                            topItem.setProcessPrice(busPrescription.getProcessPrice());
                            topItem.setDecoct(busPrescription.getDecoct());
                            topItem.setProcessingMethod(busPrescription.getProcessingMethod());
                            topItem.setPrNo(busPrescription.getPrescriptionNumber());
                            /*如果是中药方剂或者协定方*/
                            if (Objects.nonNull(busPrescription.getPaId())) {
                                String usages = busPrescription.getUsages();
                                String[] split = usages.split(",");
                                BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(busPrescription.getPaId());
                                if (YesNoEnum.YES.getCode().equals(busHospitalPa.getType())) {
                                    topItem.setListType(OrderListTypeEnum.CLASSICAL_FORMULA.getCode());
                                } else {
                                    topItem.setListType(OrderListTypeEnum.PARTIES.getCode());
                                }
                                List<GoodsItemVO> goodsList = new ArrayList<>();
                                GoodsItemVO goodsItem = new GoodsItemVO();

                                //换货
                                if(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(topItem.getRefundType())){
                                    // 查询原订单信息
                                    BusDrugOrderPackage busDeliveryOrderPackage = null;
                                    BusOrder busOrder = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                                            .eq(BusOrder::getAfterSaleId, topItem.getId()).last(" limit 1"));
                                    if(null!=busOrder){
                                        //查询换货包裹信息
                                        busDeliveryOrderPackage = querySendGoodStatus(busOrder.getOrderNo(), 1, YesNoEnum.NO.getCode());
                                    }
                                    //换货快递公司
                                    if(Objects.nonNull(busDeliveryOrderPackage)) {
                                        //换货快递公司
                                        topItem.setExchangeGoodsCompany(busDeliveryOrderPackage.getLogisticsCompany());
                                        //换货快递单号
                                        topItem.setExchangeGoodsNo(busDeliveryOrderPackage.getDeliveryNo());
                                    }
                                    if (Objects.nonNull(busDeliveryOrderPackage) && Objects.nonNull(busDeliveryOrderPackage.getDeliveryTime())) {
                                        topItem.setExchangeGoodsStatus("已发货");
                                    } else {
                                        // 0买家申请售后 1不同意售后，待买家处理 2已同意售后，待买家退货 3买家已退货，待确认收货 4已拒绝收货，待买家处理
                                        topItem.setExchangeGoodsStatus(checkAfterSalesStatus(topItem.getAfterSalesStatus()));
                                    }

                                }else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(topItem.getRefundType())){//退货
                                    //退货货快递公司
                                    topItem.setLogisticsCompany(topItem.getLogisticsCompany());
                                    //退货快递单号
                                    topItem.setDeliveryNo(topItem.getDeliveryNo());
                                }

                                StringBuffer stringBuffer = new StringBuffer();
                                busPrescriptionDrugs.stream().forEach(item -> {
                                    stringBuffer.append(item.getDrugsName()+""+ item.getWeight() + item.getDrugsSpecification() + " ");
                                });
                                goodsItem.setItemName(busHospitalPa.getPaName());
                                goodsItem.setNumber(busPrescriptionDrugs.size());
                                goodsItem.setItemListStr(stringBuffer.toString());
                                goodsItem.setItemQuantity(Integer.valueOf(split[1]));
                                goodsItem.setItemPrice(BigDecimal.valueOf(Double.valueOf(busHospitalPa.getAmount())));
                                goodsList.add(goodsItem);
                                topItem.setItemList(goodsList);
                            } else {
                                /*如果是中药处方*/
                                if (YesNoEnum.NO.getCode().equals(Integer.valueOf(busPrescription.getPrescriptionType()))) {
                                    String usages = busPrescription.getUsages();
                                    String[] split = usages.split(",");
                                    topItem.setListType(OrderListTypeEnum.GENERAL_PRESCRIPTION.getCode());
                                    List<GoodsItemVO> goodsList = new ArrayList<>();
                                    GoodsItemVO goodsItem = new GoodsItemVO();
                                    StringBuffer stringBuffer = new StringBuffer();
                                    //换货
                                    if(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(topItem.getRefundType())){
                                        // 查询原订单信息
                                        BusDrugOrderPackage drugOrderPackage = null;
                                        BusOrder busOrder = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                                                .eq(BusOrder::getAfterSaleId, topItem.getId()).last(" limit 1"));
                                        if(null!=busOrder){
                                            //查询换货包裹信息
                                            drugOrderPackage = querySendGoodStatus(busOrder.getOrderNo(), 1, YesNoEnum.NO.getCode());
                                        }
                                        //换货快递公司
                                        if(Objects.nonNull(drugOrderPackage)) {
                                            //换货快递公司
                                            topItem.setExchangeGoodsCompany(drugOrderPackage.getLogisticsCompany());
                                            //换货快递单号
                                            topItem.setExchangeGoodsNo(drugOrderPackage.getDeliveryNo());
                                        }
                                        if (Objects.nonNull(drugOrderPackage) && Objects.nonNull(drugOrderPackage.getDeliveryTime())) {
                                            topItem.setExchangeGoodsStatus("已发货");
                                        } else {
                                            // 0买家申请售后 1不同意售后，待买家处理 2已同意售后，待买家退货 3买家已退货，待确认收货 4已拒绝收货，待买家处理
                                            topItem.setExchangeGoodsStatus(checkAfterSalesStatus(topItem.getAfterSalesStatus()));
                                        }

                                    }else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(topItem.getRefundType())){//退货
                                        //退货货快递公司
                                        topItem.setLogisticsCompany(topItem.getLogisticsCompany());
                                        //退货快递单号
                                        topItem.setDeliveryNo(topItem.getDeliveryNo());
                                    }

                                    BigDecimal bigDecimal = new BigDecimal(0);
                                    for (BusPrescriptionDrugs item : busPrescriptionDrugs) {
                                        stringBuffer.append(item.getDrugsName() + item.getWeight() + item.getDrugsSpecification() + " ");
                                        bigDecimal = bigDecimal.add(item.getSellingPrice().multiply(BigDecimal.valueOf(Double.valueOf(item.getWeight()))));
                                    }
                                    goodsItem.setItemQuantity(Integer.valueOf(split[1]));
                                    goodsItem.setNumber(busPrescriptionDrugs.size());
                                    goodsItem.setItemPrice(bigDecimal);
                                    goodsItem.setItemListStr(stringBuffer.toString());
                                    goodsList.add(goodsItem);
                                    topItem.setItemList(goodsList);
                                    /*如果是西药处方*/
                                } else {
                                    topItem.setListType(OrderListTypeEnum.WESTERN_MEDICINE_PRESCRIPTION.getCode());
                                    List<GoodsItemVO> goodsList = new ArrayList<>();
                                    busPrescriptionDrugs.forEach(item -> {
                                        GoodsItemVO goodsItem = new GoodsItemVO();
                                        //换货
                                        if(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(topItem.getRefundType())){
                                            // 查询原订单信息
                                            BusDrugOrderPackage busDrugOrderPackage = null;
                                            BusOrder busOrder = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                                                    .eq(BusOrder::getAfterSaleId, topItem.getId()).last(" limit 1"));
                                            if(null!=busOrder){
                                                //查询换货包裹信息
                                                busDrugOrderPackage = querySendGoodStatus(busOrder.getOrderNo(), 1, YesNoEnum.NO.getCode());
                                            }  //换货快递公司
                                            if(Objects.nonNull(busDrugOrderPackage)) {
                                                //换货快递公司
                                                topItem.setExchangeGoodsCompany(busDrugOrderPackage.getLogisticsCompany());
                                                //换货快递单号
                                                topItem.setExchangeGoodsNo(busDrugOrderPackage.getDeliveryNo());
                                            }
                                            if (Objects.nonNull(busDrugOrderPackage) &&  Objects.nonNull(busDrugOrderPackage.getDeliveryTime())) {
                                                topItem.setExchangeGoodsStatus("已发货");
                                            } else {
                                                // 0买家申请售后 1不同意售后，待买家处理 2已同意售后，待买家退货 3买家已退货，待确认收货 4已拒绝收货，待买家处理
                                                topItem.setExchangeGoodsStatus(checkAfterSalesStatus(topItem.getAfterSalesStatus()));
                                            }
                                        }else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(topItem.getRefundType())){//退货
                                            //退货货快递公司
                                            topItem.setLogisticsCompany(topItem.getLogisticsCompany());
                                            //退货快递单号
                                            topItem.setDeliveryNo(topItem.getDeliveryNo());
                                        }

                                        goodsItem.setItemName(item.getDrugsName());
                                        goodsItem.setItemSpec(item.getDrugsSpecification());
                                        goodsItem.setItemQuantity(item.getQuantity());
                                        goodsItem.setItemPrice(item.getSellingPrice());
                                        goodsItem.setItemImage(item.getDrugsImg());
                                        goodsList.add(goodsItem);
                                    });
                                    topItem.setItemList(goodsList);
                                }
                            }
                        } else {
                            /*非处方*/
                            topItem.setListType(OrderListTypeEnum.OTC.getCode());
                            List<GoodsItemVO> goodsList = new ArrayList<>();
                            BusOtcDrugs item = busOtcDrugsMapper.selectOne(new LambdaQueryWrapper<BusOtcDrugs>()
                                    .eq(BusOtcDrugs::getDrugsId,topItem.getGoodsId())
                                    .eq(BusOtcDrugs::getDrugsOrderId,topItem.getOrderId())
                            );
                            if (Objects.nonNull(item)){
                                //非处方，显示药品名称
                                topItem.setProductName(item.getDrugsName());
                                GoodsItemVO goodsItem = new GoodsItemVO();

                                //换货
                                if(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(topItem.getRefundType())){
                                    // 查询原订单信息
                                    BusDrugOrderPackage busDrugOrderPackage = null;
                                    BusOrder busOrder = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                                            .eq(BusOrder::getAfterSaleId, topItem.getId()).last(" limit 1"));
                                    if(null!=busOrder){
                                        //查询换货包裹信息
                                        busDrugOrderPackage = querySendGoodStatus(busOrder.getOrderNo(), 1, YesNoEnum.NO.getCode());
                                    }
                                    //换货快递公司
                                    if(Objects.nonNull(busDrugOrderPackage)) {
                                        //换货快递公司
                                        topItem.setExchangeGoodsCompany(busDrugOrderPackage.getLogisticsCompany());
                                        //换货快递单号
                                        topItem.setExchangeGoodsNo(busDrugOrderPackage.getDeliveryNo());
                                    }

                                    if (Objects.nonNull(busDrugOrderPackage) && Objects.nonNull(busDrugOrderPackage.getDeliveryTime())) {
                                        topItem.setExchangeGoodsStatus("已发货");
                                    } else {
                                        // 0买家申请售后 1不同意售后，待买家处理 2已同意售后，待买家退货 3买家已退货，待确认收货 4已拒绝收货，待买家处理
                                        topItem.setExchangeGoodsStatus(checkAfterSalesStatus(topItem.getAfterSalesStatus()));
                                    }

                                }else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(topItem.getRefundType())){//退货
                                    //退货货快递公司
                                    topItem.setLogisticsCompany(topItem.getLogisticsCompany());
                                    //退货快递单号
                                    topItem.setDeliveryNo(topItem.getDeliveryNo());
                                }
                                goodsItem.setItemName(item.getDrugsName());
                                goodsItem.setItemImage(item.getDrugsImg());
                                goodsItem.setItemQuantity(topItem.getQuantity());
                                goodsItem.setItemSpec(item.getDrugsSpecification());
                                goodsItem.setItemPrice(item.getSellingPrice());
                                goodsList.add(goodsItem);
                                topItem.setItemList(goodsList);
                            }
                        }
                        /*如果是商品*/
                    } else {

                        if(Objects.nonNull(topItem.getOrderNo())){
                            BusDrugOrderPackage busDeliveryOrderPackage= querySendGoodStatus(topItem.getOrderNo(),topItem.getOrderId(),0,YesNoEnum.YES.getCode());
                            if(Objects.nonNull(busDeliveryOrderPackage)) {
                                //发货快递公司
                                topItem.setOrderLogisticsCompany(busDeliveryOrderPackage.getLogisticsCompany());
                                //发货快递单号
                                topItem.setOrderDeliveryNo(busDeliveryOrderPackage.getDeliveryNo());
                            }

                            if (Objects.nonNull(busDeliveryOrderPackage)&& Objects.nonNull(busDeliveryOrderPackage.getDeliveryTime())) {
                                topItem.setDeliveryStatus("已发货");
                            } else {
                                topItem.setDeliveryStatus("未发货");
                            }
                        }

                        List<GoodsItemVO> goodsList = new ArrayList<>();
                        topItem.setListType(OrderListTypeEnum.GOODS.getCode());
                        R<List<BusOrderShop>> listR = remoteBusOrderShopService.listBusOrderShopByOrderId(topItem.getOrderId());
                        if (Constants.FAIL.equals(listR.getCode())){
                            throw new ServiceException("查询商品远程调用失败");
                        }
                        List<BusOrderShop> data = listR.getData();
                        List<BusOrderShop> collect = data.stream().filter(item -> item.getShopId().equals(topItem.getGoodsId())).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(collect)){
                            BusOrderShop item = collect.get(0);
                            GoodsItemVO goodsItem = new GoodsItemVO();
                            //换货
                            if(AfterSaleRefundTypeEnum.REFUNDS_EXCHANGES.getCode().equals(topItem.getRefundType())){
                                // 查询原订单信息
                                BusDrugOrderPackage shopPackage = null;
                                BusOrder busOrder = orderMapper.selectOne(new LambdaQueryWrapper<BusOrder>()
                                        .eq(BusOrder::getAfterSaleId, topItem.getId()).last(" limit 1"));
                                if(null!=busOrder){
                                    //查询换货包裹信息
                                     shopPackage = querySendGoodStatus(busOrder.getOrderNo(), 1, YesNoEnum.YES.getCode());
                                }
                                //换货快递公司
                                if(Objects.nonNull(shopPackage)) {
                                    //换货快递公司
                                    topItem.setExchangeGoodsCompany(shopPackage.getLogisticsCompany());
                                    //换货快递单号
                                    topItem.setExchangeGoodsNo(shopPackage.getDeliveryNo());
                                }
                                if (Objects.nonNull(shopPackage) &&  Objects.nonNull(shopPackage.getDeliveryTime())) {
                                    topItem.setExchangeGoodsStatus("已发货");
                                } else {
                                   // 0买家申请售后 1不同意售后，待买家处理 2已同意售后，待买家退货 3买家已退货，待确认收货 4已拒绝收货，待买家处理
                                    topItem.setExchangeGoodsStatus(checkAfterSalesStatus(topItem.getAfterSalesStatus()));
                                }

                            }else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(topItem.getRefundType())){//退货
                                //退货货快递公司
                                topItem.setLogisticsCompany(topItem.getLogisticsCompany());
                                //退货快递单号
                                topItem.setDeliveryNo(topItem.getDeliveryNo());
                            }
                            goodsItem.setItemName(item.getShopName());
                            goodsItem.setItemImage(item.getShopImg());
                            goodsItem.setItemQuantity(topItem.getQuantity());
                            goodsItem.setItemSpec(item.getShopSpecification());
                            goodsItem.setItemPrice(item.getSellingPrice());
                            goodsList.add(goodsItem);
                            topItem.setItemList(goodsList);
                            //商品名称
                            topItem.setProductName(item.getShopName());

                        }
                    }
                    //售后状态
                    topItem.setAfterSalesStatus(OrderAfterSalesStatusEnum.getTypeByCode(topItem.getAfterSalesStatus()).getInfo());
                    //申请原因
                    if (AfterSaleRefundTypeEnum.REFUND_ONLY.getCode().equals(topItem.getRefundType())) {
                        RefundOnlyEnum refundCauseEnum = RefundOnlyEnum.getTypeByCode(topItem.getAfterSalesCause());
                        if(Objects.nonNull(refundCauseEnum)){
                            topItem.setAfterSalesCause(refundCauseEnum.getInfo());
                        }

                    } else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(topItem.getRefundType())) {
                        RefundReturnEnum refundReturnEnum = RefundReturnEnum.getTypeByCode(topItem.getAfterSalesCause());
                        if(Objects.nonNull(refundReturnEnum)){
                            topItem.setAfterSalesCause(refundReturnEnum.getInfo());
                        }
                    } else {
                        RefundsExchangesEnum exchangesEnum = RefundsExchangesEnum.getTypeByCode(topItem.getAfterSalesCause());
                        if(Objects.nonNull(exchangesEnum)){
                            topItem.setAfterSalesCause(exchangesEnum.getInfo());
                        }
                    }
                    //售后方式
                    topItem.setRefundType(AfterSaleRefundTypeEnum.getTypeByCode(topItem.getRefundType()).getInfo());
                }

                long endTime = System.currentTimeMillis();
                long spendTime = endTime - startTime;
                log.info(Thread.currentThread().getName() + "查询耗时：" + spendTime + "；分页是从【" + queryCondition.getStartNum() + "】开始");
            } catch (Exception e) {
                log.error("多线程查询导出数据失败", e);
            }
            return exifInfoList;
        }
    }




    /**
     * 处方售后药品封装
     *
     * @param afterSale       售后单
     * @param afterSaleDetail 售后详情VO
     * @param saleGoods       售后药品项集合
     */
    private void getRxDrugs(BusOrderAfterSales afterSale, BusAfterSaleDetailVO afterSaleDetail, List<BusAfterSaleGood> saleGoods) {
        // 买家(就诊人)信息
        BusPrescription rx = rxMapper.selectById(afterSale.getPrescriptionId());
        List<BusPrescriptionDrugs> rxDrugs = rxDrugsMapper.selectList(new LambdaQueryWrapper<BusPrescriptionDrugs>()
                .eq(BusPrescriptionDrugs::getPrescriptionId, rx.getId()));
        afterSaleDetail.setBuyName(rx.getFamilyName());
        afterSaleDetail.setBuyPhone(rx.getFamilyTel());
        List<BusDrugsOrder> busDrugsOrders = orderMapper.selectOrderList(afterSale.getOrderNo());
        if (Objects.isNull(busDrugsOrders) || busDrugsOrders.isEmpty()){
            log.error("售后订单号不存在相关联的药品订单{}",afterSale.getOrderNo());
            return;
        }
        Optional<String> drugsOrderNo = busDrugsOrders.stream().filter(order -> rx.getId().equals(order.getPrescriptionId()))
                .map(BusDrugsOrder::getOrderNo)
                .findFirst();
        String drugOrderNo;
        if (!drugsOrderNo.isPresent()){
            drugOrderNo = "";
            log.warn("售后订单号{}，对应药品订单号为空",afterSale.getOrderNo());
        }else {
            drugOrderNo = drugsOrderNo.get();
        }
        // 西药处方
        if (PrescriptionTypeEnum.isMm(rx.getPrescriptionType())) {
            for (BusPrescriptionDrugs rxDrug : rxDrugs) {
                BusAfterSaleGood saleGood = new BusAfterSaleGood(rxDrug.getDrugsId(),
                        rxDrug.getDrugsImg(), rxDrug.getDrugsName(), rxDrug.getSellingPrice(),
                        rxDrug.getQuantity(), rxDrug.getDrugsSpecification());
                saleGood.setPrescriptionId(rx.getId());
                saleGood.setPrescriptionType(rx.getPrescriptionType());
                saleGood.setPrescriptionCreateType(rx.getIdentity());
                saleGood.setDoctorName(rx.getDoctorName());
                saleGood.setIsOtc(isOtc(rxDrug.getDrugsId()));
                saleGood.setEnterpriseId(rxDrug.getEnterpriseId());
                // 药品类型-西药
                saleGood.setDrugsType("1");
                saleGood.setOrderNo(drugOrderNo);
                getGoodsSendStatus(afterSale, saleGoods, saleGood);
            }
        } else if (PrescriptionTypeEnum.isTcm(rx.getPrescriptionType())){
            // 剂量
            rxDrugs.forEach(rxDrug -> {
                String drugName = rxDrug.getDrugsName();
                String dosage = rx.getUsages().split(",")[1];
                rxDrug.setDoses(Integer.parseInt(dosage));
                // 药品小计价格
                BigDecimal drugsPrice = cmPiecesDrugPriceCalculator.calculate(rxDrug);
                BusAfterSaleGood saleGood = new BusAfterSaleGood(rxDrug.getDrugsId(), rxDrug.getDrugsImg(), drugName, rxDrug.getSellingPrice(), Integer.parseInt(dosage), rxDrug.getDrugsSpecification());
                saleGood.setRelPrice(drugsPrice);
                saleGood.setSpec(String.format("%s × %s", rxDrug.getDrugsSpecification(), rxDrug.getWeight()));
                saleGood.setPrescriptionId(rx.getId());
                saleGood.setPrescriptionCreateType(rx.getIdentity());
                saleGood.setDoctorName(rx.getDoctorName());
                saleGood.setDecoctingPrice(rx.getProcessPrice());
                saleGood.setDecoctingWay(rx.getProcessingMethod());
                saleGood.setPrescriptionType(rx.getPrescriptionType());
                // 药品类型-中药
                saleGood.setDrugsType("0");
                saleGood.setOrderNo(drugOrderNo);
                getGoodsSendStatus(afterSale, saleGoods, saleGood);
            });

        } else {
            // 中药方剂
            List<String> drugNameList = new ArrayList<>();

            rxDrugs.forEach(rxDrug -> {
                if ("g".equals(rxDrug.getDrugsSpecification())) {
                    drugNameList.add(rxDrug.getDrugsName() + rxDrug.getWeight() + rxDrug.getDrugsSpecification()) ;
                }
                if (rxDrug.getDrugsSpecification().contains("/")) {
                    drugNameList.add(rxDrug.getDrugsName() + rxDrug.getDrugsSpecification() + "×" + rxDrug.getWeight()) ;
                }
            });

            String drugName = String.join("、", drugNameList);
            // 单剂药材价格 = (处方金额 - 加工费)/剂数
            String dosage = rx.getUsages().split(",")[1];
            BigDecimal drugsPrice = (rx.getPrescriptionAmount().subtract(rx.getProcessPrice()==null ?BigDecimal.ZERO:rx.getProcessPrice())).divide(new BigDecimal(dosage));
            BusAfterSaleGood saleGood = new BusAfterSaleGood(-1L, null, drugName, drugsPrice, Integer.parseInt(dosage), null);
            saleGood.setPrescriptionId(rx.getId());
            saleGood.setPrescriptionCreateType(rx.getIdentity());
            saleGood.setDoctorName(rx.getDoctorName());
            saleGood.setDecoctingPrice(rx.getProcessPrice());
            saleGood.setDecoctingWay(rx.getProcessingMethod());
            saleGood.setPrescriptionType(rx.getPrescriptionType());
            saleGood.setPaName("中草药" + rxDrugs.size() + "味");
            // 药品类型-中药
            saleGood.setDrugsType("0");
            saleGood.setOrderNo(drugOrderNo);
            if(null != rx.getEnterpriseId()){
                saleGood.setEnterpriseId(rx.getEnterpriseId());
            }
            if (Objects.nonNull(rx.getPaId())) {
                BusHospitalPa pa = paMapper.selectById(rx.getPaId());
                // 协定方
                if (YesNoEnum.NO.getCode().equals(pa.getType())) {
                    saleGood.setPaName(rx.getPaName());
                }
            }
            getGoodsSendStatus(afterSale, saleGoods, saleGood);
        }
    }

    private String  checkAfterSalesStatus(String afterSalesStatus ){
        if(OrderAfterSalesStatusEnum.CONFIRM_RECEIPT_RESEND_GOODS.getCode().equals(afterSalesStatus)){
            return "未发货";
        }else {
            return "";
        }
    }

    /**
     * 获取物品发货状态
     *
     * @param afterSale 售后单
     * @param saleGoods 售后物品集合
     * @param saleGood  售后物品
     */
    private void getGoodsSendStatus(BusOrderAfterSales afterSale, List<BusAfterSaleGood> saleGoods, BusAfterSaleGood saleGood) {
        LambdaQueryWrapper<BusDrugOrderPackage> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BusDrugOrderPackage::getPackageType, afterSale.getAfterSalesType());
        wrapper.eq(BusDrugOrderPackage::getOrderNo,afterSale.getOrderNo());
        if (Objects.nonNull(afterSale.getPrescriptionId())){
            wrapper.eq(BusDrugOrderPackage::getPrescriptionId,afterSale.getPrescriptionId());
            // 西药处方
            if (PrescriptionTypeEnum.isMm(saleGood.getPrescriptionType())){
                if (Objects.isNull(saleGood.getEnterpriseId())){
                    wrapper.isNull(BusDrugOrderPackage::getEnterpriseId);
                    wrapper.eq(BusDrugOrderPackage::getDrugsGoodsId, saleGood.getId());
                }else {
                    wrapper.eq(BusDrugOrderPackage::getEnterpriseId, saleGood.getEnterpriseId());
                }
            }
        }else {
            // OTC
            wrapper.eq(BusDrugOrderPackage::getDrugsOrderId, afterSale.getOrderId());
            if (afterSale.getAfterSalesType().equals(YesNoEnum.NO.getCode())){
                if (Objects.isNull(saleGood.getEnterpriseId())){
                    wrapper.isNull(BusDrugOrderPackage::getEnterpriseId);
                    wrapper.eq(BusDrugOrderPackage::getDrugsGoodsId, saleGood.getId());
                }else {
                    wrapper.eq(BusDrugOrderPackage::getEnterpriseId, saleGood.getEnterpriseId());
                }
            }else {
                // 商品
                wrapper.eq(BusDrugOrderPackage::getDrugsGoodsId, saleGood.getId());
            }
        }
        wrapper.last("limit 1");
        BusDrugOrderPackage pack = busDrugOrderPackageMapper.selectOne(wrapper);
        if (Objects.nonNull(pack) && (StringUtils.isNotEmpty(pack.getDeliveryNo()) || StringUtils.isNotEmpty(pack.getCode()))) {
            saleGood.setSendStatus("已发货");
        } else {
            saleGood.setSendStatus("未发货");
        }
        saleGoods.add(saleGood);
    }

    /**
     * 判断是否为OTC药
     *
     * @param drugId 药品id
     * @return 0-处方 1-OTC
     */
    private Integer isOtc(Long drugId) {
        BusDrugs drugs = drugsMapper.selectById(drugId);
        if (391 == drugs.getPrescriptionIdentification()) {
            return 0;
        } else if (392 == drugs.getPrescriptionIdentification()) {
            return 1;
        }
        return null;
    }

    /**
     * 是否进行溯源码退回
     *
     * @param busOrder         订单信息
     * @param orderAfterSales  售后信息
     * @return 是否需要退回溯源码
     */
    private boolean needReturnDrugTraceCode(BusOrder busOrder, BusOrderAfterSales orderAfterSales, boolean isReceiveGoods) {
        //如果是药品售后订单，且是医保支付流程
        if (Objects.nonNull(orderAfterSales.getPrescriptionId())
                && PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay())) {
            //确认收货，或者自提退款
            if (isReceiveGoods || DeliveryTypeEnum.isPickup(busOrder.getDeliveryType())){
                //更新订单药品溯源码为待退款
                LambdaQueryWrapper<BusOrderDrugTraceCode> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BusOrderDrugTraceCode::getHospitalId, orderAfterSales.getHospitalId());
                wrapper.eq(BusOrderDrugTraceCode::getOrderNo, orderAfterSales.getOrderNo());
                wrapper.eq(BusOrderDrugTraceCode::getPrescriptionId, orderAfterSales.getPrescriptionId());
                wrapper.eq(BusOrderDrugTraceCode::getStatus, DrugTraceCodeStatusEnum.UPLOADED.getStatus());
                BusOrderDrugTraceCode update = new BusOrderDrugTraceCode();
                update.setStatus(DrugTraceCodeStatusEnum.PENDING_RETURN.getStatus());
                return SqlHelper.retBool(busOrderDrugTraceCodeMapper.update(update, wrapper));
            }
        }
        return false;
    }
}
