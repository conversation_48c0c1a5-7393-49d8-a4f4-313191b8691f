package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.RemoteStockService;
import com.puree.hospital.app.api.RemoteWxPayService;
import com.puree.hospital.app.api.model.BusStockDrugs;
import com.puree.hospital.app.api.model.event.order.OrderCancelEvent;
import com.puree.hospital.business.api.model.dto.BusHospitalOrderDTO;
import com.puree.hospital.business.domain.BusChannelOrder;
import com.puree.hospital.business.domain.BusConsultationSettings;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusEnterprise;
import com.puree.hospital.business.domain.BusEnterpriseDrugsPrice;
import com.puree.hospital.business.domain.BusEnterpriseOrder;
import com.puree.hospital.business.domain.BusEnterpriseProductOrder;
import com.puree.hospital.business.domain.BusFreightBaseSetting;
import com.puree.hospital.business.domain.BusHospitalPa;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.domain.BusOrderAfterSales;
import com.puree.hospital.business.domain.BusOrderShop;
import com.puree.hospital.business.domain.BusOtcDrugs;
import com.puree.hospital.business.domain.BusPartners;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusPrescriptionDrugs;
import com.puree.hospital.business.domain.dto.BusOrderDrugTraceCodeQueryDTO;
import com.puree.hospital.business.domain.dto.BusOrderDto;
import com.puree.hospital.business.domain.dto.HospitalOfficinaDrugsDTO;
import com.puree.hospital.business.domain.vo.AddressVO;
import com.puree.hospital.business.domain.vo.BusOrderDetailVo;
import com.puree.hospital.business.domain.vo.BusOrderDrugTraceCodeVO;
import com.puree.hospital.business.domain.vo.BusOrderVo;
import com.puree.hospital.business.domain.vo.DrugsAndGoodsVO;
import com.puree.hospital.business.domain.vo.GoodsVO;
import com.puree.hospital.business.domain.vo.OrderPackageVO;
import com.puree.hospital.business.domain.vo.OrderVO;
import com.puree.hospital.business.mapper.BusDrugsOrderMapper;
import com.puree.hospital.business.mapper.BusEnterpriseDrugsMapper;
import com.puree.hospital.business.mapper.BusEnterpriseMapper;
import com.puree.hospital.business.mapper.BusEnterpriseOrderMapper;
import com.puree.hospital.business.mapper.BusEnterpriseProductOrderMapper;
import com.puree.hospital.business.mapper.BusHospitalPaMapper;
import com.puree.hospital.business.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.business.mapper.BusOrderMapper;
import com.puree.hospital.business.mapper.BusOtcDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.channel.service.IBusChannelOrderService;
import com.puree.hospital.business.service.IBusConsultationSettingsService;
import com.puree.hospital.business.service.IBusDrugOrderPackageService;
import com.puree.hospital.business.service.IBusFreightBaseSettingService;
import com.puree.hospital.business.service.IBusHospitalEnterpriseService;
import com.puree.hospital.business.service.IBusOrderService;
import com.puree.hospital.business.service.IBusOrderShopService;
import com.puree.hospital.business.service.IBusOtcDrugsService;
import com.puree.hospital.business.service.IBusPartnersService;
import com.puree.hospital.business.service.IBusPrescriptionDrugsService;
import com.puree.hospital.business.service.refund.RefundFactory;
import com.puree.hospital.common.core.calculator.price.IPriceCalculator;
import com.puree.hospital.common.core.calculator.price.drug.CmPiecesDrugPriceCalculator;
import com.puree.hospital.common.core.calculator.price.drug.MmDrugPriceCalculator;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.QueueConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.AfterSaleRefundTypeEnum;
import com.puree.hospital.common.core.enums.ChannelOrderEnum;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.DeliveryTypeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.EnterpriseEnum;
import com.puree.hospital.common.core.enums.GoodsOrderEnum;
import com.puree.hospital.common.core.enums.HrOrderStatusEnum;
import com.puree.hospital.common.core.enums.InvoiceStatusEnum;
import com.puree.hospital.common.core.enums.OrderAfterSalesStatusEnum;
import com.puree.hospital.common.core.enums.OrderStatusEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.RefundOnlyEnum;
import com.puree.hospital.common.core.enums.RefundReturnEnum;
import com.puree.hospital.common.core.enums.RefundsExchangesEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.enums.YfOrderEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.supplier.yf.constant.ActionConstant;
import com.puree.hospital.common.supplier.yf.util.YfDeliverUtil;
import com.puree.hospital.insurance.api.RemotePaymentService;
import com.puree.hospital.insurance.api.model.MiPayResult;
import com.puree.hospital.order.api.RemoteBusDrugsOrderService;
import com.puree.hospital.order.api.RemoteBusOrderService;
import com.puree.hospital.order.api.RemoteBusOrderShopService;
import com.puree.hospital.order.api.RemoteBusShopOrderService;
import com.puree.hospital.order.api.model.BusDrugsOrderRequest;
import com.puree.hospital.order.api.model.BusShopOrder;
import com.puree.hospital.order.api.model.BusSubOrderDTO;
import com.puree.hospital.order.api.model.vo.BusDrugsOrderVO;
import com.puree.hospital.order.api.model.vo.OrderExportVO;
import com.puree.hospital.order.api.model.vo.OrderShopExportVO;
import com.puree.hospital.shop.api.RemoteShopGoodsService;
import com.puree.hospital.shop.api.model.BusShopGoods;
import com.puree.hospital.supplier.api.RemoteHRService;
import com.puree.hospital.supplier.api.model.hr.HrRefundDetails;
import com.puree.hospital.supplier.api.model.hr.HrRefundOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOrderServiceImpl implements IBusOrderService {
    private final BusOrderMapper busOrderMapper;
    private final RemoteBusOrderService remoteBusOrderService;
    private final RemoteBusDrugsOrderService remoteBusDrugsOrderService;
    private final RemoteBusShopOrderService remoteBusShopOrderService;
    private final IBusPrescriptionDrugsService busPrescriptionDrugsService;
    private final IBusOtcDrugsService busOtcDrugsService;
    private final IBusOrderShopService busOrderShopService;
    public  final RedisTemplate<String, String> redisTemplate;
    private final IBusConsultationSettingsService busConsultationSettingsService;
    private final IBusFreightBaseSettingService busFreightBaseSettingService;
    private final RemoteStockService remoteStockService;
    private final RemoteBusOrderShopService remoteBusOrderShopService;
    private final RemoteShopGoodsService remoteShopGoodsService;
    private final IBusChannelOrderService busChannelOrderService;
    private final BusHospitalPaMapper busHospitalPaMapper;
    private final BusEnterpriseProductOrderMapper busEnterpriseProductOrderMapper;
    // 远程调用微信支付服务
    private final RemoteWxPayService remoteWxPayService;
    @Autowired
    private IBusDrugOrderPackageService busDrugOrderPackageService;
    private final IBusPartnersService busPartnersService;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusOrderAfterSalesMapper busOrderAfterSalesMapper;
    private final BusDrugsOrderMapper drugsOrderMapper;
    private final BusPrescriptionDrugsMapper prescriptionDrugsMapper;
    private final BusOtcDrugsMapper otcDrugsMapper;
    private final BusEnterpriseMapper enterpriseMapper;
    private final BusEnterpriseDrugsMapper enterpriseDrugsMapper;
    private final IBusHospitalEnterpriseService hospitalEnterpriseService;
    private final RemoteHRService remoteHRService;
    private final RefundFactory refundFactory;
    @Autowired
    private BusEnterpriseOrderMapper enterpriseOrderMapper ;
    @Autowired
    private YfDeliverUtil yfDeliverUtil ;
    private final RemotePaymentService remotePaymentService;
    @Resource(name = "businessTaskExecutor")
    private Executor threadPoolExecutor;
    private final IPriceCalculator<GoodsVO> cmPiecesDrugPriceCalculator = new CmPiecesDrugPriceCalculator<>();

    private final IPriceCalculator<GoodsVO> wmDrugPriceCalculator = new MmDrugPriceCalculator<>();

    @Resource
    private ApplicationEventPublisher publisher;

    @Override
    public List<BusOrderVo> selectBusOrderList(BusOrderDto dto) {
        return busOrderMapper.selectBusOrderList(dto);
    }

    @Override
    public BusOrderDetailVo selectBusOrderDetail(BusOrder order) {
        return busOrderMapper.selectBusOrderDetail(order);
    }

    @Override
    public int examine(BusOrder order) {
        return busOrderMapper.updateById(order);
    }

    @Override
    public int delivery(BusOrder order) {
        return busOrderMapper.updateById(order);
    }

    /**
     * 查询订单详细
     *
     * @param orderNo - 订单号
     * @return - 订单详细
     */
    @Override
    public OrderVO selectOrderInfo(String orderNo) {
        OrderVO orderVo = new OrderVO();
        R<List<com.puree.hospital.order.api.model.BusOrder>> r = remoteBusOrderService.getOrderByNo(orderNo);
        if (Constants.SUCCESS == r.getCode()) {

            List<com.puree.hospital.order.api.model.BusOrder> orderList = r.getData();
            if (CollUtil.isNotEmpty(orderList)) {
                // 补充订单信息
                BusOrder order = new BusOrder();
                BeanUtils.copyProperties(orderList.get(0),order);
                order.setFreight(orderList.get(0).getFreight()+"");
                orderVo.setFreightType(order.getFreightType());
                orderVo.setReceivingUser(order.getReceiver());
                orderVo.setReceivingTel(order.getReceivePhone());
                orderVo.setReceivingAddress(order.getReceiveAddress());
                orderVo.setDetailedAddress(order.getDetailedAddress());
                orderVo.setStatus(order.getOrderStatus());
                orderVo.setPayWay(order.getPayWay());
                orderVo.setCancelTime(order.getCancelTime());
                orderVo.setPaymentTime(order.getPaymentTime());
                orderVo.setCompleteTime(order.getCompleteTime());
                orderVo.setPickUpTime(order.getPickUpTime());
                orderVo.setDeliveryType(order.getDeliveryType());
                orderVo.setOrderNo(order.getOrderNo());
                orderVo.setOrderTime(order.getOrderTime());
                orderVo.setAfterOrderId(order.getAfterSaleId());
                BigDecimal payAmount = BigDecimal.valueOf(order.getRelPrice());
                BigDecimal freight = new BigDecimal(order.getFreight());
                orderVo.setFreight(freight);
                orderVo.setCancelResult(order.getCancelResult());
                orderVo.setProcessPrice(new BigDecimal(0));
                orderVo.setRemarks(order.getRemarks());
                orderVo.setExaminationFee(order.getExaminationFee());
                orderVo.setExaminationName(order.getExaminationName());
                //医保订单
                R<MiPayResult> payResult = remotePaymentService.queryPayResult(orderNo);
                MiPayResult payResultData = payResult.getData();
                if (Objects.nonNull(payResultData)){
                    //设置为医保支付
                    orderVo.setPayWay(PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode());
                    //现金支付
                    orderVo.setOwnPayAmt(payResultData.getOwnPayAmt() == null ? new BigDecimal("0.00") : payResultData.getOwnPayAmt());
                    //个账支付
                    orderVo.setFundPay(payResultData.getFundPay() == null ? new BigDecimal("0.00") : payResultData.getFundPay());
                    //医保基金支付
                    orderVo.setPsnAcctPay(payResultData.getPsnAcctPay() == null?new BigDecimal("0.00") : payResultData.getPsnAcctPay());
                    //其他信息支付金额
                    orderVo.setOtherCashAmount(payResultData.getOtherCashAmount() == null ? BigDecimal.ZERO : payResultData.getOtherCashAmount());
                    //设置医保结算类型
                    orderVo.setMiMedType(payResultData.getMedType());
                }
                // 校验该订单是否是合作机构订单或合作渠道订单
                verificationOrder(order, orderVo);
                List<Integer> stringList = new ArrayList<>();
                if (StringUtils.isNotEmpty(order.getProvince())) {
                    AddressVO addressProvinceCode = busOrderMapper.getAddressProvinceCode(order.getProvince());
                    stringList.add(Integer.valueOf(addressProvinceCode.getCode()));
                    addressProvinceCode.setName(order.getCity());
                    addressProvinceCode.setProvinceCode(addressProvinceCode.getCode());
                    AddressVO addressCityCode = busOrderMapper.getAddressCityCode(addressProvinceCode);
                    stringList.add(Integer.valueOf(addressCityCode.getCode()));
                    addressCityCode.setName(order.getArea());
                    addressCityCode.setCityCode(addressCityCode.getCode());
                    addressCityCode.setProvinceCode(addressProvinceCode.getCode());
                    AddressVO addressAreaCode = busOrderMapper.getAddressAreaCode(addressCityCode);
                    stringList.add(Integer.valueOf(addressAreaCode.getCode()));
                }
                orderVo.setAddressCode(stringList);
                orderVo.setCity(order.getCity());
                orderVo.setProvince(order.getProvince());
                orderVo.setArea(order.getArea());
                Integer logisticsType = order.getDeliveryType().equals(CodeEnum.YES.getCode()) ? 1 : 0;
                orderVo.setLogisticsType(logisticsType);
                // 统计金额
                BigDecimal goodsAmount = new BigDecimal("0");
                // 商品信息集合
                List<GoodsVO> goodsList = new ArrayList<>();
                for (int i = 0; i < orderList.size(); i++) {
                    BusOrder busOrder = new BusOrder();
                    BeanUtils.copyProperties(orderList.get(i),busOrder);
                    // 药品订单
                    if (CodeEnum.NO.getCode().equals(busOrder.getSubOrderType())) {
                        R<com.puree.hospital.order.api.model.BusDrugsOrder> r1 = remoteBusDrugsOrderService.selectDrugsOrderById(busOrder.getSubOrderId());
                        if (Constants.SUCCESS == r1.getCode()) {
                            BusDrugsOrder drugsOrder = JSONObject.parseObject(JSONObject.toJSONString(r1.getData()), BusDrugsOrder.class);
                            handleDrugsOrderInfo(drugsOrder, orderVo, goodsList, busOrder);
                            BusPrescription busPrescription = busPrescriptionMapper.selectById(drugsOrder.getPrescriptionId());
                            // 加上药品总价
                            if (busPrescription != null && busPrescription.getPrescriptionAmount() != null) {
                                goodsAmount = goodsAmount.add(busPrescription.getPrescriptionAmount());
                            }
                        } else {
                            log.error("远程调用查询药品订单失败={}", r1.getMsg());
                        }
                    } else { // 商品订单
                        R<BusShopOrder> r1 = remoteBusShopOrderService.selectShopOrderById(busOrder.getSubOrderId());
                        if (Constants.SUCCESS == r1.getCode()) {
                            BusShopOrder shopOrder = r1.getData();
                            orderVo.setFamilyId(shopOrder.getFamilyId());
                            orderVo.setFamilyName(shopOrder.getFamilyName());
                            orderVo.setFamilySex(shopOrder.getFamilySex());
                            orderVo.setFamilyAge(shopOrder.getFamilyAge());
                            shopOrder.setStatus(order.getOrderStatus());
                            handleGoodsOrderInfo(shopOrder, goodsList,orderList.get(0).getOrderStatus());

                        } else {
                            log.error("远程调用查询商品订单失败={}", r1.getMsg());
                        }
                    }
                }
                for (GoodsVO vo : goodsList) {
                    // 计算商品总价
                    if (ObjectUtil.isEmpty(vo.getPrescriptionType())) {
                        goodsAmount = goodsAmount.add(vo.getSubtotalPrice());
                    }
                }
                orderVo.setGoodsList(goodsList);
                orderVo.setTotalPrice(goodsAmount.setScale(2, RoundingMode.HALF_UP));
                orderVo.setPayAmount(payAmount);
                // 查询订单包裹信息
                int barterOrNot = 0;
                if (new BigDecimal(0).compareTo(BigDecimal.valueOf(order.getRelPrice())) == 0 && YesNoEnum.NO.getCode().equals(order.getChangePrice())) {
                    barterOrNot = 1;
                }
                List<BusDrugOrderPackage> packages = busDrugOrderPackageService.selectPackageList(orderNo, barterOrNot);
                log.info("包裹列表={}", JSON.toJSONString(packages));
                if (CollUtil.isNotEmpty(packages)) {
                    for (int i = 0; i < packages.size(); i++) {
                        BusDrugOrderPackage orderPackage = packages.get(i);
                        List<OrderPackageVO> orderPackageList = orderPackage.getOrderPackageList();
                        if (i == 0) {
                            orderVo.setDeliveryTime(orderPackage.getDeliveryTime());
                            OrderPackageVO vo = orderPackageList.get(0);
                            orderVo.setCreateBy(vo.getCreateBy());
                            orderVo.setCode(vo.getCode());
                            if ("2".equals(vo.getCode())) {
                                orderVo.setCode(null);
                            }
                        }
                        // 定义包裹详情集合
                        List<DrugsAndGoodsVO> drugsAndGoodsList = new ArrayList<>();
                        for (OrderPackageVO vo : orderPackageList) {
                            if (Objects.nonNull(vo.getPrescriptionId())) {
                                // 查询处方信息
                                BusPrescription busPrescription = busPrescriptionMapper.selectById(vo.getPrescriptionId());
                                if (CodeEnum.YES.getCode().equals(busPrescription.getPrescriptionType())) {
                                    if (Objects.isNull(vo.getEnterpriseId())) {
                                        orderPackage.setPrescriptionId(vo.getPrescriptionId());
                                        orderPackage.setDrugsGoodsId(vo.getDrugsGoodsId());
                                        drugsAndGoodsList.add(busPrescriptionDrugsService.selectWmDrugs(orderPackage));
                                    } else {
                                        orderPackage.setPrescriptionId(vo.getPrescriptionId());
                                        orderPackage.setEnterpriseId(vo.getEnterpriseId());
                                        List<DrugsAndGoodsVO> wmList = busPrescriptionDrugsService.selectEnterpriseWmList(orderPackage);
                                        drugsAndGoodsList.addAll(wmList);
                                    }
                                } else {
                                    orderPackage.setPrescriptionId(vo.getPrescriptionId());
                                    orderPackage.setEnterpriseId(vo.getEnterpriseId());
                                    List<DrugsAndGoodsVO> tcmList = busPrescriptionDrugsService.selectTcmList(orderPackage);
                                    drugsAndGoodsList.addAll(tcmList);
                                }
                            } else if (YesNoEnum.NO.getCode().equals(vo.getPackageType())) {
                                if (Objects.isNull(vo.getEnterpriseId())) {
                                    orderPackage.setDrugsOrderId(vo.getDrugsOrderId());
                                    orderPackage.setDrugsGoodsId(vo.getDrugsGoodsId());
                                    drugsAndGoodsList.add(busPrescriptionDrugsService.selectOtcDrugs(orderPackage));
                                } else {
                                    orderPackage.setDrugsOrderId(vo.getDrugsOrderId());
                                    orderPackage.setEnterpriseId(vo.getEnterpriseId());
                                    List<DrugsAndGoodsVO> otcList = busPrescriptionDrugsService.selectEnterpriseOtcList(orderPackage);
                                    drugsAndGoodsList.addAll(otcList);
                                }
                            } else {
                                orderPackage.setDrugsOrderId(vo.getDrugsOrderId());
                                orderPackage.setDrugsGoodsId(vo.getDrugsGoodsId());
                                drugsAndGoodsList.add(busOrderShopService.selectShops(orderPackage));
                            }
                        }
                        orderPackage.setDrugsAndGoodsList(drugsAndGoodsList);
                    }
                }
                orderVo.setPackageList(packages);
                if (GoodsOrderEnum.PENDING_PAYMENT.getCode().equals(orderVo.getStatus())) {
                    orderVo.setStepActive(1);
                    BusConsultationSettings bs = new BusConsultationSettings();
                    bs.setHospitalId(order.getHospitalId());
                    bs = busConsultationSettingsService.selectOne(bs);
                    long expireTime;
                    if (Objects.isNull(bs) || Objects.isNull(bs.getConsultationOrderExpire())) {
                        expireTime = QueueConstant.QUEUE_DRUGS_ORDER_CANCEL_TIME + order.getOrderTime().getTime();
                    } else {
                        long time = bs.getConsultationOrderExpire() * 60 * 60 * 1000L;
                        expireTime = time + order.getOrderTime().getTime();
                    }
                    orderVo.setQueueExpireTime(new Date(expireTime));
                } else if (GoodsOrderEnum.TO_BE_SHIPPED.getCode().equals(orderVo.getStatus())) {
                    orderVo.setStepActive(2);
                } else if (GoodsOrderEnum.TOBEPICKEDUP.getCode().equals(orderVo.getStatus()) ||
                        GoodsOrderEnum.GOODS_TO_BE_RECEIVED.getCode().equals(orderVo.getStatus())) {
                    orderVo.setStepActive(3);
                    Date deliveryTime = orderVo.getDeliveryTime();
                    if (Objects.nonNull(deliveryTime)) {
                        Calendar instance = Calendar.getInstance();
                        instance.setTime(deliveryTime);
                        instance.add(Calendar.DATE, 7);
                        Date time = instance.getTime();
                        orderVo.setQueueExpireTime(time);
                    }
                } else if (OrderStatusEnum.FINISH.getCode().equals(orderVo.getStatus())) {
                    orderVo.setStepActive(4);
                }
            }
        } else {
            log.error("医院后台交易订单详情查询失败={}", r.getMsg());
        }
        return orderVo;
    }

    /**
     * 校验订单归属
     * @param order - 订单
     * @param orderVo - 订单详情
     */
    private void verificationOrder(BusOrder order, OrderVO orderVo) {
        String channelName = "";
        String partner = "";
        if (StringUtils.isNotEmpty(order.getPartnersCode())) {
            // 查询机构名称
            BusPartners busPartners = busPartnersService.selectPartnersByCode(order.getPartnersCode());
            channelName = busPartners.getFullName();
        } else {
            // 校验是否是合作渠道订单
            BusChannelOrder channelOrder = busChannelOrderService.selectChannelOrderInfo(ChannelOrderEnum.TO.getCode(), order.getId());
            if (Objects.nonNull(channelOrder)) {
                channelName = channelOrder.getPartnerName();
                partner = channelOrder.getAgentName();
            }
        }
        orderVo.setChannelName(channelName);
        orderVo.setPartner(partner);
    }

    /**
     * 处理商品信息
     *
     * @param shopOrder   - 商品订单
     * @param list        - 展示列表
     * @param orderStatus
     */
    private void handleGoodsOrderInfo(BusShopOrder shopOrder, List<GoodsVO> list, String orderStatus) {
        // 查询商品信息
        List<GoodsVO> goodsList = busOrderShopService.selectGoodsList(shopOrder.getId());
        for (GoodsVO vo : goodsList) {
            vo.setOrderNo(shopOrder.getOrderNo());
            if (StringUtils.isNotEmpty(vo.getCode())) {
                vo.setDeliveryStatus("已发货");
            } else {
                vo.setDeliveryStatus(YesNoEnum.NO.getCode().equals(vo.getDeliveryMark()) ? "未发货" : "已发货");
                if (DeliveryTypeEnum.isVirtual(shopOrder.getDeliveryType()) && (OrderStatusEnum.isFinish(orderStatus) || OrderStatusEnum.isWaitReceive(orderStatus))){
                    vo.setDeliveryStatus("已发货");
                }
            }
            if (null != vo.getEnterpriseId() && vo.getEnterpriseId().equals(0L)) {
                vo.setDistribution("众爱云仓");
            }
            // 查询售后信息
            LambdaQueryWrapper<BusOrderAfterSales> query = Wrappers.lambdaQuery();
            query.eq(BusOrderAfterSales::getAfterSalesType, CodeEnum.YES.getCode())
                    .eq(BusOrderAfterSales::getOrderId, vo.getOrderId())
                    .eq(BusOrderAfterSales::getGoodsId, vo.getId())
                    .ne(BusOrderAfterSales::getAfterSalesStatus, OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode());
            BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(query);
            if (YesNoEnum.YES.getCode().equals(shopOrder.getDelivery()) && null != busOrderAfterSales &&
                    busOrderAfterSales.getQuantity().equals(vo.getQuantity())) {
                continue;
            }
            if (null != busOrderAfterSales) {
                if (vo.getQuantity().equals(busOrderAfterSales.getQuantity())) {
                    vo.setAfterSalesId(busOrderAfterSales.getId());
                }
                vo.setOrderAfterSalesStatus(busOrderAfterSales.getAfterSalesStatus());
                vo.setAfterQuantity(vo.getQuantity()-busOrderAfterSales.getQuantity());
            } else {
                vo.setAfterQuantity(vo.getQuantity());
            }
            // 商品目前直接按照正常值的计算-直接相乘计算出小计和
            vo.setSubtotalPrice(vo.getPrice().multiply(BigDecimal.valueOf(vo.getQuantity())));
            list.add(vo);
        }
    }

    /**
     * 处理药品信息
     * @param drugsOrder - 药品订单
     * @param orderVo - 订单详情
     * @param goodsList - list展示列表
     * @param order - 订单
     */
    private void handleDrugsOrderInfo(BusDrugsOrder drugsOrder, OrderVO orderVo, List<GoodsVO> goodsList, BusOrder order) {
        // 查询发货信息
        int barterOrNot = 0;
        if (new BigDecimal(0).compareTo(BigDecimal.valueOf(order.getRelPrice())) == 0 &&
                YesNoEnum.NO.getCode().equals(order.getChangePrice())) {
            barterOrNot = 1;
        }
        // 查询药品信息
        if (null != drugsOrder.getPrescriptionId()) {
            List<GoodsVO> drugsList = busPrescriptionDrugsService.selectDrugsList(Collections.singletonList(drugsOrder.getPrescriptionId()));
            GoodsVO goodsVO = drugsList.get(0);
            goodsVO.setBarterOrNot(barterOrNot);
            goodsVO.setOrderId(drugsOrder.getId());
            // 查询售后信息
            LambdaQueryWrapper<BusOrderAfterSales> query = Wrappers.lambdaQuery();
            query.eq(BusOrderAfterSales::getPrescriptionId, goodsVO.getPrescriptionId())
                    .eq(BusOrderAfterSales::getOrderId, drugsOrder.getId())
                    .ne(BusOrderAfterSales::getAfterSalesStatus, OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode());
            BusOrderAfterSales orderAfterSales = busOrderAfterSalesMapper.selectOne(query);
            if (YesNoEnum.YES.getCode().equals(drugsOrder.getDelivery()) && null != orderAfterSales) {
                return;
            }
            if (PrescriptionTypeEnum.isMm(goodsVO.getPrescriptionType())) {
                // 组装西药列表
                westernDrugs(drugsOrder, goodsList, drugsList, barterOrNot, orderAfterSales,order.getDeliveryType());
            } else {
                // 组装中药列表
                chinaDrugs(drugsOrder, orderVo, goodsList, goodsVO, drugsList, orderAfterSales);
            }
            orderVo.setFamilyName(goodsVO.getFamilyName());
            orderVo.setFamilyTel(goodsVO.getFamilyTel());
            orderVo.setFamilySex(goodsVO.getFamilySex());
            orderVo.setFamilyAge(goodsVO.getFamilyAge());
        } else {
            List<GoodsVO> drugsList = busOtcDrugsService.selectDrugsList(drugsOrder.getId());
            for (GoodsVO d : drugsList) {
                d.setIsSendSuccess(drugsOrder.getIsSendSuccess());
                d.setDrugsType(1);
                d.setOrderNo(drugsOrder.getOrderNo());
                if (StringUtils.isNotEmpty(d.getCode())) {
                    d.setDeliveryStatus("已发货");
                } else {
                    d.setDeliveryStatus(YesNoEnum.NO.getCode().equals(d.getDeliveryMark()) ? "未发货" : "已发货");
                }
                // 查询售后信息
                LambdaQueryWrapper<BusOrderAfterSales> query = Wrappers.lambdaQuery();
                query.eq(BusOrderAfterSales::getAfterSalesType, CodeEnum.NO.getCode())
                        .eq(BusOrderAfterSales::getOrderId, d.getOrderId())
                        .eq(BusOrderAfterSales::getGoodsId, d.getId())
                        .ne(BusOrderAfterSales::getAfterSalesStatus, OrderAfterSalesStatusEnum.AFTER_SALE_CLOSURE.getCode());
                BusOrderAfterSales busOrderAfterSales = busOrderAfterSalesMapper.selectOne(query);
                if (YesNoEnum.YES.getCode().equals(drugsOrder.getDelivery()) && null != busOrderAfterSales) {
                    continue;
                }
                if (null != busOrderAfterSales) {
                    if (busOrderAfterSales.getQuantity().equals(d.getQuantity())) {
                        d.setAfterSalesId(busOrderAfterSales.getId());
                    }
                    d.setOrderAfterSalesStatus(busOrderAfterSales.getAfterSalesStatus());
                    d.setAfterQuantity(d.getQuantity() - busOrderAfterSales.getQuantity());
                } else {
                    d.setAfterQuantity(d.getQuantity());
                }
                goodsList.add(d);
            }
        }
    }

    /**
     * 药品详细列表组装中药列表 - 2024-9-30 提取为方法
     * @param drugsOrder - 药品订单
     * @param orderVo - 订单详情
     * @param goodsList - 展示列表
     * @param goodsVO - 详细信息
     * @param drugsList - 查询药品列表
     * @param orderAfterSales - 售后信息
     */
    public void chinaDrugs(BusDrugsOrder drugsOrder, OrderVO orderVo, List<GoodsVO> goodsList, GoodsVO goodsVO, List<GoodsVO> drugsList, BusOrderAfterSales orderAfterSales) {
        //暂时先将
        goodsVO.setExaminationFee(drugsOrder.getExaminationFee());
        goodsVO.setExaminationName(drugsOrder.getExaminationName());
        // 查询中药处方包裹信息
        List<BusDrugOrderPackage> tcmPackageList = busDrugOrderPackageService.selectPrescriptionPackageList(goodsVO);
        log.info("药品订单查询处方tcmPackageList：{},药品订单查询处方goodsVO：{}",tcmPackageList,goodsVO);
        String usages = goodsVO.getUsages();
        String[] split = usages.split(",");
        Integer dose = Integer.valueOf(split[1]);
        // 计算除去加工费的价格
        if (goodsVO.getProcessPrice() == null || goodsVO.getProcessPrice().compareTo(BigDecimal.ZERO) <= 0){
            goodsVO.setProcessPrice(BigDecimal.ZERO);
        }
        BigDecimal priceWithNoProcessPrice = goodsVO.getPrescriptionAmount().subtract(goodsVO.getProcessPrice());
        log.info("药品订单查询处方：{},处方金额：{},处方剂量：{}",goodsVO.getPrescriptionId(),goodsVO.getPrescriptionAmount(),dose);
        if (PrescriptionTypeEnum.isTcm(goodsVO.getPrescriptionType())) {
            for (GoodsVO vo : drugsList) {
                // 设置未中药饮片类型
                vo.setPrescriptionType(goodsVO.getPrescriptionType());
                // 2024-9-30 中药饮片修改为列表
                GoodsVO drug = new GoodsVO();
                BeanUtils.copyProperties(goodsVO, drug);
                // g/袋  g/瓶
                String sb = vo.getSpecification() + "×" + vo.getWeight();
                drug.setName(vo.getName());
                drug.setSpecification(sb);
                drug.setPrice(vo.getPrice());
                drug.setDoses(dose);
                drug.setWeight(vo.getWeight());
                drug.setQuantity(dose);
                drug.setAfterQuantity(dose);
                // 饮片-不设置type
                drug.setIsSendSuccess(drugsOrder.getIsSendSuccess());
                drug.setOrderNo(drugsOrder.getOrderNo());
                // 校验发货状态
                checkDeliveryStatus(tcmPackageList, drug);
                drug.setOrderClassify(CodeEnum.NO.getCode());
                // 计算小计价格
                BigDecimal subtotal = cmPiecesDrugPriceCalculator.calculate(drug);
                drug.setSubtotalPrice(subtotal);
                log.info("药品订单查询处方药品{},销售单价：{},小计：{}", vo.getName(), vo.getPrice(), subtotal);
                goodsList.add(drug);
                if (null != orderAfterSales) {
                    drug.setAfterSalesId(orderAfterSales.getId());
                    drug.setOrderAfterSalesStatus(orderAfterSales.getAfterSalesStatus());
                }
            }
            BigDecimal decimal = Optional.ofNullable(orderVo.getProcessPrice()).orElse(BigDecimal.ZERO);
            if (!drugsList.isEmpty()){
                orderVo.setProcessPrice(decimal.add(drugsList.get(0).getProcessPrice()));
            }
        } else {
            // 查询方剂类型 -未修改（2024-9-30）
            BusHospitalPa busHospitalPa = busHospitalPaMapper.selectById(goodsVO.getPaId());
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < drugsList.size(); i++) {
                GoodsVO vo = drugsList.get(i);
                sb.append(vo.getName())
                        .append(vo.getSpecification())
                        .append("×")
                        .append(vo.getQuantity())
                        .append(" ");
                if (i != drugsList.size() - 1) {
                    sb.append("，");
                }
            }
            if (YesNoEnum.NO.getCode().equals(busHospitalPa.getType())) {
                goodsVO.setName(busHospitalPa.getPaName());
                goodsVO.setType(YesNoEnum.NO.getCode());
                // 校验发货状态
            } else {
                goodsVO.setName(drugsList.size() + "");
                goodsVO.setType(YesNoEnum.YES.getCode());
                // 校验发货状态
            }
            //设置处方单价：处方单价=(处方总额-加工费)/数量
            //为了防止处方价格变动后，之前的单价显示错误，所以从总额去倒推，而不是获取数据库中处方的单价
            goodsVO.setPrice(priceWithNoProcessPrice.divide(BigDecimal.valueOf(dose), 6, RoundingMode.HALF_UP));
            goodsVO.setQuantity(dose);
            goodsVO.setSubtotalPrice(priceWithNoProcessPrice.setScale(2, RoundingMode.HALF_UP));
            goodsVO.setAfterQuantity(dose);
            // 方剂类型
            goodsVO.setPrescriptionType(goodsVO.getPrescriptionType());
            goodsVO.setSpecification(sb.toString());
            goodsVO.setIsSendSuccess(drugsOrder.getIsSendSuccess());
            goodsVO.setOrderNo(drugsOrder.getOrderNo());
            checkDeliveryStatus(tcmPackageList, goodsVO);
            goodsVO.setOrderClassify(CodeEnum.NO.getCode());
            goodsList.add(goodsVO);
            if (null == orderVo.getProcessPrice()){
                orderVo.setProcessPrice(BigDecimal.ZERO);
            }
            orderVo.setProcessPrice(orderVo.getProcessPrice().add(goodsVO.getProcessPrice()));
        }
    }

    /**
     * 药品详情组装西药列表- 2024-9-30 提取为方法
     *
     * @param drugsOrder      - 药品订单
     * @param goodsList       - 展示列表
     * @param drugsList       - 查询药品列表
     * @param barterOrNot     - 是否为代购
     * @param orderAfterSales - 售后信息
     * @param deliveryType 订单发货类型 0 自提 1 快递
     */
    public void westernDrugs(BusDrugsOrder drugsOrder, List<GoodsVO> goodsList, List<GoodsVO> drugsList, int barterOrNot, BusOrderAfterSales orderAfterSales, String deliveryType) {
        for (GoodsVO vo : drugsList) {
            // 设置为西药类型
            vo.setPrescriptionType(PrescriptionTypeEnum.MM.getCode());
            //订单明细需要展示诊查费信息，暂时先放到药品列表中
            vo.setExaminationFee(drugsOrder.getExaminationFee());
            vo.setExaminationName(drugsOrder.getExaminationName());
            vo.setOrderClassify(drugsOrder.getOrderClassify());
            vo.setOrderId(drugsOrder.getId());
            vo.setBarterOrNot(barterOrNot);
            List<BusDrugOrderPackage> prescriptionPackage = busDrugOrderPackageService.selectPackageStatusList(vo);
            if (null != orderAfterSales) {
                vo.setAfterSalesId(orderAfterSales.getId());
                vo.setOrderAfterSalesStatus(orderAfterSales.getAfterSalesStatus());
            }
            vo.setAfterQuantity(vo.getQuantity());
            vo.setIsSendSuccess(drugsOrder.getIsSendSuccess());
            vo.setOrderNo(drugsOrder.getOrderNo());
            if (CollUtil.isEmpty(prescriptionPackage)) {
                vo.setDeliveryStatus("未发货");
                vo.setDeliveryMark(YesNoEnum.NO.getCode());
            } else {
                if (null != vo.getEnterpriseId()) {
                    boolean flag = true;
                    for (BusDrugOrderPackage orderPackage : prescriptionPackage) {
                        if (vo.getEnterpriseId().equals(orderPackage.getEnterpriseId()) &&  (deliveryType.equals("0")
                                || CharSequenceUtil.isNotBlank(orderPackage.getDeliveryNo())) ) {
                            vo.setDeliveryStatus("已发货");
                            vo.setDeliveryMark(YesNoEnum.YES.getCode());
                            vo.setDeliveryNo(orderPackage.getDeliveryNo());
                            vo.setLogisticsCompany(orderPackage.getLogisticsCompany());
                            vo.setExpressCode(orderPackage.getExpressCode());
                            vo.setDeliveryTime(orderPackage.getDeliveryTime());
                            flag = false;
                            break;
                        }
                    }
                    if (flag) {
                        vo.setDeliveryStatus("未发货");
                        vo.setDeliveryMark(YesNoEnum.NO.getCode());
                    }
                } else {
                    boolean flag = true;
                    for (BusDrugOrderPackage orderPackage : prescriptionPackage) {
                        if (vo.getId().equals(orderPackage.getDrugsGoodsId())) {
                            vo.setDeliveryStatus("已发货");
                            vo.setDeliveryMark(YesNoEnum.YES.getCode());
                            vo.setDeliveryNo(orderPackage.getDeliveryNo());
                            vo.setLogisticsCompany(orderPackage.getLogisticsCompany());
                            vo.setExpressCode(orderPackage.getExpressCode());
                            vo.setDeliveryTime(orderPackage.getDeliveryTime());
                            flag = false;
                            break;
                        }
                    }
                    if (flag) {
                        vo.setDeliveryStatus("未发货");
                        vo.setDeliveryMark(YesNoEnum.NO.getCode());
                    }
                }
            }
            // 西药计算小计
            BigDecimal subtotal = wmDrugPriceCalculator.calculate(vo);
            vo.setSubtotalPrice(subtotal);
        }

        goodsList.addAll(drugsList);
    }

    /**
     * 校验发货状态
     * @param tcmPackageList - 处方包裹信息
     * @param goodsVO - 药品信息
     */
    private void checkDeliveryStatus(List<BusDrugOrderPackage> tcmPackageList, GoodsVO goodsVO) {
        if (CollUtil.isNotEmpty(tcmPackageList)) {
            BusDrugOrderPackage orderPackage = tcmPackageList.get(0);
            goodsVO.setDeliveryStatus("已发货");
            goodsVO.setDeliveryMark(YesNoEnum.YES.getCode());
            goodsVO.setDeliveryNo(orderPackage.getDeliveryNo());
            goodsVO.setLogisticsCompany(orderPackage.getLogisticsCompany());
            goodsVO.setExpressCode(orderPackage.getExpressCode());
            goodsVO.setDeliveryTime(orderPackage.getDeliveryTime());
        } else {
            goodsVO.setDeliveryStatus("未发货");
            goodsVO.setDeliveryMark(YesNoEnum.NO.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOrderAddress(BusHospitalOrderDTO dto) {
        log.info("更新订单地址busHospitalOrderDto={}", dto);
        List<BusOrder> orderList = getOrderList(dto.getOrderNo());
        int update = 0;
        if (CollUtil.isNotEmpty(orderList)) {
            BusOrder busOrder = orderList.get(0);
            // 订单状态待支付或订单未生成包裹可以修改收货地址
            if (DrugsOrderEnum.PENDING_PAYMENT.getCode().equals(busOrder.getOrderStatus()) ||
                    DrugsOrderEnum.TO_BE_SHIPPED.getCode().equals(busOrder.getOrderStatus())) {
                //根据配置判断是否允许修改
                checkIsModifyAddress(busOrder, dto);
                for (BusOrder order : orderList) {
                    // 查询包裹信息
                    List<BusDrugOrderPackage> packageList = busDrugOrderPackageService.selectOrderPackageList(order);
                    if (CollUtil.isNotEmpty(packageList)) {
                        BusDrugOrderPackage orderPackage = packageList.get(0);
                        if (StringUtils.isNotEmpty(orderPackage.getDeliveryNo())) {
                            throw new ServiceException("该订单已部分发货，不可以修改收货人信息");
                        }
                    }
                }
                // 修改收货地址
                BusOrder order = new BusOrder();
                order.setReceiver(dto.getReceivingUser());
                order.setReceivePhone(dto.getReceivingTel());
                order.setReceiveAddress(dto.getProvince() + dto.getCity() + dto.getArea() + dto.getDetailedAddress());
                order.setProvince(dto.getProvince());
                order.setCity(dto.getCity());
                order.setArea(dto.getArea());
                order.setDetailedAddress(dto.getDetailedAddress());
                LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusOrder::getOrderNo, busOrder.getOrderNo());
                update = busOrderMapper.update(order, lambdaQuery);
            }
        }
        return update;
    }

    private void checkIsModifyAddress(BusOrder busOrder, BusHospitalOrderDTO dto) {
        BusFreightBaseSetting bs = busFreightBaseSettingService.selectOne(new BusFreightBaseSetting().setHospitalId(busOrder.getHospitalId()));
        if (bs == null) {
            return;
        }
        //暂时不做这个逻辑判断
///        if (!Boolean.TRUE.equals(bs.getAllowModifyAddress())) {
//            throw new ServiceException("该订单配送设置为【不允许用户修改地址】，无法修改");
//        }
        if (!Objects.equals(dto.getProvince(), busOrder.getProvince()) || !Objects.equals(dto.getCity(), busOrder.getCity()) || ! Objects.equals(dto.getArea(), busOrder.getArea())) {
            throw new ServiceException("当前订单只允许修改详细地址");
        }
    }

    @Override
    public int updateDrugsOrderById(BusDrugsOrderRequest busDrugsOrderRequest) {
        R<Integer> objectR = remoteBusDrugsOrderService.updateBusDrugsOrderById(busDrugsOrderRequest);
        if (!Constants.SUCCESS.equals(objectR.getCode())) {
            throw new ServiceException(objectR.getMsg());
        }
        return objectR.getData();
    }

    @Override
    public int updateShopOrderById(BusShopOrder busShopOrder) {
        R<Integer> objectR = remoteBusShopOrderService.updateShopOrderById(busShopOrder);
        if (!Constants.SUCCESS.equals(objectR.getCode())) {
            throw new ServiceException(objectR.getMsg());
        }
        return objectR.getData();
    }

    @Override
    public List<BusOrder> getOrderList(String orderNo){
        R<List<com.puree.hospital.order.api.model.BusOrder>> r = remoteBusOrderService.getOrderByNo(orderNo);
        if (!Constants.SUCCESS.equals(r.getCode())) {
            throw new ServiceException(r.getMsg());
        }
        if (CollectionUtils.isEmpty(r.getData())) {
            return Collections.emptyList();
        }
        return r.getData().stream().map(busOrder -> {
            BusOrder order = new BusOrder();
            BeanUtils.copyProperties(busOrder, order);
            order.setFreight(busOrder.getFreight()+"");
            return order;
        }).collect(Collectors.toList());
    }

    @Override
    public BusDrugsOrderVO getDrugsOrdeByOrderNo(String orderNo){
        R<BusDrugsOrderVO> r = remoteBusDrugsOrderService.getBusDrugsOrderByOrderNo(orderNo);
        if (!Constants.SUCCESS.equals(r.getCode())) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    @Override
    public BusShopOrder getShopOrdeByOrderNo(String orderNo) {
        R<BusShopOrder> r = remoteBusShopOrderService.selectShopOrderByNo(orderNo);
        if (!Constants.SUCCESS.equals(r.getCode())) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    @Override
    public BusShopOrder getShopOrdeById(Long id) {
        R<BusShopOrder> objectR = remoteBusShopOrderService.selectShopOrderById(id);
        if (!Constants.SUCCESS.equals(objectR.getCode())) {
            throw new ServiceException(objectR.getMsg());
        }
        return objectR.getData();
    }

    @Override
    public BusDrugsOrder getDrugsOrdeById(Long id) {
        R<com.puree.hospital.order.api.model.BusDrugsOrder> r = remoteBusDrugsOrderService.selectDrugsOrderById(id);
        if (!Constants.SUCCESS.equals(r.getCode())) {
            throw new ServiceException(r.getMsg());
        }
        return JSON.parseObject(JSON.toJSONString(r.getData()), BusDrugsOrder.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCancelOrder(BusHospitalOrderDTO dto) {
        log.info("busHospitalOrderDto ={}", dto);
        List<BusOrder> orderList = getOrderList(dto.getOrderNo());
        List<BusOrderAfterSales> orderAfterSalesList = busOrderAfterSalesMapper.selectList(
           new LambdaQueryWrapper<BusOrderAfterSales>()
             .eq(BusOrderAfterSales::getOrderNo, dto.getOrderNo())
        );
        // 校验该订单是否全部申请售后
        BusOrder busOrderFirst = orderList.get(0);
        if (Objects.isNull(busOrderFirst)){
            throw new ServiceException("订单不存在");
        }
        if (!PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrderFirst.getPayWay())) {
            // 非医保订单，校验是否申请了售后，如果申请了售后，则不允许取消订单
            verifyOrder(orderList, orderAfterSalesList);
        }
        int update = 0;
        if (CollUtil.isEmpty(orderList)) {
            return update;
        }
        if (!OrderStatusEnum.WAIT_DELIVER.getCode().equals(busOrderFirst.getOrderStatus())) {
            log.info("当前订单状态为{}，不执行退款操作", busOrderFirst.getOrderStatus());
            return update;
        }
        // 更新订单状态为已经取消
        update = setOrderToCancel(dto);
        // 释放库存
        releaseStockByOrderList(orderList);
        // 推送新售后单
        pushNewAfterSalesOrderToEnterprise(dto, busOrderFirst);
        if (Double.compare(busOrderFirst.getRelPrice(), 0.0) == 0) {
            // 订单金额为0，不执行退款操作
            sendOrderCancelEventNotification(busOrderFirst);
            return update;
        }
        BigDecimal refundAmount = getRefundAmount(busOrderFirst, orderAfterSalesList);
        // 退款取消订单
        refundCancelOrder(busOrderFirst, refundAmount);
        return update;
    }

    /**
     *  发送订单取消事件通知
     * @param order 订单信息
     */
    private void sendOrderCancelEventNotification(BusOrder order) {
        // 组装订单取消事件通知
        OrderCancelEvent orderCancelEvent = new OrderCancelEvent();
        orderCancelEvent.setHospitalId(order.getHospitalId());
        orderCancelEvent.setTotalOrderNo(order.getOrderNo());
        orderCancelEvent.setJumpHomePage(Boolean.TRUE);
        orderCancelEvent.setHasRefund(Boolean.FALSE);
        publisher.publishEvent(orderCancelEvent);
    }

    /**
     * 取消订单发起退款
     * @param busOrder - 订单信息
     * @param refundAmount - 退款金额
     */
    private void refundCancelOrder(BusOrder busOrder, BigDecimal refundAmount) {
        //判断支付类型退款
        if (PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode().equals(busOrder.getPayWay())) {
            //微信医保取消订单退款
            wxInsuranceOrderCancelToRefund(busOrder.getOrderNo(), busOrder);
        } else {
            refundFactory.processRefund(busOrder, refundAmount ,OrderTypeEnum.GOODS);
        }
        log.info("取消订单{}退款成功", busOrder.getOrderNo());
    }

    /**
     * 获取退款金额
     * @param busOrderFirst - 订单信息
     * @param orderAfterSalesList - 售后单列表
     * @return 退款金额
     */
    private BigDecimal getRefundAmount(BusOrder busOrderFirst, List<BusOrderAfterSales> orderAfterSalesList) {
        BigDecimal refundAmount = BigDecimal.valueOf(busOrderFirst.getRelPrice());
        if (CollUtil.isNotEmpty(orderAfterSalesList)) {
            List<BigDecimal> list = orderAfterSalesList.stream().filter(s -> OrderAfterSalesStatusEnum.isEnd(s.getAfterSalesStatus())).map(BusOrderAfterSales::getRefundAmount).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                BigDecimal bigDecimal = list.stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                refundAmount = refundAmount.subtract(bigDecimal);
            }
        }
        return refundAmount;
    }

    /**
     * 按照订单新推送售后订单给配送企业
     * @param dto - 订单信息
     * @param busOrderFirst - 订单信息
     */
    private void pushNewAfterSalesOrderToEnterprise(BusHospitalOrderDTO dto, BusOrder busOrderFirst) {
        BusOrderAfterSales orderAfterSales = new BusOrderAfterSales();
        orderAfterSales.setRefundType(AfterSaleRefundTypeEnum.REFUND_ONLY.getCode());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        orderAfterSales.setAfterSalesNumber(simpleDateFormat.format(new Date()));
        orderAfterSales.setAfterSalesCause(RefundOnlyEnum.four.getCode());
        orderAfterSales.setHospitalId(busOrderFirst.getHospitalId());
        // 推送售后单
        this.splitOrder(dto.getOrderNo(), orderAfterSales);
    }

    /**
     * 根据订单信息回滚库存
     * @param orderList - 订单列表
     */
    private void releaseStockByOrderList(List<BusOrder> orderList) {
        for (BusOrder busOrder : orderList) {
            if (CodeEnum.NO.getCode().equals(busOrder.getSubOrderType())) {
                BusDrugsOrder drugsOrderById = this.getDrugsOrdeById(busOrder.getSubOrderId());
                BusStockDrugs busStockDrugs = OrikaUtils.convert(drugsOrderById, BusStockDrugs.class);
                //更新药品订单的库存
                this.updateReleaseStock(busStockDrugs);
            } else {
                /*更新库存信息*/
                releaseShopOrderStock(busOrder);
            }
        }
    }

    /**
     * 更新订单状态为已经取消
     * @param dto - 订单信息
     * @return 更新数量
     */
    private int setOrderToCancel(BusHospitalOrderDTO dto) {
        int update;
        BusOrder order = new BusOrder();
        order.setOrderStatus(DrugsOrderEnum.CANCELLED.getCode());
        order.setCancelTime(DateUtils.getNowDate());
        order.setCancelResult(dto.getCancelResult());
        order.setInvoiceStatus(InvoiceStatusEnum.CAN_NOT_ISSUE.getStatus());
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getOrderNo, dto.getOrderNo());
        update = busOrderMapper.update(order, lambdaQuery);
        return update;
    }

    /**
     * 更新商品库存信息
     * @param busOrder - 订单信息
     */
    private void releaseShopOrderStock(BusOrder busOrder) {
        List<BusOrderShop> busOrderShops = this.listBusOrderShopByOrderId(busOrder.getSubOrderId());
        log.info("库存更新busOrderShops ={}",busOrderShops);
        for(BusOrderShop busOrderShop :busOrderShops){
            BusShopGoods busShopGoods = this.getBusShopGoodsById(busOrderShop.getShopId());
            Integer stock = busShopGoods.getStock();
            busShopGoods.setStock(stock + busOrderShop.getQuantity());
            log.info("更新商品库存入参={}", busShopGoods);
            R objectR = remoteShopGoodsService.updateStock(busShopGoods);
            if (Constants.SUCCESS.equals(objectR.getCode())) {
                log.info("取消订单，库存更新成功");
                // 取消云仓订单
                long shopOrderId = busOrder.getSubOrderId();
                LambdaQueryWrapper<BusEnterpriseProductOrder> lambdaEnterQuery = Wrappers.lambdaQuery();
                lambdaEnterQuery.eq(BusEnterpriseProductOrder::getSubOrderId,shopOrderId);
                List<BusEnterpriseProductOrder> busEnterpriseProductOrderList = busEnterpriseProductOrderMapper.selectList(lambdaEnterQuery);
                for(BusEnterpriseProductOrder busEnterpriseProductOrder : busEnterpriseProductOrderList){
                    busEnterpriseProductOrder.setStatus("3");
                    busEnterpriseProductOrderMapper.updateById(busEnterpriseProductOrder);
                }
            }else{
                log.error("取消订单，库存更新失败busOrderShop={}",busOrderShop);
                throw new ServiceException("取消订单，库存更新失败");
            }
        }
    }

    /**
     * 微信医保订单后台取消-发起退款
     * @param orderNo - 订单编号
     * @param order - 订单信息
     */
    private void wxInsuranceOrderCancelToRefund(String orderNo, BusOrder order) {
        //医保订单退款流程
        // 查询是否是微信医保支付
        R<MiPayResult> result = remotePaymentService.queryPayResult(orderNo);
        if (result == null || Constants.SUCCESS != result.getCode() || result.getData() == null) {
            throw new ServiceException(String.format("医保订单支付结果查询失败, 结果：%s", result));
        }
        // 查询售后订单
        List<BusOrderAfterSales> busOrderAfterSales = busOrderAfterSalesMapper.selectListByOrderNo(orderNo);
        // 如果有售后订单处于退款中或者已经退款的状态，则不再发起退款
        if (!CollectionUtils.isEmpty(busOrderAfterSales)) {
            // 查找是否含有退款中和售后结束的状态的订单
            boolean hasRefundingOrAfterSaleEndOrder = busOrderAfterSales.stream().anyMatch(s -> OrderAfterSalesStatusEnum.UNDER_REFUND.getCode().equals(s.getAfterSalesStatus()) ||
                    OrderAfterSalesStatusEnum.AFTER_SALE.getCode().equals(s.getAfterSalesStatus()));
            if (hasRefundingOrAfterSaleEndOrder) {
                throw new ServiceException("医保售后订单有处于退款或退款结束的订单，无法取消");
            }
        }
        // 微信医保订单退款
        refundFactory.processRefund(order, null,null);
        // 批量流转售后订单状态
        R<Boolean> r = remoteWxPayService.updateBatchAfterSalesOrderStatus(orderNo);
        if (Objects.isNull(r) || !Constants.SUCCESS.equals(r.getCode())) {
            log.error("批量流转售后订单状态失败 r={}", r);
        }
        log.info("调用app服务修改售后订单状态={}", r.getCode());

    }


    private void verifyOrder(List<BusOrder> orderList, List<BusOrderAfterSales> orderAfterSalesList) {
        // 定义订单可申请售后数
        int afterNumber = 0;
        for (BusOrder order : orderList) {
            if (CodeEnum.NO.getCode().equals(order.getSubOrderType())) {
                afterNumber++;
            } else {
                List<BusOrderShop> orderShopList = busOrderShopService.selectOrderGoodsList(order.getSubOrderId());
                afterNumber = orderShopList.stream().mapToInt(BusOrderShop::getQuantity).sum();
            }
        }
        int total = 0 ;

        for ( BusOrderAfterSales orderAfterSales : orderAfterSalesList ) {
            // 表示 处方药品
            if ( null==orderAfterSales.getQuantity() ) {
                total += 1 ;
            }else {
                // 表示 非处方药 或者 商品
                total += orderAfterSales.getQuantity() ;
            }
        }

        if (afterNumber == total) {
            throw new ServiceException("当前订单已全部申请售后，无法取消");
        }
    }

    @Override
    public List<BusOrderShop> listBusOrderShopByOrderId(Long orderId) {
        R<List<com.puree.hospital.order.api.model.BusOrderShop>> listR = remoteBusOrderShopService.listBusOrderShopByOrderId(orderId);
        if (!Constants.SUCCESS.equals(listR.getCode())) {
            throw new ServiceException(listR.getMsg());
        }
        List<com.puree.hospital.order.api.model.BusOrderShop> data = listR.getData();
        return OrikaUtils.converts(data, BusOrderShop.class);
    }

    @Override
    public BusShopGoods getBusShopGoodsById(Long id) {
        R<BusShopGoods> detail = remoteShopGoodsService.getDetail(id);
        if (!Constants.SUCCESS.equals(detail.getCode())) {
            throw new ServiceException(detail.getMsg());
        }
        return detail.getData() ;
    }

    /**
     * 回滚药品订单库存
     * @param busStockDrugs - 药品订单库存信息
     */
    @Override
    public void updateReleaseStock(BusStockDrugs busStockDrugs) {
        R objectR = remoteStockService.updateReleaseStock(busStockDrugs);
        if (!Constants.SUCCESS.equals(objectR.getCode())) {
            throw new ServiceException(objectR.getMsg());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOrderAmount(BusHospitalOrderDTO dto) {
        log.info("busHospitalOrderDto ={}", dto);
        List<BusOrder> orderList = getOrderList(dto.getOrderNo());
        int update = 0;
        if (!orderList.isEmpty()) {
            BusOrder order = orderList.get(0);
            // 待支付可以修改运费
            if (DrugsOrderEnum.PENDING_PAYMENT.getCode().equals(order.getOrderStatus())) {
                BusOrder busOrder = new BusOrder();
                // 普通运费
                busOrder.setFreight(dto.getFreight().toString());
                BigDecimal orderAmount = BigDecimal.valueOf(order.getRelPrice());
                BigDecimal orderFreight = new BigDecimal(order.getFreight());
                BigDecimal relPrice;
                // 修改运费
                BigDecimal subtract = orderAmount.subtract(orderFreight);
                relPrice = subtract.add(dto.getFreight());
                busOrder.setRelPrice(relPrice.doubleValue());
                busOrder.setChangePrice(YesNoEnum.YES.getCode());
                LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(BusOrder::getOrderNo, dto.getOrderNo());
                update = busOrderMapper.update(busOrder, lambdaQuery);
            } else {
                throw new ServiceException("只有待支付状态下可修改运费");
            }
        }
        return update;
    }

    /**
     * 统计待发货订单数量
     *
     * @param order
     * @return
     */
    @Override
    public int countNewOrder(BusOrder order) {
        return busOrderMapper.countNewOrder(order);
    }

    /**
     * 修改华润订单状态为已复核
     *
     * @param orderNo 子订单编号
     * @return 修改行数
     */
    @Override
    public int updateOrderStatus(String orderNo) {
        // 修改华润订单状态
        LambdaQueryWrapper<BusDrugsOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusDrugsOrder::getOrderNo, orderNo);
        BusDrugsOrder drugsOrder = new BusDrugsOrder();
        drugsOrder.setHrStatus(HrOrderStatusEnum.HR_30.getCode());
        return drugsOrderMapper.update(drugsOrder, lambdaQuery);
    }

    /**
     * 校验华润订单是否可以退款
     *
     * @param orderNo 子订单编号
     * @return true-可以退款，false-不可以退款
     */
    @Override
    public boolean checkHrOrderStatus(String orderNo) {
        Integer hrStatus = busOrderMapper.checkHrOrderStatus(orderNo);
        return !HrOrderStatusEnum.HR_30.getCode().equals(hrStatus);
    }

    /**
     * 订单拆单推送售后单到配送企业
     * @param orderNo - 订单号
     * @param orderAfterSales - 售后单信息
     */
    @Override
    public void splitOrder(String orderNo, BusOrderAfterSales orderAfterSales) {
        List<BusDrugsOrder> drugsOrders = busOrderMapper.selectOrderList(orderNo);
        if (CollUtil.isNotEmpty(drugsOrders)) {
            // 处方单
            List<BusDrugsOrder> pdList = new ArrayList<>();
            // 非处方单
            List<BusDrugsOrder> otcList = new ArrayList<>();
            for (BusDrugsOrder drugsOrder : drugsOrders) {
                if (Objects.isNull(drugsOrder.getPrescriptionId())) {
                    otcList.add(drugsOrder);
                } else {
                    pdList.add(drugsOrder);
                }
            }
            if (CollUtil.isNotEmpty(pdList)) {
                for (BusDrugsOrder order : pdList) {
                    // 查询处方药品信息
                    LambdaQueryWrapper<BusPrescriptionDrugs> lambdaQuery = Wrappers.lambdaQuery();
                    lambdaQuery
                            .eq(BusPrescriptionDrugs::getPrescriptionId, order.getPrescriptionId());
                    List<BusPrescriptionDrugs> drugsList = prescriptionDrugsMapper.selectList(lambdaQuery);
                    // 退单到配送企业
                    pushOrderToEnterprise(orderAfterSales, drugsList, order.getOrderNo());

                    pushOrderToYF(order.getOrderNo(), orderAfterSales.getHospitalId());

                }
            }
            if (CollUtil.isNotEmpty(otcList)) {
                for (BusDrugsOrder order : otcList) {
                    // 查询非处方药品信息
                    LambdaQueryWrapper<BusOtcDrugs> lambdaQuery = Wrappers.lambdaQuery();
                    lambdaQuery
                            .eq(BusOtcDrugs::getDrugsOrderId, order.getId());
                    List<BusOtcDrugs> otcDrugs = otcDrugsMapper.selectList(lambdaQuery);
                    List<BusPrescriptionDrugs> drugsList = OrikaUtils.converts(otcDrugs, BusPrescriptionDrugs.class);
                    // 退单到配送企业
                    pushOrderToEnterprise(orderAfterSales, drugsList, order.getOrderNo());
                }
            }
        }
    }

    /**
     * 查询发货商品/药品信息
     *
     * @param orderNo - 订单号
     * @return - 发货商品/药品信息
     */
    @Override
    public OrderVO selectDeliveryList(String orderNo) {
        OrderVO orderVo = new OrderVO();
        // 查询订单信息
        List<BusOrder> orderList = this.getOrderList(orderNo);
        List<GoodsVO> goodsList = new ArrayList<>();
        for (int i = 0; i < orderList.size(); i++) {
            BusOrder busOrder = orderList.get(i);
            orderVo.setReceivingUser(busOrder.getReceiver());
            orderVo.setReceivingAddress(busOrder.getReceiveAddress());
            // 药品订单
            if (CodeEnum.NO.getCode().equals(busOrder.getSubOrderType())) {
                R<com.puree.hospital.order.api.model.BusDrugsOrder> r1 = remoteBusDrugsOrderService.selectDrugsOrderById(busOrder.getSubOrderId());
                if (Constants.SUCCESS == r1.getCode()) {
                    BusDrugsOrder drugsOrder = JSON.parseObject(JSON.toJSONString(r1.getData()), BusDrugsOrder.class);
                    drugsOrder.setDelivery(YesNoEnum.YES.getCode());
                    handleDrugsOrderInfo(drugsOrder, orderVo, goodsList, busOrder);
                } else {
                    log.error("远程调用查询药品订单失败={}", r1.getMsg());
                }
            } else { // 商品订单
                R<BusShopOrder> r1 = remoteBusShopOrderService.selectShopOrderById(busOrder.getSubOrderId());
                if (Constants.SUCCESS == r1.getCode()) {
                    BusShopOrder shopOrder = r1.getData();
                    shopOrder.setDelivery(YesNoEnum.YES.getCode());
                    handleGoodsOrderInfo(shopOrder, goodsList, orderList.get(0).getOrderStatus());
                } else {
                    log.error("远程调用查询商品订单失败={}", r1.getMsg());
                }
            }
        }
        orderVo.setGoodsList(goodsList);
        return orderVo;
    }

    /**
     * order导出为excel
     * @param dto
     * @param outputStream
     */
    @Override
    public void exportOrder(BusSubOrderDTO dto, OutputStream outputStream) {
        // 不应该一次性全部查询出来后，然后还做数据的再一次copy，方案应该是查出来一部分后直接写入表格
        //先查询有多少条数据
        // TODO - 同时为了响应速度快，可以考虑需要适当在数据库对统计的字段加索引
        Integer count = remoteBusOrderService.countExportOrder(dto).getData();
        if (count <= 0) {
            throw new ServiceException("暂无可导出的数据");
        }
        int pageSize = 5000;
        ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();

        for (int i = 0; i < (count / pageSize) + 1; i++) {
            int finalI = i;
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                if (YesNoEnum.NO.getCode().equals(dto.getExportType())) {
                    //计算每一页有多少
                    int pageSizeNumber = pageSize;
                    if (finalI * pageSizeNumber > count) {
                        pageSizeNumber = finalI * pageSizeNumber - count;
                    }
                    //分片查找
                    R<List<OrderExportVO>> listR = remoteBusOrderService.listExportOrder(dto, finalI,pageSizeNumber);
                    if (!Constants.SUCCESS.equals(listR.getCode())){
                        throw new ServiceException("远程调用交易订单导出信息查询失败");
                    }
                    List<OrderExportVO> exportList = listR.getData();
                    if (!CollectionUtils.isEmpty(exportList)) {
                        WriteSheet writeSheet = EasyExcel.
                                writerSheet(finalI, "订单信息" + (finalI + 1)).head(OrderExportVO.class)
                                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
                        synchronized (excelWriter) {
                            excelWriter.write(exportList, writeSheet);
                        }
                    }
                } else {
                    //计算每一页有多少
                    int pageSizeNumber = pageSize;
                    if (finalI * pageSizeNumber > count) {
                        pageSizeNumber = finalI * pageSizeNumber - count;
                    }
                    R<List<OrderShopExportVO>> listR= remoteBusOrderService.listExportOrderShop(dto,finalI,pageSizeNumber);
                    if (!Constants.SUCCESS.equals(listR.getCode())){
                        throw new ServiceException("远程调用交易订单商品导出信息查询失败");
                    }
                    List<OrderShopExportVO> exportList = listR.getData();
                    if (!CollectionUtils.isEmpty(exportList)) {
                        WriteSheet writeSheet = EasyExcel.
                                writerSheet(finalI, "商品信息" + (finalI + 1)).head(OrderShopExportVO.class)
                                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
                        synchronized (excelWriter) {
                            excelWriter.write(exportList, writeSheet);
                        }
                    }
                }
            }, threadPoolExecutor);
            completableFutures.add(completableFuture);
        }

        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
        //刷新流
        excelWriter.finish();
    }

    private void pushOrderToEnterprise(BusOrderAfterSales orderAfterSales, List<BusPrescriptionDrugs> drugsList, String orderNo) {
        String refundType = orderAfterSales.getRefundType();
        // 根据配送企业ID分组
        Map<Long, List<BusPrescriptionDrugs>> pdMap =
                drugsList.stream().collect(Collectors.groupingBy(e -> Optional.ofNullable(e.getEnterpriseId()).orElse(0L)));
        for (Long enterpriseId : pdMap.keySet()) {
            // 查询配送企业信息
            BusEnterprise enterprise = enterpriseMapper.selectById(enterpriseId);
            // 华润
            if (Objects.nonNull(enterprise) && EnterpriseEnum.HR.getInfo().equals(enterprise.getIdentifying())) {
                Integer hrOrderStatus = busOrderMapper.checkHrOrderStatus(orderNo);
                if (Objects.isNull(hrOrderStatus)) {
                    List<BusPrescriptionDrugs> drugs = pdMap.get(enterpriseId);
                    List<Long> drugsIds = drugs.stream().map(BusPrescriptionDrugs::getDrugsId).collect(Collectors.toList());
                    // 封装华润退款/退货接口参数
                    HrRefundOrder hrRefundOrder = new HrRefundOrder();
                    hrRefundOrder.setOrderId(orderNo);
                    hrRefundOrder.setRefundNo(orderAfterSales.getAfterSalesNumber());
                    String refundDate = DateUtils.parse_YYYY_MM_DD_HH_MM_SS(new Date());
                    hrRefundOrder.setRefundDate(refundDate);
                    // 退货/退款原因
                    String reason;
                    // 是否退货
                    boolean isRetunGoods;
                    // 是否退款
                    boolean isRefund;
                    if (AfterSaleRefundTypeEnum.REFUND_ONLY.getCode().equals(refundType)) {
                        reason = RefundOnlyEnum.getTypeByCode(orderAfterSales.getAfterSalesCause()).getInfo();
                        isRetunGoods = false;
                        isRefund = true;
                    } else if (AfterSaleRefundTypeEnum.REFUND_RETURN.getCode().equals(refundType)) {
                        reason = RefundReturnEnum.getTypeByCode(orderAfterSales.getAfterSalesCause()).getInfo();
                        isRetunGoods = true;
                        isRefund = true;
                    } else {
                        reason = RefundsExchangesEnum.getTypeByCode(orderAfterSales.getAfterSalesCause()).getInfo();
                        isRetunGoods = true;
                        isRefund = false;
                    }
                    hrRefundOrder.setRefundReason(reason);
                    hrRefundOrder.setIsRetunGoods(isRetunGoods);
                    hrRefundOrder.setIsRefund(isRefund);
                    // 查询配送企业药品信息
                    HospitalOfficinaDrugsDTO query = new HospitalOfficinaDrugsDTO();
                    query.setHospitalId(orderAfterSales.getHospitalId());
                    query.setDrugsIds(drugsIds);
                    query.setEnterpriseId(enterpriseId);
                    List<BusEnterpriseDrugsPrice> enterpriseDrugs = enterpriseDrugsMapper.selectEnterpriseDrugsListV2(query);

                    List<HrRefundDetails> details = new ArrayList<>();
                    // 退款金额
                    double refundAmount = 0d;
                    for (BusEnterpriseDrugsPrice enterpriseDrug : enterpriseDrugs) {
                        for (BusPrescriptionDrugs drug : drugs) {
                            if (drug.getDrugsId().equals(enterpriseDrug.getDrugsId())) {
                                HrRefundDetails hrDetails = new HrRefundDetails();
                                hrDetails.setProductId(enterpriseDrug.getEnterpriseDrugsId());
                                hrDetails.setOriQuantity(drug.getQuantity());
                                if (Objects.isNull(orderAfterSales.getQuantity())) {
                                    hrDetails.setRefundQuantity(drug.getQuantity());
                                } else {
                                    hrDetails.setRefundQuantity(orderAfterSales.getQuantity());
                                }
                                hrDetails.setPrice(enterpriseDrug.getReferencePurchasePrice().doubleValue());
                                hrDetails.setUnit(drug.getUnit());
                                // 计算原销售金额
                                Double oriAmount = enterpriseDrug.getReferencePurchasePrice().multiply(BigDecimal.valueOf(drug.getQuantity())).doubleValue();
                                hrDetails.setOriAmount(oriAmount);
                                // 计算单个药品退款金额
                                BigDecimal amount = getDrugPrice(orderAfterSales, enterpriseDrug, drug);
                                hrDetails.setRefundAmount(amount.doubleValue());
                                details.add(hrDetails);
                                refundAmount = BigDecimal.valueOf(refundAmount).add(amount).doubleValue();
                            }
                        }
                    }
                    hrRefundOrder.setRefundAmount(refundAmount);
                    hrRefundOrder.setDetails(details);
                    // 查询医院与华润配置信息
                    Map<String, String> entConfig = hospitalEnterpriseService.getByHosIdAndEntCode(orderAfterSales.getHospitalId(), EnterpriseEnum.HR.getInfo());
                    hrRefundOrder.setUserName(entConfig.get("userName"));
                    hrRefundOrder.setPassword(entConfig.get("password"));
                    log.info("调用华润退款/退货接口入参={}", JSON.toJSONString(hrRefundOrder));
                    R<String> refund = remoteHRService.refund(hrRefundOrder);
                    if (Constants.FAIL == refund.getCode()) {
                        log.error("服务调用华润退款/退货接口失败={}", refund.getMsg());
                        throw new ServiceException("推送华润退款单失败");
                    }
                }
            }
        }
    }

    /**
     * 计算单个药品价格
     * @param orderAfterSales - 售后单
     * @param enterpriseDrug - 配送企业药品信息
     * @param drug - 处方药品信息
     * @return - 单个药品价格
     */
    private @NotNull BigDecimal getDrugPrice(BusOrderAfterSales orderAfterSales, BusEnterpriseDrugsPrice enterpriseDrug, BusPrescriptionDrugs drug) {
        Integer quantity;
        if (Objects.isNull(orderAfterSales.getQuantity()) || Objects.nonNull(drug.getPrescriptionId())) {
            quantity = drug.getQuantity();
        } else {
            quantity = orderAfterSales.getQuantity();
        }
        return enterpriseDrug.getReferencePurchasePrice().multiply(BigDecimal.valueOf(quantity));
    }


    /**
     * 中药处方---一方取消订单
     * */
    @Override
    public void pushOrderToYF(String subOrderNo, Long hospitalId) {
        BusEnterpriseOrder enterpriseOrder = enterpriseOrderMapper.selectOne(new LambdaQueryWrapper<BusEnterpriseOrder>()
                .eq(BusEnterpriseOrder::getOrderNo, subOrderNo).isNotNull(BusEnterpriseOrder::getThirdOrderNo) );
        if (null!=enterpriseOrder && CharSequenceUtil.isNotBlank(enterpriseOrder.getThirdOrderNo())) {
            JSONObject orderInfoJson = yfDeliverUtil.getOrderInfo(enterpriseOrder.getThirdOrderNo(), hospitalId);
            String yfOrderStatus = orderInfoJson.getJSONObject("data").getJSONObject("details").getString("orderStatus") ;
            if ( !Arrays.asList(
                    YfOrderEnum.DRAFT.getStatus(), YfOrderEnum.ORDERED.getStatus(), YfOrderEnum.COPIED.getStatus(), YfOrderEnum.COPY_REJECT.getStatus(),
                            YfOrderEnum.AUDITED.getStatus(), YfOrderEnum.AUDIT_REJECT.getStatus() ).contains(yfOrderStatus) ) {
                throw new ServiceException("不可取消配送企业订单, 状态为 : " + yfOrderStatus) ;
            }
            yfDeliverUtil.cancelOrder(enterpriseOrder.getThirdOrderNo(), hospitalId);
            enterpriseOrder.setAction(ActionConstant.CANCEL_ORDER);
            enterpriseOrderMapper.updateById(enterpriseOrder) ;
        }

    }

    /**
     * 查询已经发货的医保订单 - 已经过滤条件
     * @param query  查询条件
     * @return 订单列表
     */
    @Override
    public List<BusOrderDrugTraceCodeVO> selectDeliveredInsuranceDrugs(BusOrderDrugTraceCodeQueryDTO query) {
        return busOrderMapper.selectDeliveredInsuranceDrugs(query);
    }

}
