package com.puree.hospital.business.service.refund.impl;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.api.RemoteTongLianPayService;
import com.puree.hospital.app.api.model.BusRefundPayDTO;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.service.refund.IRefundService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.PayWayConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/***
 *
 *@title TongLianRefundServiceImpl
 *@description
 *<AUTHOR>
 *@version 1.0
 *@create 2024/12/16 11:58
 */
@Slf4j
@Service(PayWayConstant.TONGLIAN_PAY + IRefundService.REFUND_SERVICE)
public class TongLianRefundServiceImpl implements IRefundService {
    @Resource
    private RemoteTongLianPayService remoteTongLianPayService;
    @Override
    public void refund(BusOrder busOrder, BigDecimal refundAmount, OrderTypeEnum orderType) {
        BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
        busRefundPayDTO.setOrderNo(busOrder.getOrderNo());
        busRefundPayDTO.setPayWay(busOrder.getPayWay());
        busRefundPayDTO.setRefundAmount(refundAmount.doubleValue());
        busRefundPayDTO.setHospitalId(busOrder.getHospitalId());
        busRefundPayDTO.setMchType("2");
        busRefundPayDTO.setOrderType(orderType.getCode());
        R<JSONObject> tongLianRefund = remoteTongLianPayService.tongLianRefund(busRefundPayDTO);
        if (Constants.FAIL.equals(tongLianRefund.getCode())) {
            log.error("通联支付退款失败 refundOrder={}",tongLianRefund);
            throw new ServiceException("调用通联支付退款失败");
        }
        log.info("通联支付退款成功 refundOrderNo={}",busOrder.getOrderNo());
    }
}
