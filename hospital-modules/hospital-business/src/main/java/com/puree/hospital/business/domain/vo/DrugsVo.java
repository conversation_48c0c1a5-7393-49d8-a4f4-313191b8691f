package com.puree.hospital.business.domain.vo;

import lombok.Data;

@Data
public class DrugsVo {
    /**
     * 配送企业药品ID
     */
    private String enterpriseDrugsId;
    /**
     * 药品单价
     */
    private String sellingPrice;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 药品名称
     */
    private String drugsName;
    /**
     * 重量
     */
    private String weight;
    /**
     * 用药频次
     */
    private String medicationFrequency;
    /**
     * 用药频次 中文显示
     */
    private String medicationFrequencyRemarks;
    /**
     * 单次剂量
     */
    private Integer singleDose;
    /**
     * 单位
     */
    private String unit;
    /**
     * 药品类型
     */
    private String type;

    private String drugsUsageValue;

    /**
     * 详细规格
     */
    private String drugsSpecification;
    /**
     * 中药类型
     */
    private String tcmType;
    /**
     * 中药方剂类型 0-协定方  1-经典方
     */
    private String zyfjType;

    /**
     *  用药天数
     */
    private Integer medicationDays ;

    private String standardCommonName;
}
