package com.puree.hospital.business.service.refund.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.business.domain.BusOrder;
import com.puree.hospital.business.service.refund.IRefundService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.PayWayConstant;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.insurance.api.RemotePaymentService;
import com.puree.hospital.insurance.api.RemotePreprocessorService;
import com.puree.hospital.insurance.api.model.MiPayResult;
import com.puree.hospital.insurance.api.model.UniformRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/***
 *@title WeChatInsuranceRefundServiceImpl
 *@description
 *<AUTHOR>
 *@version 1.0
 *@create 2024/12/16 12:03
 */
@Slf4j
@Service(PayWayConstant.WECHAT_INSURANCE_PAY + IRefundService.REFUND_SERVICE)
public class WeChatInsuranceRefundServiceImpl implements IRefundService {
    @Resource
    private RemotePreprocessorService remotePreprocessorService;
    @Resource
    private RemotePaymentService remotePaymentService;
    @Override
    public void refund(BusOrder busOrder, BigDecimal refundAmount, OrderTypeEnum orderTypeEnum) {
        R<MiPayResult> result = remotePaymentService.queryPayResult(busOrder.getOrderNo());
        if (Constants.SUCCESS != result.getCode()) {
            throw new ServiceException(result.getMsg());
        }
        if (ObjectUtil.isNull(result.getData())) {
            throw new ServiceException("医保订单退款失败，未查询到该医保订单支付信息");
        }
        // 医保退费
        UniformRequest request = new UniformRequest();
        request.setHospitalId(busOrder.getHospitalId());
        request.setReqKey("6203");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("uldLatlnt", "0,0");
        jsonObject.put("totalOrderNo", busOrder.getOrderNo());
        request.setParams(jsonObject);
        AjaxResult ajaxResult = remotePreprocessorService.uniformRequest(request);
        if (Objects.isNull(ajaxResult) || !ajaxResult.isSuccess()) {
            log.error("医保退款失败 ajaxResult={}", JSON.toJSON(ajaxResult));
            throw new ServiceException(String.format("医保退款失败, 原因：%s", ajaxResult.getMsg()));
        }
        log.info("医保退款成功");
    }
}
