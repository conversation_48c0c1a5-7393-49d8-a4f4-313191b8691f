package com.puree.hospital.business.controller;

import com.puree.hospital.business.api.model.dto.BusHospitalOrderDTO;
import com.puree.hospital.business.api.model.event.BusDrugTraceCodeEvent;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.dto.DeliveryInfoDTO;
import com.puree.hospital.business.domain.vo.OrderSendOutResultVO;
import com.puree.hospital.business.queue.producer.DrugTraceCodeUploadProducer;
import com.puree.hospital.business.service.IBusDrugOrderPackageService;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2023/03/06/12:19
 * @Description:
 */
@RequestMapping("orderPackage")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDrugsOrderPackageController {

    private final IBusDrugOrderPackageService iBusDrugOrderPackageService;

    @Lazy
    @Resource
    private DrugTraceCodeUploadProducer drugTraceCodeUploadProducer;

    /**
     * 发货
     * @param list
     * @return
     */
    @PutMapping("/insertSendOutGoods")
    @PreAuthorize(hasPermi = "business:orderPackage:SendOutGoods")
    @Log(title = "订单发货", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public AjaxResult updateSendOutGoods(@RequestBody List<BusHospitalOrderDTO> list) {
        OrderSendOutResultVO resultVO = iBusDrugOrderPackageService.insertSendOutGoods(list);
        //如果标记了需要上传追溯码
        if (Objects.nonNull(resultVO) && Boolean.TRUE.equals(resultVO.getNeedUploadTraceCode())) {
            drugTraceCodeUploadProducer.send(new BusDrugTraceCodeEvent(resultVO.getHospitalId(), resultVO.getTotalOrderNo(), BusDrugTraceCodeEvent.EventType.UPLOAD));
        }
        return AjaxResult.success(resultVO.getPharmacist());
    }

    /**
     * 远程调用发货接口
     * @param list 商品信息
     * @return 发货药品信息
     */
    @PostMapping("feign/insertSendOutGoods")
    public AjaxResult feignInsertSendOutGoods(@RequestBody List<BusHospitalOrderDTO> list) {
        OrderSendOutResultVO resultVO = iBusDrugOrderPackageService.insertSendOutGoods(list);
        return AjaxResult.success(resultVO);
    }

    /**
     * 更新物流编号
     * @param list
     * @return
     */
    @PutMapping("/updateDeliveryNo")
    @PreAuthorize(hasPermi = "business:orderPackage:updateDeliveryNo")
    @Log(title = "更新订单物流编号", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    public AjaxResult updateDeliveryNo(@RequestBody List<DeliveryInfoDTO> list) {
        return AjaxResult.success(iBusDrugOrderPackageService.updateDeliveryNo(list));
    }

    /**
     * 查询物流信息
     * @param orderNo
     * @return
     */
    @GetMapping("/getDeliveryInfo")
    @PreAuthorize(hasPermi = "business:orderPackage:getDeliveryInfo")
    @Log(title = "查询订单物流信息", businessType = BusinessType.QUERY,operatorType = OperatorType.MANAGE)
    public AjaxResult getDeliveryInfo(@RequestParam("orderNo")String orderNo) {
        return AjaxResult.success(iBusDrugOrderPackageService.getDeliveryInfo(orderNo));
    }

    /**
     * 查询订单包裹列表
     * @param orderNo
     * @param barterOrNot
     * @return
     */
    @GetMapping("/feign/list")
    @Log(title = "查询订单包裹列表", businessType = BusinessType.QUERY)
    public R<List<BusDrugOrderPackage>> getOrderPackageList(@RequestParam("orderNo") String orderNo,
                                                            @RequestParam(name = "barterOrNot",required = false) Integer barterOrNot) {
        return R.ok(iBusDrugOrderPackageService.selectPackageList(orderNo, barterOrNot));
    }

    /**
     * 查询订单包裹信息
     * @param orderId
     * @param packageType
     * @return
     */
    @GetMapping("/feign/query-by-order-id")
    @Log(title = "查询订单包裹列表", businessType = BusinessType.QUERY)
    public R<List<BusDrugOrderPackage>> queryByOrderId(@RequestParam("orderId") Long orderId,
                                                       @RequestParam("packageType") Integer packageType) {
        return R.ok(iBusDrugOrderPackageService.queryByOrderId(orderId, packageType));
    }

}
