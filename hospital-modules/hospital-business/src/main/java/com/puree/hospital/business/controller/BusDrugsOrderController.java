package com.puree.hospital.business.controller;

import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.dto.BusAfterSaleDto;
import com.puree.hospital.business.domain.dto.BusDrugsOrderDto;
import com.puree.hospital.business.domain.dto.DeliveryDTO;
import com.puree.hospital.business.domain.vo.BusDrugsOrderDetailVo;
import com.puree.hospital.business.domain.vo.BusDrugsOrderVo;
import com.puree.hospital.business.service.IBusAfterSaleService;
import com.puree.hospital.business.service.IBusDrugsOrderService;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("drugsOrder")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDrugsOrderController extends BaseController {
    private final IBusDrugsOrderService busDrugsOrderService;
    private final IBusAfterSaleService busAfterSaleService;

    /**
     * 查询订单列表
     *
     * @return
     */
    @PreAuthorize(hasPermi = "business:drugsOrder:list")
    @GetMapping("list")
    @Log(title = "药品订单", businessType = BusinessType.QUERY)
    public TableDataInfo list(BusDrugsOrderDto dto) {
        startPage();
        List<BusDrugsOrderVo> list = busDrugsOrderService.selectListBySearch(dto);
        return getDataTable(list);
    }

    /**
     * 导出
     * @param response
     * @param dto
     * @return
     */
    @PostMapping("export")
    @PreAuthorize(hasPermi = "business:drugsOrder:export")
    @Log(title = "药品订单", businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)
    public void exportExcel(HttpServletResponse response,BusDrugsOrderDto dto) {
        busDrugsOrderService.exportExcel(response,dto);
    }

    /**
     * 查询订单详情
     *
     * @return
     */
    @PreAuthorize(hasPermi = "business:drugsOrder:detail")
    @GetMapping("detail")
    @Log(title = "药品订单", businessType = BusinessType.QUERY)
    public AjaxResult detail(@RequestParam("id") Long id,
                             @RequestParam("hospitalId") Long hospitalId) {
        BusDrugsOrderDto dto = new BusDrugsOrderDto();
        dto.setId(id);
        dto.setHospitalId(hospitalId);
        BusDrugsOrderDetailVo busDrugsOrderDetailVo = busDrugsOrderService.selectDetail(dto);
        return AjaxResult.success(busDrugsOrderDetailVo);
    }

    /**
     * 发货
     */
    @PreAuthorize(hasPermi = "business:drugsOrder:delivery")
    @Log(title = "药品订单", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PostMapping(value = "/delivery")
    public AjaxResult delivery(@Valid DeliveryDTO dto) {
        return AjaxResult.success(busDrugsOrderService.delivery(dto));
    }

    @PreAuthorize(hasPermi = "business:drugsOrder:return")
    @Log(title = "审核订单", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PutMapping("/return")
    public AjaxResult applyReturn(BusAfterSaleDto dto) {
        int i = busAfterSaleService.applyReturn(dto);
        return toAjax(i);
    }

    @PutMapping("/isreturn")
    @Log(title = "审核订单", businessType = BusinessType.UPDATE)
    public AjaxResult isReturn(BusAfterSaleDto dto) {
        boolean isOk = busAfterSaleService.isReturn(dto);
        Map<String,Object> retMap = new HashMap<>();
        retMap.put("isOk",isOk);
        if(!isOk){
            retMap.put("content","该药品订单大翔已煎煮，强制同意退款所造成的损失由院方承担，确认退款吗？");
        }else{
            retMap.put("content","配送企业方同意，正常退款。");
        }
        return AjaxResult.success(retMap);
    }

    @Log(title = "审核订单", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    @PostMapping("/regenerate/order")
    public AjaxResult regenerateOrder(@RequestBody BusDrugsOrder drugsOrder) {
        return toAjax(busDrugsOrderService.regenerateOrder(drugsOrder));
    }

    /**
     * 查询东方红退货/换货订单是否入库
     *
     * @param hospitalId
     * @return
     */
    @GetMapping("/get")
    @Log(title = "审核订单", businessType = BusinessType.UPDATE)
    public AjaxResult selectDfhStatus(@RequestParam("hospitalId") Long hospitalId) {
        // 查询药品订单状态为售后中
        BusDrugsOrderDto dto = new BusDrugsOrderDto();
        dto.setHospitalId(hospitalId);
        dto.setStatus(DrugsOrderEnum.AFTERSALES.getCode());
        List<String> orderNos = busDrugsOrderService.selectAfterSaleList(dto);
        logger.info("修改药品订单状态的编号：" + orderNos);
        if (orderNos.size() > 0) {
            // 修改订单状态
            busDrugsOrderService.updateOrderStatus(orderNos);
        }
        return AjaxResult.success();
    }
}
