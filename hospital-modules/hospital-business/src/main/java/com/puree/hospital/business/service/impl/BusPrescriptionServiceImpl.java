package com.puree.hospital.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.puree.hospital.app.api.RemotePrescriptionService;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusSignature;
import com.puree.hospital.business.domain.dto.BusPrescriptionDto;
import com.puree.hospital.business.domain.dto.BusPrescriptionRuleDTO;
import com.puree.hospital.business.domain.vo.BusPrescriptionDetailVo;
import com.puree.hospital.business.domain.vo.DiagnosisPatientInfoVo;
import com.puree.hospital.business.importDrugs.service.HycImportChinaDrugService;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.mapper.BusSignatureMapper;
import com.puree.hospital.business.service.IBusPrescriptionService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.PrescriptionTypeEnum;
import com.puree.hospital.common.core.enums.SexTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.BarcodeUtil;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPrescriptionServiceImpl implements IBusPrescriptionService {
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusSignatureMapper busSignatureMapper;
    private final RemotePrescriptionService remotePrescriptionService;

    @Value("${templatePath}")
    private String templatePath;


    @Override
    public List<BusPrescription> selectListBySearch(BusPrescriptionDto dto) {
        List<String> dateList = dto.getPrescriptionTime();
        if (CollectionUtil.isNotEmpty(dateList)) {
            dto.setStartTime(dateList.get(0) + " 00:00:00");
            dto.setEndTime(dateList.get(1) + " 23:59:59");
        }
        return busPrescriptionMapper.selectListBySearch(dto);
    }

    @Override
    public BusPrescriptionDetailVo selectDetail(BusPrescriptionDto dto) {
        BusPrescriptionDetailVo vo = busPrescriptionMapper.selectDetail(dto);
        this.handleSign(vo);
        vo.setBarcodeUrl(BarcodeUtil.generateBarcode(vo.getPrescriptionNumber()));
        return vo;
    }

    @Override
    public BusPrescriptionDetailVo selectPrescriptionDetail(BusPrescriptionDto dto) {
        BusPrescriptionDetailVo vo = busPrescriptionMapper.selectPrescriptionDetail(dto);
        String barcodeUrl = BarcodeUtil.generateBarcode(vo.getPrescriptionNumber());
        vo.setBarcodeUrl(barcodeUrl);
        this.handleSign(vo);
        if (null != vo.getPrescriptionDrugsList() && !vo.getPrescriptionDrugsList().isEmpty()) {
            if (HycImportChinaDrugService.PARTICLE_DRUG_CODE.equals(vo.getPrescriptionDrugsList().get(0).getDrugsDosageForm())) {
                vo.setShowBoilWay(YesNoEnum.NO.getCode());
            }
        }
        return vo;
    }

    @Override
    public BusPrescription selectPrescriptionDetailByUrl(BusPrescriptionDto dto) {
        BusPrescription busPrescription = busPrescriptionMapper.selectPrescriptionDetailByUrl(dto);
        // 生成pdf文件
        com.puree.hospital.app.api.model.BusPrescription prescription = OrikaUtils.convert(busPrescription, com.puree.hospital.app.api.model.BusPrescription.class);
        prescription.setPrintType(dto.getPrintType());
        R<String> r = remotePrescriptionService.generatePdf(prescription);
        if (!Constants.SUCCESS.equals(r.getCode())) {
            log.error("生成pdf设置发货药师签名失败");
        }
        String pdfPath = (String) r.getData();
        busPrescription.setPath(pdfPath);
        if (StringUtils.isEmpty(dto.getPrintType())) {
            // 更新处方签名路径
            BusPrescription prescription1 = new BusPrescription();
            prescription1.setId(busPrescription.getId());
            prescription1.setPath(pdfPath);
            busPrescriptionMapper.updateById(prescription1);
        }
        return busPrescription;
    }

    @Override
    public void importList(BusPrescriptionDto dto, HttpServletResponse response) throws IOException {
        List<BusPrescription> rxList = this.selectListBySearch(dto);
        for (BusPrescription prescription : rxList) {
            if (PrescriptionTypeEnum.isTcm(prescription.getPrescriptionType())) {
                prescription.setPrescriptionType("中药");
            } else if (PrescriptionTypeEnum.isMm(prescription.getPrescriptionType())){
                prescription.setPrescriptionType("西药");
            } else {
                if (null == prescription.getZyfjType()) {
                    prescription.setPrescriptionType("");
                } else {
                    if (prescription.getZyfjType().equals(1)) {
                        prescription.setPrescriptionType("经典方");
                    } else {
                        prescription.setPrescriptionType("协定方");
                    }
                }
            }
            if (SexTypeEnum.MAN.getCode().equals(prescription.getFamilySex())) {
                prescription.setFamilySex(SexTypeEnum.MAN.getInfo());
            } else {
                prescription.setFamilySex(SexTypeEnum.WOMAN.getInfo());
            }
            if ("0".equals(prescription.getStatus())) {
                prescription.setStatus("未审核");
            } else if ("1".equals(prescription.getStatus())) {
                prescription.setStatus("审核未通过");
            } else if ("2".equals(prescription.getStatus())) {
                prescription.setStatus("审核通过");
            } else if ("3".equals(prescription.getStatus())) {
                prescription.setStatus("作废");
            } else if ("4".equals(prescription.getStatus())) {
                prescription.setStatus("已使用");
            } else if ("5".equals(prescription.getStatus())) {
                prescription.setStatus("已失效");
            } else if ("6".equals(prescription.getStatus())) {
                prescription.setStatus("未签名");
            }
        }
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templatePath + "处方导出模板.xlsx").build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillVertical = FillConfig.builder().direction(WriteDirectionEnum.VERTICAL).build();
            excelWriter.fill(rxList, fillVertical, writeSheet);
        }
    }

    /**
     * 随访-处方诊断-过滤患者列表
     *
     * @param dto 诊断列表
     * @return 就诊人列表
     */
    @Override
    public List<DiagnosisPatientInfoVo> getPatientsByDiagnosisIds(BusPrescriptionRuleDTO dto) {
        List<DiagnosisPatientInfoVo> patients = new ArrayList<>();

        //1、只有时间范围的查询
        if (CollectionUtils.isEmpty(dto.getMmIds()) && CollectionUtils.isEmpty(dto.getTcmList())) {
            if ((dto.getQueryBeginDate() == null && dto.getQueryEndDate() == null)) {
                return patients;
            }
            patients = busPrescriptionMapper.getPatientsByDiagnosisIdsWithQueryDate(dto);
            return patients;
        }

        //2、满足全部
        if (dto.getIsAll()) {
            patients = busPrescriptionMapper.getPatientsByDiagnosisIdsWithAll(dto);
        } else {//3、满足任一
            patients = busPrescriptionMapper.getPatientsByDiagnosisIdsWithOne(dto);
        }

        return patients;
    }

    /**
     * 查询处方-根据id查询处方
     *
     * @param id
     *         处方id
     *
     * @return 处方
     */
    @Override
    public BusPrescription selectPrescriptionById(Long id) {
        return busPrescriptionMapper.selectById(id);
    }

    /**
     * 签名处理
     *
     * @param vo 处方详情
     */
    private void handleSign(BusPrescriptionDetailVo vo) {
        //通过一次查询，然后再根据objectId和objectType来组装数据
        List<Long> doctorIds = getDoctorIds(vo);
        if (CollectionUtil.isEmpty(doctorIds)) {
            return;
        }
        LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(BusSignature::getObjectId, doctorIds);
        lambdaQuery.in(BusSignature::getObjectType, Lists.newArrayList("0", "1", "6", "7"));
        //根据创建时间倒序
        lambdaQuery.orderByDesc(BusSignature::getCreateTime);
        List<BusSignature> busSignatures = busSignatureMapper.selectList(lambdaQuery);
        if (CollectionUtil.isEmpty(busSignatures)) {
            return;
        }
        Map<String, List<BusSignature>> singatureMap = busSignatures.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(s -> s.getObjectId() + ":" + s.getObjectType()));
        if (MapUtil.isEmpty(singatureMap)) {
            return;
        }
        //开方医生签名
        if (Objects.nonNull(vo.getDoctorId())) {
            List<BusSignature> signatureList = singatureMap.get(vo.getDoctorId() + ":0");
            if (CollectionUtil.isNotEmpty(signatureList)) {
                vo.setDoctorSignature(signatureList.get(0).getCertSignature());
            }
        }
        //审方医生签名
        if (Objects.nonNull(vo.getReviewPharmacistId())) {
            List<BusSignature> signatureList = singatureMap.get(vo.getReviewPharmacistId() + ":1");
            if (CollectionUtil.isNotEmpty(signatureList)) {
                vo.setReviewerSignature(signatureList.get(0).getCertSignature());
            }
        }
        //发货药师签名
        if (Objects.nonNull(vo.getDeliveryPharmacistId())) {
            List<BusSignature> signatureList = singatureMap.get(vo.getDeliveryPharmacistId() + ":6");
            if (CollectionUtil.isNotEmpty(signatureList)) {
                vo.setDeliverGoodsSignature(signatureList.get(0).getCertSignature());
            }
        }
        //调剂药师签名
        if (Objects.nonNull(vo.getDispensingPharmacistId())) {
            List<BusSignature> signatureList = singatureMap.get(vo.getDispensingPharmacistId() + ":7");
            if (CollectionUtil.isNotEmpty(signatureList)) {
                vo.setDispensingPharmacistSignature(signatureList.get(0).getCertSignature());
            }
        }
    }

    private List<Long> getDoctorIds(BusPrescriptionDetailVo vo) {
        List<Long> doctorIds = new ArrayList<>();
        if (Objects.nonNull(vo.getDoctorId()) && vo.getDoctorId() > 0) {
            doctorIds.add(vo.getDoctorId());
        }
        if (Objects.nonNull(vo.getReviewPharmacistId()) && vo.getReviewPharmacistId() > 0) {
            doctorIds.add(vo.getReviewPharmacistId());
        }
        if (Objects.nonNull(vo.getDeliveryPharmacistId()) && vo.getDeliveryPharmacistId() > 0) {
            doctorIds.add(vo.getDeliveryPharmacistId());
        }
        if (Objects.nonNull(vo.getDispensingPharmacistId()) && vo.getDispensingPharmacistId() > 0) {
            doctorIds.add(vo.getDispensingPharmacistId());
        }
        return doctorIds;
    }
}
