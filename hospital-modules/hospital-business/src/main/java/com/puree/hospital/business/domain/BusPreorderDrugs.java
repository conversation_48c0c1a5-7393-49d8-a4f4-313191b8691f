package com.puree.hospital.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * 预订单药品表
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
@Data
public class BusPreorderDrugs extends Entity {

    /**
     * 预订单ID
     */
    private Long preorderId;
    /**
     * 药品ID
     */
    private Long drugsId;
    /**
     * 药品制造商
     */
    private String drugsManufacturer;
    /**
     * 标准通用名
     */
    private String standardCommonName;
    /**
     * 药品名称
     */
    private String drugsName;
    /**
     * 药品图片地址
     */
    private String drugsImg;
    /**
     * 药品规格
     */
    private String drugsSpecification;
    /**
     * 销售价
     */
    private Double sellingPrice;
    /**
     * 用药频次
     */
    private String medicationFrequency;
    /**
     * 单次剂量
     */
    private Integer singleDose;
    /**
     * 单位
     */
    private String unit;
    /**
     * 给药途径
     */
    private String drugsUsageValue;
    /**
     * 重量（克）
     */
    private String weight;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 用药天数
     */
    private Integer medicationDays;
    /**
     * 配送企业ID
     */
    private Long enterpriseId;
    /**
     * 目录类型（1院内 2院外 3中药）
     */
    private Boolean directoryType;

    /**
     * 药品扭转id
     */
    @TableField(exist = false)
    private Long enterpriseDrugsId;
}
