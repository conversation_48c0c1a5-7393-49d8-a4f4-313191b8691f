package com.puree.hospital.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.puree.hospital.app.api.RemoteTongLianPayService;
import com.puree.hospital.app.api.RemoteWxPayService;
import com.puree.hospital.app.api.model.BusRefundPayDTO;
import com.puree.hospital.app.api.model.event.consultation.BaseConsultationEvent;
import com.puree.hospital.app.api.model.event.consultation.ConsultationRefundRefusedEvent;
import com.puree.hospital.app.api.model.event.order.DrugsOrderRefuseRefundExchangeEvent;
import com.puree.hospital.app.api.model.event.order.DrugsOrderRefuseRefundReturnEvent;
import com.puree.hospital.business.api.RemoteHospitalEnterpriseService;
import com.puree.hospital.business.api.model.BusHospitalEnterprise;
import com.puree.hospital.business.domain.BusAfterSale;
import com.puree.hospital.business.domain.BusConsultationOrder;
import com.puree.hospital.business.domain.BusDrugOrderPackage;
import com.puree.hospital.business.domain.BusDrugsOrder;
import com.puree.hospital.business.domain.BusPrescription;
import com.puree.hospital.business.domain.BusPrescriptionDrugs;
import com.puree.hospital.business.domain.dto.BusAfterSaleDto;
import com.puree.hospital.business.mapper.BusAfterSaleMapper;
import com.puree.hospital.business.mapper.BusConsultationOrderMapper;
import com.puree.hospital.business.mapper.BusDrugOrderPackageMapper;
import com.puree.hospital.business.mapper.BusDrugsOrderMapper;
import com.puree.hospital.business.mapper.BusPrescriptionDrugsMapper;
import com.puree.hospital.business.mapper.BusPrescriptionMapper;
import com.puree.hospital.business.service.IBusAfterSaleService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;
import com.puree.hospital.common.core.enums.EnterpriseEnum;
import com.puree.hospital.common.core.enums.OrderDrugsTypeEnum;
import com.puree.hospital.common.core.enums.OrderTypeEnum;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.supplier.api.RemoteDFHService;
import com.puree.hospital.supplier.api.RemoteDXService;
import com.puree.hospital.supplier.api.RemoteZXService;
import com.puree.hospital.supplier.api.model.dfh.DFHOrder;
import com.puree.hospital.supplier.api.model.dfh.OrderQueryDto;
import com.puree.hospital.supplier.api.model.zx.dto.CannelOrderDTO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusAfterSaleServiceImpl implements IBusAfterSaleService {
    private Logger logger = LoggerFactory.getLogger(BusAfterSaleServiceImpl.class);

    private final BusAfterSaleMapper afterSaleMapper;
    private final BusDrugsOrderMapper drugsOrderMapper;
    private final BusConsultationOrderMapper consultationOrderMapper;
    private final RemoteDFHService remoteDFHService;
    private final RemoteDXService remoteDXService;
    private final RemoteZXService remoteZXService;
    private final BusPrescriptionMapper busPrescriptionMapper;
    private final BusPrescriptionDrugsMapper busPrescriptionDrugsMapper;
    private final RemoteWxPayService remoteWxPayService;
    private final RemoteTongLianPayService remoteTongLianPayService;
    private final BusDrugOrderPackageMapper busDrugOrderPackageMapper;
    private final RemoteHospitalEnterpriseService remoteHosEntService;

    @Resource
    private ApplicationEventPublisher publisher;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int applyReturn(BusAfterSaleDto dto) {
        logger.info("入参：{}", JSONObject.toJSONString(dto));
        String orderNo = dto.getOrderNo();
        QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", orderNo);
        BusAfterSale busAfterSale = afterSaleMapper.selectOne(queryWrapper);
        // 判断患者是否取消申请退款
        if (StringUtils.isNull(busAfterSale)) {
            throw new ServiceException("该订单已取消申请！");
        }

        BusAfterSale afterSale = new BusAfterSale();
        afterSale.setUpdateTime(DateUtils.getNowDate());
        afterSale.setUpdateBy(SecurityUtils.getUsername());
        afterSale.setExamineTime(DateUtils.getNowDate());
        UpdateWrapper<BusAfterSale> updateWrapper = new UpdateWrapper<>();
        updateWrapper
                .eq("order_no", orderNo);
        switch (dto.getType()) {
            // 问诊订单申请退款状态（问诊中，问诊中已开处方）
            // 问诊订单
            case "0":
                // 不同意
                if (CodeEnum.REFUSE.getCode().equals(dto.getStatus())) {
                    // 修改售后表状态为不予通过
                    afterSale.setReason(dto.getReason());
                    afterSale.setStatus(dto.getStatus());
                    afterSaleMapper.update(afterSale, updateWrapper);
                    // 修改问诊订单状态为退款前状态&推送消息给患者
                    updateCoStatusAndPushMsg(dto, busAfterSale);
                }
                break;
            // 药品订单申请退款/退换状态（待发货，待收货，待自提）
            // 药品订单
            case "1":
                // 查询药品订单信息
                QueryWrapper<BusDrugsOrder> queryWrapper2 = new QueryWrapper<>();
                queryWrapper2
                        .eq("hospital_id", dto.getHospitalId())
                        .eq("order_no", orderNo);
                BusDrugsOrder busDrugsOrder = drugsOrderMapper.selectOne(queryWrapper2);
                String amount = busDrugsOrder.getAmount();
                Long hospitalId = busDrugsOrder.getHospitalId();
                // 药品类型
                String orderDrugsType = busDrugsOrder.getOrderDrugsType();
                // 订单类型
                String orderType = busDrugsOrder.getOrderType();
                String oid = "";
                // 西药配送企业订单
                if (OrderDrugsTypeEnum.ENTERPRISE.getCode().equals(orderDrugsType) && CodeEnum.YES.getCode().equals(orderType)) {
                    // 查询订单所属配送企业信息
                    List<BusDrugOrderPackage> busDrugOrderPackages = busDrugOrderPackageMapper.selectPdList(busDrugsOrder);
                    if (StringUtils.isNotEmpty(busDrugOrderPackages)) {
                        for (BusDrugOrderPackage dop : busDrugOrderPackages) {
                            logger.info("配送企业标识=" + dop.getIdentifying());
                            if (EnterpriseEnum.SST.getInfo().equals(dop.getIdentifying())) {
                                // 调用东方红订单查询接口查询子订单号
                                oid = getDfhSubOrderNo(busDrugsOrder);
                            }
                        }
                    }
                }
                // 不同意
                if (CodeEnum.REFUSE.getCode().equals(dto.getStatus())) {
                    // 修改售后表状态为不予通过
                    afterSale.setReason(dto.getReason());
                    afterSale.setStatus(dto.getStatus());
                    afterSaleMapper.update(afterSale, updateWrapper);
                    // 修改药品订单状态，推送消息给患者
                    busAfterSale.setReason(dto.getReason());
                    updateStatusAndPushMsg(busAfterSale, busDrugsOrder);
                    // 修改东方红订单状态为未退款
                    if (StringUtils.isNotEmpty(oid)) {
                        DFHOrder dfhOrder = new DFHOrder();
                        dfhOrder.setPlatformCode(orderNo);
                        dfhOrder.setOid(oid);
                        dfhOrder.setRefundState(0); // 未退款
                        R<JSONObject> refundUpdate = remoteDFHService.refundUpdate(dfhOrder);
                        JSONObject updateData = refundUpdate.getData();
                        Boolean success1 = updateData.getBoolean("success");
                        if (!success1) {
                            throw new ServiceException("调用东方红订单退款状态修改失败！");
                        }
                    }
                    break;
                } else if (OrderDrugsTypeEnum.ENTERPRISE.getCode().equals(orderDrugsType)) {
                    // 修改东方红订单状态为退款完成
                    if (StringUtils.isNotEmpty(oid)) {
                        DFHOrder dfhOrder = new DFHOrder();
                        dfhOrder.setPlatformCode(orderNo);
                        dfhOrder.setOid(oid);
                        dfhOrder.setRefundState(1); // 退款完成
                        R<JSONObject> refundUpdate = remoteDFHService.refundUpdate(dfhOrder);
                        JSONObject updateData = refundUpdate.getData();
                        Boolean success1 = updateData.getBoolean("success");
                        if (!success1) {
                            throw new ServiceException("调用东方红订单退款状态修改失败！");
                        }
                    }
                } else {
                    // 中药/协定方（配送企业订单）
                    if (CodeEnum.YES.getCode().equals(orderType)) {
                        // 查询处方信息
                        BusPrescription busPrescription = busPrescriptionMapper.selectInfo(hospitalId, orderNo);
                        // 香雪配送企业退款
                        String identifying1 = busPrescription.getIdentifying();
                        if (StringUtils.isNotEmpty(identifying1)) {
                            if (EnterpriseEnum.XX.getInfo().equals(identifying1) && "全额退款".equals(busAfterSale.getRefundType())) {
                                if (PaysTypeEnum.WECHAT_PAY.getCode().equals(busDrugsOrder.getPayWay())) {
                                    R refundOrder = remoteWxPayService.refundOrder(orderNo, "1",
                                            Double.valueOf(amount), "2", "", null, null);
                                    logger.info("XX远程调用退款结果={}", refundOrder.getCode());
                                } else {
                                    BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
                                    busRefundPayDTO.setOrderId(busDrugsOrder.getId());
                                    busRefundPayDTO.setOrderType(OrderTypeEnum.DRUGS.getCode());
                                    R<JSONObject> refund = remoteTongLianPayService.tongLianRefund(busRefundPayDTO);
                                    logger.info("XX远程调用退款结果={}", refund.getCode());
                                }
                            } else if (EnterpriseEnum.DX.getInfo().equals(identifying1)) {
                                String pspnum = busPrescription.getPrescriptionNumber();
                                //大翔接口修改不用传描述
//                                String refundReason = busAfterSale.getDescription();
                                // 新增处方申请退单
                                R<JSONObject> objectR = remoteDXService.presRefund(busDrugsOrder.getHospitalId(),busPrescription.getEnterpriseId(),busDrugsOrder.getOrderNo());
                                JSONObject data = objectR.getData();
                                String code = data.getString("dhy_error_code");
                                String msg = data.getString("dhy_error_msg");
                                logger.info("调用处方申请退单:code={},msg={}", code, msg);
                                // 退款操作
                                if ("全额退款".equals(busAfterSale.getRefundType()) && !DrugsOrderEnum.REFUNDED.getCode().equals(busDrugsOrder.getStatus())) {
                                    if (PaysTypeEnum.WECHAT_PAY.getCode().equals(busDrugsOrder.getPayWay())) {
                                        R refundOrder = remoteWxPayService.refundOrder(orderNo, "1",
                                                Double.valueOf(amount), "2", "", null, null);
                                        logger.info("DX远程调用退款结果={}", refundOrder.getCode());
                                    } else {
                                        BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
                                        busRefundPayDTO.setOrderId(busDrugsOrder.getId());
                                        busRefundPayDTO.setOrderType(OrderTypeEnum.DRUGS.getCode());
                                        R<JSONObject> refund = remoteTongLianPayService.tongLianRefund(busRefundPayDTO);
                                        logger.info("DX远程调用退款结果={}", refund.getCode());
                                    }
                                }
                                if (!CodeEnum.NO.getCode().equals(code)) {
                                    //throw new ServiceException("该药品订单大翔已煎煮不可同意退款！");
                                    // 将大翔返回异常信息存入详情
                                    BusDrugsOrder busDrugsOrderDetail = drugsOrderMapper.selectPatientInfo(orderNo);
                                    busDrugsOrderDetail.setOrderDescription("大翔说明：["+msg+"]");
                                    if("该处方已经进入调配".equals(msg)){
                                        if(null==dto.getAgreeReason()){
                                            throw new ServiceException("请填写强制同意退款的原因！");
                                        }
                                        if(dto.getAgreeReason().length()>200){
                                            throw new ServiceException("退款原因字数不能超过200个字！");
                                        }
                                        busDrugsOrderDetail.setAgreeReason(dto.getAgreeReason());
                                        drugsOrderMapper.updateById(busDrugsOrderDetail);
                                    }
                                }
                            }
                        }
                    } else {
                        if ("全额退款".equals(busAfterSale.getRefundType())) {
                            if ("全额退款".equals(busAfterSale.getRefundType()) && !DrugsOrderEnum.REFUNDED.getCode().equals(busDrugsOrder.getStatus())) {
                                if (PaysTypeEnum.WECHAT_PAY.getCode().equals(busDrugsOrder.getPayWay())) {
                                    R refundOrder = remoteWxPayService.refundOrder(orderNo, "1",
                                            Double.valueOf(amount), "2", "", null, null);
                                    logger.info("药房远程调用退款结果={}", refundOrder.getCode());
                                } else {
                                    BusRefundPayDTO busRefundPayDTO = new BusRefundPayDTO();
                                    busRefundPayDTO.setOrderId(busDrugsOrder.getId());
                                    busRefundPayDTO.setOrderType(OrderTypeEnum.DRUGS.getCode());
                                    R<JSONObject> refund = remoteTongLianPayService.tongLianRefund(busRefundPayDTO);
                                    logger.info("药房远程调用退款结果={}", refund.getCode());
                                }
                            }
                        }
                    }
                }
                // 待自提/待发货不修改售后状态
                if (!(DrugsOrderEnum.TOBEPICKEDUP.getCode().equals(busAfterSale.getOrderStatus()) ||
                        DrugsOrderEnum.TO_BE_SHIPPED.getCode().equals(busAfterSale.getOrderStatus()))) {
                    // 修改售后表状态为已通过
                    afterSale.setStatus(dto.getStatus());
                    afterSaleMapper.update(afterSale, updateWrapper);
                }
                break;
            default:
        }
        return 1;
    }

    @Override
    public boolean isReturn(BusAfterSaleDto dto) {
        logger.info("医院后台查询配送企业是否同意退款,入参：{}", JSONObject.toJSONString(dto));
        QueryWrapper<BusAfterSale> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_no", dto.getOrderNo());
        BusAfterSale busAfterSale = afterSaleMapper.selectOne(queryWrapper);
        // 判断订单类型
        switch (dto.getType()) {
            // 问诊订单
            case "0":
                return true;
            // 药品订单
            case "1":
                // 查询订单信息
                BusDrugsOrder busDrugsOrder = drugsOrderMapper.selectPatientInfo(dto.getOrderNo());
                // 查询配送企业
                List<BusPrescriptionDrugs> busPrescriptionDrugsList = busPrescriptionDrugsMapper.selectPdList(busDrugsOrder.getPrescriptionId());
                if (busPrescriptionDrugsList.size() > 0) {
                    String identifying1 = busPrescriptionDrugsList.get(0).getIdentifying();
                    if (StringUtils.isNotEmpty(identifying1)) {
                        // 调用大翔退款接口
                        if (EnterpriseEnum.DX.getInfo().equals(identifying1) && "全额退款".equals(busAfterSale.getRefundType())) {
                            R<JSONObject> objectR = remoteDXService.presRefund(busDrugsOrder.getHospitalId(),busPrescriptionDrugsList.get(0).getEnterpriseId(),dto.getOrderNo());
                            JSONObject data = objectR.getData();
                            logger.info("退款大翔返回结果:{}",data);
                            String code = data.getString("dhy_error_code");
                            String msg = data.getString("dhy_error_msg");
                            if (!code.equals(CodeEnum.NO.getCode())) {
                                if("该处方已经进入调配".equals(msg)){
                                    //throw new ServiceException("该药品订单大翔已煎煮不可同意退款！");
                                    logger.info("医院后台查询配送企业是否同意退款,返回结果：{}【{}】", "该药品订单大翔已煎煮不可同意退款！",false);
                                    // 弹出提示，提示是否继续退款
                                    return false;
                                }
                            }
                        }
                        // 调用至信退单
                        if (EnterpriseEnum.ZX.getInfo().equals(identifying1) && "全额退款".equals(busAfterSale.getRefundType())) {
                            CannelOrderDTO cannelOrderDTO = new CannelOrderDTO();
                            cannelOrderDTO.setHospitalOrderCode(dto.getOrderNo());
                            R<BusHospitalEnterprise> result = remoteHosEntService.getByHosIdAndEntId(busDrugsOrder.getHospitalId(), busPrescriptionDrugsList.get(0).getEnterpriseId());
                            logger.info("远程调用查询配送企业配置信息返回：" + JSON.toJSONString(result));
                            if (!Constants.SUCCESS.equals(result.getCode())) {
                                logger.error("至信退单失败：" + result.getMsg());
                            }
                            if (StringUtils.isNotNull(result.getData()) && StringUtils.isNotEmpty(result.getData().getConfig())) {
                                List<JSONObject> configs = JSONObject.parseArray(result.getData().getConfig(), JSONObject.class);
                                for (JSONObject config : configs) {
                                   if ("cusCode".equals(config.get("msgKey"))) {
                                        cannelOrderDTO.setCusCode(config.getString("msgValue"));
                                    }
                                }
                            } else {
                                logger.error("至信退单失败，读取配置信息为空");
                            }
                            R<String> objectR = remoteZXService.cannelOrder(cannelOrderDTO);
                            if (Objects.isNull(objectR) || !objectR.isSuccess()) {
                                throw new ServiceException("至信退单失败:" + objectR.getMsg());
                            }
                            JSONObject data = JSONObject.parseObject(objectR.getData());
                            logger.info("退单至信返回结果:{}",data);
                            String code = data.getString("code");
                            String msg = data.getString("msg");
                            if ("400".equals(code)) {
                                if("改处方已经开始煎煮，无法取消！".equals(msg)) {
                                    //throw new ServiceException("该药品订单至信已煎煮不可同意退款！");
                                    logger.info("医院后台查询配送企业是否同意退款,返回结果：{}【{}】", "该药品订单至信已煎煮不可同意退款！",false);
                                    // 弹出提示，提示是否继续退款
                                    return false;
                                }
                            }
                            if("200".equals(code)) {
                                logger.info("至信已成功取消订单，订单号：{}",busDrugsOrder.getOrderNo());
                            }
                        }
                    }
                }
                break;
            default:
                return true;
        }
        return true;
    }

    private void updateCoStatusAndPushMsg(BusAfterSaleDto dto, BusAfterSale busAfterSale) {
        String orderNo = dto.getOrderNo();
        BusConsultationOrder consultationOrder = new BusConsultationOrder();
        consultationOrder.setUpdateBy(SecurityUtils.getUsername());
        consultationOrder.setUpdateTime(DateUtils.getNowDate());
        // 查询订单信息
        BusConsultationOrder busConsultationOrder = consultationOrderMapper.selectPatientInfo(orderNo);
        if (CodeEnum.NO.getCode().equals(dto.getOrderType())) {
            consultationOrder.setStatus(busAfterSale.getOrderStatus());
        } else { // 视频问诊
            consultationOrder.setVideoStatus(busAfterSale.getOrderStatus());
        }
        UpdateWrapper<BusConsultationOrder> updateWrapper1 = new UpdateWrapper<>();
        updateWrapper1.eq("order_no", orderNo);
        consultationOrderMapper.update(consultationOrder, updateWrapper1);

        ConsultationRefundRefusedEvent refundRefusedEvent = new ConsultationRefundRefusedEvent();
        refundRefusedEvent.setEventType(BaseConsultationEvent.EventType.REFUND_REFUSED);
        refundRefusedEvent.setConsultationOrderId(busConsultationOrder.getId());
        refundRefusedEvent.setOrderNo(busConsultationOrder.getOrderNo());
        Map<String, Object> attachment = new HashMap<>();
        attachment.put("status", "5");
        refundRefusedEvent.setAttachment(attachment);
        publisher.publishEvent(refundRefusedEvent);
    }

    private void updateStatusAndPushMsg(BusAfterSale busAfterSale, BusDrugsOrder busDrugsOrder) {
        logger.info("售后信息：{}", busAfterSale.toString());
        logger.info("药品订单信息：{}", busDrugsOrder.toString());
        // 修改药品订单状态为退换前状态
        BusDrugsOrder drugsOrder = new BusDrugsOrder();
        drugsOrder.setUpdateBy(SecurityUtils.getUsername());
        drugsOrder.setUpdateTime(DateUtils.getNowDate());
        drugsOrder.setStatus(busAfterSale.getOrderStatus());
        UpdateWrapper<BusDrugsOrder> updateWrapper2 = new UpdateWrapper<>();
        updateWrapper2.eq("order_no", busDrugsOrder.getOrderNo());
        drugsOrderMapper.update(drugsOrder, updateWrapper2);


        // 消息通知
        Map<String, Object> attachment = new HashMap<>();
        attachment.put("orderId", busDrugsOrder.getId());
        attachment.put("status", "5");
        if ("换货".equals(busAfterSale.getRefundType())) {
            // 组装拒绝退换货事件通知
            DrugsOrderRefuseRefundExchangeEvent orderRefuseRefundExchangeEvent = new DrugsOrderRefuseRefundExchangeEvent();
            orderRefuseRefundExchangeEvent.setSubOrderId(busDrugsOrder.getId());
            orderRefuseRefundExchangeEvent.setAttachment(attachment);
            publisher.publishEvent(orderRefuseRefundExchangeEvent);
        } else {
            // 组装拒绝退款退货事件通知
            DrugsOrderRefuseRefundReturnEvent orderRefuseRefundReturnEvent = new DrugsOrderRefuseRefundReturnEvent();
            orderRefuseRefundReturnEvent.setSubOrderId(busDrugsOrder.getId());
            orderRefuseRefundReturnEvent.setAttachment(attachment);
            publisher.publishEvent(orderRefuseRefundReturnEvent);
        }
    }

    private String getDfhSubOrderNo(BusDrugsOrder busDrugsOrder) {
        String oid = "";
        OrderQueryDto orderQueryDto = new OrderQueryDto();
        orderQueryDto.setPageNo(1);
        orderQueryDto.setPageSize(10);
        orderQueryDto.setPlatformCode(busDrugsOrder.getOrderNo());
        R<JSONObject> objectR = remoteDFHService.getOrderInfo(orderQueryDto);
        JSONObject orderInfo = objectR.getData();
        Boolean success = orderInfo.getBoolean("success");
        logger.info("调用东方红查询订单是否成功:{}", success);
        if (!success) {
            throw new ServiceException("调用东方红查询订单失败！");
        }
        JSONArray orders = orderInfo.getJSONArray("orders");
        if (orders.size() > 0) {
            JSONObject o1 = orders.getJSONObject(orders.size() - 1);
            JSONArray details = o1.getJSONArray("details");
            if (details.size() > 0) {
                JSONObject jsonObject = details.getJSONObject(details.size() - 1);
                oid = jsonObject.getString("oid");
            }
        }
        return oid;
    }

}
