package com.puree.hospital.business.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.business.domain.*;
import com.puree.hospital.business.domain.dto.BusOfficinaPharmacistDto;
import com.puree.hospital.business.domain.vo.BusSignatureVO;
import com.puree.hospital.business.domain.vo.PharmacistInfoVo;
import com.puree.hospital.business.mapper.BusSignatureMapper;
import com.puree.hospital.business.service.IBusDoctorService;
import com.puree.hospital.business.service.IBusHospitalService;
import com.puree.hospital.business.service.IBusOfficinaPharmacistService;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

import static com.puree.hospital.common.core.constant.CacheConstants.LOGIN_TOKEN_KEY;

/**
 * 药师管理Controller
 */
@RestController
@RequestMapping("/officina")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusOfficinaPharmacistController extends BaseController {
    private final IBusOfficinaPharmacistService busOfficinaPharmacistService;
    private final IBusDoctorService busDoctorService;
    private final RedisService redisService;
    private final BusSignatureMapper busSignatureMapper;
    private final IBusHospitalService busHospitalService;

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    @PreAuthorize(hasPermi = "business:officina:list")
    @GetMapping("/list")
    public TableDataInfo list(BusOfficinaPharmacist busOfficinaPharmacist) {
        startPage();
        List<BusOfficinaPharmacist> list = busOfficinaPharmacistService.listAll(busOfficinaPharmacist);
        return getDataTable(list);
    }

    /**
     * 获取某个类型的全部药师姓名字段
     *
     * @param busPharmacyConfig
     * @return
     */
    @RequestMapping("/getName")
    public AjaxResult getOfficinaNameAll(@RequestBody BusPharmacyConfig busPharmacyConfig) {
        return AjaxResult.success(busOfficinaPharmacistService.getOfficinaNameAll(busPharmacyConfig));
    }

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    @PreAuthorize(hasPermi = "business:officina:getById")
    @GetMapping("/getById/{id}")
    public AjaxResult getById(@PathVariable Long id) {
        return AjaxResult.success(busOfficinaPharmacistService.getById(id));
    }

    /**
     * 添加药师
     *
     * @param pharmacist 药师信息
     * @return 返回影响行数
     */
    @PostMapping("/add")
    @PreAuthorize(hasPermi = "business:officina:add")
    @Log(title = "添加药师", businessType = BusinessType.INSERT, operatorType = OperatorType.MANAGE)
    public AjaxResult add(@RequestBody BusOfficinaPharmacist pharmacist) {
        return toAjax(busOfficinaPharmacistService.insert(pharmacist));
    }

    /**
     * 修改，忽略null字段
     *
     * @param busOfficinaPharmacist 修改的记录
     * @return 返回影响行数
     */
    @PreAuthorize(hasPermi = "business:officina:edit")
    @Log(title = "药师信息", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    @PutMapping("/modify")
    public AjaxResult edit(@RequestBody BusOfficinaPharmacist busOfficinaPharmacist) {
        busOfficinaPharmacist.setCreateBy(SecurityUtils.getUsername());
        // 校验该手机号是否注册过医生
        BusDoctor busDoctor = busDoctorService.selectDoctorInfo(busOfficinaPharmacist.getPhoneNumber());
        if (StringUtils.isNotNull(busDoctor)) {
            return AjaxResult.error("该手机号已经注册过医生！");
        }
        return toAjax(busOfficinaPharmacistService.updateIgnoreNull(busOfficinaPharmacist));
    }

    /**
     * 删除记录
     *
     * @param id 待删除的记录
     * @return 返回影响行数
     */
    @PreAuthorize(hasPermi = "business:officina:remove")
    @Log(title = "药师信息", businessType = BusinessType.DELETE, operatorType = OperatorType.MANAGE)
    @DeleteMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(busOfficinaPharmacistService.delete(id));
    }

    /**
     * 药师停用启用操作
     *
     * @param busOfficinaPharmacist
     * @return
     */
    @PreAuthorize(hasPermi = "business:officina:lock")
    @Log(title = "药师信息", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    @PostMapping("/lock")
    public AjaxResult lock(@RequestBody BusOfficinaPharmacist busOfficinaPharmacist) {
        busOfficinaPharmacist.setCreateBy(SecurityUtils.getUsername());
        // 1启用状态  0停用状态
        if (busOfficinaPharmacist.getStatus() == 0) {
            BusOfficinaPharmacist officinaPharmacist = busOfficinaPharmacistService.getById(busOfficinaPharmacist.getId());
            if (StringUtils.isNotBlank(officinaPharmacist.getOfficinaToken())) {
                redisService.deleteObject(LOGIN_TOKEN_KEY + officinaPharmacist.getOfficinaToken());
            }
        }
        return toAjax(busOfficinaPharmacistService.lock(busOfficinaPharmacist));
    }

    /**
     * 校验药师身份证号是否重复
     *
     * @param id
     * @param idNumber
     * @return
     */
    @GetMapping(value = "/checkIdNumber")
    @Log(title = "校验药师身份证号是否重复", businessType = BusinessType.OTHER, operatorType = OperatorType.MANAGE)
    public AjaxResult checkIdNumber(Long id, @RequestParam("idNumber") String idNumber) {
        return AjaxResult.success(busOfficinaPharmacistService.checkIdNumber(id, idNumber));
    }

    /**
     * 校验药师手机号是否重复
     *
     * @param id
     * @param phoneNumber
     * @return
     */
    @GetMapping(value = "/checkPhoneNumber")
    @Log(title = "校验药师手机号是否重复", businessType = BusinessType.OTHER, operatorType = OperatorType.OTHER)
    public AjaxResult checkPhoneNumber(Long id, @RequestParam("phoneNumber") String phoneNumber) {
        return AjaxResult.success(busOfficinaPharmacistService.checkPhoneNumber(id, phoneNumber));
    }

    @GetMapping("pharmacist/info")
    public AjaxResult getPharmacistInfo(String roleType) {
        String userName = SecurityUtils.getUsername();
        PharmacistInfoVo vo = busOfficinaPharmacistService.getPharmacistInfo(userName, roleType);
        if (vo != null) {
            String phoneNumber = DESUtil.decrypt(vo.getPhoneNumber());
            String idNumber = DESUtil.decrypt(vo.getIdNumber());
            vo.setIdNumber(idNumber);
            vo.setPhoneNumber(phoneNumber);
        }
        return AjaxResult.success(vo);
    }

    /**
     * 查询药师签名信息
     *
     * @return
     */
    @GetMapping("/role")
    public AjaxResult selectUserRole(String idNumber) {
        BusSignatureVO vo = new BusSignatureVO();
        // 查询药师信息
        BusOfficinaPharmacist pharmacist = busOfficinaPharmacistService.selectPharmacistByNo(DESUtil.encrypt(idNumber));
        vo.setPharmacistName(pharmacist.getPharmacistName());
        vo.setIdNumber(idNumber);
        vo.setPhoneNumber(DESUtil.decrypt(pharmacist.getPhoneNumber()));
        BusHospital busHospital = busHospitalService.selectBusHospitalById(pharmacist.getHospitalId());
        vo.setHospitalName(busHospital.getHospitalName());
        // 查询是否签名
        LambdaQueryWrapper<BusSignature> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusSignature::getObjectId, pharmacist.getId());
        lambdaQuery.inSql(BusSignature::getObjectType, "6,7");
        List<BusSignature> busSignatures = busSignatureMapper.selectList(lambdaQuery);
        logger.info("签名信息={}", JSONObject.toJSONString(busSignatures));
        if (StringUtils.isNotEmpty(busSignatures)) {
            for (BusSignature busSignature : busSignatures) {
                if (StringUtils.isNotEmpty(busSignature.getCertId())) {
                    vo.setCertId(busSignature.getCertId());
                    vo.setCertSignature(busSignature.getCertSignature());
                    vo.setObjectId(busSignature.getObjectId());
                    vo.setObjectType(busSignature.getObjectType());
                    break;
                }
            }
        }
        return AjaxResult.success(vo);
    }

    @GetMapping("/feign/officina/pharmacist")
    public R<String> queryOfficinaPharmacist(@RequestParam("phonenumber") String phonenumber) {
        return R.ok(busOfficinaPharmacistService.queryOfficinaPharmacist(phonenumber));
    }

    /**
     * 下载药房药师排班
     *
     * @param scheduleDate 排班时间
     * @param hospitalId   医院id
     */
    @PostMapping("/downloadScheduleTemplate")
    @PreAuthorize(hasPermi = "business:officina:downloadScheduleTemplate")
    public void downloadScheduleTemplate(String scheduleDate, Long hospitalId, HttpServletResponse response) {
        if (StringUtils.isEmpty(scheduleDate) || !scheduleDate.contains("-")) {
            throw new ServiceException("排班时间为空");
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyExcel没有关系
            String[] date = scheduleDate.split("-");
            String fileName = URLEncoder.encode("药师" + date[0] + "年" + date[1] + "月排班表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            busOfficinaPharmacistService.downloadScheduleTemplate(scheduleDate, hospitalId, response);
        } catch (Exception ex) {
            logger.error("下载药师排班模板异常", ex);
            throw new ServiceException("下载药师排班模板失败");
        }
    }

    /**
     * 导入药师排班表
     *
     * @param file       excel文件
     * @param hospitalId 医院id
     * @return 导入结果
     */
    @PostMapping("/importSchedule")
    @PreAuthorize(hasPermi = "business:officina:importSchedule")
    public AjaxResult importSchedule(MultipartFile file, Long hospitalId) {
        StringUtils.isNullThrowExp(file, "上传文件为空");
        String scheduleDate = checkFileName(file.getOriginalFilename());
        return busOfficinaPharmacistService.importSchedule(file, hospitalId, scheduleDate);
    }

    /**
     * 查询药师排班
     *
     * @param dto 筛选信息
     * @return 列表
     */
    @GetMapping("/listSchedule")
    @PreAuthorize(hasPermi = "business:officina:listSchedule")
    public TableDataInfo listSchedule(BusPharmacistSchedule dto) {
        startPage();
        return getDataTable(busOfficinaPharmacistService.listSchedule(dto));
    }

    /**
     * 检查文件名称格式
     * 格式：药师2023年04月排班表
     *
     * @param fileName 文件名称
     */
    private String checkFileName(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            throw new ServiceException("文件格式不正确，请先下载当月药师排班表，填充排班信息保存后上传");
        }
        String[] split = fileName.split("\\.");
        if (!"xlsx".equals(split[1]) && !"xls".equals(split[1])) {
            throw new ServiceException("文件类型不正确，仅支持xlsx与xls");
        }
        if (split[0].length() != 13) {
            throw new ServiceException("文件命名不正确，格式：药师xxxx年xx月排班表");
        }
        if (!Character.isDigit(fileName.charAt(2)) || !Character.isDigit(fileName.charAt(3)) ||
                !Character.isDigit(fileName.charAt(4)) || !Character.isDigit(fileName.charAt(5))
                || !Character.isDigit(fileName.charAt(7)) || !Character.isDigit(fileName.charAt(8))) {
            throw new ServiceException("文件格式不正确，请先下载当月药师排班表，填充排班信息保存后上传");
        }
        int yearIndex = fileName.indexOf("年");
        int monthIndex = fileName.indexOf("月");
        if (yearIndex != 6 || monthIndex != 9) {
            throw new ServiceException("文件格式不正确，请先下载当月药师排班表，填充排班信息保存后上传");
        }
        String s1 = String.valueOf(fileName.charAt(2));
        String s2 = String.valueOf(fileName.charAt(3));
        String s3 = String.valueOf(fileName.charAt(4));
        String s4 = String.valueOf(fileName.charAt(5));
        String s5 = String.valueOf(fileName.charAt(7));
        String s6 = String.valueOf(fileName.charAt(8));
        return s1 + s2 + s3 + s4 + "-" + s5 + s6;
    }

    /**
     * 更新药师处方签
     * @param dto
     * @return
     */
    @PreAuthorize(hasPermi = "business:update:PrescriptionLabel")
    @Log(title = "更新处方签", businessType = BusinessType.UPDATE, operatorType = OperatorType.MANAGE)
    @PutMapping("/update/label")
    public AjaxResult updatePrescriptionLabel(@RequestBody BusOfficinaPharmacistDto dto) {
        return AjaxResult.success(busOfficinaPharmacistService.updatePrescriptionLabel(dto));
    }
}
