package com.puree.hospital.insurance.domain.dto;

import com.puree.hospital.common.api.enums.ClientTypeEnum;
import lombok.Data;

/**
 * 医保支付前端入参
 * <AUTHOR>
 * @date 2023/5/31 17:56
 */
@Data
public class MiPayDTO {
    /**
     * 医院id
     */
    private Long hospitalId;
    /**
     * 总订单号
     */
    private String totalOrderNo;
    /**
     * 个账使用标识
     */
    private String acctUsedFlag;
    /**
     * 支付授权码
     */
    private String payAuthNo;
    /**
     * 经纬度
     */
    private String uldLatlnt;

    /**
     * 客户端类型
     */
    private ClientTypeEnum clientType;

    /**
     * 操作人
     */
    private String operator;
}
