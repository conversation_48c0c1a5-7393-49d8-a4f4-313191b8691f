package com.puree.hospital.insurance.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.app.api.RemoteWxPayService;
import com.puree.hospital.business.api.RemoteBusPrescriptionService;
import com.puree.hospital.business.api.model.BusPrescription;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.api.util.IpUtil;
import com.puree.hospital.common.core.enums.PaysTypeEnum;
import com.puree.hospital.common.core.enums.RefundsExchangesEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.text.UUID;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.sign.MD5Util;
import com.puree.hospital.insurance.api.model.MIPaymentFinishEvent;
import com.puree.hospital.insurance.domain.BusOrderAfterSales;
import com.puree.hospital.insurance.domain.MiHospitalConfig;
import com.puree.hospital.insurance.domain.MiHospitalMchConfig;
import com.puree.hospital.insurance.domain.MiPayResult;
import com.puree.hospital.insurance.domain.MiPaymentNotification;
import com.puree.hospital.insurance.domain.dto.MIPaymentDTO;
import com.puree.hospital.insurance.domain.dto.MiRefundDTO;
import com.puree.hospital.insurance.domain.dto.MiPayParamDTO;
import com.puree.hospital.insurance.domain.dto.PayDTO;
import com.puree.hospital.insurance.domain.dto.RefundDTO;
import com.puree.hospital.insurance.domain.vo.MiPayResultVo;
import com.puree.hospital.insurance.helper.MiClientTypeHelper;
import com.puree.hospital.insurance.infrastructure.enums.mi.OrdStasTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.pay.OrderTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.pay.PayTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.pay.UserCardTypeEnum;
import com.puree.hospital.insurance.infrastructure.properties.MiConfig;
import com.puree.hospital.insurance.infrastructure.properties.MiVideoRecordConfig;
import com.puree.hospital.insurance.infrastructure.utils.pay.PayUtil;
import com.puree.hospital.insurance.mapper.BusOrderAfterSalesMapper;
import com.puree.hospital.insurance.mapper.BusOrderMapper;
import com.puree.hospital.insurance.queue.producer.MIPaymentFinishProducer;
import com.puree.hospital.insurance.service.IBusPayService;
import com.puree.hospital.insurance.service.IMiHospitalConfigService;
import com.puree.hospital.insurance.service.IMiPayResultService;
import com.puree.hospital.order.api.RemoteBusDrugsOrderService;
import com.puree.hospital.order.api.model.BusDrugsOrder;
import com.puree.hospital.order.api.model.BusOrder;
import com.puree.hospital.pay.api.RemotePayOrderService;
import com.puree.hospital.pay.api.RemoteRefundRecordService;
import com.puree.hospital.pay.api.constants.BusPayOrderStatusEnum;
import com.puree.hospital.pay.api.model.BusPayOrder;
import com.puree.hospital.pay.api.model.dto.BusOrderRefundRecordDTO;
import com.puree.hospital.pay.api.model.enums.RefundStatusEnum;
import com.puree.hospital.pay.api.model.enums.RefundTypeEnum;
import com.puree.hospital.tool.api.RemoteBaseWxService;
import com.puree.hospital.tool.api.model.dto.WxAccessTokenDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@SuppressWarnings("SpellCheckingInspection")
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusPayServiceImpl implements IBusPayService {
    private final IMiHospitalConfigService miHospitalConfigService;
    private final IMiPayResultService miPayResultService;
    private final MiConfig miConfig;
    private final MiVideoRecordConfig videoRecordConfig;
    private final BusOrderAfterSalesMapper orderAfterSalesMapper;
    private final BusOrderMapper orderMapper;
    private final RemoteBaseWxService remoteBaseWxService;
    private final MiClientTypeHelper miClientTypeHelper;
    private final RemotePayOrderService remotePayOrderService;
    private final RemoteWxPayService remoteWxPayService;
    private final RemoteBusDrugsOrderService remoteBusDrugsOrderService;
    private final RemoteBusPrescriptionService remoteBusPrescriptionService;
    @Resource
    @Lazy
    private MIPaymentFinishProducer miPaymentFinishProducer;

    @Resource
    private RemoteRefundRecordService remoteRefundRecordService;

    /**
     * 统一下单接口
     *
     * @param dto - 支付参数
     * @return 支付结果
     */
    @Override
    public MiPayResultVo unifiedOrder(MIPaymentDTO dto) {
        log.info("统一下单接口业务入参={}", JSONObject.toJSONString(dto));
        ClientTypeEnum clientType = miClientTypeHelper.getClientTypeWithLoginUser();
        // 查询医院医保配置信息
        MiHospitalConfig hospitalConfig = miHospitalConfigService.getMiHospitalConfigByClientType(dto.getHospitalId(), clientType.getType());
        // 查询订单医保支付信息
        MiPayResult payResult = this.queryMiPayResultInfo(dto.getOrderNo());
        if (StringUtils.isNotBlank(payResult.getPayUrl()) && Objects.equals(clientType.getType(), payResult.getClientType())) {
            return new MiPayResultVo(payResult.getPayUrl(),payResult.getPayAppid());
        }
        //查询和校验订单信息
        BusOrder busOrder = checkOrder(dto.getOrderNo());
        // 设置医保支付的userId和patientId
        setPatientIdAndUserId(dto, busOrder);
        // 获取微信医保支付参数
        PayDTO payDTO = getPayDTO(dto, hospitalConfig, payResult);
        Map<String, Object> resultMap = PayUtil.unifiedOrder(payDTO);
        log.info("医保支付返回值={}", resultMap);
        String payUrl = (String) resultMap.get("pay_url");
        String payAppid = (String) resultMap.get("pay_appid");
        // 没这个会导致微信医保支付后回调失败的问题-回调做个关于这个的校验
        String medTransId = (String) resultMap.get("med_trans_id");
        if (StringUtils.isNotEmpty(medTransId)) {
            updateMiPayResult(dto.getOrderNo(), medTransId, payUrl, payAppid, clientType);
        }
        insertOrUpdatePayOrderRecord(medTransId,payResult.getMiPaySerialNo(),dto,payDTO);
        log.info("微信医保返回地址={},支付appid={}", payUrl, payAppid);
        return new MiPayResultVo(payUrl,payAppid);
    }

    /**
     * 设置医保支付的userId和patientId
     * @param dto    - 支付参数
     * @param busOrder - 订单信息
     */
    private void setPatientIdAndUserId(MIPaymentDTO dto, BusOrder busOrder) {
        // 只有药品才能走医保支付
        // 设置对应的用户id
        dto.setPatientId(busOrder.getPatientId());
        R<BusDrugsOrder> busDrugsOrderById = remoteBusDrugsOrderService.selectDrugsOrderById(busOrder.getSubOrderId());
        if (busDrugsOrderById == null || busDrugsOrderById.getCode() != HttpStatus.SUCCESS
                || busDrugsOrderById.getData() == null) {
            throw new ServiceException("查询药品订单失败！");
        }
        BusDrugsOrder busDrugsOrder = busDrugsOrderById.getData();
        // 查询处方信息
        R<BusPrescription> busPrescriptionR = remoteBusPrescriptionService.selectDrugsOrderById(busDrugsOrder.getPrescriptionId());
        if (busPrescriptionR == null || busPrescriptionR.getCode() != HttpStatus.SUCCESS
                || busPrescriptionR.getData() == null) {
            throw new ServiceException("查询处方信息失败！");
        }
        BusPrescription prescription = busPrescriptionR.getData();
        // 设置对应的患者id
        dto.setFamilyId(prescription.getFamilyId());
    }

    /**
     * 新增医保支付流水记录
     * @param medTransId - 微信生成的医疗订单id
     * @param dto - 支付入参
     * @param payDTO - 微信医保接口支付参数
     */
    private void insertOrUpdatePayOrderRecord(String medTransId,String miPaySerialNo,MIPaymentDTO dto,PayDTO payDTO) {
        BusPayOrder busPayOrder = new BusPayOrder();
        // 我方订单号
        busPayOrder.setOrderNo(dto.getOrderNo());
        // 医保支付流水号
        busPayOrder.setOutTradeNo(miPaySerialNo);
        // 三方支付流水号-微信生成的医疗订单id
        busPayOrder.setTransactionId(medTransId);
        // 订单金额
        busPayOrder.setAmount(new BigDecimal(payDTO.getTotalFee()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        busPayOrder.setCreateBy(SecurityUtils.getUsername());
        // 微信医保支付
        busPayOrder.setPayWay(PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode());
        // 购物车订单- 微信医保支付目前只有购物车订单
        busPayOrder.setOrderType("3");
        busPayOrder.setStatus(BusPayOrderStatusEnum.PAYING.getValue());
        // 微信医保商户号
        busPayOrder.setMchId(payDTO.getMchId());
        busPayOrder.setOpenid(payDTO.getOpenid());
        busPayOrder.setAppid(payDTO.getAppid());
        busPayOrder.setHospitalId(dto.getHospitalId());
        // 订单对应的真实就诊人id - 原来的familyId
        busPayOrder.setPatientId(dto.getFamilyId());
        // 订单对应的用户id - 原来的patientId
        busPayOrder.setUserId(dto.getPatientId());
        remotePayOrderService.insertOrUpdatePayOrder(busPayOrder);
    }

    /**
     * 更新医保支付结果到mipayresult表
     * @param orderNo 订单号
     * @param medTransId 医保支付流水号
     * @param payUrl 支付跳转地址
     * @param payAppid 支付appid
     * @param clientType 客户端类型
     */
    private void updateMiPayResult(String orderNo, String medTransId, String payUrl,
                                   String payAppid, ClientTypeEnum clientType) {
        // 更新表数据，当小程序预支付的时候，保存payAppid和payUrl，如果说最后没有支付，公众号会再走一遍流程，会将payAppid更新为空
        MiPayParamDTO miPayParamDTO = new MiPayParamDTO();
        miPayParamDTO.setOrderNo(orderNo);
        miPayParamDTO.setPayUrl(payUrl);
        miPayParamDTO.setPayAppid(payAppid);
        miPayParamDTO.setClientType(clientType.getType());
        miPayParamDTO.setMedTransId(medTransId);
        miPayResultService.updatePayUrlAndAppidAndMedTransId(miPayParamDTO);
    }

    /**
     * 获取医保支付参数
     * @param dto - 前端传入支付参数
     * @param hospitalConfig 医院医保支付配置
     * @param payResult 医保预支付结果
     * @return  微信医保接口支付参数
     */
    private PayDTO getPayDTO(MIPaymentDTO dto, MiHospitalConfig hospitalConfig, MiPayResult payResult) {
        PayDTO payDTO = new PayDTO();
        // 目前只支持门诊挂号
        payDTO.setOrderType(OrderTypeEnum.INT_REG_PAY.getCode());
        MiHospitalMchConfig miHospitalMchConfig = hospitalConfig.getMchConfigList().get(0);
        payDTO.setAppid(miHospitalMchConfig.getWxAppId());
        payDTO.setMchId(miHospitalMchConfig.getMchId());
        payDTO.setChannelNo(miHospitalMchConfig.getChannelNo());
        payDTO.setOpenid(dto.getOpenid());
        payDTO.setHospOutTradeNo(UUID.randomUUID(true).toString());
        payDTO.setHospitalName(hospitalConfig.getHospitalName());
        // 其他现金支付金额
        BigDecimal cashAddFee = Objects.nonNull(payResult.getOtherCashAmount()) ? payResult.getOtherCashAmount() : BigDecimal.ZERO;
        // 订单总额=医保金额+自费金额+商品金额，
        BigDecimal medFeeSumamt = payResult.getMedfeeSumamt();
        BigDecimal totalFee = medFeeSumamt.add(cashAddFee);
        totalFee = totalFee.multiply(new BigDecimal("100"));
        payDTO.setTotalFee(totalFee.intValue());
        //医保自费金额
        BigDecimal payAmt = payResult.getOwnPayAmt();
        //现金总费用 = 医保自费金额 + 其他现金支付费用
        BigDecimal cashFee = payAmt.add(cashAddFee);
        payDTO.setCashFee(cashFee.multiply(new BigDecimal("100")).intValue());
        // 其他费用
        payDTO.setCashAddFee(cashAddFee.multiply(new BigDecimal("100")).intValue());
        if (cashAddFee.compareTo(BigDecimal.ZERO) > 0) {
            payDTO.setCashAddWording("其他费用");
        }
        payDTO.setAllowFeeChange(YesNoEnum.NO.getCode());
        payDTO.setSpbillCreateIp(IpUtil.getHostIp());
        payDTO.setNotifyUrl(miConfig.getNotifyUrl());
        payDTO.setBody("门诊缴费");
        payDTO.setReturnUrl(miHospitalMchConfig.getReturnUrl());
        if (YesNoEnum.NO.getCode().equals(payDTO.getCashFee())) {
            payDTO.setPayType(PayTypeEnum.MEDICAL_INSURANCE.getCode());
        } else {
            payDTO.setPayType(PayTypeEnum.CASH_MEDICAL_INSURANCE.getCode());
        }
        payDTO.setCityId(hospitalConfig.getMdtrtareaAdmvs());
        // 统筹+个账
        BigDecimal insuranceFee = payResult.getFundPay().add(payResult.getPsnAcctPay());
        payDTO.setInsuranceFee(insuranceFee.multiply(new BigDecimal("100")).intValue());
        // 目前只支持身份证号
        payDTO.setUserCardType(UserCardTypeEnum.ID_CARD.getCode());
        String md5IdCard = MD5Util.md5Encrypt32Lower(payResult.getCertno().toUpperCase());
        payDTO.setUserCardNo(md5IdCard);
        payDTO.setUserName(payResult.getPsnName());
        payDTO.setIsDept("4");
        payDTO.setSerialNo(payResult.getMiPaySerialNo());
        payDTO.setOrgNo(hospitalConfig.getHospitalCode());
        payDTO.setGmtOutCreate(DateUtils.dateTimeNow());
        JSONObject requestContent = new JSONObject();
        requestContent.put("payAuthNo", dto.getPayAuthNo());
        requestContent.put("payOrdId", payResult.getPayOrdId());
        requestContent.put("setlLatlnt", "0,0");
        payDTO.setRequestContent(requestContent.toJSONString());
        String accessToken = getAccessToken(dto.getHospitalId(), payResult.getCertno(), dto.getClientType());
        payDTO.setAccessToken(accessToken);
        payDTO.setApiKey(miHospitalMchConfig.getPayKey());
        payDTO.setSandBox(miConfig.getSandBox());
        return payDTO;
    }

    /**
     * 查询支付单接口
     *
     * @param dto - 支付查询参数
     * @return -支付查询结果
     */
    @Override
    public String queryOrder(MIPaymentDTO dto) {
        log.info("查询支付单接口业务入参={}", JSONObject.toJSONString(dto));
        // 查询订单医保支付信息
        MiPayResult payResult = this.queryMiPayResultInfo(dto.getOrderNo());
        // 查询医院医保配置信息
        MiHospitalConfig hospitalConfig = miHospitalConfigService.getMiHospitalConfigByClientType(dto.getHospitalId(), payResult.getClientType());
        MiHospitalMchConfig miHospitalMchConfig = hospitalConfig.getMchConfigList().get(0);
        PayDTO payDTO = new PayDTO();
        payDTO.setAppid(miHospitalMchConfig.getWxAppId());
        payDTO.setMchId(miHospitalMchConfig.getMchId());
        payDTO.setMedTransId(payResult.getMedTransId());
        String accessToken = getAccessTokenByPayResult(dto.getHospitalId(), payResult);
        payDTO.setAccessToken(accessToken);
        payDTO.setApiKey(miHospitalMchConfig.getPayKey());
        return PayUtil.queryOrder(payDTO);
    }

    /**
     * 申请退款接口
     *
     * @param dto - 退款参数
     * @return - 退款结果
     */
    @Override
    public String refund(MiRefundDTO dto) {
        log.info("申请退款接口业务入参={}", JSONObject.toJSONString(dto));
        // 查询订单医保支付信息
        LambdaQueryWrapper<MiPayResult> payResultWrapper = Wrappers.lambdaQuery();
        payResultWrapper
                .eq(MiPayResult::getOrderNo, dto.getOrderNo())
                .eq(MiPayResult::getOrdStas, OrdStasTypeEnum.O4.getCode())
                .last(" limit 1");
        MiPayResult payResult = checkMiPayResult(miPayResultService.getOne(payResultWrapper));
        // 查询医院医保配置信息
        MiHospitalConfig hospitalConfig = miHospitalConfigService.getMiHospitalConfigByClientType(dto.getHospitalId(), payResult.getClientType());
        MiHospitalMchConfig miHospitalMchConfig = hospitalConfig.getMchConfigList().get(0);
        RefundDTO refundDTO = new RefundDTO();
        refundDTO.setAppid(miHospitalMchConfig.getWxAppId());
        refundDTO.setMchId(miHospitalMchConfig.getMchId());
        //生成退款流水
        BusOrderRefundRecordDTO refundRecordDTO = getBusOrderRefundRecordDTO(payResult, dto);
        // 同一个hosp_out_trade_no只能使用唯一的hosp_out_refund_no
        refundDTO.setHospOutRefundNo(refundRecordDTO.getOutRefundNo());
        refundDTO.setCancelSerialNo("MIR" + DateUtils.dateTimeNow());
        JSONObject requestContent = new JSONObject();
        refundDTO.setMedTransId(payResult.getMedTransId());
        requestContent.put("payOrdId", payResult.getPayOrdId());
        requestContent.put("ref_reason", refundRecordDTO.getRefundRemark());
        refundDTO.setRequestContent(requestContent.toJSONString());
        refundDTO.setAccessToken(getAccessTokenByPayResult(dto.getHospitalId(), payResult));
        refundDTO.setApiKey(miHospitalMchConfig.getPayKey());
        Map<String, Object> result = PayUtil.refund(refundDTO);
        payResult.setOrdStas(OrdStasTypeEnum.O7.getCode());
        miPayResultService.updateById(payResult);
        if (MapUtil.isNotEmpty(result)) {
            refundRecordDTO.setRefundId((String)result.get("insurance_refund_id"));
        }
        // 保存退款记录, 未來改造成退款事件
        remoteRefundRecordService.saveRecord(refundRecordDTO);
        return "退款成功";
    }

    /**
     * 退款查询接口
     *
     * @param payResult - 医保支付信息
     * @param dto       - 退款参数
     * @return - 退款结果
     */
    private BusOrderRefundRecordDTO getBusOrderRefundRecordDTO(MiPayResult payResult, MiRefundDTO dto) {
        BusOrderRefundRecordDTO refundRecordDTO = new BusOrderRefundRecordDTO();
        refundRecordDTO.setHospitalId(dto.getHospitalId());
        refundRecordDTO.setOrderNo(payResult.getOrderNo());
        refundRecordDTO.setPromoter(dto.getPromoter());
        refundRecordDTO.setOutTradeNo(payResult.getMiPaySerialNo());
        refundRecordDTO.setOutRefundNo(getOutRefundNo());
        refundRecordDTO.setRefundType(RefundTypeEnum.FULL.name());
        BigDecimal feeSumamt = Objects.nonNull(payResult.getFeeSumamt()) ? payResult.getFeeSumamt() : BigDecimal.ZERO;
        BigDecimal otherCashAmount = Objects.nonNull(payResult.getOtherCashAmount()) ? payResult.getOtherCashAmount() : BigDecimal.ZERO;
        //支付总金额
        BigDecimal payAmount = feeSumamt.add(otherCashAmount);
        refundRecordDTO.setPayAmount(payAmount);
        refundRecordDTO.setRefundAmount(payAmount);
        refundRecordDTO.setRefundStatus(RefundStatusEnum.REFUND_SUCCESS.name());
        refundRecordDTO.setRefundSuccessTime(new Date());
        LambdaQueryWrapper<BusOrderAfterSales> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrderAfterSales::getOrderNo, dto.getOrderNo());
        lambdaQuery.orderByDesc(BusOrderAfterSales::getId);
        lambdaQuery.last(" limit 1");
        BusOrderAfterSales orderAfterSales = orderAfterSalesMapper.selectOne(lambdaQuery);
        RefundsExchangesEnum refundType = null;
        if (null != orderAfterSales){
            refundType = RefundsExchangesEnum.getTypeByCode(orderAfterSales.getAfterSalesCause());
        }
        if (Objects.isNull(refundType)) {
            refundType = RefundsExchangesEnum.four;
        }
        //退款原因
        refundRecordDTO.setRefundRemark(refundType.getInfo());
        return refundRecordDTO;
    }

    /**
     * 根据付款时的appid获取token
     * @param hospitalId - 医院ID
     * @param payResult - 医保支付信息
     * @return token
     */
    private String getAccessTokenByPayResult(Long hospitalId, MiPayResult payResult) {
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.getByType(payResult.getClientType());
        if (clientTypeEnum == null) {
            log.error("clientType is null，payResultId={}",payResult.getId());
            throw new ServiceException("支付参数有误！");
        }
        return getAccessToken(hospitalId, payResult.getCertno(),clientTypeEnum);
    }

    /**
     * 退款查询接口
     *
     * @param dto - 退款查询参数
     * @return 退款查询结果
     */
    @Override
    public String queryRefund(MiRefundDTO dto) {
        log.info("退款查询接口业务入参={}", JSONObject.toJSONString(dto));
        MiPayResult payResult = checkMiPayResult(this.queryMiPayResultInfo(dto.getOrderNo()));
        // 查询医院医保配置信息
        MiHospitalConfig hospitalConfig = miHospitalConfigService.getMiHospitalConfigByClientType(dto.getHospitalId(), payResult.getClientType());
        MiHospitalMchConfig miHospitalMchConfig = hospitalConfig.getMchConfigList().get(0);
        RefundDTO refundDTO = new RefundDTO();
        refundDTO.setAppid(miHospitalMchConfig.getWxAppId());
        refundDTO.setMchId(miHospitalMchConfig.getMchId());
        refundDTO.setMedTransId(payResult.getMedTransId());
        refundDTO.setAccessToken(getAccessTokenByPayResult(dto.getHospitalId(), payResult));
        refundDTO.setApiKey(miHospitalMchConfig.getPayKey());
        return PayUtil.queryRefund(refundDTO);
    }

    /**
     * 支付通知处理
     *
     * @param notification
     *         - 支付通知参数
     */
    @Override
    public void payNotifyHandle(MiPaymentNotification notification) {
        String medTransId = notification.getMedTransId();
        LambdaQueryWrapper<MiPayResult> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(MiPayResult::getMedTransId, medTransId);
        MiPayResult payResult = miPayResultService.getOne(lambdaQuery, false);
        if (null == payResult) {
            log.warn("未查询到医保支付单！");
            return;
        }
        paySuccessToUpdatePayOrderRecord(notification);
        if (!OrdStasTypeEnum.isPayCompleted(payResult.getOrdStas())) {
            // 修改医保支付状态
            boolean isSuccess = miPayResultService.update(new LambdaUpdateWrapper<MiPayResult>()
                    .eq(MiPayResult::getMedTransId, medTransId)
                    .eq(MiPayResult::getOrdStas, payResult.getOrdStas())
                    .set(MiPayResult::getOrdStas,OrdStasTypeEnum.O4.getCode()));
            if(isSuccess){
                //医保支付完成事件
                MIPaymentFinishEvent event = new MIPaymentFinishEvent(payResult.getId(), payResult.getOrderNo());
                miPaymentFinishProducer.send(event);
            }
        }
        // 修改订单状态
        R<?> r = remoteWxPayService.updateOrderStatus(payResult.getOrderNo());
        log.info("支付通知处理结果={},订单号={}", r, payResult.getOrderNo());
    }

    /**
     * 支付成功后更新订单记录
     * @param notification - 支付通知参数
     */
    private void paySuccessToUpdatePayOrderRecord(MiPaymentNotification notification) {
        BusPayOrder busPayOrder = new BusPayOrder();
        busPayOrder.setOutTradeNo(notification.getSerialNo());
        busPayOrder.setPayWay(PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode());
        busPayOrder.setPaySuccessTime(new Date());
        busPayOrder.setNotifyInfo(JSONObject.toJSONString(notification));
        busPayOrder.setPayRemark(notification.getResponseContent());
        busPayOrder.setUpdateTime(new Date());
        busPayOrder.setStatus(BusPayOrderStatusEnum.PAYED.getValue());
        remotePayOrderService.insertOrUpdatePayOrder(busPayOrder);
    }

    /**
     * 校验订单信息
     * @param dto - 订单号
     * @return payResult
     */
    private MiPayResult checkMiPayResult(MiPayResult dto) {
        if (null == dto) {
            throw new ServiceException("订单不存在！");
        }
        return dto;
    }

    /**
     * 获取token
     *
     * @param hospitalId 医院ID
     * @return recordusers
     */
    private String getAccessToken(Long hospitalId, String idCard,ClientTypeEnum clientType) {
        // 开启医保录制模式-小程序和公众号同时生效
        MiVideoRecordConfig.RecordUser recordUser = videoRecordConfig.getUserByIdCard(hospitalId, idCard,clientType.name());
        WxAccessTokenDTO wxAccessTokenDTO = new WxAccessTokenDTO();
        wxAccessTokenDTO.setClientType(clientType);
        if (null != recordUser && recordUser.getOpen()) {
            wxAccessTokenDTO.setHospitalId(hospitalId);
            wxAccessTokenDTO.setAppid(recordUser.getAppid());
            wxAccessTokenDTO.setAppSecret(recordUser.getAppSecret());
            R<String> token = remoteBaseWxService.getAccessToken(wxAccessTokenDTO, SecurityConstants.INNER);
            if (token.getCode() == HttpStatus.SUCCESS){
                return token.getData();
            }
            throw new ServiceException(token.getMsg());
        }
        wxAccessTokenDTO.setHospitalId(hospitalId);
        // 正常流程
        R<String> r = remoteBaseWxService.getAccessToken(wxAccessTokenDTO,SecurityConstants.INNER);
        if (Constants.SUCCESS == r.getCode()) {
            return r.getData();
        } else {
            throw new ServiceException(r.getMsg());
        }
    }

    /**
     * 查询医保订单支付信息
     *
     * @param orderNo - 订单号
     * @return 医保订单支付信息
     */
    private MiPayResult queryMiPayResultInfo(String orderNo) {
        LambdaQueryWrapper<MiPayResult> payResultWrapper = Wrappers.lambdaQuery();
        payResultWrapper
                .eq(MiPayResult::getOrderNo, orderNo)
                .orderByDesc(MiPayResult::getCreateTime)
                .last(" limit 1");
        return checkMiPayResult(miPayResultService.getOne(payResultWrapper));
    }

    /**
     * 校验订单信息
     *
     * @param orderNo - 订单号
     */
    private BusOrder checkOrder(String orderNo) {
        LambdaQueryWrapper<BusOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(BusOrder::getOrderNo, orderNo)
                .last(" limit 1");
        BusOrder order = orderMapper.selectOne(lambdaQuery);
        if (ObjectUtil.isNull(order)) {
            throw new ServiceException("订单不存在！");
        }
        // 更新订单支付方式为医保支付
        BusOrder busOrder = new BusOrder();
        busOrder.setPayWay(PaysTypeEnum.WECHAT_INSURANCE_PAY.getCode());
        LambdaUpdateWrapper<BusOrder> lambdaUpdate = Wrappers.lambdaUpdate();
        lambdaUpdate.eq(BusOrder::getOrderNo, orderNo);
        orderMapper.update(busOrder, lambdaUpdate);
        return order;
    }

    /**
     * 获取退款流水号
     *
     * @return refundNo
     */
    private String getOutRefundNo() {
        return "MIREF" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + RandomStringUtils.randomAlphanumeric(3);
    }

}
