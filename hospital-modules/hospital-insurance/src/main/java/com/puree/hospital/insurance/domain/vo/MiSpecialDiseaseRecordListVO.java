package com.puree.hospital.insurance.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * 门特门慢(特殊病种)医保结算单记录 列表返回VO
 * <AUTHOR>
 * @date 2024-11-26 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class MiSpecialDiseaseRecordListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Integer id;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 医保支付ID
     */
    private Long payResultId;

    /**
     * 人员编号
     */
    private String psnNo;

    /**
     * 本次结算ID
     */
    private String setlId;

    /**
     * 患者姓名
     */
    private String familyName;

    /**
     * 患者性别（NONE.未知的性别  MALE.男  FEMALE.女  UNSTATED.未说明的性别）
     */
    private String familySex;

    /**
     * 患者年龄
     */
    private BigDecimal familyAge;

    /**
     * 患者证件号码
     */
    private String certno;

    /**
     * 身份证件类型  id_card.居民身份证  residence.居民户口簿 passport.护照  officer.军官证  driver_license.驾驶证 hk_id.港澳居民来往内地通行证 tw_id.台湾居民来往内地通行证 other.其他
     */
    private String psnCertType;

    /**
     * 科室ID
     */
    private Long departmentId;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 门特门慢临床诊断
     */
    private String specialDiseaseInfo;

    /**
     * 结算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date setlTime;

    /**
     * 申报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date declareTime;

    /**
     * 结算单是否已上传：NOT_UPLOAD.未上传 UPLOADED.已上传 FAIL_UPLOAD.上传失败
     */
    private String uploadStatus;

    public String getPsnCertType(){
        return Optional.ofNullable(IdCardTypeEnum.getByCode(psnCertType))
                .map(IdCardTypeEnum::getLabel)
                .orElse(null);
    }

}