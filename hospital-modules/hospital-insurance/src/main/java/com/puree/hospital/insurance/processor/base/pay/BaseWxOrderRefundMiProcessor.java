package com.puree.hospital.insurance.processor.base.pay;

import com.puree.hospital.common.core.utils.SpringUtils;
import com.puree.hospital.insurance.domain.dto.MiRefundDTO;
import com.puree.hospital.insurance.domain.dto.MiPayDTO;
import com.puree.hospital.insurance.domain.model.conext.FixedInsBusinessParam;
import com.puree.hospital.insurance.processor.IMiProcessor;
import com.puree.hospital.insurance.processor.IMiProcessorChain;
import com.puree.hospital.insurance.domain.model.conext.MiProcessContext;
import com.puree.hospital.insurance.service.IBusPayService;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 通用的微信退款
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/11 16:11
 */
@Slf4j
public class BaseWxOrderRefundMiProcessor implements IMiProcessor<FixedInsBusinessParam> {

    private final IBusPayService payService;

    public BaseWxOrderRefundMiProcessor() {
        this.payService = SpringUtils.getBean(IBusPayService.class);
    }

    @Override
    public void process(MiProcessContext<FixedInsBusinessParam> context,
                        IMiProcessorChain<FixedInsBusinessParam> processorChain) {
        MiPayDTO payDTO = context.getBusinessParam().getPayDTO();
        MiRefundDTO dto = new MiRefundDTO();
        dto.setHospitalId(payDTO.getHospitalId());
        dto.setOrderNo(payDTO.getTotalOrderNo());
        dto.setMchType("2");
        dto.setPromoter(payDTO.getOperator());
        String refund = payService.refund(dto);
        log.info("医保退款结果={}", refund);
        processorChain.doChain(context);
    }
}
