package com.puree.hospital.insurance.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.insurance.domain.MiPayResult;
import com.puree.hospital.insurance.domain.MiPaymentNotification;
import com.puree.hospital.insurance.domain.dto.MIPaymentDTO;
import com.puree.hospital.insurance.domain.dto.MiRefundDTO;
import com.puree.hospital.insurance.infrastructure.constants.WXPayConstant;
import com.puree.hospital.insurance.infrastructure.enums.mi.OrdStasTypeEnum;
import com.puree.hospital.insurance.infrastructure.utils.pay.SignUtil;
import com.puree.hospital.insurance.infrastructure.utils.pay.XMLUtil;
import com.puree.hospital.insurance.service.IBusPayService;
import com.puree.hospital.insurance.service.IMiPayResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/5/5 11:36
 * 医保支付
 */
@RestController
@RequestMapping("/mi")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MIPaymentController extends BaseController {
    private final IBusPayService busPayService;
    private final IMiPayResultService miPayResultService;

    /**
     * 统一下单接口
     * @param dto
     * @return
     */
    @PostMapping("/unified/order")
    public AjaxResult unifiedOrder(@RequestBody MIPaymentDTO dto) {
        return AjaxResult.success(busPayService.unifiedOrder(dto));
    }

    /**
     * 查询支付单接口
     * @param dto
     * @return
     */
    @PostMapping("/query/order")
    public AjaxResult queryOrder(@RequestBody MIPaymentDTO dto) {
        return AjaxResult.success(busPayService.queryOrder(dto));
    }

    /**
     * 支付结果通知接口
     * @param request
     * @param response
     */
    @PostMapping("/pay/notify")
    public void payNotify(HttpServletRequest request, HttpServletResponse response) {
        logger.debug("医保支付回调成功！");
        String result = SignUtil.readData(request);
        try {
            Map<String, Object> map = XMLUtil.doXMLParse(result);
            // map转对象
            MiPaymentNotification notification = JSONObject.parseObject(JSONObject.toJSONString(map), MiPaymentNotification.class);
            logger.info("微信支付返回入参={}", JSONObject.toJSONString(map));
            if (WXPayConstant.SUCCESS.equals(notification.getReturnCode())) {
                busPayService.payNotifyHandle(notification);
                String responseXml = getResponseXml(notification.getAttach());
                // 响应请求结果
                response.setHeader("Content-type", "application/xml");
                response.getOutputStream().write(responseXml.getBytes(StandardCharsets.UTF_8));
                response.flushBuffer();
            } else {
                String returnMsg = notification.getReturnMsg();
                logger.error("微信医保支付结果通知异常={}", returnMsg);
            }
        } catch (Exception e) {
            logger.error("解析xml异常", e);
        }
    }

    /**
     * 回复微信医保支付参数组装
     * @param key 向医保支付平台人工申请获取的key
     * @return xml字符串
     */
    private String getResponseXml(String key) {
        // 封装微信医保支付参数
        SortedMap<String,Object> resultMap = new TreeMap<>();
        resultMap.put("return_code", WXPayConstant.SUCCESS);
        resultMap.put("result_code", WXPayConstant.SUCCESS);
        resultMap.put("nonce_str", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
        // key:向医保支付平台人工申请获取
        String sign = SignUtil.createSign(resultMap, key);
        resultMap.put("sign", sign);
        return XMLUtil.getRequestXml(resultMap);
    }

    /**
     * 申请退款接口
     * @param dto
     * @return
     */
    @PostMapping("/refund")
    public AjaxResult refund(@RequestBody MiRefundDTO dto) {
        dto.setPromoter(SecurityUtils.getUsername());
        return AjaxResult.success(busPayService.refund(dto));
    }

    /**
     * 退款查询接口
     * @param dto
     * @return
     */
    @PostMapping("/query/refund")
    public AjaxResult queryRefund(MiRefundDTO dto) {
        return AjaxResult.success(busPayService.queryRefund(dto));
    }

    /**
     * 查询订单是否医保支付
     * @param orderNo 订单编号
     * @return
     */
    @GetMapping("/pay/result")
    public AjaxResult payResult(@RequestParam("orderNo") String orderNo) {
        boolean payResult = false;
        LambdaQueryWrapper<MiPayResult> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(MiPayResult::getOrderNo, orderNo)
                .eq(MiPayResult::getOrdStas, OrdStasTypeEnum.O4.getCode())
                .last(" limit 1");
        MiPayResult result = miPayResultService.getOne(lambdaQuery);
        if (StringUtils.isNotNull(result)) {
            payResult = true;
        }
        return AjaxResult.success(payResult);
    }


    /**
     * 查询医保预结算信息
     * @param orderNo
     * @return
     */
    @GetMapping("/query/pay/result")
    public R<MiPayResult> queryPayResult(@RequestParam("orderNo") String orderNo) {
        LambdaQueryWrapper<MiPayResult> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery
                .eq(MiPayResult::getOrderNo, orderNo)
                .eq(MiPayResult::getOrdStas, OrdStasTypeEnum.O4.getCode())
                .last(" limit 1");
        MiPayResult result = miPayResultService.getOne(lambdaQuery);
        return R.ok(result);
    }

}
