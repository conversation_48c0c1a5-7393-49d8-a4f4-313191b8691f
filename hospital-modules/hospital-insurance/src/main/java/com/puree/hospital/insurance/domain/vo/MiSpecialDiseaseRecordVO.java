package com.puree.hospital.insurance.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.AmdCondTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.DischargeWayEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.InoutDiagTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.MainDiagFlagEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.PatientRelationTypeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.ProfessionCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * 门特门慢(特殊病种)医保结算单记录 详情返回VO
 * <AUTHOR>
 * @date 2024-11-26 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class MiSpecialDiseaseRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * 医保编号
     */
    private String miNo;

    /**
     * 就诊ID
     */
    private String mdtrtId;

    /**
     * {@link ProfessionCodeEnum}
     * 职业：11 国家公务员 13 专业技术人员 17 职员 21 企业管理人员 24 工人 27 农民  31 学生  37 现役军人  51 自由职业者  54 个体经营者 70 无业人员 80 退（离）休人员 90 其他
     */
    private String profession;

    /**
     * 国籍：CNH.中国
     */
    private String nation;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactTel;

    /**
     * {@link PatientRelationTypeEnum}
     * 与患者关系：01本人  10配偶  20子  30女  40孙子、孙女、外孙子、外孙女  50父母  60祖父母、外祖父母  70兄、弟、姐、妹  97其他
     */
    private String contactRelation;

    /**
     * 联系人地址
     */
    private String contactAddr;

    /**
     * 就诊日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mdtrtDate;

    /**
     * 入院时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date admissionTime;

    /**
     * 出院时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dischargeTime;

    /**
     * 特级护理天数
     */
    private Integer spgaNurscareDays;

    /**
     * {@link DischargeWayEnum}
     * 离院方式：1.医嘱离院 2.医嘱转院 3.医嘱转社区卫生服务机构/乡镇卫生院 4.非医嘱离院 5.死亡 9.其他
     */
    private String dischargeWay;

    /**
     * {@link InoutDiagTypeEnum}
     * 患者出入院诊断类别：in_diag、入院诊断  out_diag、出院诊断
     */
    private String inoutDiagType;

    /**
     * {@link AmdCondTypeEnum}
     * 入院病情类型：1.有  2.临床未确定  3.情况不明  4.无
     */
    private String admCondType;

    /**
     * {@link MainDiagFlagEnum}
     * 主诊断标志 exist.否  inexist.是
     */
    private String maindiagFlag;

    /**
     * 业务流水号
     */
    private String bizSn;

    /**
     * 票据代码
     */
    private String billCode;

    /**
     * 票据号码
     */
    private String billNo;

    /**
     * 医疗机构填报部门：医院医保部门
     */
    private String medinsFillDept;

    /**
     * 医疗机构填报人
     */
    private String medinsFillPsn;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 医保支付ID
     */
    private Long payResultId;

    /**
     * 人员编号
     */
    private String psnNo;

    /**
     * 本次结算ID
     */
    private String setlId;

    /**
     * 患者姓名
     */
    private String familyName;

    /**
     * 患者性别（NONE.未知的性别  MALE.男  FEMALE.女  UNSTATED.未说明的性别）
     */
    private String familySex;

    /**
     * 患者年龄
     */
    private BigDecimal familyAge;

    /**
     * 患者证件号码
     */
    private String certno;

    /**
     * 身份证件类型  id_card.居民身份证  residence.居民户口簿 passport.护照  officer.军官证  driver_license.驾驶证 hk_id.港澳居民来往内地通行证 tw_id.台湾居民来往内地通行证 other.其他
     */
    private String psnCertType;

    /**
     * 科室ID
     */
    private Long departmentId;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 门特门慢临床诊断(json数组)
     */
    private String specialDiseaseInfo;

    /**
     * 结算时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date setlTime;

    /**
     * 申报时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date declareTime;

    /**
     * 清单流水号
     */
    private String setlNo;

    /**
     * 病案号
     */
    private String medcasNo;

    /**
     * 结算单是否已上传：NOT_UPLOAD.未上传 UPLOADED.已上传 FAIL_UPLOAD.上传失败
     */
    private String uploadStatus;

    public String getProfession(){
        return Optional.ofNullable(ProfessionCodeEnum.getByCode(profession))
                .map(ProfessionCodeEnum::getName)
                .orElse(null);
    }

    public String getContactRelation(){
        return Optional.ofNullable(PatientRelationTypeEnum.getByCode(contactRelation))
                .map(PatientRelationTypeEnum::getName)
                .orElse(null);
    }

    public String getDischargeWay(){
        return Optional.ofNullable(DischargeWayEnum.getByCode(dischargeWay))
                .map(DischargeWayEnum::getName)
                .orElse(null);
    }

    public String getInoutDiagType(){
        return Optional.ofNullable(InoutDiagTypeEnum.getByName(inoutDiagType))
                .map(InoutDiagTypeEnum::getDesc)
                .orElse(null);
    }

    public String getAdmCondType(){
        return Optional.ofNullable(AmdCondTypeEnum.getByCode(admCondType))
                .map(AmdCondTypeEnum::getName)
                .orElse(null);
    }

    public String getNation(){
        return "中国";
    }
}