package com.puree.hospital.insurance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.api.RemotePatientService;
import com.puree.hospital.app.api.RemotePrescriptionService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.app.api.model.BusPrescription;
import com.puree.hospital.app.api.model.SpecialDiseaseInfoVO;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospital;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.enums.IdCardTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.insurance.api.model.UniformRequest;
import com.puree.hospital.insurance.config.InsuranceSpecialDiseaseUploadProperties;
import com.puree.hospital.insurance.domain.MiDiagnosisRel;
import com.puree.hospital.insurance.domain.MiPayResult;
import com.puree.hospital.insurance.domain.MiSpecialDiseaseExtraData;
import com.puree.hospital.insurance.domain.MiSpecialDiseaseRecord;
import com.puree.hospital.insurance.domain.dto.FundSettleUploadDTO;
import com.puree.hospital.insurance.domain.preprocessor.MiResponse;
import com.puree.hospital.insurance.domain.query.MiSpecialDiseaseRecordListQuery;
import com.puree.hospital.insurance.domain.vo.MiSpecialDiseaseRecordListVO;
import com.puree.hospital.insurance.domain.vo.MiSpecialDiseaseRecordVO;
import com.puree.hospital.insurance.infrastructure.constants.MiConstants;
import com.puree.hospital.insurance.infrastructure.enums.mi.ProfessionCodeEnum;
import com.puree.hospital.insurance.infrastructure.enums.mi.SpecialDiseaseUploadStatusEnum;
import com.puree.hospital.insurance.mapper.MiDiagnosisRelMapper;
import com.puree.hospital.insurance.mapper.MiHospitalConfigMapper;
import com.puree.hospital.insurance.mapper.MiPayResultMapper;
import com.puree.hospital.insurance.mapper.MiSpecialDiseaseRecordMapper;
import com.puree.hospital.insurance.service.IMiPreProcessService;
import com.puree.hospital.insurance.service.IMiSpecialDiseaseRecordService;
import com.puree.hospital.order.api.RemoteBusConsultationOrderService;
import com.puree.hospital.order.api.model.BusConsultationOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 门特门慢(特殊病种)医保结算单记录 服务实现
 * <AUTHOR>
 * @date 2024/11/26 14:54
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MiSpecialDiseaseRecordServiceImpl implements IMiSpecialDiseaseRecordService {

    private final MiHospitalConfigMapper miHospitalConfigMapper;
    private final MiSpecialDiseaseRecordMapper miSpecialDiseaseRecordMapper;
    private final MiPayResultMapper miPayResultMapper;
    private final RemotePatientService remotePatientService;
    private final RemotePrescriptionService remotePrescriptionService;
    private final RemoteBusConsultationOrderService remoteBusConsultationOrderService;
    private final RemoteHospitalService remoteHospitalService;
    private final IMiPreProcessService miPreProcessService;
    private final MiDiagnosisRelMapper miDiagnosisRelMapper;

    private static final String DATETIME_FORMAT = "yyyyMMddHHmmssSSS";

    @Resource
    private InsuranceSpecialDiseaseUploadProperties insuranceSpecialDiseaseUploadProperties;


    /**
     * 分页查询 门特门慢(特殊病种)医保结算单记录列表
     * @param query 查询参数
     * @return 记录列表
     */
    @Override
    public List<MiSpecialDiseaseRecordListVO> pgagList(MiSpecialDiseaseRecordListQuery query) {
        PageUtil.startPage();
        query.setHospitalId(SecurityUtils.getHospitalId());
        List<MiSpecialDiseaseRecord> records = miSpecialDiseaseRecordMapper.getList(query);

        List<MiSpecialDiseaseRecordListVO> vos = records.stream()
                .map(item -> BeanUtil.copyProperties(item, MiSpecialDiseaseRecordListVO.class))
                .collect(Collectors.toList());

        return PageUtil.buildPage(records, vos);
    }

    /**
     * 根据记录id，查询门特门慢(特殊病种)医保结算单记录详情
     *
     * @param id 记录id
     * @return 记录详情
     */
    @Override
    public MiSpecialDiseaseRecordVO infoById(Integer id) {
        MiSpecialDiseaseRecord record = miSpecialDiseaseRecordMapper.getById(id);
        log.info("根据id：{}，获取门特门慢结算单记录：{}", id, record);
        Assert.notNull(record, String.format("记录%s不存在", id));
        MiSpecialDiseaseRecordVO vo = BeanUtil.copyProperties(record, MiSpecialDiseaseRecordVO.class);
        vo.setPsnCertType(IdCardTypeEnum.getByCode(record.getPsnCertType()).getLabel());
        //获取医保支付信息
        MiPayResult miPayResult = miPayResultMapper.selectById(record.getPayResultId());
        Assert.notNull(record, String.format("医保支付订单不存在：%s", id));

        //患者其他信息组装
        MiSpecialDiseaseExtraData extraData = assembleExtraData(record, miPayResult);
        BeanUtils.copyProperties(extraData, vo);
        return vo;
    }

    /**
     * 患者信息组装
     * @param record 门特门慢结算单记录
     * @param miPayResult 医保支付结果记录
     * @return 组装患者信息
     */
    private MiSpecialDiseaseExtraData assembleExtraData(MiSpecialDiseaseRecord record, MiPayResult miPayResult){
        MiSpecialDiseaseExtraData extraData = record.getExtraData();
        // 医保编号和人员编号相同
        extraData.setMiNo(record.getPsnNo());
        extraData.setMdtrtId(miPayResult.getMdtrtId());
        BusPatientFamilyVo familyInfo = remotePatientService.queryPatientInfo(extraData.getFamilyId()).getData();
        Assert.notNull(familyInfo, String.format("就诊人不存在：%s", extraData.getFamilyId()));
        extraData.setProfession(ProfessionCodeEnum.getCodeByName(familyInfo.getProfession()));
        extraData.setContactAddr(familyInfo.getDetailAddress());
        extraData.setEmpName(familyInfo.getWorkUnit());
        //获取处方中的问诊订单信息
        List<BusPrescription> prescriptions = remotePrescriptionService.getMiRx(Arrays.asList(miPayResult.getRxNo().split(","))).getData();
        Assert.notEmpty(prescriptions, "关联处方不存在");
        Long consultOrderId = prescriptions.stream()
                .map(BusPrescription::getConsultationOrderId)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        // 出院/入院时间，取问诊单中的开始/结束时间
        if(consultOrderId != null){
            BusConsultationOrder consultationOrder = remoteBusConsultationOrderService.getBusConsultationOrderById(consultOrderId).getData();
            log.info("获取问诊订单详情：{}", consultationOrder);
            extraData.setAdmissionTime(consultationOrder.getCreateTime());
            extraData.setDischargeTime(consultationOrder.getCompleteTime());
        }
        extraData.setClinicalDiagnosis(prescriptions.get(0).getClinicalDiagnosis());
        //医生医保编码
        String doctorMiNo = miHospitalConfigMapper.getDoctorMiNo(prescriptions.get(0).getDoctorId());
        Assert.notEmpty(doctorMiNo, String.format("医生%s未在医保局备案，不支持结算单上传", prescriptions.get(0).getDoctorId()));
        extraData.setDoctorMiNo(doctorMiNo);
        // 医疗机构填报人：医院名称
        BusHospital hospital = remoteHospitalService.getHospitalInfo(record.getHospitalId()).getData();
        log.info("根据医院id：{}，获取医院信息：{}", record.getHospitalId(), hospital);
        Assert.notNull(familyInfo, String.format("就诊人不存在：%s", extraData.getFamilyId()));
        extraData.setMedinsFillPsn(hospital.getHospitalName());
        // 执行临时方案 票据代码写固定值为“0”
        extraData.setBillCode("0");
        // 票据号码最大长度20
        extraData.setBillNo(String.format("%s%s", DateUtil.format(new Date(), DATETIME_FORMAT), RandomUtil.randomNumbers(3)));
        log.info("门特门慢结算单上传-患者信息组装：{}", extraData);
        return extraData;
    }

    /**
     * 医保结算单上传
     *
     * @param id 记录id
     */
    @Override
    public void upload(Integer id) {
        MiSpecialDiseaseRecord record = miSpecialDiseaseRecordMapper.getById(id);
        Assert.notNull(record, String.format("记录%s不存在", id));
        Assert.isFalse(SpecialDiseaseUploadStatusEnum.hasUploaded(record.getUploadStatus()), "结算单已上传，请勿重复上传");
        try{
            //结算单上传至医保
            fundSettleUpload(record);
        }catch (Exception e){
            record.setUploadStatus(SpecialDiseaseUploadStatusEnum.FAIL_UPLOAD.getName());
            miSpecialDiseaseRecordMapper.updateById(record);
            throw e;
        }
    }

    /**
     * 结算单上传至医保
     * @param record 结算单记录
     */
    private void fundSettleUpload(MiSpecialDiseaseRecord record){
        MiPayResult miPayResult = miPayResultMapper.selectById(record.getPayResultId());
        //门特门慢结算id必传：如果结算id不存在，需再次获取结算id
        if(StringUtils.isEmpty(record.getSetlId())){
            String settleId = getSettleId(record.getHospitalId(), miPayResult.getOrderNo());
            Assert.notNull(settleId, String.format("获取结算ID失败：%s", miPayResult.getOrderNo()));
        }
        //患者其他信息组装
        MiSpecialDiseaseExtraData extraData = assembleExtraData(record, miPayResult);
        Assert.notEmpty(extraData.getContactAddr(), "患者详细地址缺失");
        FundSettleUploadDTO uploadDTO = BeanUtil.copyProperties(record, FundSettleUploadDTO.class);
        BeanUtils.copyProperties(extraData, uploadDTO);
        //业务科室获取
        List<String> deptCodeList = miHospitalConfigMapper.getDeptCode(String.valueOf(record.getDepartmentId()), record.getHospitalId());
        if (CollectionUtil.isEmpty(deptCodeList) || deptCodeList.size() > 1) {
            throw new ServiceException("当前订单科室暂不支持医保支付");
        }
        uploadDTO.setDeptCode(String.format("A%s", deptCodeList.get(0)));
        uploadDTO.setInsuranceSpecialDiseaseUploadProperties(insuranceSpecialDiseaseUploadProperties);
        //诊断编码处理
        diagnosisCodeHandle(uploadDTO);
        log.info("门特门慢结算单上传-uploadDTO对象组装：{}", uploadDTO);
        //结算单上传
        MiResponse miResponse = miPreProcessService.fundSettleUpload(uploadDTO);
        log.info("门特门慢结算单上传-结果信息：{}", miResponse);
        if (!MiResponse.SUCCESS.equals(miResponse.getInfcode())) {
            log.error("门特门慢结算单上传失败，记录id：{}，上传结果信息：{}", record.getId(), miResponse);
            throw new ServiceException(String.format("门特门慢结算单上传失败：%s", miResponse.getErr_msg()));
        }
        //从结算单上传结果中获取清单流水号
        String setlNo = Optional.ofNullable(new JSONObject(miResponse.getOutput()).getJSONObject(MiConstants.DATA))
                .map(data -> data.getStr("setl_list_id"))
                .orElse(null);
        record.setSetlNo(setlNo);
        record.setUploadStatus(SpecialDiseaseUploadStatusEnum.UPLOADED.getName());
        record.setDeclareTime(new Date());
        miSpecialDiseaseRecordMapper.updateById(record);
    }

    /**
     *
     * 诊断编码处理
     * @param uploadDTO 诊断信息和门特门慢病种信息
     */
    private void diagnosisCodeHandle(FundSettleUploadDTO uploadDTO) {
        //查询门门特门慢诊断代码
        SpecialDiseaseInfoVO specialDiseaseInfoVO = JSONUtil.parseArray(uploadDTO.getSpecialDiseaseInfo()).getBean(0, SpecialDiseaseInfoVO.class);
        MiDiagnosisRel miDiagnosisRel = miDiagnosisRelMapper.selectOne(new LambdaQueryWrapper<MiDiagnosisRel>().eq(MiDiagnosisRel::getSpecialCode, specialDiseaseInfoVO.getOpspDiseCode()));
        if(miDiagnosisRel == null){
            log.error("门特门慢-医保诊断代码查询失败：{}", uploadDTO);
            throw new ServiceException(String.format("医保诊断代码查询失败：%s", specialDiseaseInfoVO.getOpspDiseName()));
        }
        uploadDTO.setOtpWmDise(miDiagnosisRel.getYbName());
        uploadDTO.setWmDiseCode(miDiagnosisRel.getYbCode());

        //查询临床诊断代码
        List<FundSettleUploadDTO.ClinicDiagnosisDTO> clinicList = JSONUtil.toList(uploadDTO.getClinicalDiagnosis(), FundSettleUploadDTO.ClinicDiagnosisDTO.class);
        List<String> icdCodeList = clinicList.stream()
                .map(FundSettleUploadDTO.ClinicDiagnosisDTO::getIcdCode)
                .collect(Collectors.toList());
        List<MiDiagnosisRel> miDiagnosisRelList = miDiagnosisRelMapper.selectList((new LambdaQueryWrapper<MiDiagnosisRel>().in(MiDiagnosisRel::getGlCode, icdCodeList)));
        Map<String,String> codeMap = miDiagnosisRelList.stream().collect(Collectors.toMap(MiDiagnosisRel::getGlCode, MiDiagnosisRel::getYbCode));

        for (FundSettleUploadDTO.ClinicDiagnosisDTO clinic : clinicList) {
            String ybCode = codeMap.get(clinic.getIcdCode());
            if(StringUtils.isNotEmpty(ybCode)){
                clinic.setIcdCode(ybCode);
            }
        }
        uploadDTO.setClinicalDiagnosis(JSONUtil.toJsonStr(clinicList));
    }

    /**
     * 根据总订单号获取医保支付结算id
     * @param hospitalId 医院id
     * @param totalOrderNo 医保支付总订单号
     * @return 结算id
     */
    @Override
    public String getSettleId(Long hospitalId, String totalOrderNo){
        log.info("获取结算ID，医院ID：{}，医保支付订单号：{}", hospitalId, totalOrderNo);
        // 构建医保支付结果查询的请求参数：6301医保接口
        UniformRequest request = new UniformRequest();
        request.setHospitalId(hospitalId);
        JSONObject params = new JSONObject().putOpt("totalOrderNo", totalOrderNo);
        request.setParams(params);
        try{
            AjaxResult result = miPreProcessService.getPayInfo(request);
            log.info("门特门慢结算单-根据总订单号：{}，查询医保支付结果：{}", totalOrderNo, result);
            return Optional.ofNullable(new JSONObject(result.getData()).getJSONObject("extData"))
                    .map(extData -> extData.getJSONObject("setlinfo"))
                    .map(setlInfo -> setlInfo.getStr("setlId"))
                    .orElse(null);
        }catch (Exception e){
            log.error("门特门慢结算单-医保支付结果查询失败 ", e);
        }
        return null;
    }


}
