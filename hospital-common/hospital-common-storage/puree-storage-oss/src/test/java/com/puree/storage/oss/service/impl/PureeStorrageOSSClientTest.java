//package com.puree.storage.oss.service.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import com.aliyun.oss.OSS;
//import com.aliyun.oss.OSSClientBuilder;
//import com.aliyun.oss.model.PutObjectRequest;
//import com.aliyuncs.exceptions.ClientException;
//import com.puree.storage.core.config.PureeStorageMapProperties;
//import com.puree.storage.core.config.PureeStorageProperties;
//import com.puree.storage.core.config.StorageSecurityTokenServiceProperties;
//import com.puree.storage.core.model.StorageObjectModel;
//import com.puree.storage.core.model.StoragePutResponse;
//import com.puree.storage.core.model.StorageSecurityTokenService;
//import com.puree.storage.core.service.impl.PureeStorageClientServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.io.File;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.UUID;
//
//import static org.mockito.Mockito.when;
//
///**
// * @ClassName PureeStorrageOSSClientTest
// * <AUTHOR>
// * @Description TODO
// * @Date 2024/9/2 16:02
// * @Version 1.0
// */
//@Slf4j
//@ExtendWith(MockitoExtension.class)
//public class PureeStorrageOSSClientTest {
//
//    @Mock
//    private PureeStorageProperties properties;
//
//    @InjectMocks
//    private PureeStorageOSSClient clientUtil;
//
//    @InjectMocks
//    private PureeStorageClientServiceImpl pureeStorageOSSClient;
//
//    public void aliConfig() {
//        when(properties.getEndpoint()).thenReturn("oss-cn-shenzhen.aliyuncs.com");
//        when(properties.getAccessKeyId()).thenReturn("LTAI5tLtZsWnUimPsqWeBLCn");
//        when(properties.getAccessKeySecret()).thenReturn("******************************");
//        when(properties.getBucketName()).thenReturn("puree-test");
//    }
//
//
//    public void stsConfig() {
//        Map<String, StorageSecurityTokenServiceProperties> sts = new HashMap<>();
//        StorageSecurityTokenServiceProperties pr = new StorageSecurityTokenServiceProperties();
//        pr.setName("test");
//        pr.setAccessKeyId("LTAI5t5uLCXCeDsFfog6Zj4L");
//        pr.setAccessKeySecret("******************************");
//        pr.setDurationSeconds(900L);
//        pr.setRegionId("cn-shenzhen");
//        pr.setRoleArn("acs:ram::1755937306429127:role/testossstsrole");
//        pr.setActionStr(CollUtil.newArrayList("oss:PutObject"));
//        pr.setResourceStr(CollUtil.newArrayList("acs:oss:*:*:puree-test/healthReport"));
//
//        sts.put("test", pr);
//        //sts
//        when(properties.getSts()).thenReturn(sts);
//    }
//
//
//    public void busMapConfig() {
//        Map<String, PureeStorageMapProperties> busMap = new HashMap<>();
//        PureeStorageMapProperties pr = new PureeStorageMapProperties();
//        pr.setBusinessName("test");
//        pr.setDirectoryName("healthReport");
////        pr.setFilterNames(CollUtil.newArrayList("test"));
//
//        busMap.put("test", pr);
//        //sts
//        when(properties.getBusMap()).thenReturn(busMap);
//    }
//
//    @Test
//    public void getSts() throws ClientException {
//        stsConfig();
//        StorageSecurityTokenService test = clientUtil.getSecurityTokenService("test");
//
//        log.info("securityToken:{}", test.getSecurityToken());
//        log.info("getAccessKeySecret:{}", test.getAccessKeySecret());
//        log.info("getAccessKeyId:{}", test.getAccessKeyId());
//        log.info("getExpiration:{}", test.getExpiration());
//    }
//
//    @Test
//    public void setSts() {
//
//        OSS ossClient = new OSSClientBuilder().build("oss-cn-shenzhen-internal.aliyuncs.com"
//                , "STS.NUd4ZbpKL2hT8hy7D75hvdXsw"
//                , "55BSS9HkQvHWHM6hJnMgKe4NHHwDxWLFRwv9A8s9Fcmt"
//                , "CAIS8wJ1q6Ft5B2yfSjIr5bRf+DWnZRthaq/Ok7I00RiOedai53YlTz2IHhMf3FvB+wdsP4ymW5Z7P0TlqBoV4QAWUvHYM0oXzysF/f7MeT7oMWQweEumv/MQBqOaXPS2MvVfJ+2Lrf0ceusbFbpjzJ6xaCAGxypQ12iN+/u6/tgdc9FZhSkSjBECdxKXCAAzvUXLnzML/2gHwf3i27LdipStxF7lHl05NbwoICV4QGMi0bhmK1H5dbpP4KpYoxwMYxjFNe41ehqdpDMyClK918I1t8v0/EZqGyY5YnDXwMAuk/ZCYeOrI0zdj0eT7MhBqtJoML7kfBFoeHJn+z1sU0VZboNDHqDH976nZaaSbnyZ8xfabvgJ3PWycuTLYPytw40zpfkx6uCnLHLQ10H6nxEIlmyQsfPc+Db+CEkP+PMVi1iZBCRY4F0GXauZjnpDfzQmk17cYRHJGhjXDU1lgfqCaQ9HmIBDRUJTtj6Q+0Gg0X098uA1RqAAV6ePxGYKPcQfMxYmx4Nm2h5ZznOfMNSS8dK6mnCBjis1Bx9hOoKwIR2yefyr9yykqCl0pSAn3gUs+qkezBzz9bVUOCi05y/UP4aWJjaMLi21FFB3O4BHL7E1YNyLB3KRlbOFq2aARucdxoE6NnFz3NhKQqRsghF4qhIUHQ9r850IAA="
//        );
//
//        String uuid = UUID.randomUUID().toString();
//        PutObjectRequest putObjectRequest = new PutObjectRequest("puree-test", "healthReport/24/09/05/" + uuid + ".jpg", new File("C:\\Users\\<USER>\\Downloads\\test.png"));
//
//
//        ossClient.putObject(putObjectRequest);
//    }
//
//    /**
//     * @Param
//     * @Return void
//     * @Description 测试下载
//     * <AUTHOR>
//     * @Date 2024/9/2 17:38
//     **/
//    @Test
//    public void getObject() {
//        aliConfig();
//        StorageObjectModel object = clientUtil.getObject(null, "healthReport/351de403f4534665b27eaeef56a0d049.pdf");
//        log.info("o:{}", object);
//    }
//
//    /**
//     * @Param
//     * @Return void
//     * @Description 测试上传
//     * <AUTHOR>
//     * @Date 2024/9/2 17:38
//     **/
//    @Test
//    public void putObject() {
//        aliConfig();
//        StorageObjectModel object = clientUtil.getObject(null, "healthReport/351de403f4534665b27eaeef56a0d049.pdf");
//
//
//        pureeStorageOSSClient.putObject(object);
//        StoragePutResponse putObjectResult = clientUtil.putObject(object, null);
//        log.info("{}", putObjectResult);
//    }
//
//    @Test
//    public void getPath() {
//        busMapConfig();
//        //healthReport/24/09/10/
//        String test = pureeStorageOSSClient.getResourcePathByBusinessName("test");
//        log.info("{}", test);
//    }
//
//
//}
