package com.puree.storage.oss.service.impl;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.PutObjectResult;
import com.puree.storage.core.common.utils.ObjectNameGenerateUtil;
import com.puree.storage.core.model.StorageObjectModel;
import com.puree.storage.core.model.StoragePutResponse;
import com.puree.storage.core.service.PureeStorageClientService;
import com.puree.storage.core.service.impl.PureeStorageClientServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
@Slf4j
public class PureeStorageOSSPutObjectMockClientTest {


    @Mock
    private OSSClient ossClient;


    @InjectMocks
    private PureeStorageClientServiceImpl pureeStorageOSSClient;


    /**
     * @Param
     * @Return void
     * @Description 使用MultipartFile上传对象, 提供对象名称
     * <AUTHOR>
     * @Date 2024/8/30 18:25
     **/
    @Test
    public void putObject_WithMultipartFile_ObjectNameProvided() throws IOException {
        MultipartFile multipartFile = mock(MultipartFile.class);
        String bucketName = "bucket";
        String objectName = "object";
        //目录
        List<String> directoryNames = Collections.singletonList("dir");

        //oss返回
        PutObjectResult result = new PutObjectResult();
        result.setETag("123456");

        when(multipartFile.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));
        when(ObjectNameGenerateUtil.getObjectNameByMultipartFile(multipartFile)).thenReturn("testName");
        when(ObjectNameGenerateUtil.getSuffixByMultipartFile(multipartFile)).thenReturn("jpg");
        when(ossClient.putObject(anyString(), anyString(), any(ByteArrayInputStream.class))).thenReturn(result);

        StoragePutResponse response = pureeStorageOSSClient.putObject(multipartFile, bucketName,"", objectName, directoryNames);
        //传了文件名，应该为文件名称
        log.info("response:{}", response);

        verify(ossClient).putObject(anyString(), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * @Param
     * @Return void
     * @Description 使用MultipartFile上传对象，不直接提供对象名称，采用 MultipartFile 提供
     * <AUTHOR>
     * @Date 2024/8/30 18:26
     **/
    @Test
    public void putObject_WithMultipartFile_ObjectNameNotProvided() throws IOException {
        // 准备Mock的MultipartFile和相关参数
        MultipartFile multipartFile = mock(MultipartFile.class);
        String bucketName = "bucket";
        String objectName = "";
        List<String> directoryNames = Collections.singletonList("dir");
        //oss返回
        PutObjectResult result = new PutObjectResult();
        result.setETag("123456");


        // 配置MultipartFile的行为以返回预期值
        when(multipartFile.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));
        when(multipartFile.getOriginalFilename()).thenReturn("originalName.jpg");
        when(ossClient.putObject(anyString(), anyString(), any(ByteArrayInputStream.class))).thenReturn(result);

        // 调用putObject方法并验证返回值为null
        StoragePutResponse response = pureeStorageOSSClient.putObject(multipartFile, bucketName,"", objectName, directoryNames);
        //未传文件名，应该生成 originalName.jpg
        log.info("response:{}", response);

        // 验证ossClient的putObject方法被调用
        verify(ossClient).putObject(anyString(), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * @Param
     * @Return void
     * @Description 使用MultipartFile上传对象, 没有文件名称，存在 ContentType
     * <AUTHOR>
     * @Date 2024/8/30 18:46
     **/
    @Test
    public void putObject_WithMultipartFile_NoObjectName() throws IOException {
        // 准备Mock的MultipartFile和相关参数
        MultipartFile multipartFile = mock(MultipartFile.class);
        String bucketName = "bucket";
        String objectName = "";
        List<String> directoryNames = Collections.singletonList("dir");
        //oss返回
        PutObjectResult result = new PutObjectResult();
        result.setETag("123456");


        // 配置MultipartFile的行为以返回预期值
        when(multipartFile.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[0]));
        when(multipartFile.getContentType()).thenReturn("image/jpeg");
        when(ossClient.putObject(anyString(), anyString(), any(ByteArrayInputStream.class))).thenReturn(result);

        // 调用putObject方法并验证返回值为null
        StoragePutResponse response = pureeStorageOSSClient.putObject(multipartFile, bucketName,"", objectName, directoryNames);
        //未传文件名，应该生成为 随机名称+ contentType
        log.info("response:{}", response);

        // 验证ossClient的putObject方法被调用
        verify(ossClient).putObject(anyString(), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * @Param
     * @Return void
     * @Description 使用InputStream上传对象
     * <AUTHOR>
     * @Date 2024/9/2 12:22
     **/
    @Test
    public void putObject_WithInputStream() {
        // 准备参数
        String bucketName = "bucket";
        List<String> directoryNames = Collections.singletonList("dir");
        String objectName = "object";
        String suffix = "jpg";
        InputStream inputStream = new ByteArrayInputStream(new byte[0]);
        //oss返回
        PutObjectResult result = new PutObjectResult();
        result.setETag("123456");

        when(ossClient.putObject(anyString(), anyString(), any(ByteArrayInputStream.class))).thenReturn(result);

        // 调用putObject方法并验证返回值为null
        StoragePutResponse response = pureeStorageOSSClient.putObject(bucketName,"", directoryNames, objectName, suffix, inputStream);
        //应该为 dir/object.jpg
        log.info("response:{}", response);

        // 验证ossClient的putObject方法被调用
        verify(ossClient).putObject(anyString(), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * @Param
     * @Return void
     * @Description StorageObjectModel
     * <AUTHOR>
     * @Date 2024/9/2 12:22
     **/
    @Test
    public void putObject_WithStorageObjectModel() {
        // 准备StorageObjectModel并配置其属性
        StorageObjectModel model = new StorageObjectModel();
        model.setBucketName("bucket");
        model.setDirectoryNames(Collections.singletonList("dir"));
        model.setObjectName("object");
        model.setObjectSuffix("jpg");
        model.setStream(new ByteArrayInputStream(new byte[0]));

        //oss返回
        PutObjectResult result = new PutObjectResult();
        result.setETag("123456");

        when(ossClient.putObject(anyString(), anyString(), any(ByteArrayInputStream.class))).thenReturn(result);

        StoragePutResponse response = pureeStorageOSSClient.putObject(model);
        //应该为 目录 + 文件名 + 后缀 dir/object.jpg
        log.info("response:{}", response);

        // 验证ossClient的putObject方法被调用
        verify(ossClient).putObject(anyString(), anyString(), any(ByteArrayInputStream.class));
    }

}
