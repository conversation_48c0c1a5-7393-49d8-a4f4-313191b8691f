package com.puree.storage.oss.config;

import cn.hutool.extra.spring.SpringUtil;
import com.puree.storage.core.config.AbstractPureeStorageProperties;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OSSStorageProperties
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/19 16:16
 * @Version 1.0
 */
@Data
@Configuration
public class OSSStorageProperties extends AbstractPureeStorageProperties<OSSStorageSetting,OSSPureeStorageConfig> {
    protected OSSStorageProperties(){
        super("oss");
    }

    @Override
    public OSSStorageSetting getSettings() {
        return SpringUtil.getBean(OSSStorageSetting.class);
    }

    @Override
    public OSSPureeStorageConfig getConfig() {
        return SpringUtil.getBean(OSSPureeStorageConfig.class);
    }

    public OSSStorageProperties(OSSStorageSetting setting) {
        super("oss");
    }

    /**
     * sts配置
     */
    @Setter(AccessLevel.PRIVATE)
    private OSSStorageSecurityTokenService sts;

    /**
     * @Param
     * @Return OSSStorageSecurityTokenService
     * @Description 获取 sts
     * <AUTHOR>
     * @Date 2024/9/20 17:36
     **/
    public OSSStorageSecurityTokenService getSts() {
        return SpringUtil.getBean(OSSStorageSecurityTokenService.class);
    }
}
