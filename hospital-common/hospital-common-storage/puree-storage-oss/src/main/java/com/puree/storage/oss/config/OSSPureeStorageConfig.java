package com.puree.storage.oss.config;

import com.puree.storage.core.common.constant.StorageStringConstant;
import com.puree.storage.core.config.AbstractPureeStorageConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName LocalPureeStorageConfig
 * <AUTHOR>
 * @Description 配置
 * @Date 2024/9/20 16:34
 * @Version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = StorageStringConstant.CONFIGURATION_PROPERTIES_PATH + ".oss")
public class OSSPureeStorageConfig extends AbstractPureeStorageConfig {
    /**
     * 终点
     */
    private String endpoint;
    /**
     * 访问密钥 ID
     */
    private String accessKeyId;
    /**
     * 文件地址前缀
     */
    private String fileAddressPrefix;
    /**
     * 访问密钥秘密
     */
    private String accessKeySecret;
    /**
     * 存储桶名 TEST D:/TEST
     */
    private String bucketName;

}
