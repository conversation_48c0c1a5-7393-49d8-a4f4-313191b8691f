package com.puree.storage.oss.config;

import com.puree.storage.core.common.constant.StorageStringConstant;
import com.puree.storage.core.config.AbstractPureeStorageSettings;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OSSStorageSetting
 * <AUTHOR>
 * @Description 业务设置
 * @Date 2024/9/18 17:50
 * @Version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = StorageStringConstant.CONFIGURATION_PROPERTIES_PATH + ".settings")
public class OSSStorageSetting extends AbstractPureeStorageSettings<OSSPureeStorageBusiness> {


    public OSSStorageSetting() {
        super();
    }

    public OSSStorageSetting(String businessName) {
        super(businessName);
    }


}
