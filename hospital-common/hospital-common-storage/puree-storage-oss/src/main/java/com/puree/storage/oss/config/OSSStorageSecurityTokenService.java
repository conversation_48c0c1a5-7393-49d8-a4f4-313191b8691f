package com.puree.storage.oss.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.puree.storage.core.common.constant.StorageStringConstant;
import com.puree.storage.core.model.StorageSecurityTokenServiceAction;
import com.puree.storage.core.model.StorageSecurityTokenServiceResource;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OSSStorageSecurityTokenService
 * <AUTHOR>
 * @Description STS配置
 * @Date 2024/9/20 14:25
 * @Version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = StorageStringConstant.CONFIGURATION_PROPERTIES_PATH + ".sts")
public class OSSStorageSecurityTokenService {



    /**
     * 地区编号
     */
    private String regionId = "cn-shenzhen";
    /**
     * 必须要有权限的账户
     */
    private String accessKeyId;
    /**
     * 必须要有权限的账户
     */
    private String accessKeySecret;
    /**
     * 角色 arn
     */
    private String roleArn;
    /**
     * resource 允许访问的目录,与 resourceStr 相同，如果同时配置则以 resourceStr 为准
     */
    private List<StorageSecurityTokenServiceResource> resource;
    /**
     * resource 允许访问的目录 可以直接使用此字段，优先级高于 resource
     */
    private List<String> resourceStr;

    /**
     * 对应的业务名称，如 app、3rd
     */
    private String name;
    /**
     * 持续时间,900 - 3600 之间
     */
    private Long durationSeconds = 900L;
    /**
     * 权限,与 actionStr 相同，如果同时配置则以 actionStr 为准
     */
    @Getter(AccessLevel.PRIVATE)
    private List<StorageSecurityTokenServiceAction> actionList;
    /**
     * 可以直接使用此字段，优先级高于 action
     */
    private List<String> action = CollUtil.newArrayList("oss:PutObject");

    @Getter(AccessLevel.PRIVATE)
    private String resourceSimple = "acs:oss:*:*:{}/{}/*";


    public String getResourceStr(String bucketName, String businessName) {
        List<String> list = new ArrayList<>();
        CollUtil.addAll(list,resourceStr);

        if (CollUtil.isNotEmpty(list)) {
            return JSON.toJSONString(list);
        }
        if (ObjectUtil.isNotNull(resource)){
            resource.stream().forEach(resource -> {
                list.add(resource.toString());
            });
        }
        if (CollUtil.isEmpty(resourceStr) && CollUtil.isEmpty(resource)) {
            list.add(CharSequenceUtil.format(resourceSimple, bucketName, businessName));
        }
        return JSON.toJSONString(list);
    }


    /**
     * @Param
     * @Return String
     * @Description 获取 actionStr
     * <AUTHOR>
     * @Date 2024/9/3 15:36
     **/
    public String getActionStr() {
        if (CollUtil.isNotEmpty(action)) {
            return JSON.toJSONString(action);
        }
        actionList.stream().forEach(item -> {
            action.add(item.toString());
        });
        return JSON.toJSONString(action);
    }
}
