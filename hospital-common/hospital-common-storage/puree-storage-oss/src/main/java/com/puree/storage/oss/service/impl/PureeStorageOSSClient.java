package com.puree.storage.oss.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.puree.storage.core.common.constant.StorageStringConstant;
import com.puree.storage.core.common.exception.FileReadException;
import com.puree.storage.core.common.utils.ObjectNameGenerateUtil;
import com.puree.storage.core.config.AbstractPureeStorageBusiness;
import com.puree.storage.core.model.StorageObjectModel;
import com.puree.storage.core.model.StoragePutResponse;
import com.puree.storage.core.model.StorageSecurityTokenService;
import com.puree.storage.core.service.PureeStorageClient;
import com.puree.storage.oss.config.OSSPureeStorageConfig;
import com.puree.storage.oss.config.OSSStorageProperties;
import com.puree.storage.oss.config.OSSStorageSecurityTokenService;
import com.puree.storage.oss.config.OSSStorageSetting;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Optional;

/**
 * @ClassName OSSClientUtil
 * <AUTHOR>
 * @Description OSS客户端工具
 * @Date 2024/9/2 16:53
 * @Version 1.0
 */
@Slf4j
@Component
@AllArgsConstructor
public class PureeStorageOSSClient implements PureeStorageClient {
    private final OSSStorageProperties properties;
    private final OSSStorageSecurityTokenService sts;
    private final OSSPureeStorageConfig storageConfig;
    public static OSS client;

    /**
     * @Param
     * @Return void
     * @Description 初始化 OSS 客户端
     * <AUTHOR>
     * @Date 2024/9/2 17:19
     **/
    @PostConstruct
    public void initializeOSSClient() {

        log.info("OSS client initialized start");
        OSSPureeStorageConfig config = properties.getConfig();
        client = new OSSClientBuilder().build(config.getEndpoint(),
                config.getAccessKeyId(),
                config.getAccessKeySecret());
        log.info("OSS client initialized end");
    }

    /**
     * @Param bucketName 桶名称
     * @Param objectPath 对象路径
     * @Return OSSObject
     * @Description 获取 object，bucketName 为空时使用默认 bucketName
     * <AUTHOR>
     * @Date 2024/9/2 17:19
     **/
    @Override
    public StorageObjectModel getObject(String bucketName, String objectPath) {
        if (CharSequenceUtil.isEmpty(bucketName)) {
            OSSPureeStorageConfig config = properties.getConfig();
            bucketName = config.getBucketName();
        }
        ensureClientInitialized();

        try {
            OSSObject object = client.getObject(bucketName, objectPath);
            StorageObjectModel model = new StorageObjectModel();
            model.setBucketName(bucketName);
            model.setObjectName(objectPath);
            model.setObjectSize(object.getObjectMetadata().getContentLength());
            model.setStream(object.getObjectContent());
            return model;
        } catch (OSSException e) {
            throw new FileReadException(e.getErrorMessage());
        }
    }

    /**
     * @Param model
     * @Return PutObjectResult
     * @Description 上传
     * <AUTHOR>
     * @Date 2024/9/2 17:40
     **/
    public StoragePutResponse putObject(StorageObjectModel model) {
        return putObject(model, null);
    }

    /**
     * @Param
     * @Return StorageSecurityTokenService
     * @Description 获取 STS
     * <AUTHOR>
     * @Date 2024/9/3 11:25
     **/
    @Override
    public StorageSecurityTokenService getSecurityTokenService(String businessName) {

        Assert.notEmpty(businessName, "Business name cannot be null or empty");

        OSSPureeStorageConfig config = properties.getConfig();
        OSSStorageSecurityTokenService sts = properties.getSts();
        OSSStorageSetting settings = properties.getSettings();
        AbstractPureeStorageBusiness setting = settings.getBusinessSetting(businessName);
        Assert.notNull(setting, "No STS properties found for business name: " + businessName);

        String accessKeyId = sts.getAccessKeyId();
        String accessKeySecret = sts.getAccessKeySecret();
        String regionId = sts.getRegionId();
        Long durationSeconds = sts.getDurationSeconds();


        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);

        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setRoleArn(sts.getRoleArn());
        //写死
        request.setRoleSessionName(StorageStringConstant.STS_TOKEN);

        request.setDurationSeconds(durationSeconds);
        request.setPolicy(getPolicy(sts.getActionStr(), sts.getResourceStr(config.getBucketName(),businessName)));


        AssumeRoleResponse response = null;
        try {
            response = client.getAcsResponse(request);
        } catch (ClientException e) {
            log.error("Error assuming role: {}", e);
            throw new RuntimeException(e);
        }
        return getStorageSecurityTokenService(response, durationSeconds);

    }

    /**
     * @Param response
     * @Param durationSeconds
     * @Param resourcePath
     * @Return StorageSecurityTokenService
     * @Description 构建对象
     * <AUTHOR>
     * @Date 2024/9/5 18:43
     **/
    private StorageSecurityTokenService getStorageSecurityTokenService(AssumeRoleResponse response, Long durationSeconds) {
        StorageSecurityTokenService securityTokenService = new StorageSecurityTokenService();
        securityTokenService.setAccessKeyId(response.getCredentials().getAccessKeyId());
        securityTokenService.setAccessKeySecret(response.getCredentials().getAccessKeySecret());
        securityTokenService.setSecurityToken(response.getCredentials().getSecurityToken());
        securityTokenService.setExpiration(durationSeconds);
        return securityTokenService;
    }

    /**
     * @Param businessName 业务名称
     * @Return java.lang.String
     * @Description get policy
     * <AUTHOR>
     * @Date 2024/4/28 18:06
     **/
    private String getPolicy(String action, String resource) {
        return CharSequenceUtil.format(StorageStringConstant.STORAGE_SECURITY_TOKEN_SERVICE_POLICY, action, resource);
    }

    /**
     * @Param model
     * @Param objectPath
     * @Return PutObjectResult
     * @Description 上传
     * <AUTHOR>
     * @Date 2024/9/2 17:40
     **/
    @Override
    public StoragePutResponse putObject(StorageObjectModel model, String objectPath) {
        String bucketName = model.getBucketName();
        if (CharSequenceUtil.isEmpty(bucketName)) {
            OSSPureeStorageConfig config = properties.getConfig();
            bucketName = config.getBucketName();
        }
        ensureClientInitialized();
        if (CharSequenceUtil.isEmpty(objectPath)) {
            objectPath = ObjectNameGenerateUtil.build(bucketName, model.getDirectoryNames(),
                    model.getObjectName(),
                    model.getObjectSuffix());
        }
        if (StrUtil.startWith(objectPath,StrUtil.SLASH)){
            //去除最前面的 /
            objectPath = StrUtil.subAfter(objectPath,StrUtil.SLASH,false);
        }
        PutObjectResult putObjectResult = client.putObject(bucketName, objectPath, model.getStream());
        StoragePutResponse respone = new StoragePutResponse();
        if (ObjectUtil.isNotNull(putObjectResult)) {
            respone.setETag(putObjectResult.getETag());
        }
        respone.setObjectPath(objectPath);
        respone.setFullPath(getFullPath(objectPath));
        return respone;
    }

    /**
     * @Param objectPath
     * @Return String
     * @Description 获取完整路径
     * <AUTHOR>
     * @Date 2024/10/16 14:47
     **/
    public String getFullPath(String objectPath){
        OSSPureeStorageConfig config = properties.getConfig();
        String fileAddressPrefix = config.getFileAddressPrefix();
        if (StrUtil.isEmpty(fileAddressPrefix)) {
             fileAddressPrefix = config.getEndpoint();
        }
        String bucketName = config.getBucketName();
        return StrUtil.format("http://{}.{}/{}",bucketName,fileAddressPrefix,objectPath);
    }

    /**
     * @Param
     * @Return void
     * @Description 关闭 OSS 客户端
     * <AUTHOR>
     * @Date 2024/9/5 17:55
     **/
    @PreDestroy
    public void closeOSSClient() {
        Optional.ofNullable(client).ifPresent(c -> {
            c.shutdown();
            log.info("OSS client closed");
        });
    }

    /**
     * @Param
     * @Return void
     * @Description 确保客户端已初始化
     * <AUTHOR>
     * @Date 2024/9/11 15:47
     **/
    private void ensureClientInitialized() {
        if (client == null) {
            synchronized (this) {
                if (client == null) {
                    initializeOSSClient();
                }
            }
        }
    }
}