package com.puree.storage.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.ObjectUtil;
import com.puree.storage.core.common.exception.FileReadException;
import com.puree.storage.core.common.filter.ObjectFilter;
import com.puree.storage.core.common.handler.FilteringUploadExecute;
import com.puree.storage.core.common.utils.ContentTypeUtil;
import com.puree.storage.core.common.utils.IOUtils;
import com.puree.storage.core.common.utils.ObjectNameGenerateUtil;
import com.puree.storage.core.config.AbstractPureeStorageBusiness;
import com.puree.storage.core.config.AbstractPureeStorageConfig;
import com.puree.storage.core.config.AbstractPureeStorageProperties;
import com.puree.storage.core.config.AbstractPureeStorageSettings;
import com.puree.storage.core.model.StorageObjectModel;
import com.puree.storage.core.model.StoragePutResponse;
import com.puree.storage.core.model.StorageSecurityTokenService;
import com.puree.storage.core.service.PureeStorageClient;
import com.puree.storage.core.service.PureeStorageClientService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName PureeStorageClientServiceImpl
 * <AUTHOR>
 * @Description
 * @Date 2024/9/12 18:32
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class PureeStorageClientServiceImpl implements PureeStorageClientService {

    private final PureeStorageClient client;

    private final AbstractPureeStorageProperties properties;


    @Override
    public Boolean createBucket(String bucketName) {
        throw new UnsupportedOperationException("oss 不支持创建bucket");
    }

    @Override
    public Boolean deleteBucket(String bucketName) {
        throw new UnsupportedOperationException("oss 不支持删除bucket");
    }

    @Override
    public Boolean doesBucketExist(String bucketName) {
        throw new UnsupportedOperationException("oss 不支持判断bucket是否存在");
    }

    @Override
    public Boolean createDirectory(String bucketName, String directoryName) {
        throw new UnsupportedOperationException("oss 不支持创建目录");
    }

    @Override
    public Boolean deleteDirectory(String bucketName, String directoryName) {
        throw new UnsupportedOperationException("oss 不支持删除目录");
    }

    @Override
    public Boolean doesDirectoryExist(String bucketName, String directoryName) {
        throw new UnsupportedOperationException("oss 不支持判断目录是否存在");
    }

    @Override
    public List<StorageObjectModel> listObject(String bucketName, String directoryName) {
        return Collections.emptyList();
    }

    /**
     * @Param objectPath
     * @Return String
     * @Description 获取全路径名
     * <AUTHOR>
     * @Date 2024/10/16 18:24
     **/
    @Override
    public String getFullName(String objectPath) {
        return client.getFullPath(objectPath);
    }

    /**
     * @Param businessName
     * @Return String
     * @Description 通过业务名称获取上传路径
     * <AUTHOR>
     * @Date 2024/9/10 12:11
     **/
    @Override
    public String getResourcePathByBusinessName(String businessName) {
        AbstractPureeStorageSettings settings = properties.getSettings();
        AbstractPureeStorageBusiness setting = settings.getBusinessSetting(businessName);
        Assert.notNull(setting, "No configuration found for business name: " + businessName);
        String pathTemplate = setting.getPathTemplate();
        //截断最后一个 /
        if (!CharSequenceUtil.endWith(pathTemplate, StrPool.SLASH)) {
            pathTemplate = CharSequenceUtil.subBefore(pathTemplate, StrPool.SLASH, true);
        }

        ArrayList<String> pathList = CollUtil.newArrayList(pathTemplate);
        return ObjectNameGenerateUtil.build(businessName, pathList, null, "");
    }

    /**
     * @Param businessName
     * @Return StorageSecurityTokenService
     * @Description 获取Sts
     * <AUTHOR>
     * @Date 2024/9/11 16:05
     **/
    @Override
    public StorageSecurityTokenService getSecurityTokenService(String businessName) {
        StorageSecurityTokenService sts = client.getSecurityTokenService(businessName);
        sts.setResourcePath(getResourcePathByBusinessName(businessName));
        return sts;
    }


    /**
     * @Param bucketName
     * @Param objectPath
     * @Param charset
     * @Return String
     * @Description 读取文件 str
     * <AUTHOR>
     * @Date 2024/9/3 16:13
     **/
    @Override
    public String getObjectAsString(String bucketName, String objectPath, String charset) throws IOException {
        InputStream objectAsInputStream = getObjectAsInputStream(bucketName, objectPath);
        charset = ObjectUtil.defaultIfNull(charset, "UTF-8");
        return IOUtils.readStreamAsString(objectAsInputStream, charset);
    }

    /**
     * @Param objectPath
     * @Param charset
     * @Return String
     * @Description 读取文件 str
     * <AUTHOR>
     * @Date 2024/9/3 16:13
     **/
    @Override
    public String getObjectAsString(String objectPath, String charset) throws IOException {
        AbstractPureeStorageConfig config = properties.getConfig();
        return getObjectAsString(config.getBucketName(), objectPath, charset);
    }

    /**
     * @Param objectPath
     * @Return String
     * @Description 读取为 String 默认 bucketName,UTF-8
     * <AUTHOR>
     * @Date 2024/9/12 19:09
     **/
    @Override
    public String getObjectAsString(String objectPath) {
        try {
            return getObjectAsString(objectPath, CharsetUtil.UTF_8);
        } catch (IOException e) {
            log.error("读取文件失败，路径: {}", objectPath, e);
            throw new FileReadException("无法读取文件: " + objectPath, e);
        }

    }

    /**
     * @Param bucketName
     * @Param objectPath
     * @Return byte
     * @Description 读取 byteArray
     * <AUTHOR>
     * @Date 2024/9/2 15:44
     **/
    @Override
    public byte[] getObjectAsByteArray(String bucketName, String objectPath) throws IOException {
        InputStream objectAsInputStream = getObjectAsInputStream(bucketName, objectPath);
        return IOUtils.readStreamAsByteArray(objectAsInputStream);
    }

    /**
     * @Param objectPath
     * @Return byte
     * @Description 读取 byteArray
     * <AUTHOR>
     * @Date 2024/9/3 16:13
     **/
    @Override
    public byte[] getObjectAsByteArray(String objectPath) throws IOException {
        AbstractPureeStorageConfig config = properties.getConfig();
        return getObjectAsByteArray(config.getBucketName(), objectPath);
    }

    /**
     * @Param response
     * @Param objectPath
     * @Return void
     * @Description 获取 文件 后写入 response
     * <AUTHOR>
     * @Date 2024/10/24 14:45
     **/
    @Override
    public void getObjectPutResponse(HttpServletResponse response, String objectPath, HttpServletRequest request) throws IOException {
        ContentTypeUtil.setContentType(response,objectPath);
        StorageObjectModel object = getObject(objectPath);
        try(InputStream in = object.getStream()) {
            long objectSize = object.getObjectSize();
            //设置 响应头
            response.setHeader("Accept-Ranges", "bytes");
            //开启 keep-alive
            response.setHeader("Connection", "keep-alive");
            //写入 etag
            response.setHeader("ETag", String.valueOf(HashUtil.tianlHash(object.getObjectName())));
            //控制跨域
            response.setHeader("Access-Control-Allow-Origin", "*");
            long start = computeContentLength(response, request, objectSize);


            writeInputStreamToResponse(in, start,response,request);
        } catch (Exception e) {
            // 处理异常
            log.error("写入文件失败 objectPath:{}",objectPath, e);
            throw new FileReadException("无法读取文件: "+objectPath , e);
        }
    }

    /**
     * @param response
     * @param request
     * @param objectSize
     * @return long
     * @description 写入长度
     * <AUTHOR>
     * @date 2024/12/5 12:21
     **/
    private long computeContentLength(HttpServletResponse response,HttpServletRequest request,long objectSize) {
        String rangeHeader = request.getHeader(HttpHeaders.RANGE);
        long rangeStart = 0;
        long rangeEnd = objectSize - 1;
        if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
            try {
                String[] ranges = rangeHeader.substring(6).split("-");
                // 验证范围格式
                if (ranges.length > 2) {
                    throw new IllegalArgumentException("Invalid range format");
                }

                // 解析起始位置
                if (!ranges[0].isEmpty()) {
                    rangeStart = Long.parseLong(ranges[0]);
                    if (rangeStart < 0 || rangeStart >= objectSize) {
                        throw new IllegalArgumentException("Invalid range start");
                    }
                }

                // 解析结束位置
                if (ranges.length > 1 && !ranges[1].isEmpty()) {
                    rangeEnd = Long.parseLong(ranges[1]);
                    if (rangeEnd < rangeStart || rangeEnd >= objectSize) {
                        throw new IllegalArgumentException("Invalid range end");
                    }
                }

                // 设置206状态码
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid range format", e);
            }
        }
        response.addHeader(HttpHeaders.CONTENT_RANGE, "bytes " + rangeStart + "-" + rangeEnd + "/" + objectSize);
        response.setContentLengthLong(rangeEnd - rangeStart + 1);
        return rangeStart;
    }

    /**
     * @Param inputStream
     * @Param response
     * @Return void
     * @Description 写入
     * <AUTHOR>
     * @Date 2024/11/21 11:56
     **/
    private void writeInputStreamToResponse(InputStream inputStream,long start, HttpServletResponse response, HttpServletRequest request) {
        try (OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[8192];
            int length;
            inputStream.skip(start);
            while ((length = inputStream.read(buffer)) > 0) {
                os.write(buffer, 0, length);
            }
            os.flush();
        } catch (Exception e) {
            // 处理异常
            log.error("写入文件失败", e);
            throw new FileReadException("无法读取文件: " , e);
        }
    }




    /**
     * @Param bucketName
     * @Param objectPath
     * @Return InputStream
     * @Description 读取 InputStream
     * <AUTHOR>
     * @Date 2024/9/2 15:44
     **/
    @Override
    public InputStream getObjectAsInputStream(String bucketName, String objectPath) {
        StorageObjectModel object = client.getObject(bucketName, objectPath);
        Assert.notNull(object, "object is null");
        return object.getStream();
    }

    /**
     * @Param objectPath
     * @Return InputStream
     * @Description 读取 InputStream
     * <AUTHOR>
     * @Date 2024/9/3 16:14
     **/
    @Override
    public InputStream getObjectAsInputStream(String objectPath) {
        AbstractPureeStorageConfig config = properties.getConfig();
        return getObjectAsInputStream(config.getBucketName(), objectPath);
    }

    /**
     * @param objectPath
     * @return StorageObjectModel
     * @description 获取存储对象
     * <AUTHOR>
     * @date 2024/12/5 11:56
     **/
    @Override
    public StorageObjectModel getObject(String objectPath) {
        AbstractPureeStorageConfig config = properties.getConfig();
        StorageObjectModel object = client.getObject(config.getBucketName(), objectPath);
        Assert.notNull(object, "object is null");
        return object;
    }

    /**
     * @Param multipartFile
     * @Param bucketName
     * @Param objectName 为空则取 multipartFile 中的 文件名
     * @Param directoryNames
     * @Return StoragePutResponse
     * @Description 上传
     * <AUTHOR>
     * @Date 2024/8/30 18:00
     **/
    @Override
    public StoragePutResponse putObject(MultipartFile multipartFile, String bucketName, String businessName, String objectName, List<String> directoryNames) throws IOException {
        if (CharSequenceUtil.isEmpty(objectName)) {
            objectName = ObjectNameGenerateUtil.getObjectNameByMultipartFile(multipartFile);
        }
        String suffix = ObjectNameGenerateUtil.getSuffixByMultipartFile(multipartFile);
        return putObject(bucketName, businessName, directoryNames, objectName, suffix, multipartFile.getInputStream(), multipartFile.getSize());
    }


    /**
     * @Param multipartFile
     * @Param businessName
     * @Return StoragePutResponse
     * @Description 指定业务名称，通过 过滤器上传
     * <AUTHOR>
     * @Date 2024/9/12 16:46
     **/
    @Override
    public StoragePutResponse putObject(MultipartFile multipartFile, String businessName) throws IOException {
        // 检查输入参数是否为空
        Assert.notNull(multipartFile, "MultipartFile must not be null");
        Assert.notEmpty(businessName, "Business name must not be empty");


        AbstractPureeStorageSettings settings = properties.getSettings();
        AbstractPureeStorageConfig config = properties.getConfig();

        AbstractPureeStorageBusiness setting = settings.getBusinessSetting(businessName);
        Assert.notNull(setting, "No configuration found for business name: " + businessName);

        //构建目录
        // 获取路径模板
        List<String> dirs = CollUtil.newArrayList(setting.getPathTemplate());
        //获取过滤链
        List<ObjectFilter<StorageObjectModel>> filterList = setting.getFilterList();
        //文件名
        String objectName = "";


        if (CollUtil.isNotEmpty(filterList)) {
            FilteringUploadExecute execute = new FilteringUploadExecute(filterList, objectModel -> this.putObject(objectModel));
            return putFilterObject(multipartFile, config.getBucketName(), businessName, dirs, execute);
        }
        return putObject(multipartFile, config.getBucketName(), businessName, objectName, dirs);
    }

    /**
     * @Param inputStream
     * @Param businessName
     * @Param objectName
     * @Param objectSuffix
     * @Return StoragePutResponse
     * @Description 指定业务名称，通过 过滤器上传
     * <AUTHOR>
     * @Date 2024/10/12 16:59
     **/
    @Override
    public StoragePutResponse putObject(InputStream inputStream, String businessName, String objectName, String objectSuffix) throws IOException {
        // 检查输入参数是否为空
        Assert.notNull(inputStream, "MultipartFile must not be null");
        Assert.notEmpty(businessName, "Business name must not be empty");
        Assert.notEmpty(objectSuffix, "object Suffix  must not be empty");

        AbstractPureeStorageSettings settings = properties.getSettings();
        AbstractPureeStorageConfig config = properties.getConfig();

        AbstractPureeStorageBusiness setting = settings.getBusinessSetting(businessName);
        Assert.notNull(setting, "No configuration found for business name: " + businessName);

        //构建目录
        // 获取路径模板
        List<String> dirs = CollUtil.newArrayList(setting.getPathTemplate());
        //获取过滤链
        List<ObjectFilter<StorageObjectModel>> filterList = setting.getFilterList();


        if (CollUtil.isNotEmpty(filterList)) {
            FilteringUploadExecute execute = new FilteringUploadExecute(filterList, objectModel -> this.putObject(objectModel));
            return putFilterObject(getStorageObjectModel(config.getBucketName(), businessName, dirs, objectName, objectSuffix, inputStream, -1L), execute);
        }

        return putObject(config.getBucketName(), businessName, dirs, objectName, objectSuffix, inputStream, -1L);
    }

    /**
     * @Param multipartFile
     * @Param objectName
     * @Param businessName
     * @Return StoragePutResponse
     * @Description 指定文件名上传
     * <AUTHOR>
     * @Date 2024/9/12 16:44
     **/
    @Override
    public StoragePutResponse putObject(MultipartFile multipartFile, String objectName, String businessName) throws IOException {
        AbstractPureeStorageSettings settings = properties.getSettings();
        AbstractPureeStorageConfig config = properties.getConfig();
        AbstractPureeStorageBusiness setting = settings.getBusinessSetting(businessName);
        Assert.notNull(setting, "No configuration found for business name: " + businessName);

        //todo
        List<String> dirs = CollUtil.newArrayList(setting.getDirectoryPath());
        dirs.addAll(ObjectNameGenerateUtil.getDirectoryNameYYMMddList());
        return putObject(multipartFile, config.getBucketName(), businessName, objectName, dirs);
    }

    /**
     * @Param bucketName 桶名
     * @Param directoryNames 目录
     * @Param objectName 文件名
     * @Param suffix 文件后缀
     * @Param inputStream 文件流
     * @Return StoragePutResponse
     * @Description 上传
     * <AUTHOR>
     * @Date 2024/8/30 17:59
     **/
    @Override
    public StoragePutResponse putObject(String bucketName, String businessName, List<String> directoryNames, String objectName, String suffix, InputStream inputStream, Long objectSize) {
        StorageObjectModel model = getStorageObjectModel(bucketName, businessName, directoryNames, objectName, suffix, inputStream, objectSize);
        return putObject(model);
    }

    @Override
    public StoragePutResponse putObject(String bucketName, String businessName, List<String> directoryNames, String objectName, String suffix, InputStream inputStream) {
        StorageObjectModel model = getStorageObjectModel(bucketName, businessName, directoryNames, objectName, suffix, inputStream, -1L);
        return putObject(model);
    }


    /**
     * @Param bucketName
     * @Param directoryNames
     * @Param objectName
     * @Param suffix
     * @Param inputStream
     * @Return StorageObjectModel
     * @Description 获取StorageObjectModel
     * <AUTHOR>
     * @Date 2024/9/2 15:14
     **/
    @Override
    public StorageObjectModel getStorageObjectModel(String bucketName, String businessName, List<String> directoryNames, String objectName, String suffix, InputStream inputStream, Long objectSize) {
        StorageObjectModel model = new StorageObjectModel();
        model.setBucketName(bucketName);
        model.setBusinessName(businessName);
        model.setDirectoryNames(directoryNames);
        model.setObjectName(objectName);
        model.setObjectSuffix(suffix);
        model.setStream(inputStream);
        model.setObjectSize(objectSize);
        return model;
    }

    /**
     * @Param objectModel
     * @Return StoragePutResponse
     * @Description 文件上传
     * <AUTHOR>
     * @Date 2024/8/30 17:59
     **/
    @Override
    public StoragePutResponse putObject(StorageObjectModel objectModel) {
        String objectName = objectModel.getObjectName();
        String build = ObjectNameGenerateUtil.build(objectModel.getBusinessName(), objectModel.getDirectoryNames(), objectName, objectModel.getObjectSuffix());
        return client.putObject(objectModel, build);
    }

    /**
     * @Param multipartFile
     * @Param bucketName
     * @Param objectName
     * @Param directoryNames
     * @Param handler
     * @Return StoragePutResponse
     * @Description 过滤上传
     * <AUTHOR>
     * @Date 2024/9/2 15:26
     **/
    @Override
    public StoragePutResponse putFilterObject(MultipartFile multipartFile,
                                              String bucketName,
                                              String businessName,
                                              List<String> directoryNames,
                                              FilteringUploadExecute execute) throws IOException {
        String objectName = ObjectNameGenerateUtil.getObjectNameByMultipartFile(multipartFile);
        String suffix = ObjectNameGenerateUtil.getSuffixByMultipartFile(multipartFile);
        return putFilterObject(getStorageObjectModel(bucketName, businessName, directoryNames, objectName, suffix, multipartFile.getInputStream(), multipartFile.getSize()), execute);
    }


    /**
     * @Param bucketName
     * @Param directoryNames
     * @Param objectName
     * @Param suffix
     * @Param inputStream
     * @Param handler
     * @Return StoragePutResponse
     * @Description 过滤上传
     * <AUTHOR>
     * @Date 2024/9/2 15:26
     **/
    @Override
    public StoragePutResponse putFilterObject(String bucketName,
                                              List<String> directoryNames,
                                              String objectName,
                                              String suffix,
                                              InputStream inputStream,
                                              Long objectSize,
                                              FilteringUploadExecute execute) {
        return putFilterObject(getStorageObjectModel(bucketName, "", directoryNames, objectName, suffix, inputStream, objectSize), execute);
    }


    /**
     * @Param objectModel
     * @Param handler
     * @Return StoragePutResponse
     * @Description 过滤上传
     * <AUTHOR>
     * @Date 2024/9/2 15:46
     **/
    @Override
    public StoragePutResponse putFilterObject(StorageObjectModel objectModel, FilteringUploadExecute execute) {
        return execute.run(objectModel);
    }
}
