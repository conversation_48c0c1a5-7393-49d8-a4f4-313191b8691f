package com.puree.storage.core.service;

import com.puree.storage.core.common.handler.FilteringUploadExecute;
import com.puree.storage.core.model.StorageObjectModel;
import com.puree.storage.core.model.StoragePutResponse;
import com.puree.storage.core.model.StorageSecurityTokenService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * @ClassName PureeStorage
 * <AUTHOR>
 * @Description puree存储接口
 * @Date 2024/8/29 15:12
 * @Version 1.0
 */
public interface PureeStorageClientService {

    /** ---------------------- bucket相关 ---------------------- **/

    /**
     * @Param bucketName
     * @Return Boolean
     * @Description 创建
     * <AUTHOR>
     * @Date 2024/8/29 15:14
     **/
    Boolean createBucket(String bucketName);

    /**
     * @Param bucketName
     * @Return Boolean
     * @Description 删除
     * <AUTHOR>
     * @Date 2024/8/29 15:14
     **/
    Boolean deleteBucket(String bucketName);

    /**
     * @Param bucketName
     * @Return Boolean
     * @Description 桶是否存在
     * <AUTHOR>
     * @Date 2024/8/29 15:14
     **/
    Boolean doesBucketExist(String bucketName);

    /** ---------------- Directory 相关------------------- **/

    /**
     * @Param bucketName
     * @Param directoryName
     * @Return String
     * @Description 创建目录
     * <AUTHOR>
     * @Date 2024/8/29 15:25
     **/
    Boolean createDirectory(String bucketName, String directoryName);

    /**
     * @Param bucketName
     * @Param directoryName
     * @Return String
     * @Description 删除目录，有文件时抛出异常
     * <AUTHOR>
     * @Date 2024/8/29 15:25
     **/
    Boolean deleteDirectory(String bucketName, String directoryName);

    /**
     * @Param bucketName
     * @Param directoryName
     * @Return Boolean
     * @Description 目录是否存在
     * <AUTHOR>
     * @Date 2024/8/29 15:26
     **/
    Boolean doesDirectoryExist(String bucketName, String directoryName);

    /**
     * @Param bucketName
     * @Param directoryName
     * @Return List<StorageObjectModel>
     * @Description 获取目录下所有文件
     * <AUTHOR>
     * @Date 2024/8/29 15:26
     **/
    List<StorageObjectModel> listObject(String bucketName, String directoryName);

    /** ---------------- Object 相关------------------- **/
    /**
     * @Param objectPath
     * @Return String
     * @Description 获取全路径名
     * <AUTHOR>
     * @Date 2024/10/16 18:24
     **/
    String getFullName(String objectPath);

    /** -------------------上传下载------------------- **/

    /**
     * @Param businessName
     * @Return String
     * @Description 通过业务名称获取上传路径
     * <AUTHOR>
     * @Date 2024/9/10 12:11
     **/
    String getResourcePathByBusinessName(String businessName);

    /**
     * @Param businessName
     * @Return StorageSecurityTokenService
     * @Description 获取Sts
     * <AUTHOR>
     * @Date 2024/9/11 16:06
     **/
    StorageSecurityTokenService getSecurityTokenService(String businessName);

    /**
     * @Param bucketName
     * @Param objectPath
     * @Param charset 为空则默认 UTF-8
     * @Return String
     * @Description 读取为 String
     * <AUTHOR>
     * @Date 2024/9/2 15:46
     **/
    String getObjectAsString(String bucketName, String objectPath, String charset) throws IOException;

    /**
     * @Param objectPath
     * @Param charset
     * @Return String
     * @Description 读取为 String 默认 bucketName
     * <AUTHOR>
     * @Date 2024/9/2 16:58
     **/
    String getObjectAsString(String objectPath, String charset) throws IOException;

    /**
     * @Param objectPath
     * @Return String
     * @Description 读取为 String 默认 bucketName,UTF-8
     * <AUTHOR>
     * @Date 2024/9/12 19:08
     **/
    String getObjectAsString(String objectPath) throws IOException;

    /**
     * @Param bucketName
     * @Param objectPath
     * @Return byte
     * @Description 获取字节数组
     * <AUTHOR>
     * @Date 2024/8/29 15:47
     **/
    byte[] getObjectAsByteArray(String bucketName, String objectPath) throws IOException;

    /**
     * @Param objectPath
     * @Return byte
     * @Description 获取字节数组 默认 bucketName
     * <AUTHOR>
     * @Date 2024/9/2 16:58
     **/
    byte[] getObjectAsByteArray(String objectPath) throws IOException;

    /**
     * @Param response
     * @Param objectPath
     * @Return void
     * @Description 获取 文件 后写入 response
     * <AUTHOR>
     * @Date 2024/10/24 14:54
     **/
    void getObjectPutResponse(HttpServletResponse response, String objectPath, HttpServletRequest request) throws IOException;

    /**
     * @Param bucketName
     * @Param objectPath
     * @Return InputStream
     * @Description 获取输入流
     * <AUTHOR>
     * @Date 2024/8/29 15:47
     **/
    InputStream getObjectAsInputStream(String bucketName, String objectPath);

    /**
     * @Param objectPath
     * @Return InputStream
     * @Description 获取输入流 默认 bucketName
     * <AUTHOR>
     * @Date 2024/9/2 16:58
     **/
    InputStream getObjectAsInputStream(String objectPath);
    /**
     * @param objectPath
     * @return StorageObjectModel
     * @description 获取存储对象
     * <AUTHOR>
     * @date 2024/12/5 11:56
     **/
    StorageObjectModel getObject(String objectPath);

    /**
     * @Param multipartFile
     * @Param bucketName
     * @Param objectName 为空则取 multipartFile 中的 文件名
     * @Param directoryNames
     * @Return StoragePutResponse
     * @Description 上传
     * <AUTHOR>
     * @Date 2024/8/30 17:37
     **/
    StoragePutResponse putObject(MultipartFile multipartFile, String bucketName, String businessName, String objectName, List<String> directoryNames) throws IOException;

    /**
     * @Param multipartFile
     * @Param businessName
     * @Return StoragePutResponse
     * @Description 上传 默认 bucketName
     * <AUTHOR>
     * @Date 2024/9/12 16:44
     **/
    StoragePutResponse putObject(MultipartFile multipartFile, String businessName) throws IOException;
    /**
     * @Param inputStream
     * @Param businessName
     * @Param objectName
     * @Param objectSuffix
     * @Return StoragePutResponse
     * @Description 指定业务名称，通过 过滤器上传
     * <AUTHOR>
     * @Date 2024/10/12 16:59
     **/
    StoragePutResponse putObject(InputStream inputStream, String businessName, String objectName, String objectSuffix) throws IOException;

    /**
     * @Param multipartFile
     * @Param objectName 为空则取 multipartFile 中的 文件名
     * @Param directoryNames
     * @Return StoragePutResponse
     * @Description 上传 默认 bucketName
     * <AUTHOR>
     * @Date 2024/9/2 16:58
     **/
    StoragePutResponse putObject(MultipartFile multipartFile, String objectName, String businessName) throws IOException;

    /**
     * @Param bucketName 桶名
     * @Param directoryNames 目录
     * @Param objectName 文件名
     * @Param suffix 文件后缀
     * @Param inputStream 文件流
     * @Return StoragePutResponse
     * @Description 上传
     * <AUTHOR>
     * @Date 2024/8/29 16:00
     **/
    StoragePutResponse putObject(String bucketName, String businessName, List<String> directoryNames, String objectName, String suffix, InputStream inputStream, Long objectSize);

    /**
     * @Param bucketName
     * @Param directoryNames
     * @Param objectName
     * @Param suffix
     * @Param inputStream
     * @Return StoragePutResponse
     * @Description 上传
     * <AUTHOR>
     * @Date 2024/9/12 16:15
     **/
    StoragePutResponse putObject(String bucketName, String businessName, List<String> directoryNames, String objectName, String suffix, InputStream inputStream);

    /**
     * @Param bucketName
     * @Param businessName
     * @Param directoryNames
     * @Param objectName
     * @Param suffix
     * @Param inputStream
     * @Param objectSize
     * @Return StorageObjectModel
     * @Description 获取StorageObjectModel
     * <AUTHOR>
     * @Date 2024/10/12 16:53
     **/
    StorageObjectModel getStorageObjectModel(String bucketName, String businessName, List<String> directoryNames, String objectName, String suffix, InputStream inputStream, Long objectSize);

    /**
     * @Param objectModel
     * @Return StoragePutResponse
     * @Description 上传文件
     * <AUTHOR>
     * @Date 2024/8/29 15:58
     **/
    StoragePutResponse putObject(StorageObjectModel objectModel);


    /**
     * @Param multipartFile
     * @Param bucketName
     * @Param directoryNames
     * @Param handler
     * @Return StoragePutResponse
     * @Description 过滤器后上传文件
     * <AUTHOR>
     * @Date 2024/8/29 16:09
     **/
    StoragePutResponse putFilterObject(MultipartFile multipartFile,
                                       String bucketName,
                                       String businessName,
                                       List<String> directoryNames,
                                       FilteringUploadExecute execute) throws IOException;


    /**
     * @Param bucketName
     * @Param directoryNames
     * @Param objectName
     * @Param inputStream
     * @Param handler
     * @Return StoragePutResponse
     * @Description 过滤器后上传文件
     * <AUTHOR>
     * @Date 2024/8/29 16:09
     **/
    StoragePutResponse putFilterObject(String bucketName,
                                       List<String> directoryNames,
                                       String objectName,
                                       String suffix,
                                       InputStream inputStream,
                                       Long objectSize,
                                       FilteringUploadExecute execute);


    /**
     * @Param objectModel
     * @Param handler
     * @Return StoragePutResponse
     * @Description 过滤器后上传文件
     * <AUTHOR>
     * @Date 2024/8/29 16:10
     **/
    StoragePutResponse putFilterObject(StorageObjectModel objectModel, FilteringUploadExecute execute);


}
