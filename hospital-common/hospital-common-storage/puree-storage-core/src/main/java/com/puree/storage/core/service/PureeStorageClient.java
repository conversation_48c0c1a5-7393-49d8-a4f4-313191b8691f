package com.puree.storage.core.service;

import com.puree.storage.core.model.StorageObjectModel;
import com.puree.storage.core.model.StoragePutResponse;
import com.puree.storage.core.model.StorageSecurityTokenService;
import org.springframework.http.HttpRequest;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName PureeStorageClient
 * <AUTHOR>
 * @Description 客户端实现
 * @Date 2024/9/12 18:34
 * @Version 1.0
 */
public interface PureeStorageClient {

    StorageObjectModel getObject(String bucketName, String objectPath);

    String getFullPath(String objectPath);

    StorageSecurityTokenService getSecurityTokenService(String businessName);

    StoragePutResponse putObject(StorageObjectModel model, String objectPath);

}
