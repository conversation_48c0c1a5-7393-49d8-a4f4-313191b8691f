package com.puree.storage.local.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.puree.storage.core.common.utils.ObjectNameGenerateUtil;
import com.puree.storage.core.model.StorageObjectModel;
import com.puree.storage.core.model.StoragePutResponse;
import com.puree.storage.core.model.StorageSecurityTokenService;
import com.puree.storage.core.service.PureeStorageClient;
import com.puree.storage.local.config.LocalPureeStorageConfig;
import com.puree.storage.local.config.LocalStorageProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;

/**
 * @ClassName PureeStorageLocalClient
 * <AUTHOR>
 * @Description 本地实现
 * @Date 2024/9/12 18:51
 * @Version 1.0
 */
@Slf4j
@Component
@AllArgsConstructor
public class PureeStorageLocalClient implements PureeStorageClient {

    //    private final PureeStorageProperties properties;
    private final LocalStorageProperties properties;

    /**
     * @Param bucketName
     * @Param objectPath
     * @Return StorageObjectModel
     * @Description 获取对象
     * <AUTHOR>
     * @Date 2024/9/12 19:00
     **/
    @Override
    public StorageObjectModel getObject(String bucketName, String objectPath) {
        File file = FileUtil.file(getResourcePath(bucketName, objectPath));
        if (!file.exists()) {
            log.error("File not found: {}", file.getAbsolutePath());
            return null;
        }
        BufferedInputStream inputStream = FileUtil.getInputStream(file);
        StorageObjectModel model = new StorageObjectModel();
        model.setBucketName(bucketName);
        model.setObjectName(objectPath);
        model.setStream(inputStream);
        try {
            model.setObjectSize((long) inputStream.available());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return model;
    }

    /**
     * @Param objectPath
     * @Return String
     * @Description 获取全链路名
     * <AUTHOR>
     * @Date 2024/10/16 18:22
     **/
    @Override
    public String getFullPath(String objectPath) {

        LocalPureeStorageConfig config = properties.getConfig();
        String fullPathPrefix = config.getFullPathPrefix();
        log.info("全链路前缀:{}", fullPathPrefix);
        Assert.notBlank(fullPathPrefix, "全链路前缀为空:"+fullPathPrefix);
        return StrUtil.format(fullPathPrefix, objectPath);
    }

    private String getResourcePath(String bucketName, String objectPath) {
        LocalPureeStorageConfig config = properties.getConfig();
        String rootPath = config.getRootPath();
        StringBuilder builder = StrUtil.builder();
        builder.append(rootPath);
        if (!StrUtil.startWith(rootPath, StrPool.SLASH) && !StrUtil.contains(rootPath, StrPool.C_COLON)) {
            builder.append(StrPool.C_COLON);
        }

        if (StrUtil.isNotEmpty(bucketName)) {
            builder.append(StrPool.SLASH);
            builder.append(bucketName);
        }
        log.info("objectPath：{}",objectPath);
        if (!StrUtil.startWith(objectPath, StrPool.SLASH) ) {
            builder.append(StrPool.SLASH);
        }
        builder.append(objectPath);
        return builder.toString();
    }

    /**
     * @Param businessName
     * @Return StorageSecurityTokenService
     * @Description sts 无实现
     * <AUTHOR>
     * @Date 2024/9/12 18:52
     **/
    @Override
    public StorageSecurityTokenService getSecurityTokenService(String businessName) {
        throw new UnsupportedOperationException("Not implemented");
    }

    /**
     * @Param model
     * @Param objectPath
     * @Return StoragePutResponse
     * @Description 写入文件
     * <AUTHOR>
     * @Date 2024/9/12 19:01
     **/
    @Override
    public StoragePutResponse putObject(StorageObjectModel model, String objectPath) {
        String bucketName = model.getBucketName();
        if (CharSequenceUtil.isEmpty(bucketName)) {
            LocalPureeStorageConfig config = properties.getConfig();
            bucketName = config.getBucketName();
        }
        if (CharSequenceUtil.isEmpty(objectPath)) {
            objectPath = ObjectNameGenerateUtil.build(bucketName, model.getDirectoryNames(),
                    model.getObjectName(),
                    model.getObjectSuffix());
        }
        String resourcePath = getResourcePath(bucketName, objectPath);
        File file = FileUtil.file(resourcePath);
        // 确保目录存在
        FileUtil.mkParentDirs(file);
        //写入文件
        FileUtil.writeFromStream(model.getStream(), file);
        StoragePutResponse respone = new StoragePutResponse();
        if (StrUtil.startWith(objectPath, StrPool.SLASH)) {
            //去除掉第一个/
            objectPath = StrUtil.subAfter(objectPath, StrPool.SLASH, false);
        }
        respone.setObjectPath(objectPath);
        respone.setFullPath(resourcePath);
        respone.setBucketName(model.getBucketName());
        return respone;
    }
}
