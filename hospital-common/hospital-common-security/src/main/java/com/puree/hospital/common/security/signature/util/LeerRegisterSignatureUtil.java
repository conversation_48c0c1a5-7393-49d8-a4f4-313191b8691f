package com.puree.hospital.common.security.signature.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 九合一设备-对接签名
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/26
 */
public class LeerRegisterSignatureUtil {

    /**
     * 生成签名头
     * @param appKey 应用密钥
     * @param appSecret 应用秘钥
     * @param body 请求体
     * @return 包含签名的请求头Map
     */
    public static Map<String, String> generateSignatureHeaders(String appKey, String appSecret, String body) {
        // 签名规则: appKey + appSecret + timestamp + body
        String timestamp = String.valueOf(System.currentTimeMillis());
        String contentToSign = appKey + appSecret + timestamp + body;
        // 使用SHA256算法计算签名
        String signature = DigestUtils.sha256Hex(contentToSign);
        // 封装请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("X-App-Id", appKey);
        headers.put("X-Timestamp", timestamp);
        headers.put("X-Payload-Signature", signature);
        headers.put("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

}
