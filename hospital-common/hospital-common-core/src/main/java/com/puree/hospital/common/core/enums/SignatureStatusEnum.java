package com.puree.hospital.common.core.enums;
/**
 *
 *  是否
 * <AUTHOR>
 */
public enum SignatureStatusEnum {
    NORMAL(0, "正常"),
    EXPIRED(1, "已失效"),
    NOT_APPLIED_FOR(2, "未申请");

    private final Integer code;
    private final String info;

    SignatureStatusEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
