package com.puree.hospital.common.core.enums;
/**
 *
 *  患教-标签用户行为类型;0为点赞，1为关注，2为收藏
 * <AUTHOR>
 * @Data 2024-2-29 11:54:53
 */

public enum LabelBehaviorEnum {
    LIKE(0, "点赞"),
    FOLLOW(1, "关注"),
    COLLECT(2, "收藏");

    private final Integer code;
    private final String info;

    LabelBehaviorEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
