package com.puree.hospital.common.core.utils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 体检项范围命中工具
 *
 * <AUTHOR>
 * @date 2022/11/30 15:42
 */
public class ScopeUtil {
    /**
     * 判断数值是否在区间范围内
     *
     * @param number   数值
     * @param numRange 开闭区间
     * @return boolean
     */
    public static boolean inNumRange(Double number, String numRange) {
        Objects.requireNonNull(numRange);
        String[] pairs = numRange.split(",");
        // 获取开闭区间的最小值和最大值
        List<String> rangeNums = Arrays.stream(pairs).map(str -> str.replaceAll("[(|)|\\[|\\]]", "").trim()).collect(Collectors.toList());
        Double minValue = "".equals(rangeNums.get(0)) ? null : Double.valueOf(rangeNums.get(0));
        Double maxValue = "".equals(rangeNums.get(1)) ? null : Double.valueOf(rangeNums.get(1));

        // 判定数值是否大于最小值
        boolean minMatched = (minValue == null) || (pairs[0].startsWith("[") ? number >= minValue : number > minValue);
        // 判定数值是否小于最大值
        boolean maxMatched = (maxValue == null) || (pairs[1].endsWith("]") ? number <= maxValue : number < maxValue);

        return minMatched && maxMatched;
    }

    /**
     * 获取范围卡点
     *
     * @param rangeList 范围字符串集合
     * @return 卡点集合
     */
    public static List<Map<String, String>> getRangeDots(List<String> rangeList) {
        Set<BigDecimal> set = new TreeSet<>();
        if (StringUtils.isNotEmpty(rangeList)) {
            for (String range : rangeList) {
                String[] pairs = range.split(",");
                // 获取开闭区间的最小值和最大值
                List<String> rangeNums = Arrays.stream(pairs).map(str -> str.replaceAll("[(|)|\\[|\\]]", "").trim()).collect(Collectors.toList());
                Double minValue = "".equals(rangeNums.get(0)) ? null : Double.valueOf(rangeNums.get(0));
                Double maxValue = "".equals(rangeNums.get(1)) ? null : Double.valueOf(rangeNums.get(1));
                if (StringUtils.isNotNull(minValue)) {
                    set.add(BigDecimal.valueOf(minValue));
                }
                if (StringUtils.isNotNull(maxValue)) {
                    set.add(BigDecimal.valueOf(maxValue));
                }
            }
        }
        List<BigDecimal> list = new ArrayList<>(set);
        if (set.size() == 1) {
            set.add(BigDecimal.valueOf(0));
            set.add(list.get(0).multiply(BigDecimal.valueOf(2)));
        } else if (set.size() == 2) {
            BigDecimal sub = list.get(1).subtract(list.get(0));
            set.add(list.get(1).add(sub));
            if (list.get(0).compareTo(sub) < 0) {
                set.add(BigDecimal.valueOf(0));
            } else {
                set.add(list.get(0).subtract(sub));
            }
        } else if (set.size() > 2) {
            BigDecimal subMax = BigDecimal.valueOf(0);
            for (int i = 0; i < list.size() && i < list.size() - 1; i++) {
                BigDecimal sub = list.get(i + 1).subtract(list.get(i));
                subMax = sub.max(subMax);
            }
            set.add(list.get(list.size() - 1).add(subMax));
            if (list.get(0).compareTo(subMax) < 0) {
                set.add(BigDecimal.valueOf(0));
            } else {
                set.add(list.get(0).subtract(subMax));
            }
        }
        list.clear();
        list.addAll(set);
        List<Map<String, String>> returnList = new ArrayList<>();
        for (int i = 0; i < list.size() && i < list.size() - 1; i++) {
            HashMap<String, String> map = new HashMap<>();
            map.put("min", list.get(i)+"");
            map.put("max", list.get(i + 1)+"");
            returnList.add(map);
        }
        return returnList;
    }
}
