package com.puree.hospital.common.core.enums;

/**
 * 诊疗订单状态枚举
 */
public enum ConsultationOrderStatusEnum {
    /**
     * 待支付
     */
    UNPAID("0", "待支付"),
    PAID("1", "待接诊"),
    VISITING("2", "问诊中"),
    PRESCRIBED("3", "问诊中(已开处方)"),
    COMPLETE("4", "已完成"),
    RETURNED("6", "已退号"),
    REFUNDED("7", "已退款"),
    CANCEL("8", "已取消"),
    REFUNDING("9", "退款中"),
    RESERVED_NOT_ARRIVED("10", "已预约(未报到)"),
    RESERVED_ALREADY_ARRIVED("11", "已预约(已报到)"),
    NUMBER_PASSED("12", "已过号");

    private final String code;
    private final String info;

    ConsultationOrderStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo() {
        return info;
    }

    /**
     * 根据状态获取枚举
     *
     * @param status 状态码
     * @return 枚举
     */
    public static ConsultationOrderStatusEnum getByStatus(String status) {
        for (ConsultationOrderStatusEnum statusEnum : ConsultationOrderStatusEnum.values()) {
            if (statusEnum.getCode().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 是否在问诊中
     *
     * @param status 状态码
     * @return 是否在问诊中
     */
    public static boolean isInProgress(String status) {
        return VISITING.getCode().equals(status) || PRESCRIBED.getCode().equals(status);
    }

    public static boolean isUnComplete(String status) {
        return !COMPLETE.getCode().equals(status);
    }
}
