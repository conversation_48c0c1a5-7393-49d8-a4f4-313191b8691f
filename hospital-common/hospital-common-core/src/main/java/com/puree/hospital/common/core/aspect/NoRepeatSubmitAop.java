package com.puree.hospital.common.core.aspect;

import com.puree.hospital.common.core.annotation.NoRepeatSubmit;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.api.domain.AjaxResult;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

@Aspect
@Configuration
public class NoRepeatSubmitAop {
    private Logger logger= LoggerFactory.getLogger(NoRepeatSubmitAop.class);
    @Autowired
    private RedisTemplate redisTemplate;

    @Pointcut("@annotation(noRepeatSubmit)")
    public void pointCut(NoRepeatSubmit noRepeatSubmit) {
    }

    @Around("pointCut(noRepeatSubmit)")
    public AjaxResult around(ProceedingJoinPoint pjp, NoRepeatSubmit noRepeatSubmit) throws Throwable {
        ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ra.getRequest();
        if(StringUtils.isNull(request)){
            return AjaxResult.error("请求为空");
        }

        int seconds = noRepeatSubmit.seconds();
        int maxCount = noRepeatSubmit.maxCount();
        //获取请求Ip
        String ip=request.getRemoteAddr();
        //将请求路径及ip组成键值 如：/finance/fastApply/test:************
        String key = request.getServletPath() + ":" + ip ;
        //获取请求次数
        Integer count = (Integer) redisTemplate.opsForValue().get(key);

        if (null == count) {//第一次访问
            redisTemplate.opsForValue().set(key, 1,seconds, TimeUnit.SECONDS);
            pjp.proceed();
            return AjaxResult.success("第一次请求成功");
        }else if (count < maxCount) {//在允许的访问次数内
            count = count+1;
            redisTemplate.opsForValue().set(key, count,0);
            pjp.proceed();
            return AjaxResult.success("请求成功");
        }else  {//超出访问次数
            logger.info("访问过快ip ===> " + ip + " 且在 " + seconds + " 秒内超过最大限制 ===> " + maxCount + " 请求次数达到 ===> " + count);
            return AjaxResult.error("请求过快");
        }
    }

}
