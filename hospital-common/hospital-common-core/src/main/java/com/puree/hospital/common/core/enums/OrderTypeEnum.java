package com.puree.hospital.common.core.enums;

public enum OrderTypeEnum {
    // 订单分类
    CONSULTATION("0", "问诊订单"),
    DRUGS("1", "药品订单"),
    SERVICE_PACK("2", "服务包订单"),
    GOODS("3", "购物车/商品订单");

    private final String code;
    private final String info;

    OrderTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
