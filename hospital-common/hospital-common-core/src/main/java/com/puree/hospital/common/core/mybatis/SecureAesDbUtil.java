package com.puree.hospital.common.core.mybatis;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

/**
 * @ClassName SecureAesDbUtil
 * <AUTHOR>
 * @Description 数据库的 AES 安全工具类
 * @Date 2023/12/1 11:34
 * @Version 1.0
 */
public final class SecureAesDbUtil {


    /**
     * AES 加密密钥 256 位
     */
    private final static byte[] AES_KEY = new byte[]{-99, 23, -123, 63, 0, 39, 87, 21, -14, 82, -107, -37, -125, 20, -8, 103, -106, -62, 51, -17, 80, -63, -91, -17, 71, -57, 64, -38, -43, 23, -8, 105};

    /**
     * aes 加密
     */
    private final static AES aes = SecureUtil.aes(AES_KEY);

    private SecureAesDbUtil() {
        throw new IllegalStateException("工具类不允许实例化");
    }


    /**
     * @Param str
     * @Return java.lang.String
     * @Description 默认采用 UTF-8 字符集 加密
     * <AUTHOR>
     * @Date 2023/12/1 11:36
     **/
    public static String encryptHex(String str){
        if (StrUtil.isEmpty(str)) {
            return "";
        }
        return aes.encryptHex(str);
    }


    /**
     * @Param str
     * @Return java.lang.String
     * @Description 默认采用 UTF-8 字符集 解密
     * <AUTHOR>
     * @Date 2023/12/1 11:38
     **/
    public static String decryptStr(String str){
        if (StrUtil.isEmpty(str)) {
            return "";
        }
        return aes.decryptStr(str);
    }
}
