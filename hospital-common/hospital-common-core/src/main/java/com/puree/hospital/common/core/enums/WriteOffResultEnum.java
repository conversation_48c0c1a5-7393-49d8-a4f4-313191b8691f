package com.puree.hospital.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务包核销码核销结果枚举
 *
 * <AUTHOR>
 * @date 2024/2/21 10:48
 */
@Getter
@AllArgsConstructor
public enum WriteOffResultEnum {
    SUCCESS(0, "核销成功"),
    NOT_CHANNEL_USER(1, "非渠道合伙人用户"),
    SERVICE_NOT_EXIST(2, "服务包已下架或删除"),
    USER(3, "核销码已使用"),
    DISCARD(4, "核销码已作废"),
    EXPIRED(5, "核销码已过期"),
    GROUP_LIMIT(6, "群组达到上限");

    private Integer code;
    private String desc;
}
