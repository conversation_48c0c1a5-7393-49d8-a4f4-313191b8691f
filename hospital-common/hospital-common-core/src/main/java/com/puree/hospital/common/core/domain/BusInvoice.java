package com.puree.hospital.common.core.domain;

import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * @ClassName: BusInvoice
 * @Date 2023/11/30 18:43
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
@Data
public class BusInvoice extends Entity {

    private String orderNo ;

    // 1-consultation 2-five 3-order
    private Integer orderType ;

    /**
     * 发票代码,   发蓝色发票返回此字段，用于开具红色发票
     */
    private String invoiceCode ;

    /**
     * 发票号码,   发蓝色发票返回此字段，用于开具红色发票
     */
    private String invoiceNo ;

    /**
     * 发票下载地址
     */
    private String downloadUrl ;

    /**
     * 发票预览地址
     */
    private String picUrl ;

    /**
     * 开具发票失败错误码
     */
    private String invoiceErrorCode ;

    /**
     * 开具发票失败错误信息
     */
    private String invoiceErrorMsg ;

    /**
     * 仅仅用于红冲发票，0-非全额退款 1-全额退款
     */
    private Integer isAllRefund ;






}
