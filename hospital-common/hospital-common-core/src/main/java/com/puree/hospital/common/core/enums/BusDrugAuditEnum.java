package com.puree.hospital.common.core.enums;

public enum BusDrugAuditEnum {
    TO_BE_EXAMINED("0", "待审核"),
    AUDIT_COMPLETED("1", "审核通过"),
    AUDIT_RETURN("2", "审核不通过"),
    CANCEL_THE_AUDIT("3", "已取消");


    private final String  code;
    private final String info;

    BusDrugAuditEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (BusDrugAuditEnum enums : BusDrugAuditEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }
}
