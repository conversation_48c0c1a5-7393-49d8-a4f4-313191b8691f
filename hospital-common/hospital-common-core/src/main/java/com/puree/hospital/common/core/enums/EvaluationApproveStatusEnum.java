package com.puree.hospital.common.core.enums;

/**
 * <p>
 * 评论审批状态
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/27 19:17
 */
public enum EvaluationApproveStatusEnum {

    /**
     * 待审核
     */
    PENDING_APPROVE(0, "待审核"),

    /**
     * 审核通过
     */
    PASSED(1, "审核通过"),

    /**
     * 审核拒绝
     */
    REJECTED(2, "审核拒绝"),;

    private final Integer status;

    private final String desc;

    EvaluationApproveStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isPendingApprove(Integer status) {
        return PENDING_APPROVE.getStatus().equals(status);
    }

    public static boolean isPassed(Integer status) {
        return PASSED.getStatus().equals(status);
    }

    public static boolean isRejected(Integer status) {
        return REJECTED.getStatus().equals(status);
    }

}
