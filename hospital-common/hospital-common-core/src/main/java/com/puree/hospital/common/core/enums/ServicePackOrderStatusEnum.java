package com.puree.hospital.common.core.enums;

/**
 * 服务包订单状态枚举
 */
public enum ServicePackOrderStatusEnum {
    /**
     * 待支付
     */
    TO_BE_PAID("0","待支付"),
    /**
     * 已取消
     */
    CANCELLED("1","已取消"),
    TO_BE_ACTIVATED("2","待激活"),
    ACTIVATED("3","已激活"),
    ENDED("4","已结束"),
    AFTER_SALES("5","售后中"),
    REFUNDED("6","已退款"),
    REFUNDING("7","退款中");


    private final String code;
    private final String info;

    ServicePackOrderStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 判断是否为待支付状态
     * @param code 状态码
     * @return true：待支付；false：其他
     */
    public static boolean isUnPaid(String code) {
        return TO_BE_PAID.getCode().equals(code);
    }

    /**
     * 判断是否为已取消状态
     * @param code 状态码
     * @return true：已取消；false：其他
     */
    public static boolean isCancelled(String code) {
        return CANCELLED.getCode().equals(code);
    }

    /**
     *  判断是否为已激活状态
     * @param code 状态码
     * @return true：未支付或已取消；false：其他
     */
    public static boolean isUnActivated(String code) {
        return !ACTIVATED.getCode().equals(code) && !ENDED.getCode().equals(code) && !AFTER_SALES.getCode().equals(code);
    }
}
