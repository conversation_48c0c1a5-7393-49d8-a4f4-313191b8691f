package com.puree.hospital.common.core.utils.excel.converter.order;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.puree.hospital.common.core.enums.ConsultationOrderTypeEnum;

/**
 * 问诊订单类型转换
 * <AUTHOR>
 */
public class OrderTypeConverter implements Converter<String> {

    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData readCellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) {
        if (readCellData.getStringValue().equals(ConsultationOrderTypeEnum.IMAGETEXT.getInfo())) {
            return ConsultationOrderTypeEnum.IMAGETEXT.getCode();
        }
        if (readCellData.getStringValue().equals(ConsultationOrderTypeEnum.VIDEO.getInfo())) {
            return ConsultationOrderTypeEnum.VIDEO.getCode();
        }
        return "";
    }

    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration
            globalConfiguration) {
        if (value.equals(ConsultationOrderTypeEnum.IMAGETEXT.getCode())) {
            return new WriteCellData(ConsultationOrderTypeEnum.IMAGETEXT.getInfo());
        }
        if (value.equals(ConsultationOrderTypeEnum.VIDEO.getCode())) {
            return new WriteCellData(ConsultationOrderTypeEnum.VIDEO.getInfo());
        }
        return null;
    }
}
