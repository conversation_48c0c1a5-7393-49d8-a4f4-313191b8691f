package com.puree.hospital.common.core.enums;

/**
 * @ClassName: InvoiceDirectEnum
 * @Date 2023/10/24 16:34
 * <AUTHOR> jian
 * @Description: InvoiceDirectEnum
 * @Version 1.0
 */
public enum InvoiceDirectEnum {

    BLUE_INVOICE(0, "正数发票") ,
    RED_INVOICE(1, "负数发票")

    ;

    private Integer id;
    private String msg;

    InvoiceDirectEnum(Integer id, String msg){
        this.id=id;
        this.msg=msg;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }



}
