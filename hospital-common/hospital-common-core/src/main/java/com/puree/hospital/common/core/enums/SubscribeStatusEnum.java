package com.puree.hospital.common.core.enums;

/**
 * 订阅状态
 * <AUTHOR>
 * @date 2024/09/10
 */
public enum SubscribeStatusEnum {
    /**
     * 订阅状态
     */
    SUBSCRIBE(1,"订阅"),

    /**
     * 未订阅
     */
    UNSUBSCRIBE(0,"未订阅");

    private final Integer value;

    private final String status;

    SubscribeStatusEnum(Integer value, String status) {
        this.value = value;
        this.status = status;
    }

    public Integer getValue() {
        return value;
    }

    public String getStatus() {
        return status;
    }
}
