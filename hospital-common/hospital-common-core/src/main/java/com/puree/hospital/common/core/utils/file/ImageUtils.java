package com.puree.hospital.common.core.utils.file;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.puree.hospital.common.core.utils.StringUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
public class ImageUtils {
    private static final Logger log = LoggerFactory.getLogger(ImageUtils.class);

    public static byte[] getImage(String imagePath) {
        InputStream is = getFile(imagePath);
        try {
            return IOUtils.toByteArray(is);
        } catch (Exception e) {
            log.error("图片加载异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(is);
        }
    }

    public static InputStream getFile(String imagePath) {
        try {
            byte[] result = readFile(imagePath);
            result = Arrays.copyOf(result, result.length);
            return new ByteArrayInputStream(result);
        } catch (Exception e) {
            log.error("获取图片异常 {}", e);
        }
        return null;
    }

    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @return 字节数据
     */
    public static byte[] readFile(String url) {
        try {
            return IOUtils.toByteArray(readFileToStream(url));
        }catch (Exception e){
            log.error("访问文件异常", e);
            return null;
        }
    }

    /**
     * 读取网络文件为流
     *
     * @param url 地址
     * @return InputStream
     */
    public static InputStream readFileToStream(String url) {
        InputStream in = null;
        ByteArrayOutputStream baos = null;
        try {
            // 网络地址
            URL urlObj = new URL(url);
            URLConnection urlConnection = urlObj.openConnection();
            urlConnection.setConnectTimeout(30 * 1000);
            urlConnection.setReadTimeout(60 * 1000);
            urlConnection.setDoInput(true);
            return urlConnection.getInputStream();
        } catch (Exception e) {
            log.error("访问文件异常", e);
            return null;
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(baos);
        }
    }

    /**
     * dataUriScheme base64转图片
     *
     * @param base64 base64
     * @return 输入流
     * data: image/gif;base64, ---------------- base64编码的gif图片数据
     * data: image/png;base64, -------------- base64编码的png图片数据
     * data: image/jpeg;base64, ------------- base64编码的jpeg图片数据
     * data: image/bmp;base64, ------------- base64编码的bmp图片数据
     */
    public static InputStream dataUriSchemeBase64ToFile(String base64) {
        base64 = base64.split(",")[1];
        return base64ToStream(base64);
    }

    /**
     * base64转图片
     *
     * @param base64 base64
     * @return 流
     */
    public static InputStream base64ToStream(String base64) {
        if (StringUtils.isEmpty(base64)) {
            return null;
        }
        try {
            // Base64解码
            byte[] bytes = Base64.decodeBase64(base64);
            for (int i = 0; i < bytes.length; ++i) {
                // 调整异常数据
                if (bytes[i] < 0) {
                    bytes[i] += 256;
                }
            }
            // 生成文件
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            log.error("base64转文件异常", e);
            return null;
        }
    }

    /**
     * 将base64字符解码保存文件
     *
     * @param base64Code base64字符串
     * @param file 文件
     * @throws Exception 未知异常
     */
    public static void decodeBase64File(String base64Code, File file) throws Exception {
        byte[] buffer = Base64.decodeBase64(base64Code);
        FileOutputStream out = new FileOutputStream(file);
        out.write(buffer);
        out.close();
    }

    /**
     * 添加水印
     *
     * @param text            水印字符串
     * @param sourceImageFile 源图片
     * @param destImageFile   加完水印图片
     */
    public static void addTextWatermark(String text, File sourceImageFile, File destImageFile) {
        try {
            BufferedImage sourceImage = ImageIO.read(sourceImageFile);
            Graphics2D g2d = (Graphics2D) sourceImage.getGraphics();

            int height = sourceImage.getHeight();
            int width = sourceImage.getWidth();
            int fontSize = (height + width) / 35;
            // initializes necessary graphic properties
            AlphaComposite alphaChannel = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f);
            g2d.setComposite(alphaChannel);
            g2d.setColor(Color.GRAY);
            g2d.setFont(new Font("黑体", Font.PLAIN, fontSize));
            g2d.rotate(-0.2);
            // 间隔
            int split = fontSize * 3;
            // 文字占用的宽度
            int xWidth = getStrWidth(text, fontSize);
            // x,y可以绘制的数量,多加一个补充空白
            int xCanNum = width / xWidth + 1;
            int yCanNum = height / fontSize + 1;
            for (int i = 1; i <= yCanNum; i++) {
                int y = fontSize * i + split * i;
                for (int j = 0; j < xCanNum; j++) {
                    int x = xWidth * j + split * j;
                    g2d.drawString(text, x, y - (fontSize + split) * j);
                }
            }
            ImageIO.write(sourceImage, "png", destImageFile);
            g2d.dispose();
        } catch (IOException ex) {
            log.error("添加水印失败", ex);
        }
    }

    /**
     * 获取字符串占用宽度
     *
     * @param str      字符串
     * @param fontSize 字体大小
     * @return 宽度
     */
    public static int getStrWidth(String str, int fontSize) {
        char[] chars = str.toCharArray();
        int fontSize2 = fontSize / 2;

        int width = 0;

        for (char c : chars) {
            int len = String.valueOf(c).getBytes().length;
            // 汉字为3,其余1
            // 可能还有一些特殊字符占用2等等,统统计为汉字
            if (len != 1) {
                width += fontSize;
            } else {
                width += fontSize2;
            }
        }

        return width;
    }

    /**
     * 将网络路径图片转为base64的格式
     * @param requestUrl 请求网络路径
     * @param photoType 响应的格式(png,jpg,ico等)
     * @throws Exception
     */
    public static String getUrlImageToBase64(String requestUrl, String photoType) throws Exception {
        ByteArrayOutputStream data = new ByteArrayOutputStream();
        try {
            // 创建URL
            URL url = new URL(requestUrl);
            byte[] by = new byte[1024];


            HttpRequest get = HttpUtil.createGet(requestUrl);
            HttpResponse execute = get.execute();
            log.info("响应数据长度为:{}",execute.contentLength());
            InputStream inputStream = execute.bodyStream();

            // 将内容读取内存中
            int len = -1;
            while ((len = inputStream.read(by)) != -1) {

                data.write(by, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 对字节数组Base64编码
        java.util.Base64.Encoder encoder = java.util.Base64.getEncoder();
        byte[] byteArray = data.toByteArray();

        return "data:image/" + photoType + ";base64," + encoder.encodeToString(byteArray);
    }

}
