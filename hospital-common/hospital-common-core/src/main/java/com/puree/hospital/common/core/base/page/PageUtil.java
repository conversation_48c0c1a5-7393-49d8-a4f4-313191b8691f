package com.puree.hospital.common.core.base.page;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.utils.ServletUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.sql.SqlUtil;
import com.puree.hospital.common.api.domain.PageDomain;
import com.puree.hospital.common.core.web.page.TableSupport;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @ClassName PageUtil
 * <AUTHOR>
 * @Description 分页工具类
 * @Date 2024/2/5 12:00
 * @Version 1.0
 */
@Slf4j
public final class PageUtil {

    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 分页参数合理化
     */
    public static final String REASONABLE = "reasonable";
    /**
     * 默认页码
     */
    public static final Integer DEFAULT_PAGE_NUM = 1;
    /**
     * 默认页大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 10;

    /**
     * @Param
     * @Return void
     * @Description 设置请求分页数据
     * <AUTHOR>
     * @Date 2024/2/5 12:07
     **/
    public static void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        pageDomain.setReasonable(false);
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    /**
     * @Param pageNum 第几页
     * @Param pageSize 页大小
     * @Return void
     * @Description 设置请求分页数据
     * <AUTHOR>
     * @Date 2024/3/26 16:06
     **/
    public static void startPage(Integer pageNum, Integer pageSize) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        pageDomain.setReasonable(false);
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    /**
     * @Param null
     * @Return
     * @Description 分页信息转换
     * <AUTHOR>
     * @Date 2024/2/27 15:37
     **/
    public static <T, E> List<E> buildPage(List<T> sourceList, List<E> targetList) {
        if (sourceList instanceof Page) {
            Page page = (Page) sourceList;
            long total = page.getTotal();
            int pageNum = page.getPageNum();
            int pageSize = page.getPageSize();
            //构建目标 page
            Page<E> targetPage = new Page<>(pageNum, pageSize);
            targetPage.setTotal(total);
            targetPage.addAll(targetList);
            return targetPage;
        }
        return targetList;
    }

    /**
     * @Param sourceList
     * @Param targetList
     * @Param total 自定义 total 倍率
     * @Param operator 1 +，2 -，3 *，4 /
     * @Return java.util.List<E>
     * @Description 自定义 total 倍率
     * <AUTHOR>
     * @Date 2024/6/6 18:16
     **/
    public static <T, E> List<E> buildPage(List<T> sourceList, List<E> targetList, long totalMagnification, int operator) {
        if (sourceList instanceof Page) {
            Page page = (Page) sourceList;
            long total = 0;

            if (operator == 1) {
                total = page.getTotal() + totalMagnification;
            } else if (operator == 2) {
                total = page.getTotal() - totalMagnification;
            } else if (operator == 3) {
                total = page.getTotal() * totalMagnification;
            } else if (operator == 4) {
                total = page.getTotal() / totalMagnification;
            }

            int pageNum = page.getPageNum();
            int pageSize = page.getPageSize();
            //构建目标 page
            Page<E> targetPage = new Page<>(pageNum, pageSize);
            targetPage.setTotal(total);
            targetPage.addAll(targetList);
            return targetPage;
        }
        return targetList;
    }

    /**
     * @Param list
     * @Return java.lang.Long
     * @Description 获取分页总数
     * <AUTHOR>
     * @Date 2024/2/5 12:07
     **/
    public static Long getTotal(List<?> list) {
        return new PageInfo<>(list).getTotal();
    }

    /**
     * @Param
     * @Return java.lang.Integer
     * @Description 获取页码
     * <AUTHOR>
     * @Date 2024/2/5 12:18
     **/
    public static Integer getPageNum() {
        return ServletUtils.getParameterToInt(PAGE_NUM);
    }

    /**
     * @Param
     * @Return java.lang.Integer
     * @Description 获取页面大小
     * <AUTHOR>
     * @Date 2024/2/5 12:18
     **/
    public static Integer getPageSize() {
        return ServletUtils.getParameterToInt(PAGE_SIZE);
    }

    public static <T> List<T> buildPage(List<T> sourceList, Integer inputPageSize , Integer inputPageNum) {
        if ( null==inputPageSize || null==inputPageNum || 0==inputPageSize || 0==inputPageNum ) {
            return sourceList ;
        }

        Integer totalCount = sourceList.size() ;
        Integer pageSize = Math.min(totalCount, inputPageSize) ;
        int totalPage = (0 == (totalCount % pageSize)) ? (totalCount / pageSize) : ((totalCount / pageSize) +1 ) ;
        int pageNum = Math.min(inputPageNum, totalPage) ;

        int fromIndex = (pageNum - 1) * pageSize ;
        int toIndex = Math.min(( fromIndex + pageSize), totalCount) ;
        return sourceList.subList(fromIndex, toIndex);

    }

    /**
     * Paging对镜已经放到api中
     *
     * @param obj 分月数据
     * @return Paging
     * @param <T> 泛型类
     */
    public static <T> Paging<T> buildPage(T obj) {
        if (!(obj instanceof List)) {
            throw new RuntimeException("数据类型错误");
        }
        return Paging.success(obj, getTotal((List) obj), getPageNum(), getPageSize());
    }

}
