package com.puree.hospital.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 用药预订单状态枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/20 19:05
 */
@Getter
@AllArgsConstructor
public enum PreOrderStatusEnum {

    /**
     * 待开处方
     */
    PENDING_PRESCRIBE("0", "待开处方"),

    /**
     * 已开处方
     */
    PRESCRIBED("1", "已开处方");

    private final String status;

    private final String desc;

    /**
     * 是否已开处方
     *
     * @param status 状态
     * @return boolean
     */
    public static boolean isPrescribed(String status) {
        return PRESCRIBED.getStatus().equals(status);
    }

    /**
     * 是否待开处方
     *
     * @param status 状态
     * @return boolean
     */
    public static boolean isPendingPrescribe(String status) {
        return PENDING_PRESCRIBE.getStatus().equals(status);
    }

}
