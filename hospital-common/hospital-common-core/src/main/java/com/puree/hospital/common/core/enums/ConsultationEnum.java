package com.puree.hospital.common.core.enums;

public enum ConsultationEnum {

    GRAPHIC(0,"图文问诊"),
    VIDEO(1,"视频问诊"),
    FURTHER(2,"复诊");

    private final Integer code;
    private final String info;

    ConsultationEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}
