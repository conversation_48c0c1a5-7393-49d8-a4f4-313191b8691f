package com.puree.hospital.common.core.utils.excel.converter.order;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.OrderDrugsTypeEnum;

public class DrugsOrderDrugsTypeConverter implements Converter<String> {
    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration
            globalConfiguration) {
        if (value.equals(OrderDrugsTypeEnum.OFFICINA.getCode())) {
            return new WriteCellData(OrderDrugsTypeEnum.OFFICINA.getInfo());
        }
        if (value.equals(OrderDrugsTypeEnum.ENTERPRISE.getCode())) {
            return new WriteCellData(OrderDrugsTypeEnum.ENTERPRISE.getInfo());
        }
        if (value.equals(OrderDrugsTypeEnum.PARTIES.getCode())) {
            return new WriteCellData(OrderDrugsTypeEnum.PARTIES.getInfo());
        }
        return new WriteCellData("");
    }
}
