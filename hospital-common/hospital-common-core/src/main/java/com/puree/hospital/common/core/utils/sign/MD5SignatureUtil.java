package com.puree.hospital.common.core.utils.sign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.puree.hospital.common.core.utils.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <p>
 * MD5签名工具类
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/5 14:18
 */
public class MD5SignatureUtil {

    /**
     * 根据packageParams和API_KEY生成签名
     *
     * @param signParam 待签名参数
     * @param appSecret 密钥
     * @return 签名
     */
    public static String createSign(Object signParam, String appSecret) {
        return DigestUtil.md5Hex(getPreSignString(signParam, appSecret).getBytes(StandardCharsets.UTF_8));
    }


    /**
     * 是否签名正确,规则是:按参数名称a-z排序,遇到空值的参数不参加签名。
     *
     * @param signParam 待签名的参数
     * @param appSecret 秘钥信息
     * @param sign      待验证签名信息
     * @return 验证结果过
     */
    public static boolean verifySign(Object signParam, String appSecret, String sign) {
        //计算签名
        String calcSign = createSign(signParam, appSecret);
        return calcSign.equals(sign);
    }

    /**
     * 获取签名签名前的字符串
     *
     * @param signParams 需要签名的参数
     * @param appSecret  签名的密钥
     * @return 签名前的字符串
     */
    public static String getPreSignString(Object signParams, String appSecret) {
        String params = getPreSignString(signParams);
        return params + "key=" + appSecret;
    }

    /**
     * 获取签名签名前的字符串
     *
     * @param signParam 需要签名的参数
     * @return 签名前的字符串
     */
    public static String getPreSignString(Object signParam) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> paramsMap = MapUtil.sort(BeanUtil.beanToMap(signParam));
        for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
            String k = entry.getKey();
            String v = String.valueOf(entry.getValue());
            if (StringUtils.isNotBlank(v) && !"sign".equals(k) && !"key".equals(k) && !"signature".equals(k)) {
                sb.append(k).append("=").append(v).append("&");
            }
        }
        return sb.toString();
    }
}
