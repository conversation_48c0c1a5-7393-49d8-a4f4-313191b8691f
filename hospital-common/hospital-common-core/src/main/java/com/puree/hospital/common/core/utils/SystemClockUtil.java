package com.puree.hospital.common.core.utils;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SystemClockUtil {
    private volatile long now;
    private long period;

    private SystemClockUtil(long period) {
        this.period = period;
        // 先初始化为当前时间，不然第一次调用会有问题
        this.now = System.currentTimeMillis();
        // 后台线程任务开启定时刷新 now 的值
        scheduleGetSystemTime();
    }

    // 对外方法
    public static long currentTimeMillis() {
        return Instance.INSTANCE.getCurrentMillis();
    }

    private void scheduleGetSystemTime() {
        ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(runnable -> {
            Thread thread = new Thread(runnable, "system-lock-thread");
            thread.setDaemon(true);
            return thread;
        });
        scheduledExecutorService.scheduleAtFixedRate(() -> now = System.currentTimeMillis(), 0, period, TimeUnit.MILLISECONDS);
    }

    private static class Instance {
        private final static SystemClockUtil INSTANCE = new SystemClockUtil(1);
    }

    private long getCurrentMillis() {
        return now;
    }

    public static void main(String[] args) {
        long time = SystemClockUtil.currentTimeMillis();
        long time1 = time + 2 * 60 * 1000;
        System.out.println(time);
        System.out.println(time1);
    }
}
