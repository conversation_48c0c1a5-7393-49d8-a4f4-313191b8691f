package com.puree.hospital.common.core.enums;

/**
 * 服务包im状态
 */
public enum ServicePackImStatusEnum {
    TO_BE_ACTIVATED("0", "待激活"),
    ACTIVATED("1", "已激活"),
    ENDED("2", "已过期"),
    REFUNDED("3", "已退款");
    private final String code;
    private final String info;

    ServicePackImStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
