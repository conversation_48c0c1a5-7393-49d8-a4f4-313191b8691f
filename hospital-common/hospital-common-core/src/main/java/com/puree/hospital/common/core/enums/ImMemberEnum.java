package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

public enum ImMemberEnum {
    DOCTOR("0","医生"),
    PSYCHOLOGY_CONSULT("1","药师"),
    NURSE("2","护师"),
    ASSISTANT("3","医助"),
    HEALTH_MANAGER("4","健康管理师"),
    PSYCHOLOGICAL_CONSULTATION_TEACHER("5","心理咨询师"),
    REHABILITATION_SPECIALIST("9","康复师"),
    DIETITIAN("10","营养师"),
    PATIENT("15","患者");
    private final String code;
    private final String info;

    ImMemberEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static ImMemberEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (ImMemberEnum enums : ImMemberEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
