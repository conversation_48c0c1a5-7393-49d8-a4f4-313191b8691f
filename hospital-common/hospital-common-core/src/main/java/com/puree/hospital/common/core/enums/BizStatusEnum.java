package com.puree.hospital.common.core.enums;

/**
 * <AUTHOR>
 * @date 2023/10/20 18:32
 */
public enum BizStatusEnum {
    CAN_NOT_TERMINATE("188", "当前回合未结束,无法结束问诊"),
    CAN_NOT_MODIFY("168", "您与该医生有已完成的订单，不支持修改"),
    NOT_WAITING_STATUS("108", "该问诊订单不是待接诊状态");
    private String code;
    private String info;

    BizStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
