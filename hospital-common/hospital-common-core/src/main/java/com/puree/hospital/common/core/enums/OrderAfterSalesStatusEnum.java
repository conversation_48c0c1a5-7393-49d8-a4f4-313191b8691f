package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * @ClassName: OrderAfterSalesStatusEnum
 * @Date 2023/3/6 17:33
 * <AUTHOR>
 * @Description: 药品/商品售后状态
 * @Version 1.0
 */
public enum OrderAfterSalesStatusEnum {

    APPLY_AFTER_SALES_SERVICE("0", "买家申请售后"),
    DISAPPROVED_AFTER_SALE("1", "不同意售后，待买家处理"),
    AGREE_TO_RETURN_AFTER_SALE("2", "已同意售后，待买家退货"),
    RETURNED_GOODS_TO_BE_CONFIRMED("3", "买家已退货，待确认收货"),
    REFUSE_RECEIVE_GOODS("4", "已拒绝收货，待买家处理"),
    CONFIRM_RECEIPT_RESEND_GOODS("5", "已确认收货，待重新发货"),
    RESHIP_BUYER_RECEIVES_GOODS("6", "已重新发货，待买家收货"),
    AFTER_SALE("7", "售后结束"),
    AFTER_SALE_CLOSURE("8", "售后关闭"),
    UNDER_REFUND("9", "退款中");
    private final String code;
    private final String info;


    OrderAfterSalesStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }


    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static OrderAfterSalesStatusEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (OrderAfterSalesStatusEnum enums : OrderAfterSalesStatusEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 判断是否售后关闭或结束
     * @param value 状态值
     * @return true：售后关闭或结束，false：未关闭或结束
     */
    public static boolean isCloseOrEnd(String value){
        return AFTER_SALE_CLOSURE.getCode().equals(value) || AFTER_SALE.getCode().equals(value);
    }

    public static boolean isClose(String value) {
        return AFTER_SALE_CLOSURE.getCode().equals(value);
    }

    public static boolean isEnd(String value) {
        return AFTER_SALE.getCode().equals(value);
    }
}
