package com.puree.hospital.common.core.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class DoubleUtils {
    private static final int DEF_DIV_SCALE = 2;

    /**
     * @Description 两个Double数相加
     *
     * @param d1
     * @param d2
     * @return Double
     */
    public static Double add(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.add(b2).doubleValue();
    }

    /**
     * @Description 两个Double数相减
     *
     * @param d1
     * @param d2
     * @return Double
     */
    public static Double sub(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.subtract(b2).doubleValue();
    }

    /**
     * @Description 两个Double数相乘
     *
     * @param d1
     * @param d2
     * @return Double
     */
    public static Double mul(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.multiply(b2).doubleValue();
    }

    /**
     * @Description 两个Double数相除
     *
     * @param d1
     * @param d2
     * @return Double
     */
    public static Double div(Double d1,Double d2){
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.divide(b2,DEF_DIV_SCALE,BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * @Description 两个Double数相除，并保留scale位小数
     *
     * @param d1
     * @param d2
     * @param scale
     * @return Double
     */
    public static Double div(Double d1,Double d2,int scale){
        if(scale<0){
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(d1.toString());
        BigDecimal b2 = new BigDecimal(d2.toString());
        return b1.divide(b2,scale,BigDecimal.ROUND_HALF_UP).doubleValue();
    }



    /**
     * 将double格式化为指定小数位的String，不足小数位用0补全
     *
     * @param v     需要格式化的数字
     * @param scale 小数点后保留几位
     * @return
     */
    public static String roundByScaleOfDob(double v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("保留小数位必须是正整数或零");
        }
        if (scale == 0) {
            return new DecimalFormat("0").format(v);
        }
        StringBuilder formatStr = new StringBuilder("0.");
        for (int i = 0; i < scale; i++) {
            formatStr.append("0");
        }
        return new DecimalFormat(formatStr.toString()).format(v);
    }

    public static String roundByScaleOfStr(String v, int scale) {
        double parseDouble = Double.parseDouble(v);
        return roundByScaleOfDob(parseDouble,scale);
    }
}
