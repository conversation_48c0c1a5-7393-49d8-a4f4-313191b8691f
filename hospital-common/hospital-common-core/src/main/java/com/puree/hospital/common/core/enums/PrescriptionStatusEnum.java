package com.puree.hospital.common.core.enums;

/**
 * 处方状态
 *
 * <AUTHOR>
 * @date  2024/3/4 17:17
 */
public enum PrescriptionStatusEnum {

    NOT_EXAMINE("0", "待审核") ,
    NOT_PASS("1", "未通过") ,
    PASS("2", "通过") ,
    CANCELLATION("3", "已作废") ,
    USED("4", "已使用"),
    INVALID("5", "失效")

    ;


    private String status ;
    private String msg ;

    PrescriptionStatusEnum(String status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
