package com.puree.hospital.common.core.constant;

/**
 * 腾讯Im 通用常量信息
 * <AUTHOR>
 *
 */
public class TencentyunImConstants {

	
	/**
	 * redis IM缓存名称
	 */
	public static final String USERSIG = "usersig:";

	/**
	 * 医生账号前缀
	 */
	public static final String DOCTOR_IM_ACCOUNT = "DOCTOR_";

    /**
     * 医生账号前缀
     */
    public static final String DOCTOR_IM_ACCOUNT_PREFIX = "DOCTOR";

	/**
	 * 护师 医助 健康管理师 心理咨询师 账号前缀
	 */
	public static final String PHYSICIAN_IM_ACCOUNT = "PHYSICIAN_";

	/**
	 * 药师账号前缀
	 */
	public static final String PHARMACIST_IM_ACCOUNT = "PHARMACIST_";

	/**
	 * 患者账号前缀
	 */
	public static final String PATIENT_IM_ACCOUNT = "PATIENT_";

    /**
     * 患者账号前缀
     */
	public static final String PATIENT_IM_ACCOUNT_PREFIX = "PATIENT";

	/**
	 * redis RTC缓存名称
	 */
	public static final String PRIVATEMAPKEY = "privatemapkey";
	
	/**
	 * Im群组类型
	 */
	public static final String IM_GROUP_TYPE = "Private";

	/**
	 * 接口返回状态
	 */
	public static final String ACTIONSTATUS = "ActionStatus";

	/**
	 * app管理员
	 */
	public static final String ADMINISTRATOR = "administrator";

	/**
	 * 文本消息
	 */
	public static final String TIM_TEXT_ELEM = "TIMTextElem";

	/**
	 * 自定义消息
	 */
	public static final String TIM_CUSTOM_ELEM = "TIMCustomElem";

    /**
     *  图片消息
     */
    public static final String TIM_IMAGE_ELEM = "TIMImageElem";

    /**
     * 音频
     */
    public static final String TIM_SOUND_ELEM = "TIMSoundElem";

    /**
     * 视频
     */
    public static final String TIM_VIDEO_FILE_ELEM = "TIMVideoFileElem";

    /**
     * 文件
     */
    public static final String TIM_FILE_ELEM = "TIMFileElem";

	/**
	 * 导入单个帐号
	 */
	public static final String ACCOUNT_IMPORT="https://console.tim.qq.com/v4/im_open_login_svc/account_import?"
	 + "sdkappid=%s"
	 + "&identifier=%s"
	 + "&usersig=%s"
	 + "&random=%s"
	 + "&contenttype=json";

	/**
	 * 查询账号信息
	 */
	public static final String ACCOUNT_CHECK="https://console.tim.qq.com/v4/im_open_login_svc/account_check?"
			+ "sdkappid=%s"
			+ "&identifier=%s"
			+ "&usersig=%s"
			+ "&random=%s"
			+ "&contenttype=json";

	/**
	 * 导入多个帐号
	 */
	public static final String MULTIACCOUNT_IMPORT="https://console.tim.qq.com/v4/im_open_login_svc/multiaccount_import?"
	 + "sdkappid=%s"
	 + "&identifier=%s"
	 + "&usersig=%s"
	 + "&random=%s"
	 + "&contenttype=json"; 
	/**
	 * 删除账号
	 */
	public static final String ACCOUNT_DELETE="https://console.tim.qq.com/v4/im_open_login_svc/account_delete?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";

	/**
	 * 创建群组
	 */
	public static final String CREATE_GROUP="https://console.tim.qq.com/v4/group_open_http_svc/create_group?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json"; 

	/**
	 * 获取用户所在群组
	 */
	public static final String GET_JOINED_GROUP_LIST="https://console.tim.qq.com/v4/group_open_http_svc/get_joined_group_list?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json"; 

	
	/**
	 * 增加群成员
	 */
	public static final String ADD_GROUP_MEMBER="https://console.tim.qq.com/v4/group_open_http_svc/add_group_member?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";
	
	/**
	 * 删除群成员
	 */
	public static final String DELETE_GROUP_MEMBER="https://console.tim.qq.com/v4/group_open_http_svc/delete_group_member?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json"; 
	
	/**
	 * 解散群组
	 */
	public static final String DESTROY_GROUP="https://console.tim.qq.com/v4/group_open_http_svc/destroy_group?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";
	/**
	 * 发送普通消息
	 */
	public static final String SEND_GROUP_MSG="https://console.tim.qq.com/v4/group_open_http_svc/send_group_msg?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json"; 
	
	/**
	 * 下载最近消息记录
	 */
	public static final String GET_HISTORY="https://console.tim.qq.com/v4/open_msg_svc/get_history?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";
	
	/**
	 * 消息回调
	 */
	public static final String  MESSAGE_CALLBACK="https://www.example.com?"
			+ "SdkAppid=%s"
			+ "&CallbackCommand=%s"
			+ "&contenttype=json"
			+ "&ClientIP=%s"
			+ "&OptPlatform=$OptPlatform";
	
	/**
	 * 获取群成员详细资料
	 */
	public static final String GET_GROUP_MEMBER_INFO="https://console.tim.qq.com/v4/group_open_http_svc/get_group_member_info?"
			 + "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";
	/**
	 * 更新群资料信息
	 */
	public static final String MODIFY_GROUP_BASE_INFO="https://console.tim.qq.com/v4/group_open_http_svc/modify_group_base_info?"
			+ "sdkappid=%s"
			+ "&identifier=%s"
			+ "&usersig=%s"
			+ "&random=%s"
			+ "&contenttype=json";
	
	/**
	 * 发送系统消息到群组
	 */
	public static final String SEND_GROUP_SYSTEM_NOTIFICATION="https://console.tim.qq.com/v4/group_open_http_svc/send_group_system_notification?"
			+ "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";

	/**
	 * 修改群人员资料
	 */
	public static final String MODIFY_GROUP_MEMBER_INFO="https://console.tim.qq.com/v4/group_open_http_svc/modify_group_member_info?"
			+ "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";
	
	/**
	 * 获取群组人员详细资料
	 */
	public static final String GROUP_OPEN_HTTP_SVC="https://console.tim.qq.com/v4/group_open_http_svc/get_group_member_info?"
			+ "sdkappid=%s"
			 + "&identifier=%s"
			 + "&usersig=%s"
			 + "&random=%s"
			 + "&contenttype=json";

	/**
	 * 查询群组资料
	 */
	public static final String GET_GROUP_INFO="https://console.tim.qq.com/v4/group_open_http_svc/get_group_info?" +
			"sdkappid=%s" +
			"&identifier=%s" +
			"&usersig=%s" +
			"&random=%s" +
			"&contenttype=json";


	/**
	 * 查询群组历史消息
	 */
	public static final String GROUP_MSG_GET_SIMPLE="https://console.tim.qq.com/v4/group_open_http_svc/group_msg_get_simple?" +
			"sdkappid=%s" +
			"&identifier=%s" +
			"&usersig=%s" +
			"&random=%s" +
			"&contenttype=json";

	/**
	 * 拉取资料
	 */
	public static final String PORTRAIT_GET="https://console.tim.qq.com/v4/profile/portrait_get?" +
			"sdkappid=%s" +
			"&identifier=%s" +
			"&usersig=%s" +
			"&random=%s" +
			"&contenttype=json";

	/**
	 * 设置资料
	 */
	public static final String PORTRAIT_SET="https://console.tim.qq.com/v4/profile/portrait_set?" +
			"sdkappid=%s" +
			"&identifier=%s" +
			"&usersig=%s" +
			"&random=%s" +
			"&contenttype=json";

	/**
	 * 导入群成员
	 */
	public static final String IMPORT_GROUP_MEMBER="https://console.tim.qq.com/v4/group_open_http_svc/import_group_member?" +
			"sdkappid=%s" +
			"&identifier=%s" +
			"&usersig=%s" +
			"&random=%s" +
			"&contenttype=json";

	/**
	 * 获取用户会话列表
	 */
	public static final String GET_LIST="https://console.tim.qq.com/v4/recentcontact/get_list?"+
			"sdkappid=%s"
			+"&identifier=%s"
			+"&usersig=%s"
			+"&random=%s"
			+"&contenttype=json";

	/**
	 * 消息不计入未读中
	 */
	public static final String NO_UN_READ = "NoUnread";

	public static final String ONE_TO_ONE_URL = "https://console.tim.qq.com/v4/openim/sendmsg?" +
			"sdkappid=%s"
			+"&identifier=%s"
			+"&usersig=%s"
			+"&random=%s"
			+"&contenttype=json" ;


}
