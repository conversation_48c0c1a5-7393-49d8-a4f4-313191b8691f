package com.puree.hospital.common.core.enums;

/**
 * 医生认证状态
 * <AUTHOR>
 */
public enum DoctorAuthStatusEnum {
    UNCERTIFIED (0,"未认证"),
    OUR_HOSPITAL(1,"实名认证"),
    PHYSICIAN_CERTIFICATION(2,"医师认证"),
    AUTH_SUCCESS(3,"认证成功"),
    AUTH_FAIL(4,"认证失败");
    private final Integer code;
    private final String info;

    DoctorAuthStatusEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
