package com.puree.hospital.common.core.utils;

import com.puree.hospital.common.core.enums.SexTypeEnum;

/**
 * @ClassName: BirthUtils
 * @Date 2022/12/2 14:45
 * <AUTHOR>
 * @Description: 身份证识别
 * @Version 1.0
 */
public class BirthUtils {
    /**
     * 根据身份证号判断用户性别
     *
     * @param cardNo 身份证号
     * @return 1-男 0-女
     */
    public static String getSex(String cardNo) {
        String sexStr = "0";
        if (cardNo.length() == 15) {
            sexStr = cardNo.substring(14, 15);
        } else if (cardNo.length() == 18) {
            sexStr = cardNo.substring(16, 17);
        }
        int sexNo = Integer.parseInt(sexStr);
        return sexNo % 2 == 0 ? SexTypeEnum.WOMAN.getCode() : SexTypeEnum.MAN.getCode();
    }

    /**
     * 根据身份证号计算出出生年月日
     *
     * @param idNumber 身份证号
     * @return 出生年月日 yyyy-mm-dd
     */
    public static String calculateBirthday(String idNumber) {
        if (18 == idNumber.length()) {
            String year = idNumber.substring(6, 10);
            String month = idNumber.substring(10, 12);
            String day = idNumber.substring(12, 14);
            return year + "-" + month + "-" + day;
        } else if (15 == idNumber.length()) {
            String year = "19" + idNumber.substring(6, 8);
            String month = idNumber.substring(8, 10);
            String day = idNumber.substring(10, 12);
            return (year + "-" + month + "-" + day);
        } else {
            throw new IllegalArgumentException("请输入正确的身份证号码");
        }
    }


}
