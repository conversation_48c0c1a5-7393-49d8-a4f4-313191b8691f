package com.puree.hospital.common.core.enums;

public enum DeliveryPriority {
    YYYF("0", "医院药房"),
    PSQY("1", "配送企业");

    private final String code;
    private final String info;

    DeliveryPriority(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }
    public String getInfo()
    {
        return info;
    }
}
