package com.puree.hospital.common.core.enums;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName: ChinaDrugTypeEnum
 * @Date 2023/12/26 16:47
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
public enum ChinaDrugTypeEnum {

    COMMON_DRUG(1, "普通中药饮片") ,
    BROKEN_DRUG(2, "破壁中药饮片") ,
    OTHER_DRUG(3, "其他中药") ,
    PARTICLE_DRUG(4, "中药颗粒")

    ;


    private Integer type ;
    private String msg;

    ChinaDrugTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static List<Integer> chinaDrugTypeList(){
        return Arrays.asList(ChinaDrugTypeEnum.COMMON_DRUG.getType(), ChinaDrugTypeEnum.BROKEN_DRUG.getType(), ChinaDrugTypeEnum.OTHER_DRUG.getType(), ChinaDrugTypeEnum.PARTICLE_DRUG.getType()) ;
    }

}
