package com.puree.hospital.common.core.utils.encrypt;

import lombok.Data;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @date 2022/8/8
 */
@Component
public class RSAUtils {
    public static String RSA_ALGORITHM = "RSA";
    public static String SIGN_ALGORITHM = "SHA1WithRSA";
    public static String UTF8 = "UTF-8";

    /**
     * 创建公钥私钥
     */
    public static KeyStore createKeys() throws Exception {
        KeyPairGenerator keyPairGeno = KeyPairGenerator.getInstance(RSA_ALGORITHM);
        keyPairGeno.initialize(1024);
        KeyPair keyPair = keyPairGeno.generateKeyPair();

        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

        KeyStore keyStore = new KeyStore();
        keyStore.setPublicKey(Base64.encode(publicKey.getEncoded()));
        keyStore.setPrivateKey(Base64.encode(privateKey.getEncoded()));
        return keyStore;
    }

    /**
     * 获取公钥对象
     *
     * @param pubKeyData 公钥
     */
    public static RSAPublicKey getPublicKey(byte[] pubKeyData) throws Exception {
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(pubKeyData);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return (RSAPublicKey) keyFactory.generatePublic(keySpec);
    }

    /**
     * 获取公钥对象
     *
     * @param pubKey 公钥
     */
    public static RSAPublicKey getPublicKey(String pubKey) throws Exception {
        return getPublicKey(Base64.decode(pubKey));
    }

    /**
     * 获取私钥对象
     *
     * @param priKey 私钥
     */
    public static RSAPrivateKey getPrivateKey(String priKey) throws Exception {
        return getPrivateKey(Base64.decode(priKey));
    }

    /**
     * 通过私钥byte[]将公钥还原，适用于RSA算法
     *
     * @param keyBytes 私钥数组
     */
    public static RSAPrivateKey getPrivateKey(byte[] keyBytes) throws Exception {
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);

    }

    public static String encryptByPublicKey(String data, String publicKey) throws Exception {
        return encryptByPublicKey(data, getPublicKey(publicKey));
    }

    /**
     * 公钥加密
     *
     * @param data      明文
     * @param publicKey 公钥
     */
    public static String encryptByPublicKey(String data, RSAPublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);

        byte[] plainTextData = data.getBytes(UTF8);
        int length = plainTextData.length;
        int offset = 0;
        byte[] cache;
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        int i = 0;
        while (length - offset > 0) {
            if (length - offset > 117) {
                cache = cipher.doFinal(plainTextData, offset, 117);
            } else {
                cache = cipher.doFinal(plainTextData, offset, length - offset);
            }
            outStream.write(cache, 0, cache.length);
            i++;
            offset = i * 117;
        }
        return Base64.encode(outStream.toByteArray());
    }

    public static String decryptByPublicKey(String data, String rsaPublicKey) throws Exception {
        return decryptByPublicKey(data, getPublicKey(rsaPublicKey));
    }

    /**
     * 公钥解密
     *
     * @param data         密文
     * @param rsaPublicKey 公钥
     */
    public static String decryptByPublicKey(String data, RSAPublicKey rsaPublicKey) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, rsaPublicKey);
        byte[] encryptData = Base64.decode(data);
        int length = encryptData.length;
        int offset = 0;
        int i = 0;
        byte[] cache;
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        while (length - offset > 0) {
            if (length - offset > 128) {
                cache = cipher.doFinal(encryptData, offset, 128);
            } else {
                cache = cipher.doFinal(encryptData, offset, length - offset);
            }
            outStream.write(cache, 0, cache.length);
            i++;
            offset = i * 128;
        }
        return outStream.toString(UTF8);
    }

    public static String encryptByPrivateKey(String data, String privateKey) throws Exception {
        return encryptByPrivateKey(data, getPrivateKey(privateKey));
    }

    /**
     * 私钥加密
     *
     * @param data       明文
     * @param privateKey 私钥
     */
    public static String encryptByPrivateKey(String data, RSAPrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] plainTextData = data.getBytes(UTF8);
        int length = plainTextData.length;
        int offset = 0;
        byte[] cache;
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        int i = 0;
        while (length - offset > 0) {
            if (length - offset > 117) {
                cache = cipher.doFinal(plainTextData, offset, 117);
            } else {
                cache = cipher.doFinal(plainTextData, offset, length - offset);
            }
            outStream.write(cache, 0, cache.length);
            i++;
            offset = i * 117;
        }
        return Base64.encode(outStream.toByteArray());
    }

    public static String decryptByPrivateKey(String data, String privateKey) throws Exception {
        return decryptByPrivateKey(data, getPrivateKey(privateKey));
    }

    /**
     * 私钥解密
     *
     * @param data       密文
     * @param privateKey 私钥
     */
    public static String decryptByPrivateKey(String data, RSAPrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] encryptData = Base64.decode(data);
        int length = encryptData.length;
        int offset = 0;
        int i = 0;
        byte[] cache;
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        while (length - offset > 0) {
            if (length - offset > 128) {
                cache = cipher.doFinal(encryptData, offset, 128);
            } else {
                cache = cipher.doFinal(encryptData, offset, length - offset);
            }
            outStream.write(cache, 0, cache.length);
            i++;
            offset = i * 128;
        }
        return outStream.toString(UTF8);
    }

    /**
     * RSA签名算法
     *
     * @param content    加签内容
     * @param privateKey 私钥
     */
    public static String sign(String content, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance(SIGN_ALGORITHM);
        signature.initSign(privateKey);
        signature.update(content.getBytes(UTF8));
        byte[] signed = signature.sign();
        return Base64.encode(signed);
    }

    public static String sign(String content, String privateKey) throws Exception {
        return sign(content, getPrivateKey(privateKey));
    }

    /**
     * RSA验签名检查
     *
     * @param content   待签名数据
     * @param sign      签名值
     * @param publicKey 分配给开发商公钥
     * @return 布尔值
     */
    public static boolean verify(String content, String sign, PublicKey publicKey) throws Exception {
        Signature signature = Signature.getInstance(SIGN_ALGORITHM);
        signature.initVerify(publicKey);
        signature.update(content.getBytes(UTF8));
        return signature.verify(Base64.decode(sign));
    }

    public static boolean verify(String content, String sign, String publicKey) throws Exception {
        return verify(content, sign, getPublicKey(publicKey));
    }

    @Data
    public static class KeyStore {
        private String publicKey;
        private String privateKey;
    }
}
