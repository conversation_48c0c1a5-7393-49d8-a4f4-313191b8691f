package com.puree.hospital.common.core.enums;

public enum PhysiqueTypeEnum {
    PEACE_AND_QUALITY("平和质", "特征：正常的体质。<br/>调节：饮食有节制，不要常吃过冷过热或不干净的食物，粗细粮食要合理搭配。"),
    QI_DEFICIENCY_QUALITY("气虚质", "特征：肌肉松软，声音低，易出汗，易累，易感冒。<br/>调节：多食用具有益气健脾作用的食物，如黄豆、白扁豆、鸡肉等。少食空心菜、生萝卜等。"),
    YANG_DEFICIENCY_QUALITY("阳虚质", "特征：肌肉不健壮，常常感到手脚发凉，衣服比别人穿得多，夏天不喜欢吹空调，喜欢安静，性格多沉静、内向。<br/>调节：平时可多食牛肉、羊肉等温阳之品，少食梨、西瓜、荸荠等生冷寒凉食物，少饮绿茶。"),
    YIN_DEFICIENCY_QUALITY("阴虚质", "特征：体形多瘦长，不耐暑热，常感到眼睛干涩，口干咽燥，总想喝水，皮肤干燥，经常大便干结，容易失眠。<br/>调节：多食瘦猪肉、鸭肉、绿豆、冬瓜等甘凉滋润之品，少食羊肉、韭菜、辣椒、葵花子等性温燥烈之品。适合太极拳、太极剑、气功等项目。"),
    BLOOD_STASIS_QUALITY("血瘀质", "特征：皮肤较粗糙，眼睛里的红丝很多，牙龈易出血。<br/>调节：多食山楂、醋、玫瑰花等，少食肥肉等滋腻之品。可参加各种舞蹈、步行健身法、徒手健身操等。"),
    PHLEGM_DAMPNESS_QUALITY("痰湿质", "特征：体形肥胖，腹部肥满而松软。易出汗，且多黏腻。经常感觉脸上有一层油。<br/>调节：饮食应以清淡为主，可多食冬瓜等。因体形肥胖，易于困倦，故应根据自己的具体情况循序渐进，长期坚持运动锻炼。"),
    DAMP_HEAT_MASS("湿热质", "特征：面部和鼻尖总是油光发亮，脸上易生粉刺，皮肤易瘙痒。常感到口苦、口臭，脾气较急躁。<br/>调节：饮食以清淡为主，可多食赤小豆、绿豆、芹菜、黄瓜、藕等甘寒的食物。适合中长跑、游泳、爬山、各种球类、武术等。"),
    QI_DEPRESSION_QUALITY("气郁质", "特征：体形偏瘦，常感到闷闷不乐、情绪低沉，常有胸闷，经常无缘无故地叹气,易失眠。<br/>调节：多食黄花菜、海带、山楂、玫瑰花等具有行气、解郁、消食、醒神作用的食物。气郁体质的人不要总待在家里，要多参加群众性的体育运动项目。"),
    SPECIAL_QUALITY("特禀质", "特征：这是一类体质特殊的人群。其中过敏体质的人易对药物、食物、气味、花粉、季节过敏。<br/>调节：多食益气固表的食物，少食荞麦(含致敏物质荞麦荧光素)、蚕豆等。居室宜通风良好。保持室内清洁，被褥、床单要经常洗晒，可防止对尘螨过敏。"),;


    private final String  code;
    private final String info;

    PhysiqueTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (PhysiqueTypeEnum enums : PhysiqueTypeEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }
}
