package com.puree.hospital.common.core.calculator.price;

import cn.hutool.core.collection.CollectionUtil;
import com.puree.hospital.common.core.calculator.ICalculator;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <p>
 * 价格计算器接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/15 15:42
 */
public interface IPriceCalculator<I extends IPrice> extends ICalculator<I, BigDecimal> {

    /**
     * 计算总价
     *
     * @param itemList 计算总金额
     * @return 总金额
     */
    default BigDecimal calcTotal(List<I> itemList) {
        if (CollectionUtil.isEmpty(itemList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (I item : itemList) {
            BigDecimal subTotal = calculate(item);
            totalAmount = totalAmount.add(subTotal);
        }
        return totalAmount.setScale(getTotalScale(), getTotalRoundingMode());
    }

    /**
     * 如果是空值则转成0
     *
     * @param value BigDecimal
     * @return value
     */
    default BigDecimal nullToZero(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    /**
     * 如果是空值则转成0
     *
     * @param value Integer
     * @return value
     */
    default BigDecimal nullToZero(Integer value) {
        return value == null ? BigDecimal.ZERO : new BigDecimal(value);
    }

    /**
     * 获取总价的精度
     *
     * @return 默认2位
     */
    default int getTotalScale() {
        return 2;
    }

    /**
     * 获取总价的四舍五入方式
     *
     * @return 默认四舍五入
     */
    default RoundingMode getTotalRoundingMode() {
        return RoundingMode.HALF_UP;
    }
}
