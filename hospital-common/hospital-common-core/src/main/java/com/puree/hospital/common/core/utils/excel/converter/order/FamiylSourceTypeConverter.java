package com.puree.hospital.common.core.utils.excel.converter.order;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.puree.hospital.common.core.enums.FamilySourceTypeEnum;

/**
 * 就诊人来源类型转换
 * <AUTHOR>
 */
public class FamiylSourceTypeConverter implements Converter<String> {

    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData readCellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) {
        return FamilySourceTypeEnum.getKeyByInfo(readCellData.getStringValue());
    }

    @Override
    public WriteCellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration
            globalConfiguration) {
        String info = FamilySourceTypeEnum.getInfoByKey(value);
        return new WriteCellData<>(info);
    }
}
