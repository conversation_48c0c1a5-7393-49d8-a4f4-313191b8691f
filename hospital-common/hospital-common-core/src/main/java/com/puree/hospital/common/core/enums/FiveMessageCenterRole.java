package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * 五师共管角色枚举
 */
public enum FiveMessageCenterRole {
    GROUP_LEADER("0","群主"),
    ADMIN("1","管理员"),
    COMMON("2","普通人"),
    SUPER_ADMIN("3","超级管理员"),
    QR_JOIN("4","二维码加入");


    private final String code;
    private final String info;

    FiveMessageCenterRole(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static FiveMessageCenterRole getTypeByValue(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (FiveMessageCenterRole enums : FiveMessageCenterRole.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }



    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (FiveMessageCenterRole enums : FiveMessageCenterRole.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }


}
