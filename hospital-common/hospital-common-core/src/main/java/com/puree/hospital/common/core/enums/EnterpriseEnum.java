package com.puree.hospital.common.core.enums;

/**
 *
 *  配送方标识
 * <AUTHOR>
 */
public enum EnterpriseEnum {
    SST("0", "SST"),
    GYJT("1", "GYJT"),
    XX("2", "XX"),
    DX("3", "DX"),
    ZX("16", "ZX"),
    HR("17", "HR")
    ,
    YF("26", "YF")
    ;

    private final String code;
    private final String info;

    EnterpriseEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
