package com.puree.hospital.common.core.constant;

/**
 * @ClassName: OrderAfterSalesConstants
 * @Date 2023/3/7 15:45
 * <AUTHOR>
 * @Description: 订单售后
 * @Version 1.0
 */
public class OrderAfterSalesConstants {

    /**
     * 患者发起退款
     */
    public static final String SQTK = "发起了退款申请，等待医院处理";
    /**
     * 同意退款
     */
    public static final String TYTK = "同意了退款申请，本次售后结束";
    /**
     * 拒绝退款
     */
    public static final String JJTK = "拒绝退款，等待患者处理";
    /**
     * 申请退货退款
     */
    public static final String SQTHTK = "发起了退货退款申请，等待医院处理";
    /**
     * 同意退款等待收货
     */
    public static final String TYTKDDSH = "已同意退款，等待患者退货";
    /**
     * 拒绝退货退款
     */
    public static final String JJTKTH = "拒绝退货退款，等待患者处理";
    /**
     * 拒绝收货
     */
    public static final String JJSH = "拒绝收货，等待患者处理";
    /**
     * 确认收货
     */
    public static final String QRSH = "已收到退货并退款患者，本次售后结束";
    /**
     * 已退货，等待医院收货
     */
    public static final String YTHDDSF = "已退货，等待医院收货";
    /**
     * 修改退货信息，等待医院收货
     */
    public static final String XGTHXX = "修改退货信息，等待医院收货";
    /**
     * 撤销售后，本次售后关闭
     */
    public static final String CXSH = "已撤销售后，本次售后关闭";
    /**
     * 已确认收货，待重新发货
     */
    public static final String YQRSH = "已确认收货，待重新发货";

    /**
     * 发起了退换货申请
     */
    public static final String THH = "发起了退换货申请，等待医院处理";

    /**
     * 同意换货等待收货
     */
    public static final String HHTH = "已同意换货，等待患者退货";

    /**
     * 拒绝退货换货
     */
    public static final String JJTHHH = "拒绝退货换货，等待患者处理";

}
