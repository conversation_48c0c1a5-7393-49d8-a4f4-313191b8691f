package com.puree.hospital.common.core.enums;

/**
 * @ClassName: InvoiceOrderTypeEnum
 * @Date 2023/12/1 9:58
 * <AUTHOR> jian
 * @Description: InvoiceOrderTypeEnum
 * @Version 1.0
 */
public enum InvoiceOrderTypeEnum {

    CONSULTATION(1, "bus_consultation_order") ,
    FIVE(2, "bus_five_service_pack_order") ,
    ORDER(3, "bus_order"),
    ORDER_AFTER_SALES(4, "bus_order_after_sales") ,

    FIVE_AFTER_SALES(5, "five_after_sales") ,

    ;


    private Integer type ;
    private String msg ;

    InvoiceOrderTypeEnum(Integer type, String msg){
        this.type = type ;
        this.msg = msg ;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
