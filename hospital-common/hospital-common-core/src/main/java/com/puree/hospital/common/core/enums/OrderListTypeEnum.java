package com.puree.hospital.common.core.enums;
/**
 *
 *  是否
 * <AUTHOR>
 */
public enum OrderListTypeEnum {
    GENERAL_PRESCRIPTION(0, "中药处方"),
    PARTIES(1, "协定方"),
    CLASSICAL_FORMULA(2, "经典方"),
    OTC(3, "OTC"),
    GOODS(4, "商品"),
    WESTERN_MEDICINE_PRESCRIPTION(5, "西药处方");

    private final Integer code;
    private final String info;

    OrderListTypeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
