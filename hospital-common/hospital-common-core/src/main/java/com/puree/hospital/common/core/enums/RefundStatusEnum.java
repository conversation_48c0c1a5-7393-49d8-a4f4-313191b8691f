package com.puree.hospital.common.core.enums;

/**
 * <p>
 * 退款状态枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/4 16:46
 */
public enum RefundStatusEnum {

    /**
     * 退款中
     */
    REFUNDING("0", "退款中"),

    /**
     * 退款失败
     */
    REFUND_FAILED("1", "退款失败"),

    /**
     * 退款成功
     */
    REFUND_SUCCESS("2", "退款成功");

    /**
     * 状态
     */
    private final String status;

    /**
     * 描述
     */
    private final String desc;

    RefundStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
