package com.puree.hospital.common.core.utils.excel.converter.order;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.puree.hospital.common.core.enums.SexTypeEnum;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConsultationOrderSexConverter implements Converter<String> {
    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration
            globalConfiguration) {
        if (value.equals(SexTypeEnum.WOMAN.getCode())) {
            return new WriteCellData<>(SexTypeEnum.WOMAN.getInfo());
        } else if (value.equals(SexTypeEnum.MAN.getCode())){
            return new WriteCellData<>(SexTypeEnum.MAN.getInfo());
        } else {
            return new WriteCellData<>("");
        }
    }
}
