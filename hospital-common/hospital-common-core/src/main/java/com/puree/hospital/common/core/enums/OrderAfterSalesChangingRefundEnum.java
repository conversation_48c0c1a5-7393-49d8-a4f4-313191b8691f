package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * 换货拒绝原因枚举
 * @Date 2023/3/15 19:16
 * <AUTHOR>
 * @Description: 换货拒绝原因枚举
 * @Version 1.0
 */
public enum OrderAfterSalesChangingRefundEnum {
    INCONSISTENCY("0", "退回商品与售后商品不一致"),
    INCOMPLETE("1", "退回商品不完整"),
    EMPTY_PARCEL("2", "空包裹"),
    NO_REFUND_RETURNED("3", "已协商完毕不换货"),
    REPLACEMENT_BEEN_NEGOTIATED("4", "已协商完毕换货"),
    OTHER("5", "其他");
    private final String code;
    private final String info;

    OrderAfterSalesChangingRefundEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static OrderAfterSalesChangingRefundEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (OrderAfterSalesChangingRefundEnum enums : OrderAfterSalesChangingRefundEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
