package com.puree.hospital.common.core.utils.http;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.DnsResolver;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 通用http发送方法
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {

    /**
     * 发送get请求
     * @param url       请求链接
     * @return  body 响应体
     */
    public static String sendGet(String url) {
        return sendGet(url, null, null, null);
    }

    /**
     * 发送get请求
     * @param url       请求链接
     * @param params    携带参数
     * @return  body 响应体
     */
    public static String sendGet(String url, Map<String, String> params) {
        return sendGet(url, params, null, null);
    }

    /**
     * 发送get请求
     * @param url       请求链接
     * @param params    携带参数
     * @param headers   请求头
     * @param dnsIp     DNS IP
     * @return  body 响应体
     */
    public static String sendGet(String url, Map<String, String> params, Map<String, String> headers, String dnsIp) {
        String body;
        HttpGet httpGet = null;
        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(3000)
                    .setConnectionRequestTimeout(10000)
                    .setSocketTimeout(30000)
                    .build();
            // 构建带参数的URI
            URIBuilder uriBuilder = new URIBuilder(url);
            if (params != null) {
                for (Entry<String, String> entry : params.entrySet()) {
                    uriBuilder.addParameter(entry.getKey(), entry.getValue());
                }
            }
            URI uri = uriBuilder.build();
            // 创建HttpGet对象
            httpGet = new HttpGet(uri);
            if (headers != null) {
                for (Entry<String, String> entry : headers.entrySet()) {
                    httpGet.setHeader(entry.getKey(), entry.getValue());
                }
            }
            HttpClientBuilder httpClientBuilder = HttpClients.custom().setDefaultRequestConfig(requestConfig);
            //设置dns代理
            if (StringUtils.isNotBlank(dnsIp)) {
                InetAddress dnsInetAddress = InetAddress.getByName(dnsIp);
                DnsResolver dnsResolver = s -> new InetAddress[]{dnsInetAddress};
                httpClientBuilder.setDnsResolver(dnsResolver);
            }
            CloseableHttpClient httpClient = httpClientBuilder.build();
            HttpResponse httpResponse = httpClient.execute(httpGet);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                log.error("请求失败，响应状态码：{}, 响应body：{}", statusCode, EntityUtils.toString(httpResponse.getEntity()));
                throw new ServiceException("请求失败");
            } else {
                body = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
            }
        } catch (Exception ex) {
            log.error("发送请求{}异常：{}", url, ex.getMessage(), ex);
            throw new ServiceException("调用Http请求失败");
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
        return body;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            log.info("sendPost - {}", url);
            URL realUrl = new URL(url);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("contentType", "utf-8");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(conn.getOutputStream());
            out.print(param);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            log.info("send post result:{}", result);
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendPost ConnectException, url={},param={}", url, param, e);
        } catch (SocketTimeoutException e) {
            log.error("调用HttpUtils.sendPost SocketTimeoutException, url={},param={}", url, param, e);
        } catch (IOException e) {
            log.error("调用HttpUtils.sendPost IOException, url={},param={}", url, param, e);
        } catch (Exception e) {
            log.error("调用HttpsUtil.sendPost Exception, url={},param={}", url, param, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("调用in.close Exception, url={},param={}", url, param, ex);
            }
        }
        return result.toString();
    }

    /**
     * 方法描述: 发送post请求-json数据
     *
     * @param url     请求url
     * @param body    请求body
     * @param header  请求头
     * @return {@link String}
     * @date 2021年5月21日 10:10:54
     */
    public static String postJson(String url, Object body, Map<String, String> header) {
        return sendPostJson(url, JSONObject.toJSONString(body), header);
    }
    /**
     * 方法描述: 发送post请求-json数据
     *
     * @param url    请求url
     * @param json   请求参数
     * @param header 请求头
     * @return 响应信息
     */
    public static String sendPostJson(String url, String json, Map<String, String> header) {
        return sendPostJson(url, json, header, null);
    }

    /**
     * 方法描述: 发送post请求-json数据
     *
     * @param url       请求url
     * @param json      请求参数
     * @param header    请求头
     * @param proxyIp   走dns代理
     * @return {@link String}
     * @date 2021年5月21日 10:10:54
     */
    public static String sendPostJson(String url, String json, Map<String, String> header, String proxyIp) {
        HttpPost httpPost = null;
        String body;
        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(3000)
                    .setConnectionRequestTimeout(10000)
                    .setSocketTimeout(30000)
                    .build();
            HttpClientBuilder httpClientBuilder = HttpClients.custom().setDefaultRequestConfig(requestConfig);
            //设置dns代理
            if (StringUtils.isNotBlank(proxyIp)) {
                InetAddress dnsInetAddress = InetAddress.getByName(proxyIp);
                DnsResolver dnsResolver = s -> new InetAddress[]{dnsInetAddress};
                httpClientBuilder.setDnsResolver(dnsResolver);
            }
            CloseableHttpClient httpClient = httpClientBuilder.build();
            httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
            httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            if (header != null) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                log.error("请求失败，响应状态码：{}, 响应body：{}", statusCode, EntityUtils.toString(response.getEntity()));
                throw new ServiceException("请求失败");
            } else {
                body = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception ex) {
            log.error("发送请求{}异常：{}", url, ex.getMessage(), ex);
            throw new ServiceException("调用Http请求失败");
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return body;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     * url 发送请求的 URL
     * params 请求的参数集合
     *
     * @return 远程资源的响应结果
     */
    @SuppressWarnings("unused")
    public static String sendPost(String url, Map<String, String> params) {
        OutputStreamWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // POST方法
            conn.setRequestMethod("POST");
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.connect();
            // 获取URLConnection对象对应的输出流
            out = new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8);
            // 发送请求参数
            if (params != null) {
                StringBuilder param = new StringBuilder();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (param.length() > 0) {
                        param.append("&");
                    }
                    param.append(entry.getKey());
                    param.append("=");
                    param.append(entry.getValue());
                    System.out.println(entry.getKey() + ":" + entry.getValue());
                }
                System.out.println("param:" + param);
                out.write(param.toString());
            }
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            log.error("发送 POST 请求出现异常！{}", e.getMessage(), e);
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("关闭流异常：{}", ex.getMessage(), ex);
            }
        }
        return result.toString();
    }

}