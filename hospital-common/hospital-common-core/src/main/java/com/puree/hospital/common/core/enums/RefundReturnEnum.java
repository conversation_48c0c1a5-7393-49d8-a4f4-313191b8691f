package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/3/8 15:20
 */
public enum RefundReturnEnum {
    // 退货退款
    zero("0", "拍多/拍错/不想要"),
    one("1", "未按期发货"),
    two("2", "货品破损/少件"),
    three("3", "货品与描述不符"),
    four("4", "发错货品"),
    five("5", "质量问题"),
    six("6", "退运费"),
    seven("7", "其他");

    private final String code;
    private final String info;

    RefundReturnEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static RefundReturnEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (RefundReturnEnum enums : RefundReturnEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
