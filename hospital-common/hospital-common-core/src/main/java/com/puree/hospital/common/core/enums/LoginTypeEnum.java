package com.puree.hospital.common.core.enums;

import lombok.Getter;

/**
 *  登录类型
 * <AUTHOR>
 */
@SuppressWarnings("SpellCheckingInspection")
@Getter
public enum LoginTypeEnum {

    /**
     * 医院公众昂
     */
    OFFICIAL_ACCOUNT("0", "公众号登录"),

    /**
     * 众爱医伴
     */
    AIDMED_APPLET("1", "合作渠道小程序登录"),


    OTHER_APPLET("2","其他小程序登录"),

    UNI_APP_APPLET("3","医院小程序登录");

    private final String code;
    private final String info;

    LoginTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    /**
     * 校验是否为微信小程序
     *
     * @param code 登录类型
     * @return 结果
     */
    public static boolean isWxUniApp(String code) {
        return UNI_APP_APPLET.getCode().equals(code);
    }

    /**
     * 判断是否是微信公众号
     *
     * @param code 登录类型
     * @return 是否是微信公众号
     */
    public static boolean isOfficialAccount(String code) {
        return OFFICIAL_ACCOUNT.getCode().equals(code);
    }

}
