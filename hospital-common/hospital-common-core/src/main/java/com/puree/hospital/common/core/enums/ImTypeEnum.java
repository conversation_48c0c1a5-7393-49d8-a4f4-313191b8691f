package com.puree.hospital.common.core.enums;

/**
 * @ClassName: ImTypeEnum
 * @Date 2024/6/26 17:58
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum ImTypeEnum {

    ONE_TO_ONE(0, "1v1私聊") ,
    SINGLE_GROUP(1, "1v1群组") ,
    MULTIPLE_GROUP(2, "服务包群组") ,

    ;


    private Integer index ;

    private String name ;


    ImTypeEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }




}


