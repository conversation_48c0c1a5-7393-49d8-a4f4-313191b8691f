package com.puree.hospital.common.core.utils;

import javax.servlet.http.HttpServletRequest;

import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.core.text.Convert;
import com.puree.hospital.common.core.utils.des.DESUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 权限获取工具类
 * 
 * <AUTHOR>
public class SecurityUtils
{
    private static final Logger log = LoggerFactory.getLogger(SecurityUtils.class);
    /**
     * 获取用户
     */
    public static String getUsername() {

        HttpServletRequest request = ServletUtils.getRequest();

        if (null==request) {
            return "";
        }

        String username = request.getHeader(SecurityConstants.DETAILS_USERNAME);
        if (StringUtils.isEmpty(username)){
            username = "";
        }
        return ServletUtils.urlDecode(username);
    }
    /**
     * 获取医院ID
     */
    public static Long getHospitalId() {

        HttpServletRequest request = ServletUtils.getRequest();

        if (null==request) {
            return null;
        }

        String  hospitalId= request.getHeader(SecurityConstants.HOSPITAL_ID);
        if (StringUtils.isEmpty(hospitalId)||"undefined".equals(hospitalId)||"null".equals(hospitalId)){
           return null;
        }
        boolean numeric = StringUtils.isNumeric(hospitalId);
        if(!numeric){
            hospitalId= DESUtil.decrypt(hospitalId);
        }
        return Long.valueOf(hospitalId);
    }
    /**
     * 获取用户ID
     */
    public static Long getUserId()
    {
        return Convert.toLong(ServletUtils.getRequest().getHeader(SecurityConstants.DETAILS_USER_ID));
    }

    /**
     * 根据request获取请求Partnerscode
     */
    public static String getPartnerscode()
    {
        return ServletUtils.getRequest().getHeader(SecurityConstants.PARTNERSCODE);
    }

    /**
     * 获取请求token
     */
    public static String getToken()
    {
        return getToken(ServletUtils.getRequest());
    }

    /**
     * 根据request获取请求token
     */
    public static String getToken(HttpServletRequest request)
    {
        String token = request.getHeader(SecurityConstants.TOKEN_AUTHENTICATION);
        return replaceTokenPrefix(token);
    }

    /**
     * 替换token前缀
     */
    public static String replaceTokenPrefix(String token)
    {
        if (StringUtils.isNotEmpty(token) && token.startsWith(SecurityConstants.TOKEN_PREFIX))
        {
            token = token.replace(SecurityConstants.TOKEN_PREFIX, "");
        }
        return token;
    }

    /**
     * 是否为管理员
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword)
    {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
