package com.puree.hospital.common.core.enums;

/**
 * @ClassName: EhrSourceEnum
 * @Date 2022/12/6 17:21
 * <AUTHOR>
 * @Description: 健康档案来源
 * @Version 1.0
 */
public enum EhrSourceEnum {
    DOCTOR("0","医生"),
    PATIENT("1","患者"),
    XKSB("2","携康设备"),
    SONKASB("3","双佳设备"),
    EHOMESB("4","E家设备");

    private final String code;
    private final String info;

    EhrSourceEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }
    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
