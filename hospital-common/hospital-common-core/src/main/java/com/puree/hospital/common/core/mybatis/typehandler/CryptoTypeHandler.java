package com.puree.hospital.common.core.mybatis.typehandler;

import com.puree.hospital.common.core.mybatis.SecureAesDbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @ClassName CryptoTypeHandler
 * <AUTHOR>
 * @Description 数据库加解密转换器
 * @Date 2023/11/29 17:44
 * @Version 1.0
 */

@Slf4j
public class CryptoTypeHandler extends BaseTypeHandler<String> {

    /**
     * @Param ps
     * @Param i
     * @Param parameter
     * @Param jdbcType
     * @Return void
     * @Description 对入参进行处理
     * <AUTHOR>
     * @Date 2023/11/30 10:44
     **/
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            //加密
            String encryptHex = SecureAesDbUtil.encryptHex(parameter);
            ps.setString(i, encryptHex);
        }
    }

    /**
     * @Param rs
     * @Param columnName
     * @Return java.lang.String
     * @Description 根据列字段名获取返回结果
     * <AUTHOR>
     * @Date 2023/11/30 10:45
     **/
    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String originRes = rs.getString(columnName);

        if (originRes != null) {
            //64为身份证加密后的长度
            if (originRes.length() != 64 && originRes.length() != 32) {
                return originRes;
            }
            String res = SecureAesDbUtil.decryptStr(originRes);
            return res;
        }

        return null;
    }

    /**
     * @Param rs
     * @Param columnIndex
     * @Return java.lang.String
     * @Description 根据列下标获取返回结果
     * <AUTHOR>
     * @Date 2023/11/30 10:45
     **/
    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String originRes = rs.getString(columnIndex);

        if (originRes != null) {
            //64为身份证加密后的长度
            if (originRes.length() != 64) {
                return originRes;
            }
            String res = SecureAesDbUtil.decryptStr(originRes);
            return res;
        }
        return null;
    }

    /**
     * @Param cs
     * @Param columnIndex
     * @Return java.lang.String
     * @Description 根据列下标获取返回结果（存储过程）
     * <AUTHOR>
     * @Date 2023/11/30 10:45
     **/
    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String originRes = cs.getString(columnIndex);
        if (originRes != null) {
            //64为身份证加密后的长度
            if (originRes.length() != 64) {
                return originRes;
            }
            String res = SecureAesDbUtil.decryptStr(originRes);
            return res;
        }
        return null;
    }
}
