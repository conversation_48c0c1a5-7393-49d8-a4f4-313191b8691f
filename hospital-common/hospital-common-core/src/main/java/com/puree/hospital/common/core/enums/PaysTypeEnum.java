package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.constant.PayWayConstant;
import lombok.Getter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付类型枚举
 */
@Getter
public enum PaysTypeEnum {
    WECHAT_PAY("1","微信支付", PayWayConstant.WECHAT_PAY),
    TONGLIAN_PAY("2","通联支付", PayWayConstant.TONGLIAN_PAY),
    WECHAT_INSURANCE_PAY("3","微信医保支付", PayWayConstant.WECHAT_INSURANCE_PAY);
    private final String code;
    private final String info;
    private final String constantName;

    private static final Map<String, PaysTypeEnum> PAYS_TYPE_ENUMS ;

    static {
        Map<String, PaysTypeEnum> map = new HashMap<>();
        for (PaysTypeEnum enums : PaysTypeEnum.values()) {
            map.put(enums.getCode(), enums);
        }
        // 防止外部修改-唯一映射
        PAYS_TYPE_ENUMS = Collections.unmodifiableMap(map);
    }
    PaysTypeEnum(String code, String info, String constantName) {
        this.code = code;
        this.info = info;
        this.constantName = constantName;
    }

    public static PaysTypeEnum getEnum(String code) {
        if (code == null) {
            throw new IllegalArgumentException("支付类型不能为空");
        }
        PaysTypeEnum result = PAYS_TYPE_ENUMS.get(code);
        if (result == null) {
            throw new IllegalArgumentException(String.format("支付类型%s不存在", code));
        }
        return result;
    }
}
