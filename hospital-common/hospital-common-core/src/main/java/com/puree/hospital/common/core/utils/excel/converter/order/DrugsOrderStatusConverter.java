package com.puree.hospital.common.core.utils.excel.converter.order;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.core.enums.DrugsOrderEnum;

public class DrugsOrderStatusConverter implements Converter<String> {

    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration
            globalConfiguration) {
        if (value.equals(DrugsOrderEnum.PENDING_PAYMENT.getCode())) {
            return new WriteCellData(DrugsOrderEnum.PENDING_PAYMENT.getInfo());
        }
        if (value.equals(DrugsOrderEnum.TO_BE_SHIPPED.getCode())) {
            return new WriteCellData(DrugsOrderEnum.TO_BE_SHIPPED.getInfo());
        }
        if (value.equals(DrugsOrderEnum.TOBEPICKEDUP.getCode())) {
            return new WriteCellData(DrugsOrderEnum.TOBEPICKEDUP.getInfo());
        }
        if (value.equals(DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getCode())) {
            return new WriteCellData(DrugsOrderEnum.GOODS_TO_BE_RECEIVED.getInfo());
        }
        if (value.equals(DrugsOrderEnum.CANCELLED.getCode())) {
            return new WriteCellData(DrugsOrderEnum.CANCELLED.getInfo());
        }
        if (value.equals(DrugsOrderEnum.INVALID.getCode())) {
            return new WriteCellData(DrugsOrderEnum.INVALID.getInfo());
        }if (value.equals(DrugsOrderEnum.AFTERSALES.getCode())) {
            return new WriteCellData(DrugsOrderEnum.AFTERSALES.getInfo());
        }
        if (value.equals(DrugsOrderEnum.REFUNDED.getCode())) {
            return new WriteCellData(DrugsOrderEnum.REFUNDED.getInfo());
        }
        if (value.equals(DrugsOrderEnum.COMPLETED.getCode())) {
            return new WriteCellData(DrugsOrderEnum.COMPLETED.getInfo());
        }
        if (value.equals(DrugsOrderEnum.RECEIVED.getCode())) {
            return new WriteCellData(DrugsOrderEnum.RECEIVED.getInfo());
        }
        if (value.equals(DrugsOrderEnum.AFTERSALES_COMPLETION.getCode())) {
            return new WriteCellData(DrugsOrderEnum.AFTERSALES_COMPLETION.getInfo());
        }
        if (value.equals(DrugsOrderEnum.REFUNDED_ZHONG.getCode())) {
            return new WriteCellData(DrugsOrderEnum.REFUNDED_ZHONG.getInfo());
        }
        if (value.equals(DrugsOrderEnum.PART_DELIVERY.getCode())) {
            return new WriteCellData(DrugsOrderEnum.PART_DELIVERY.getInfo());
        }
        return new WriteCellData("");
    }
}
