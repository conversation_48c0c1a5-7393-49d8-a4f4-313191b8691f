package com.puree.hospital.common.core.enums;

/**
 * @ClassName: ProductTypeInvoiceEnum
 * @Date 2023/10/23 15:45
 * <AUTHOR> jian
 * @Description: 产品适配发票的类型
 * @Version 1.0
 */
public enum ProductTypeInvoiceEnum {

    MEDICINE("10", "西药") ,
    CHINESE_MEDICINE("20", "中药") ,
    CHINESE_MEDICINE_BOIL("30", "中药代煎") ,
    CONSULT("40", "问诊费")




    ;

    private String code;
    private String msg;

    ProductTypeInvoiceEnum(String code, String msg){
        this.code=code;
        this.msg=msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public static String getMsgByCode(String code){
        ProductTypeInvoiceEnum[] values = ProductTypeInvoiceEnum.values();
        for (ProductTypeInvoiceEnum e : values) {
            if (e.getCode().equals(code)){
                return e.getMsg();
            }
        }
        return "" ;
    }



}
