package com.puree.hospital.common.core.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.puree.hospital.common.core.enums.CycleEnum;
import com.puree.hospital.common.core.exception.base.BaseException;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 时间工具类
 * 
 * <AUTHOR>
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static String ISO8601 = "yyyy-MM-dd'T'HH:mm:ssXXX";
    public static String T_DATE = "yyyy-MM-dd'T'HH:mm:ss";

    private static final long nd = 1000 * 24 * 60 * 60;
    private static final long nh = 1000 * 60 * 60;
    private static final long nm = 1000 * 60;
    
    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", 
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     * 
     * @return Date() 当前日期
     */
    @Deprecated
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     * 
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    public static String formatDateTimeISO8601(Date date) {
        // 将 Date 转换为 LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());

        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, zoneId);

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(ISO8601);

        // 格式化为字符串
        return zonedDateTime.format(formatter);
    }

    public static Date formatISO8601ToDateTime(String isoString) {
        LocalDateTime dateTime = LocalDateTime.parse(isoString, DateTimeFormatter.ISO_DATE_TIME);
        Instant instant = dateTime.atZone(ZoneId.systemDefault()).toInstant();
        // 格式化为日期类型
        return Date.from(instant);
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }
    
    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }


    /**
     * 年月时间处理
     * @param date
     * @return
     */
    public static LocalDateTime parseLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime;
    }

    /**
     * 年月日时间处理
     * @param time
     * @return
     */
    public static String parse_yyyyMMdd(LocalDateTime time) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return df.format(time);
    }

    /**
     * 年月时间处理
     * @param time
     * @return
     */
    public static String parse_yyyyMM(Date time) {
        return DateFormatUtils.format(time, "yyyy-MM");
    }

    /**
     * 年时间处理
     * @param time
     * @return
     */
    public static String parse_yyyy(Date time) {
        return DateFormatUtils.format(time, "yyyy");
    }

    /**
     * 年月日时间处理
     * @param time
     * @return
     */
    public static String parse_yyyyMMdd(Date time) {
        return DateFormatUtils.format(time, "yyyy-MM-dd");
    }


    /**
     * 年月日时间处理
     * @param time
     * @return
     */
    public static String parse_YYYY_MM_DD_HH_MM_SS(Date time) {
        return DateFormatUtils.format(time, YYYY_MM_DD_HH_MM_SS);
    }
    /**
     * 判断时间区间
     * @param leftStartDate
     * @param leftEndDate
     * @param rightStartDate
     * @param rightEndDate
     * @return
     */
    public static boolean isOverlap(Date leftStartDate, Date leftEndDate, Date rightStartDate, Date rightEndDate) {
        return ((leftStartDate.getTime() >= rightStartDate.getTime())
                && leftStartDate.getTime() < rightEndDate.getTime())
                ||
                ((leftEndDate.getTime() > rightStartDate.getTime())
                        && leftEndDate.getTime() <= rightEndDate.getTime())
                ||
                ((leftStartDate.getTime() <= rightStartDate.getTime())
                        && leftEndDate.getTime() >= rightEndDate.getTime());
    }


    /**
     * 年月时间处理
     * @param date
     * @return
     */
    public static String judgeDate(Date date) {
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date old = sdf.parse(sdf.format(date));
            Date now = sdf.parse(sdf.format(new Date()));
            long oldTime = old.getTime();
            long nowTime = now.getTime();
            long day = (nowTime - oldTime) / (24 * 60 * 60 * 1000);
            if (day < 1) {  //今天
                return "0";
            } else if (day == 1) {     //昨天
                return "1";
            } else {    //可依次类推
                return "2";
            }
        }catch (Exception  e){
            throw new BaseException("转换失败");
        }
    }

    public static String changeHHmmDate(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        return  sdf.format(date);
    }


    public static  int getAge(Date birthDay) {
        try{
            Calendar cal = Calendar.getInstance();
            if (cal.before(birthDay)) { //出生日期晚于当前时间，无法计算
                throw new IllegalArgumentException(
                        "The birthDay is before Now.It's unbelievable!");
            }
            int yearNow = cal.get(Calendar.YEAR);  //当前年份
            int monthNow = cal.get(Calendar.MONTH);  //当前月份
            int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH); //当前日期
            cal.setTime(birthDay);
            int yearBirth = cal.get(Calendar.YEAR);
            int monthBirth = cal.get(Calendar.MONTH);
            int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
            int age = yearNow - yearBirth;   //计算整岁数
            if (monthNow <= monthBirth) {
                if (monthNow == monthBirth) {
                    if (dayOfMonthNow < dayOfMonthBirth) age--;//当前日期在生日之前，年龄减一
                }else{
                    age--;//当前月份在生日之前，年龄减一
                } }
            return age;
        }catch (Exception e){
            throw new BaseException("年龄转换失败");
        }
    }

    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime 当前时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     * <AUTHOR>
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    public static int pdTime(Date date){
        SimpleDateFormat df = new SimpleDateFormat("HH");
        String str = df.format(date);
        int a = Integer.parseInt(str);
        if (a >= 0 && a < 12) {
            return 1;
        }else if (a >= 12 && a < 18) {
            return 2;
        }else{
            return 3;
        }
    }

    public static Date pjTime(Date YYYYMMDDDate,Date HHMMSSDate){
        SimpleDateFormat ymddf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat hmsdf = new SimpleDateFormat("HH:mm:ss");
        String format = ymddf.format(YYYYMMDDDate);
        String format1 = hmsdf.format(HHMMSSDate);
        String ymdhms=format+" "+format1;
        Date date = parseDate(ymdhms);
        return date;
    }

    public static Date AddHour (Date date){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR,1);
        return c.getTime();
    }

    public static Date pjTimeTow(Date YYYYMMDDDate,Date HHMMSSDate){
        Calendar c = Calendar.getInstance();
        c.setTime(YYYYMMDDDate);
        c.add(Calendar.DAY_OF_MONTH,1);
        SimpleDateFormat ymddf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat hmsdf = new SimpleDateFormat("HH:mm:ss");

        String format = ymddf.format(c.getTime());
        String format1 = hmsdf.format(HHMMSSDate);
        String ymdhms=format+" "+format1;
        Date date = parseDate(ymdhms);
        return date;
    }

    /**
     * 校验2个时间是否相差多少分钟
     * @param d1
     * @param d2
     * @param num
     * @return
     */
    public static boolean checkDateSize(Date d1,Date d2,Integer num){
        Calendar c = Calendar.getInstance();
        c.setTime(d2);
        c.add(Calendar.MINUTE,num);
        Date time = c.getTime();
        if(time.compareTo(d1)>0||time.compareTo(d1)==0){
            return false;
        }
        return true;
    }

    public static long jsDate(Date date1,Date date2){
        long diff = date2.getTime() - date1.getTime();
        long min = diff / 60 / 1000;
        return min;
    }

    public static boolean isAvailableDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()) {
            return true;
        }
        if (nowTime.getTime() == endTime.getTime()) {
            return false;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 计算两个时间段时间差，精确到秒
     * @param startTime 2019-04-10 17:16:11
     * @param endTime 2019-04-10 17:28:17
     * @return
     */
    public static int computationTime(Date startTime, Date endTime){
        try {
            //时间差（毫秒级）
            Long totalTime=endTime.getTime()-startTime.getTime();
            int hours = (int) (totalTime / (1000 * 60 * 60));
            return Math.abs(hours);
        }catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 计算时间相隔
     * @param startTime 2019-04-10 17:16:11
     * @return
     */
    public static String calculateInterval(Date startTime, CycleEnum cycleEnum){
        try {
            String endTime="";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
            Calendar c = Calendar.getInstance();
            c.setTime(startTime);
            switch (cycleEnum){
                case ONE_DAY:
                    c.add(Calendar.DAY_OF_YEAR, CycleEnum.getTypeByInfoToDay(CycleEnum.ONE_DAY));
                    endTime = simpleDateFormat.format(c.getTime());
                    break;
                case ONE_WEEK:
                    c.add(Calendar.DAY_OF_YEAR,CycleEnum.getTypeByInfoToDay(CycleEnum.ONE_WEEK));
                    endTime = simpleDateFormat.format(c.getTime());
                    break;
                case ONE_MONTH:
                    c.add(Calendar.DAY_OF_YEAR,CycleEnum.getTypeByInfoToDay(CycleEnum.ONE_MONTH));
                    endTime = simpleDateFormat.format( c.getTime());
                    break;
                case ONE_SEASON:
                    c.add(Calendar.DAY_OF_YEAR,CycleEnum.getTypeByInfoToDay(CycleEnum.ONE_SEASON));
                    endTime = simpleDateFormat.format( c.getTime());
                    break;
                case HALF_YEAR:
                    c.add(Calendar.DAY_OF_YEAR,CycleEnum.getTypeByInfoToDay(CycleEnum.HALF_YEAR));
                    endTime = simpleDateFormat.format( c.getTime());
                    break;
                case ONE_YEAR:
                    c.add(Calendar.DAY_OF_YEAR, CycleEnum.getTypeByInfoToDay(CycleEnum.ONE_YEAR));
                    endTime = simpleDateFormat.format( c.getTime());
                    break;
                default:
            }
            return endTime;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public static String calculateIntervalDay(Date startTime,Integer num){
        try {
            String endTime="";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
            Calendar c = Calendar.getInstance();
            c.setTime(startTime);
            c.add(Calendar.DAY_OF_YEAR, num);
            endTime = simpleDateFormat.format(c.getTime());
            return endTime;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getTimeDiff(long date,long date1) {
        String result = "";
        long day = 0;//天数差
        long hour = 0;//小时数差
        long min = 0;//分钟数差
        long diff=0 ;//毫秒差
        diff = date - date1;
        day = diff / (24 * 60 * 60 * 1000);
        hour = (diff / (60 * 60 * 1000) - day * 24);
        min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
        return hour+"小时"+min+"分钟";
    }

    public static  String minZHour(Integer min){
        int hours = (int) Math.floor(min / 60);
        int minute = min % 60;
        if(hours==0){
            return minute + "分钟";
        }
        if(minute==0){
            return hours + "小时";
        }
        return hours + "小时" + minute + "分钟";
    }

    /**
     * 获取上一个月1号0点0分0秒的时间
     */
    public static String getBeforeFirstMonthdate(){
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至00
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        //将分钟至00
        calendar.set(Calendar.MINUTE, 00);
        //将秒至00
        calendar.set(Calendar.SECOND,00);
        String format1 = format.format(calendar.getTime());
        return format1;
    }

    /**
     * 获取上个月的最后一天23点59分59秒的时间
     */
    public static  String getBeforeLastMonthdate(){
        SimpleDateFormat sf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar=Calendar.getInstance();
        int month=calendar.get(Calendar.MONTH);
        calendar.set(Calendar.MONTH, month-1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        calendar.set(Calendar.MINUTE, 59);
        //将秒至59
        calendar.set(Calendar.SECOND,59);
        String format = sf.format(calendar.getTime());
        return format;
    }

    /**
     * 获取某天的最大时间：23点59分59秒
     */
    public static Date getMaxDate(Date date){
        SimpleDateFormat sf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        calendar.set(Calendar.MINUTE, 59);
        //将秒至59
        calendar.set(Calendar.SECOND,59);
        return calendar.getTime();
    }

    public static String pjDays(String date){
        String[] split = date.split("-");
        int year=Integer.valueOf(split[0]);
        int month=Integer.valueOf(split[1]);
        int days=0;
        switch (month){
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                days=31;
                break;
            case 4:
            case 6:
            case 9:
            case 11:
                days=30;
                break;
            case 2:
                if((year%4==0&&year%100!=0) || year%400==0){
                    days=29;
                }   else {
                    days=28;
                }
                break;
        }
        return date+"-"+days;
    }

    /**
     * 获取某年某月天数
     * @param date 日期
     * @return 天数
     */
    public static int getMonthTotalDays(Date date){
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        return instance.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 计算年与年之间的间隔
     *
     * @return Long
     */
    public static Long yearGap(Date date){
        ZoneId zoneId = ZoneId.systemDefault();

        Date nowDate = new Date();
        Instant instant1 = nowDate.toInstant();
        LocalDate localDate1 = instant1.atZone(zoneId).toLocalDate();

        Instant instant2 = date.toInstant();
        LocalDate localDate2 = instant2.atZone(zoneId).toLocalDate();

        return ChronoUnit.YEARS.between(localDate2, localDate1) ;

    }


    /**
     * 获取今天的日期（时分秒为：00:00:00）
     * @return 今天的日期
     */
    public static Date getToday(){
        LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS);
        Date today = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        return today;
    }

    /**
     * 清除Date对象中的时间（时分秒为：00:00:00）
     * @return
     */
    public static Date clearTime(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 给指定的日期添加指定天数
     * @return 处理后的日期
     */
    public static Date addDays(Date date, Integer daysToAdd){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 给日期加上指定的天数
        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
        return calendar.getTime();
    }

    /**
     * 判断两个时间区间是否重叠
     * @param begin1 区间1开始时间
     * @param end1 区间1开始时间
     * @param begin2 区间2开始时间
     * @param end2 区间2开始时间
     * @return true.重叠 false.不重叠
     */
    public static Boolean isLapped(Date begin1, Date end1, Date begin2, Date end2){
        return !end1.before(begin2) && !begin1.after(end2);
    }

    /**
     * 时间拼接
     * @param date 日期
     * @param time 时间字符串
     * @return 拼接后的日期
     */
    public static Date timeJoint(Date date, String time){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = dateFormat.format(date);
        dateStr = dateStr + " " + time;
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dateTime = null;
        try {
            dateTime = dateFormat.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return date;
        }
        return dateTime;
    }

    /**
     * 获取指定日期所在周的周一的日期
     * @param date 日期
     * @return 周一的日期
     */
    public static Date getFirstDayOfWeek(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate monday = localDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        return new Date(monday.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    /**
     * 获取指定日期是本周的周几（周一为1、周二为2......周日为7）
     * @param date 日期
     * @return 周几
     */
    public static Integer dayOfWeek(Date date){
        Integer dayOfWeek = DateUtil.dayOfWeek(date) - 1;
        dayOfWeek = dayOfWeek == 0 ? 7 : dayOfWeek;
        return dayOfWeek;
    }


    public static Integer getBetweenDays(String dateStr, Date today) {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date dateTime = null;
        try {
            dateTime = dateFormat.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return (int)(DateUtil.betweenDay(dateTime, today,true));
    }

    /**
     * 判断两个日期是否相等：这两个日期可以都为null
     * @param date1 日期1
     * @param date2 日期2
     * @return 是否相等
     */
    public static boolean isEqual(Date date1, Date date2) {
        if(date1 == null && date2 == null)
            return true;
        if(date1 != null && date2 != null)
            return date1.compareTo(date2) == 0;
        return false;
    }

    /**
     * 计算两个日期之间间隔的天数
     * @param date1 日期1
     * @param date2 日期2
     * @return 间隔的天数
     */
    public static Integer getDiffInDays(Date date1, Date date2) {
        //重置时分秒为0:00:00
        date1 = DateUtils.clearTime(date1);
        date2 = DateUtils.clearTime(date2);

        // 计算两个日期的时间差（毫秒）
        long diffInMillies = date2.getTime() - date1.getTime();

        // 将毫秒转化为天数
        int diffInDays = (int) (TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS));

        return Math.abs(diffInDays);
    }

    /**
     * 根据年龄计算生日
     * @param age 年龄
     * @return 是否相等
     */
    public static Date getBirthday(Integer age) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // 给日期加上指定的年数
        calendar.add(Calendar.YEAR, -age);
        return calendar.getTime();
    }

    /**
     * 根据年龄获取生日字符串：yyyy-MM-dd格式
     * @param ageStr 年龄字符串，目前只有这三种case：4岁、2个月13天、8个月
     * @return yyyy-MM-dd格式的患者生日
     */
    public static String guessBirthdayByAge(String ageStr){
        if(StringUtils.isEmpty(ageStr)) {
            return parse_yyyyMMdd(new Date());
        }

        //根据不同的case，定义三种正则
        String reg1="\\d+岁";
        String reg2="(\\d+)个月(\\d+)天";
        String reg3="(\\d+)个月";
        //年月日初始化
        int years = 0;
        int months = 0;
        int days = 0;
        try{
            //X岁
            if(ageStr.matches(reg1)){
                years = Integer.parseInt(ageStr.replace("岁", ""));
                //X个月Y天
            }else if(ageStr.matches(reg2)){
                Pattern pattern = Pattern.compile(reg2);
                Matcher matcher = pattern.matcher(ageStr);
                if(matcher.find()){
                    months = Integer.parseInt(matcher.group(1));
                    days = Integer.parseInt(matcher.group(2));
                }
                //X个月
            } else if(ageStr.matches(reg3)){
                months = Integer.parseInt(ageStr.replace("个月", ""));
            } else if (NumberUtil.isNumber(ageStr)) {
                years = Integer.parseInt(ageStr);
            }

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            // 给日期加上指定的年数、月数、天数
            calendar.add(Calendar.YEAR, -years);
            calendar.add(Calendar.MONTH, -months);
            calendar.add(Calendar.DAY_OF_YEAR, -days);
            return DateUtils.parse_yyyyMMdd(calendar.getTime());
        }catch (Exception e){
            return DateUtils.parse_yyyyMMdd(new Date());
        }
    }
}
