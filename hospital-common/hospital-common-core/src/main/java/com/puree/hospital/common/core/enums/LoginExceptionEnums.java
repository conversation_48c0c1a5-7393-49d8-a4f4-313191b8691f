package com.puree.hospital.common.core.enums;

import lombok.Getter;

/**
 * <p>
 * 患者端登录错误
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/15 15:29
 */
@Getter
public enum LoginExceptionEnums {

    /**
     * ALREADY_LOGGED_IN(0,"该微信号已绑定其它手机号"),
     *      * BOUND_FAIL(1,"该手机号码在其他微信已登录");
     *      * UNBOUND(2,"该手机号码未绑定"),
     */
    ALREADY_LOGGED_IN(0,"该手机号码在其他微信已登录"),
    BOUND_FAIL(1,"该微信号已绑定其它手机号"),
    UNBOUND(2,"该手机号码未绑定");

    private final int code;
    private final String message;

    LoginExceptionEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }


    public static LoginExceptionEnums value(String message) {
        for (LoginExceptionEnums e : LoginExceptionEnums.values()) {
            if (e.getMessage().equals(message)) {
                return e;
            }
        }
        return null;
    }
}
