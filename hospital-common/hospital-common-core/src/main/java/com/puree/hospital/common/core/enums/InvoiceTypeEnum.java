package com.puree.hospital.common.core.enums;

/**
 * @ClassName: InvoiceTypeEnum
 * @Date 2023/10/24 16:27
 * <AUTHOR> jian
 * @Description: InvoiceTypeEnum
 * @Version 1.0
 */
public enum InvoiceTypeEnum {

    TAX_CONTROl(1, "税控盘") ,
    ELECTRONIC(2, "全电")


    ;

    private Integer id;
    private String msg;

    InvoiceTypeEnum(Integer id, String msg){
        this.id=id;
        this.msg=msg;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
