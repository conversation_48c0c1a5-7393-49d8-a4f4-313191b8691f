package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * @ClassName: EhrDataInfoEnum
 * @Date 2022/12/1 11:19
 * <AUTHOR>
 * @Description: 健康档案信息枚举
 * @Version 1.0
 */
public enum EhrDataInfoEnum {
    BMI("0","身体质量指数"),
    BP("1","血压 mmHg"),
    GLU("2","血糖 mmol/L"),
    TEMPERATURE("3","体温"),
    BLOODLIPIDS("4","血脂 mmol/L"),
    UA("5","尿酸 mmol/L"),
    BLOODOXYGEN("6","血氧");
    private final String code;
    private final String info;

    EhrDataInfoEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static EhrDataInfoEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (EhrDataInfoEnum enums : EhrDataInfoEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
