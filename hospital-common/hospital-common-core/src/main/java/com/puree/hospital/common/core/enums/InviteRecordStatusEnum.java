package com.puree.hospital.common.core.enums;

/**
 *
 *  是否
 * <AUTHOR>
 */
public enum InviteRecordStatusEnum {
    WAIT_JOIN("0", "未入组"),
    JOINED("1", "已入组"),
    REFUSED("2","已拒绝"),
    INVALID("3","已失效");


    private final String code;
    private final String info;

    InviteRecordStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
