package com.puree.hospital.common.core.enums;

public enum FamilySourceTypeEnum {
    PATIENT("0", "患者添加"),
    DOCTOR("1", "医生二维码添加"),
    AGENT("2", "经纪人二维码添加"),
    GUIDANCE("3", "导引平台添加");


    private final String code;
    private final String info;

    FamilySourceTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static String getInfoByKey(String key) {
        for (FamilySourceTypeEnum value : FamilySourceTypeEnum.values()) {
            if (value.getCode().equals(key)) {
                return value.getInfo();
            }
        }
        return "";
    }

    public static String getKeyByInfo(String info) {
        for (FamilySourceTypeEnum value : FamilySourceTypeEnum.values()) {
            if (value.getInfo().equals(info)) {
                return value.getCode();
            }
        }
        return "";
    }
}
