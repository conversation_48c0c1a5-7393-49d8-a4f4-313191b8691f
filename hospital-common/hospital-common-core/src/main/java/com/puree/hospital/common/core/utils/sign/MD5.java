package com.puree.hospital.common.core.utils.sign;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * MD5签名工具
 */
@Slf4j
public class MD5 {

    /**
     * MD5方法
     *
     * @param text 明文
     * @param key  密钥
     * @return 密文
     * @throws Exception 异常
     */
    public static String md5(String text, String key) throws Exception {
        String encodeStr = DigestUtils.md5Hex(text + key);
        log.info("MD5加密后密文为：{}", encodeStr);
        return encodeStr;
    }

    /**
     * MD5验证方法
     *
     * @param text 明文
     * @param key  密钥
     * @param md5  密文
     * @return true/false
     * @throws Exception 异常
     */
    public static boolean verify(String text, String key, String md5) throws Exception {
        return md5(text, key).equalsIgnoreCase(md5);
    }
}
