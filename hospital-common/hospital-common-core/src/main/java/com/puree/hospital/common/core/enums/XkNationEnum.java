package com.puree.hospital.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 民族枚举
 * <AUTHOR>
 * @date 2022/11/10 18:58
 */
@Getter
@AllArgsConstructor
public enum XkNationEnum {
    HAN_DYNASTY("汉族", "1"),
    MONGOLIAN("蒙古族", "2"),
    HUI_NATIONALITY("回族", "3"),
    TIBETAN("藏族", "4"),
    UIGHUR("维吾尔族", "5"),
    MIAO("苗族", "6"),
    YI("彝族", "7"),
    ZHUANG("壮族", "8"),
    BUYI("布依族", "9"),
    KOREAN("朝鲜族", "10"),
    MANCHU("满族", "11"),
    DONG("侗族", "12"),
    YAO("瑶族", "13"),
    BAI("白族", "14"),
    TUJIA("土家族", "15"),
    HANI("哈尼族", "16"),
    KAZAK("哈萨克族", "17"),
    DAI("傣族", "18"),
    LI("黎族", "19"),
    LISU_NATIONALITY("傈僳族", "20"),
    WA("佤族", "21"),
    SHE("畲族", "22"),
    GAOSHAN("高山族", "23"),
    LAHU("拉祜族", "24"),
    SHUI("水族", "25"),
    DONGXIANG("东乡族", "26"),
    NAXI("纳西族", "27"),
    JINGPO("景颇族", "28"),
    KIRGHIZ("柯尔克孜族", "29"),
    DU("土族", "30"),
    DAUR("达斡尔族", "31"),
    MULAO_NATIONALITY("仫佬族", "32"),
    QIANG("羌族", "33"),
    BLANG("布朗族", "34"),
    SALAR("撒拉族", "35"),
    MAONAN("毛南族", "36"),
    GELAO("仡佬族", "37"),
    XIBE("锡伯族", "38"),
    ACHANG("阿昌族", "39"),
    PUMI("普米族", "40"),
    TAJIK("塔吉克族", "41"),
    NU("怒族", "42"),
    UZBEK("乌孜别克族", "43"),
    RUSSIAN("俄罗斯族", "44"),
    EWENKI("鄂温克族", "45"),
    GERMAN_PLEIADES("德昴族", "46"),
    BONAN("保安族", "47"),
    YUGUR("裕固族", "48"),
    GIN("京族", "49"),
    TATAR("塔塔尔族", "50"),
    DRUNG("独龙族", "51"),
    OROQIN("鄂伦春族", "52"),
    HEZHEN("赫哲族", "53"),
    MONBA_NATIONALITY("门巴族", "54"),
    LHOBA("珞巴族", "55"),
    KENO("基诺族", "56"),
    MINORITY("少数民族","99");

    private final String doc;
    private final String value;
}
