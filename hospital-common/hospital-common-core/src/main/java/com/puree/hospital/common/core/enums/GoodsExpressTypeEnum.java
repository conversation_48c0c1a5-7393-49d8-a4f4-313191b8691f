package com.puree.hospital.common.core.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * 商品物流类型枚举
 *
 * <AUTHOR>
 * @date 2023/8/3 16:09
 */
@Getter
public enum GoodsExpressTypeEnum {
    /**
     * 普通商品
     */
    NORMAL("0", "普通商品"),
    /**
     * 冷链商品
     */
    COLD("1", "冷链商品"),
    /**
     * 无需物流
     */
    NOT("2", "无需物流"),
    /**
     * 虚拟商品
     */
    VIRTUAL("3", "虚拟商品");

    /**
     * 商品物流类型编码
     */
    private final String code;

    /**
     * 描述信息
     */
    private final String info;

    GoodsExpressTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 是否无需物流
     * 目前：虚拟商品、无需物流商品
     *
     * @param expressType 商品物流类型
     * @return true: 无需物流
     */
    public static boolean unnecessaryLogistics(String expressType) {
        return NOT.getCode().equals(expressType) || VIRTUAL.getCode().equals(expressType);
    }
}
