package com.puree.hospital.common.core.enums;

/**
 * @ClassName: ShopCartEnum
 * @Date 2023/2/2 11:55
 * <AUTHOR>
 * @Description: 购物车类型枚举
 * @Version 1.0
 */
public enum ShopCartTypeEnum {
    PRESCRIPTION("0", "处方"),
    ADVANCE_ORDER("1", "预订单"),
    DRUGS("2", "药品"),
    GOODS("3", "商品");

    private final String code;
    private final String info;

    ShopCartTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
