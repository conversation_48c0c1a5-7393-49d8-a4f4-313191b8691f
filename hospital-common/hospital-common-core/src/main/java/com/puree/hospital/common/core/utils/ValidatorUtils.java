package com.puree.hospital.common.core.utils;

import com.puree.hospital.common.core.exception.ServiceException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName ValicatedUtil
 * @Description 对象属性校验工具类
 * @Date 2024/11/25  14:13
 * <AUTHOR>
 */

public class ValidatorUtils {

    private static Validator validator;

    static {
        validator= Validation.buildDefaultValidatorFactory().getValidator();
    }

    /**\
     * 对单个对象进行校验
     * @param param 待校验的对象
     * @param groups 分组
     */
    public static void validate(Object param,Class... groups){
        validate(new Object[] {param},groups);
    }

    /**
     * 对多个对象进行校验
     * @param params 待校验的对象
     * @param groups 分组
     */
    public static void validate(Object[] params,Class... groups){
        if(Objects.isNull(params)){
            return;
        }
        for (Object param : params) {
            Set<ConstraintViolation<Object>> validate = validator.validate(param, groups);

            //当有一个对象校验失败，就抛出异常
            if (!validate.isEmpty()) {
                //抛出所有校验失败信息
                throw new ServiceException("参数异常："+validate.stream().map(ConstraintViolation::getMessage).collect(Collectors.toList()));
            }
        }
    }
}
