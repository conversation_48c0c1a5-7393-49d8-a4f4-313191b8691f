package com.puree.hospital.common.core.enums;

public enum CallbackResultEnum {
    SUCCESS(0, "成功"),
    FAILURE(-1, "接口异常"),
    VALIDATE_PARAMS_ERROR(1001, "校验参数有误"),
    TIMESTAMP_INVALID(1002, "时间戳无效"),
    REPEAT_REQUEST(1003, "10s内重复请求"),
    SIGN_NOT_PASSED(1004, "签名不通过"),
    PARAMS_MISSING(1005, "body参数缺失"),
    NO_EXIT_INSTITUTION(1006, "机构不存在"),
    REPORT_TYPE_ERROR(1007, "报告类型错误"),
    SIGNATURE_ALGORITHM_ERROR(1008, "签名算法错误"),
    SIGN_PARAM_ERROR(1009, "该参数加签未实现");

    private final Integer code;
    private final String info;

    CallbackResultEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
