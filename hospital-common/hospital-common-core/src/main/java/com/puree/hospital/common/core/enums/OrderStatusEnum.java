package com.puree.hospital.common.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/24 17:52
 */
@Getter
public enum OrderStatusEnum {
    /**
     * 待支付
     */
    WAIT_PAY("0", "待支付"),
    WAIT_DELIVER("1", "待发货"),
    WAIT_PICK("2", "待自提"),
    WAIT_RECEIVE("3", "待收货"),
    CANCEL("4", "已取消"),
    FINISH("5", "已完成"),
    FINISH_NOAFTERSALE("6", "已完成且不支持售后"),
    FREIGHT_WAIT_PAY("7", "运费待支付");
    private String code;
    private String info;

    OrderStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public static String getInfoByCode(String code) {
        OrderStatusEnum[] statusEnums = values();
        for (OrderStatusEnum item : statusEnums) {
            if (item.getCode().equals(code)) {
                return item.getInfo();
            }
        }
        return null;
    }

    public static boolean isWaitPay(String code) {
        return WAIT_PAY.getCode().equals(code);
    }

    public static boolean isCancel(String code) {
        return CANCEL.getCode().equals(code);
    }

    /**
     * 判断自提商品是否已核销
     *
     * @param code 法典
     * @return boolean
     */
    public static boolean isVerify(String code) {
        return FINISH_NOAFTERSALE.getCode().equals(code) || FINISH.getCode().equals(code);
    }

    public static boolean isWaitDeliver(String code) {
        return WAIT_DELIVER.getCode().equals(code);
    }

    public static boolean isWaitReceive(String code) {
        return WAIT_RECEIVE.getCode().equals(code);
    }


    public static boolean isFinish(String code) {
        return FINISH.getCode().equals(code) || FINISH_NOAFTERSALE.getCode().equals(code);
    }

    public static boolean isWaitPick(String code) {
        return WAIT_PICK.getCode().equals(code);
    }

    public static boolean isUnComplete(String code) {
            return !OrderStatusEnum.isWaitPick(code)
                    && !OrderStatusEnum.isFinish(code)
                    && !OrderStatusEnum.isWaitReceive(code);
    }

    public static boolean isNotFinishWithAfterSale(String code) {
        return !FINISH_NOAFTERSALE.getCode().equals(code);
    }
}
