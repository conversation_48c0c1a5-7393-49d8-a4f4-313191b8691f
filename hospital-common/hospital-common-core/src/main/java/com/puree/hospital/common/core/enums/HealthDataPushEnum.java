package com.puree.hospital.common.core.enums;

public enum HealthDataPushEnum {
    PUSHURL("0", "https://za.aidmed.net/stage-api/hardware/eHome/uploadCheckData"),
    REQUEST_URL("1", "https://hfmeditech.com:8070/TF02/client/url/insertBatch"),
    DELETEBATCH_REQUEST_URL("1", "https://hfmeditech.com:8070/TF02/client/url/deleteBatch"),
    CLIENTKEY("2", "6B18EC07E1A94BD80BB8365E6F6844D1"),
    TYPE("3", "kz_zayl");
    private final String code;
    private final String info;

    HealthDataPushEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
