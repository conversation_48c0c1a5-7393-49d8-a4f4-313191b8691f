package com.puree.hospital.common.core.enums;

public enum BusScalesEnum {
    //   量表类型 0-标准 1-自定义 2-心血管 3-中医体质
    CARDIOVASCULAR_SYSTEM("2", "心血管"),
    TRADITIONAL_CHINESE_MEDICINE_PHYSIQUE("3", "中医体质"),
    STANDARD_SCALES("0", "标准量表"),
    SEX_MEN("1","男性"),
    SEX_WOMEN("0","女性"),
    CUSTOM_DEFINITION_SCALES("1", "自定义量表");



    private final String  code;
    private final String info;

    BusScalesEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (BusScalesEnum enums : BusScalesEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }
}
