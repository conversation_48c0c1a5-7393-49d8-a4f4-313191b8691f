package com.puree.hospital.common.core.enums;


/**
 * 就诊人分析
 *
 */
public enum ProportionEnum {
    AGE_ANALYSIS(1,"年龄分析"),
    SEX_ANALYSIS(2,"性别分析"),
    REGIONAL_ANALYSIS(3,"地域分析"),
    DIAGNOSIS_ANALYSIS(4,"诊断分析"),
    REGIONAL_ANALYSIS5(5,"地域分析"),;
    private final int  code;
    private final String info;

    ProportionEnum(int code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public int getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}
