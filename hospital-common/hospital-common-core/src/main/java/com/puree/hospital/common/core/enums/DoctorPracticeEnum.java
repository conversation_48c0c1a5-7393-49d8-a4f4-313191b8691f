package com.puree.hospital.common.core.enums;

/**
 * 医生执业方式
 * <AUTHOR>
 */
public enum DoctorPracticeEnum {
    MULTIPOINT (0,"多点"),
    OUR_HOSPITAL(1,"本院"),
    SPECIAL_INVITATION(2,"特邀");
    private final Integer code;
    private final String info;

    DoctorPracticeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
