package com.puree.hospital.common.core.enums;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
public enum PharmacistCategoryEnum {
    DELIVERY_PHARMACIST("0", "发货药师"),
    PHARMACIST("1", "审方药师"),
    DISPENSING_PHARMACIST("2", "调剂药师");

    private final String value;
    private final String name;

    PharmacistCategoryEnum(String value, String name)
    {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }
    public String getName() {
        return name;
    }

    public static String getNameByCode(String code){
        for (PharmacistCategoryEnum item : PharmacistCategoryEnum.values()) {
            if (item.value.equals(code)){
                return item.name;
            }
        }
        return null;
    }

    /**
     * 通过Info取Code
     */
    public static String getCodeByInfo(String info){
        for (PharmacistCategoryEnum item : PharmacistCategoryEnum.values()) {
            if (item.name.equals(info)){
                return item.value;
            }
        }
        return null;
    }
}
