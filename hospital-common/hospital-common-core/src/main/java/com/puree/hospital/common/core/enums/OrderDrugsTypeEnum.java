package com.puree.hospital.common.core.enums;

public enum OrderDrugsTypeEnum {

    OFFICINA("0","中药订单"),
    ENTERPRISE("1","西药订单"),
    PARTIES("2","协定方");

    private final String code;
    private final String info;

    OrderDrugsTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}
