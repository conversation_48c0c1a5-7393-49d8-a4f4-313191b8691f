package com.puree.hospital.common.core.utils.http;

import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Http请求工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseHttpUtil {
    private static final int TIME_OUT = 60 * 1000;

    /**
     * Get请求
     *
     * @param url    请求地址
     * @param params 请求参数
     * @return 请求结果
     */
    public static String sendGetRequest(String url, Map<String, String> params) {
        return sendGetRequest(url, params, null);
    }

    /**
     * Post请求
     * contentType为 application/json
     *
     * @param url    请求地址
     * @param body   请求参数
     * @param header 请求头
     * @return 请求结果
     */
    public static String sendPostJsonRequest(String url, String body, Map<String, String> header) {
        return sendPostRequest(url, body, ContentType.APPLICATION_JSON, header);
    }

    /**
     * Get请求带请求头
     *
     * @param url    请求地址
     * @param params 请求参数
     * @param header 请求头
     * @return 请求结果
     */
    public static String sendGetRequest(String url, Map<String, String> params, Map<String, String> header) {
        try {
            StringBuilder urlBuilder = new StringBuilder(url);
            if (params != null && !params.isEmpty()) {
                urlBuilder.append("?");
                for (Map.Entry<String, String> param : params.entrySet()) {
                    String paramKey = param.getKey();
                    String paramValue = param.getValue();
                    String encodeValue = URLEncoder.encode(paramValue, StandardCharsets.UTF_8.name());
                    urlBuilder.append(paramKey).append("=").append(encodeValue).append("&");
                }
                urlBuilder.setLength(urlBuilder.length() - 1);
            }
            HttpGet httpGet = new HttpGet(urlBuilder.toString());
            if (header != null && !header.isEmpty()) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpGet.setHeader(entry.getKey(), entry.getValue());
                }
            }
            // 设置超时时间
            RequestConfig config = RequestConfig.custom().setConnectTimeout(TIME_OUT)
                    .setSocketTimeout(TIME_OUT).build();
            httpGet.setConfig(config);
            log.info("Get请求" + url + "参数：" + JSON.toJSONString(params));
            log.info("Get请求" + url + "请求头：" + JSON.toJSON(header));
            return handleResponse(httpGet);
        } catch (Exception exception) {
            log.error("HttpUtil调用Get请求异常", exception);
            throw new ServiceException("HttpUtil调用Get请求异常：" + exception.getMessage());
        }
    }

    /**
     * Post请求
     *
     * @param url         请求地址
     * @param body        请求参数
     * @param header      请求头
     * @param contentType 参数内容类型
     * @return 请求结果
     */
    public static String sendPostRequest(String url, String body, ContentType contentType, Map<String, String> header) {
        try {
            HttpPost httpPost = new HttpPost(url);
            if (contentType != null) {
                httpPost.setHeader(HttpHeaders.CONTENT_TYPE, contentType.getMimeType());
            }
            if (body != null) {
                StringEntity requestEntity = new StringEntity(body, ContentType.APPLICATION_JSON);
                httpPost.setEntity(requestEntity);
            }
            if (header != null && !header.isEmpty()) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            // 设置超时时间
            RequestConfig config = RequestConfig.custom().setConnectTimeout(TIME_OUT)
                    .setSocketTimeout(TIME_OUT).build();
            httpPost.setConfig(config);
            log.info("Post请求" + url + "body参数：" + body);
            log.info("Post请求" + url + "请求头：" + JSON.toJSON(header));
            return handleResponse(httpPost);
        } catch (Exception exception) {
            log.error("HttpUtil调用Post请求异常", exception);
            throw new ServiceException("HttpUtil调用Post请求异常：" + exception.getMessage());
        }
    }

    private static String handleResponse(HttpRequestBase httpReq) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(httpReq)) {
            HttpEntity entity = response.getEntity();
            if (null == entity) {
                throw new IOException("HTTP request succeeded, but the response body is empty");
            }
            String result = EntityUtils.toString(response.getEntity());
            log.info("请求返回日志：" + result);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200 || statusCode == 201) {
                return result;
            } else {
                throw new IOException("HTTP request failed with status code: " + statusCode);
            }
        }
    }
}

