package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * 跟进报告设备来源
 */
public enum ReportFollowUpEnum {
    //来源 0-携康（暂未存进来）1-绘云 2-双佳 3-E家

    XKSB("1","绘云"),
    SONKASB("2","双佳"),
    EHOMESB("3","E家");
    private final String code;
    private final String info;

    ReportFollowUpEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static FiveRoleEnum getTypeByValue(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (FiveRoleEnum enums : FiveRoleEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }



    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (FiveRoleEnum enums : FiveRoleEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }
}
