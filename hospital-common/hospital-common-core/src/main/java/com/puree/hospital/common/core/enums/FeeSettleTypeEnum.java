package com.puree.hospital.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * 医保类型枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/8 10:24
 */
@Getter
public enum FeeSettleTypeEnum {

    /**
     * 自费
     */
    SELF( "SELF", "自费"),

    /**
     * 医保（普通门诊）
     */
    MI_GENERAL("MI_GENERAL", "医保（普通门诊）"),

    /**
     * 医保（门特慢）
     */
    MI_SPECIAL("MI_SPECIAL", "医保（门特门慢）");

    /**
     * 费用类别
     */
    private final String type;

    /**
     * 描述
     */
    private final String desc;

    FeeSettleTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static FeeSettleTypeEnum getByType(String type) {
        for (FeeSettleTypeEnum feeTypeEnum : FeeSettleTypeEnum.values()) {
            if (feeTypeEnum.getType().equals(type)) {
                return feeTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否为自费
     *
     * @param type 费用类别
     * @return 是否是自费
     */
    public static boolean isSelfSettle(String type) {
        return SELF.getType().equals(type);
    }

    /**
     * 是否医保结算
     *
     * @param type 费用类别
     * @return 是否是医保支付
     */
    public static boolean isMiSettle(String type) {
        return MI_GENERAL.getType().equals(type) || MI_SPECIAL.getType().equals(type);
    }

    /**
     * 是否医保门特慢
     *
     * @param type 费用类别
     * @return 是否是医保支付
     */
    public static boolean isMiSpecialsSettle(String type) {
        return MI_SPECIAL.getType().equals(type);
    }

    /**
     * 是否匹配
     *
     * @param type 费用类别
     * @return 是否匹配
     */
    public static boolean isMatch(String type) {
        return Objects.nonNull(getByType(type));
    }
}
