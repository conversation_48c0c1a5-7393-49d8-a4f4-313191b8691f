package com.puree.hospital.common.core.enums;

/**
 * <AUTHOR>
 * @date 2024/09/09
 */
public enum PatientLoginTypeEnum {
    OFFI_ACCOUNT("0", "公众号登录"),
    UNIAPP_LOGIN("1", "小程序登录");


    private final String code;
    private final String info;

    PatientLoginTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
