package com.puree.hospital.common.core.enums;

public enum ConsultationOrderTypeEnum {
    /**
     * 图文问诊
     */
    IMAGETEXT("0", "图文问诊"),

    /**
     * 视频问诊
     */
    VIDEO("1", "视频问诊");

    private final String code;
    private final String info;

    ConsultationOrderTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    /**
     * 判断是否是图文问诊
     *
     * @param code 订单类型
     * @return true:是图文问诊
     */
    public static boolean isImageText(String code) {
        return IMAGETEXT.getCode().equals(code);
    }

    /**
     * 判断是否是视频问诊
     *
     * @param code 订单类型
     * @return true:是视频问诊
     */
    public static boolean isVideo(String code) {
        return VIDEO.getCode().equals(code);
    }
}
