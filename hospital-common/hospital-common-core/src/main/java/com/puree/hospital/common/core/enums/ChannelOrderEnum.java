package com.puree.hospital.common.core.enums;

/**
 * 合作渠道订单枚举
 */
public enum ChannelOrderEnum {
    // 合作渠道订单类型
    CO(0, "问诊订单"),
    TO(1, "药品/商品订单"),
    SO(2, "服务包订单");
    private final Integer code;
    private final String info;

    ChannelOrderEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
