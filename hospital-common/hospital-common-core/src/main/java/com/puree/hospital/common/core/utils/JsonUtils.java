package com.puree.hospital.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class JsonUtils {
	private static final Logger logger = (Logger) LoggerFactory.getLogger(JsonUtils.class);
	/**
	 * 
	 * 定义jackson对象
	 */
	private static final ObjectMapper MAPPER = new ObjectMapper();

	private JsonUtils() {
		throw new IllegalStateException("Utility class");
	}

	/**
	 * 将对象转换成json字符串。
	 * <p>
	 * Title: pojoToJson
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 * 
	 * @param data
	 * @return
	 * @throws JsonProcessingException
	 */
	public static String objectToJson(Object data) throws JsonProcessingException {
		try {
			return MAPPER.writeValueAsString(data);
		} catch (JsonProcessingException e) {
			logger.error(e.getMessage());
			throw e;
		}
	}
	

	 /**
	 * 泛型返回，json字符串转对象
	 * @param jsonAsString
	 * @param pojoClass
	 * @return
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 * @throws IOException
	 */
	 public static <T> T fromJson(String jsonAsString, Class<T> pojoClass) throws JsonMappingException,
	  JsonParseException, IOException {
		 return MAPPER.readValue(jsonAsString, pojoClass);
	 }
	 
	 /**
	  * json字符串转Map
	  * @param jsonStr
	  * @return
	  * @throws IOException
	  */
	public static Map<String, Object> parseMap(String jsonStr) throws IOException {
		@SuppressWarnings("unchecked")
		Map<String, Object> map = MAPPER.readValue(jsonStr, Map.class);
		return map;
	}

	/**
	 * 将json结果集转化为对象
	 * 
	 * @param jsonData json数据
	 * @param beanType    对象中的object类型
	 * @return
	 * @throws IOException
	 * @throws Exception
	 */
	public static <T> T jsonToPojo(String jsonData, Class<T> beanType) throws IOException {
		try {
			return MAPPER.readValue(jsonData, beanType);
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw e;
		}
	}

	/**
	 * 将json数据转换成pojo对象list
	 * <p>
	 * Title: jsonToList
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 * 
	 * @param jsonData
	 * @param beanType
	 * @return
	 * @throws Exception
	 */
	public static <T> List<T> jsonToList(String jsonData, Class<T> beanType) throws IOException {
		JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, beanType);
		try {
			return MAPPER.readValue(jsonData, javaType);
		} catch (IOException e) {
			logger.error(e.getMessage());
			throw e;
		}
	}

	/**
	 * 去除json值前后空格
	 * @param jsonStr jsonStr
	 * @return
	 */
	public static JSONObject jsonTrim(String jsonStr) {
		return jsonTrim(JSONObject.parseObject(jsonStr));
	}
	/**
	 * 去除value的空格
	 *
	 * @param jsonObject jsonObject
	 * @return
	 */
	public static JSONObject jsonTrim(JSONObject jsonObject) {
		Iterator<Map.Entry<String, Object>> iterator = jsonObject.entrySet().iterator();
		while (iterator.hasNext()) {
			Map.Entry<String, Object> next = iterator.next();
			Object value = next.getValue();
			if (value != null) {
				if (value instanceof String) {
					//清空值前后空格
					jsonObject.put(next.getKey(), ((String) value).trim());
				} else if (value instanceof JSONObject) {
					jsonTrim((JSONObject) value);
				} else if (value instanceof JSONArray) {
					jsonTrimArray((JSONArray) value);
				}
			}
		}

		return jsonObject;
	}

	/**
	 * 清空JSONArray 值前后空格
	 * @param array
	 */
	private static void jsonTrimArray(JSONArray array) {
		if (array.size() > 0) {
			for (int i = 0; i < array.size(); i++) {
				Object object = array.get(i);
				if (object != null) {
					if (object instanceof String) {
						array.set(i, ((String) object).trim());
					} else if (object instanceof JSONObject) {
						jsonTrim((JSONObject) object);
					} else if (object instanceof JSONArray) {
						jsonTrimArray((JSONArray) object);
					}
				}
			}
		}
	}

	/**
	 * json数组字符串转map
	 *
	 * @param jsonString json
	 * @param keyName keyName
	 * @param valueName valName
	 * @return Map
	 */
	public static Map<String, String> jsonToHashMap(String jsonString,String keyName,String valueName) {
		JSONArray jsonArray = JSON.parseArray(jsonString);
		Map<String, String> hashMap = new HashMap<>(jsonArray.size());
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject jsonObject = jsonArray.getJSONObject(i);
			String key = jsonObject.getString(keyName);
			String value = jsonObject.getString(valueName);
			hashMap.put(key, value);
		}
		return hashMap;
	}

	/**
	 * object转实体
	 *
	 * @param params 转换参数对象
	 * @param clazz 转换后实体类
	 * @return 实体
	 * @param <T> 泛型
	 */
	public static <T> T convertParams(Object params,Class<T> clazz) {
		StringUtils.isNullThrowExp(params,"convert object is null");
		return (T) JSON.parseObject(JSON.toJSONString(params), clazz);
	}
}
