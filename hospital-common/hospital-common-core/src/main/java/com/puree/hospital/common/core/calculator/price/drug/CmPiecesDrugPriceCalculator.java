package com.puree.hospital.common.core.calculator.price.drug;

import cn.hutool.core.util.NumberUtil;
import com.puree.hospital.common.core.calculator.price.IPriceCalculator;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 中药饮片价格计算器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/15 16:11
 */
public class CmPiecesDrugPriceCalculator<I extends IDrugPrice> implements IPriceCalculator<I> {

    @Override
    public BigDecimal calculate(I input) {
        BigDecimal unitPrice = nullToZero(input.getUnitPrice());
        BigDecimal weight = NumberUtil.isNumber(input.getWeight()) ? new BigDecimal(input.getWeight()) : BigDecimal.ZERO;
        //单剂价格 =  单价 * 克重
        BigDecimal amount = unitPrice.multiply(weight).setScale(4, RoundingMode.HALF_UP);
        BigDecimal doses = nullToZero(input.getDoses());
        //小计 = 单剂价格 * 剂数
        return amount.multiply(doses).setScale(2, RoundingMode.HALF_UP);
    }
}
