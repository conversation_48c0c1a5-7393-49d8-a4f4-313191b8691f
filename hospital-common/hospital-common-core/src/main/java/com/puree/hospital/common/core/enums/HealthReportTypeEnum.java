package com.puree.hospital.common.core.enums;

/**
 * 健康报告类型枚举
 * <AUTHOR>
 */
public enum HealthReportTypeEnum {
    MEDICAL_EXAM_REPORT("0", "体检报告"),
    CHECK_REPORT("1", "检查单报告"),
    INSPECTION_REPORT("2","检验单报告");


    private final String code;
    private final String info;

    HealthReportTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
