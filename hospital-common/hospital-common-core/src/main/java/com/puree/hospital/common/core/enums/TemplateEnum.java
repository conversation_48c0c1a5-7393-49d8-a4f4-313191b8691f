package com.puree.hospital.common.core.enums;

/**
 *
 *  模板常量枚举
 * <AUTHOR>
 */
public enum TemplateEnum {
    IMAGE_TEXT_CANCELED("0", "图文问诊已取消"),
    IMAGE_TEXT_RECEIVED_DIAGNOSIS("1", "图文问诊已接诊"),
    IMAGE_TEXT_NUMBER_REFUNDED("2", "图文问诊已退号"),
    IMAGE_TEXT_REFUNDED("3", "图文问诊已退款"),
    IMAGE_TEXT_COMPLETED("4", "图文问诊已完成"),
    IMAGE_TEXT_REFUSAL_TO_REFUND("5", "图文问诊拒绝退款"),
    VIDEO_CANCELED("6", "视频问诊已取消"),
    VIDEO_BOOKED("7", "视频问诊已预约"),
    VIDEO_NUMBER_REFUNDED("8", "视频问诊已退号"),
    VIDEO_NUMBER_PASSED("9", "视频问诊已过号"),
    VIDEO_REFUNDED("10", "视频问诊已退款"),
    VIDEO_COMPLETED("11", "视频问诊已完成"),
    VIDEO_VISITING("111", "视频问诊中"),
    VIDEO_REFUSAL_TO_REFUND("12", "视频问诊拒绝退款"),
    SERVICE_PACK_ORDER_CANCELED("13", "服务包订单已取消"),
    SERVICE_PACK_ORDER_REFUNDED("14", "服务包订单已退款"),
//    DRUGS_ORDER_CANCELED("15", "药品订单已取消"),
//    DRUGS_ORDER_TO_BE_PICKED("16", "药品订单待自提"),
//    DRUGS_ORDER_TO_BE_RECEIVED("17", "药品订单待收货"),
//    DRUGS_ORDER_REFUNDED("18", "药品订单已退款"),
//    DRUGS_ORDER_COMPLETED("19", "药品订单已完成"),

//    DRUGS_ORDER("20", "药品订单"),
    //DRUGS_ORDER_REFUSAL_TO_REFUND("201", "拒绝退款"),
    //DRUGS_ORDER_AGREE_TO_RETURN_AND_REFUND("202", "药品订单同意退货退款"),
//    DRUGS_ORDER_REFUSE_RETURN_AND_REFUND("203", "药品订单拒绝退货退款"),
    //DRUGS_ORDER_AGREE_TO_RETURN_OR_EXCHANGE_GOODS("204", "药品订单同意退换货"),
//    DRUGS_ORDER_REFUSE_RETURNS_AND_EXCHANGES("205", "药品订单拒绝退换货"),
    //DRUGS_ORDER_AGREE_TO_EXCHANGE_GOODS("206", "药品订单同意换货"),
    //DRUGS_ORDER_REJECTION_OF_GOODS("207", "药品订单拒绝收货"),
//    DRUGS_ORDER_SHIPPED("208", "药品订单已发货"),
//    DRUGS_ORDER_AFTER_SALES_SHUTDOWN("21", "药品订单售后关闭"),
//    DRUGS_ORDER_AFTER_SALES_END("22", "药品订单售后结束"),

//    SHOP_ORDER_CANCELED("23", "商品订单已取消"),
//    SHOP_ORDER_TO_BE_PICKED("24", "商品订单待自提"),
//    SHOP_ORDER_TO_BE_RECEIVED("25", "商品订单待收货"),
//    SHOP_ORDER_REFUNDED("26", "商品订单已退款"),
//    SHOP_ORDER_COMPLETED("27", "商品订单已完成"),

//    SHOP_ORDER("28", "商品订单"),
    //SHOP_ORDER_REFUSAL_TO_REFUND("32", "商品订单拒绝退款"),
    //SHOP_ORDER_AGREE_TO_RETURN_AND_REFUND("33", "商品订单同意退货退款"),
    //SHOP_ORDER_REFUSE_RETURN_AND_REFUND("34", "商品订单拒绝退货退款"),
    //SHOP_ORDER_AGREE_TO_RETURN_OR_EXCHANGE_GOODS("35", "商品订单同意退换货"),
    //SHOP_ORDER_REFUSE_RETURNS_AND_EXCHANGES("36", "商品订单拒绝退换货"),
    //SHOP_ORDER_AGREE_TO_EXCHANGE_GOODS("37", "商品订单同意换货"),
    //SHOP_ORDER_REJECTION_OF_GOODS("38", "商品订单拒绝收货"),
//    SHOP_ORDER_AFTER_SALES_SHUTDOWN("29", "商品订单售后关闭"),
//    SHOP_ORDER_AFTER_SALES_END("30", "商品订单售后结束"),

    DOCTOR_NO_RESPONSE_RECEIVED("31", "医生正在忙，稍后回复您，请耐心等待"),
    DOCTOR_UNREVIEWED("32", "您的订单正在排号，请耐心等待医生接诊"),
    PATIENT_NO_RESPONSE_RECEIVED("33", "医生已回复，点击查看消息"),
    PATIENT_NOT_UPLOADED_MEDICAL_RECORD("34", "请填写病历信息，以便医生更全面评估"),
    SYSTEM_NOTIFICATIONS("35", "您预约的问诊还有15分钟开始，请做好准备"),
    DEPARTMENT_ORDER_NOTIFICATIONS("36", "科室医生正在忙，请耐心等待，感谢谅解"),
    SERVICE_ORDER_NOTIFICATIONS("37", "服务包医生发来新消息，请及时查看回复"),
    TUTORIAL_NOTIFICATIONS("38", "您的指标异常，点击查看科普"),
    TUTORIAL_UNKNOWN_EXCEPTION("38", "患教指标未知异常，请及时查看")
    ;




    private final String code;
    private final String info;

    TemplateEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
