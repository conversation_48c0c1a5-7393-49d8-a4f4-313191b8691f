package com.puree.hospital.common.core.enums;

import lombok.Getter;

/**
 * <p>
 * 挂号类型
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/14 15:10
 */
@Getter
public enum RegisterTypeEnum {

    /**
     * 互联网复诊
     */
    VISIT(1, "互联网复诊"),

    /**
     * 互联网咨询
     */
    CONSULT(2, "互联网咨询");

    private final Integer code;

    private final String info;

    RegisterTypeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static boolean isVisit(Integer code) {
        return VISIT.getCode().equals(code);
    }

}
