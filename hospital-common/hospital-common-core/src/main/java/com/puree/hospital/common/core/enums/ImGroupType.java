package com.puree.hospital.common.core.enums;

import lombok.Getter;

/**
 * im群组类型
 * <AUTHOR>
 */
@Getter
public enum ImGroupType {

    /**
     * 0:问诊
     */
    INQUIRIES("0","问诊"),
    /**
     * 1:服务包
     */
    SERVICE_PACK("1","服务包"),
    /**
     * 2:医助
     */
    ASSISTANT("2","医助");

    private final String code;
    private final String info;

    ImGroupType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 群组类型是否问诊
     *
     * @param code 区组类型
     * @return 是否问诊
     */
    public static boolean isInquiries(String code) {
        return INQUIRIES.getCode().equals(code);
    }

    /**
     * 群组类型是否服务包
     *
     * @param code 区组类型
     * @return 是否服务包
     */
    public static boolean isServicePack(String code) {
        return SERVICE_PACK.getCode().equals(code);
    }
}
