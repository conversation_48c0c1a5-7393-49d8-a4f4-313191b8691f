package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * @ClassName: GluTypeEnum
 * @Date 2022/12/1 15:10
 * <AUTHOR>
 * @Description: 血糖状态
 * @Version 1.0
 */
public enum GluTypeEnum {
    KF("0","空腹"),
    FH("1","餐后"),
    SJ("2","随机");

    private final String code;
    private final String info;

    GluTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static GluTypeEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (GluTypeEnum enums : GluTypeEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
