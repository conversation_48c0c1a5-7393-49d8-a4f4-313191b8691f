package com.puree.hospital.common.core.enums;

/**
 * <AUTHOR>
 * @date 2023/3/24 17:52
 */
public enum EntOrderStatusEnum {
    WAIT_DELIVER("0", "待发货"),
    WAIT_RECEIVE("1", "待收货"),
    FINISH("2", "已完成"),
    CANCEL("3", "已取消");
    private String code;
    private String info;

    EntOrderStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
