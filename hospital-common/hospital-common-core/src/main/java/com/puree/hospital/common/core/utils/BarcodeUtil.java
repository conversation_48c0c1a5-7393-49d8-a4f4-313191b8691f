package com.puree.hospital.common.core.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.imageio.ImageIO;
import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;

/**
 * 条码生成器
 *
 * <AUTHOR>
 * @date 2025/5/20 17:05:45
 */
@Slf4j
public class BarcodeUtil {

    public static String generateBarcode(String contents) {
        try {
            // 生成条形码
            BitMatrix bitMatrix = new MultiFormatWriter().encode(contents, BarcodeFormat.CODE_128, 500, 100, null);
            MatrixToImageConfig config = new MatrixToImageConfig(0xFF000000, 0x00FFFFFF);
            BufferedImage barcodeImage = MatrixToImageWriter.toBufferedImage(bitMatrix, config);

            // 创建透明背景的图片
            int width = barcodeImage.getWidth();
            int height = barcodeImage.getHeight() + 50;
            BufferedImage combinedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g = combinedImage.createGraphics();

            // 设置透明背景
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.CLEAR, 0.0f));
            g.fillRect(0, 0, width, height);
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1.0f));

            // 绘制条形码
            g.drawImage(barcodeImage, 0, 0, null);

            // 绘制文本
            g.setColor(Color.BLACK);
            g.setFont(new Font("Arial", Font.BOLD, 32));
            FontMetrics fm = g.getFontMetrics();
            int textWidth = fm.stringWidth(contents);
            g.drawString(contents, (width - textWidth) / 2, height - 10);

            g.dispose();
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            ImageIO.write(combinedImage, "png", outStream);
            return Base64.encodeBase64String(outStream.toByteArray());
        } catch (Exception e) {
            log.info("条形码生成失败，报错:", e);
            throw new ServiceException("条形码生成失败,请稍后重试！");
        }
    }

}
