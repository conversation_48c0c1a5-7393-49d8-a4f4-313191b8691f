package com.puree.hospital.common.core.enums;
/**
 *
 *  数据来源：0.运营平台 1.医院后台
 * <AUTHOR>
 * @Data 2024年2月20日11:54:53
 */

public enum AdminSourceEnum {
    PLATFORM(0, "运营平台"),
    HOSPITAL(1, "医院后台");

    private final Integer code;
    private final String info;

    AdminSourceEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
