package com.puree.hospital.common.core.enums;

/**
 * 个推推送类型
 * <AUTHOR>
 * @date 2023/3/28 18:10
 */
public enum DoctorPushTypeEnum {
    NO_PUSH (0,"所有消息都不推送"),
    ONLY_REL_ME(1,"仅@我的消息推送"),
    ALL_PUSH(2,"全部消息推送"),
    TEAM_MSG_NO_PUSH(3,"医助团队消息不推送");
    private final Integer code;
    private final String info;

    DoctorPushTypeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
