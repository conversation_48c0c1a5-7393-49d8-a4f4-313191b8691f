package com.puree.hospital.common.core.mybatis;


import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.puree.hospital.common.core.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

/**
 * @ClassName MyMetaObjectHandler
 * <AUTHOR>
 * @Description 使用 myBatisPlus 插入的时候自动填充
 * @Date 2023/10/20 16:40
 * @Version 1.0
 */
@Component
@Slf4j
public class MyMetaObjectHandler implements MetaObjectHandler {

    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "updateTime";


    /**
     * @Param metaObject
     * @Return void
     * @Description 插入填充
     * <AUTHOR>
     * @Date 2023/10/20 16:50
     **/
    @Override
    public void insertFill(MetaObject metaObject) {
        //写入创建时间
        if (metaObject.hasGetter(CREATE_TIME)) {
            Object fieldValByName = getFieldValByName(CREATE_TIME, metaObject);
            //为空则填充，不为空则不修改业务写入的值
            if (fieldValByName == null) {
                log.info("写入创建时间.............");
                setFieldValByName(CREATE_TIME, DateUtils.getNowDate(), metaObject);
            }
        }
    }

    /**
     * @Param metaObject
     * @Return void
     * @Description 修改填充
     * <AUTHOR>
     * @Date 2023/10/20 16:51
     **/
    @Override
    public void updateFill(MetaObject metaObject) {
        //写入修改时间
        if (metaObject.hasGetter(UPDATE_TIME)) {
            Object fieldValByName = getFieldValByName(UPDATE_TIME, metaObject);
            //为空则填充，不为空则不修改业务写入的值
            if (fieldValByName == null) {
                log.info("写入修改时间.............");
                setFieldValByName(UPDATE_TIME, DateUtils.getNowDate(), metaObject);
            }
        }
    }
}