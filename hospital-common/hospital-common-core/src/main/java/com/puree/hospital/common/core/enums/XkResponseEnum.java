package com.puree.hospital.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 携康返回编码枚举
 * <AUTHOR>
 * @date 2022/11/1 16:50
 */
@Getter
@AllArgsConstructor
public enum XkResponseEnum {
    SUCCESS(200,"推送成功"),
    NETWORK_ERROR(400,"网络故障"),
    PUSH_FAIL(500,"推送失败"),
    SIGNATURE_ERROR(105,"签名错误"),
    OTHER_ERROR(999,"其他错误");

    private final Integer code;
    private final String msg;
}
