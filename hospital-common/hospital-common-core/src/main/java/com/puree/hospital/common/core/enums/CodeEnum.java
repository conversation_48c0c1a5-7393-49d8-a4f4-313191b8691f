package com.puree.hospital.common.core.enums;

/**
 *
 *  是否
 * <AUTHOR>
 */
public enum CodeEnum {
    STATUS("0", "状态"),
    OPEN("1", "是否开放问诊"),
    DELETE("2","删除"),
    NO("0","否"),
    YES("1","是"),
    REFUSE("1","拒绝"),
    AGREE("2","同意");


    private final String code;
    private final String info;

    CodeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
