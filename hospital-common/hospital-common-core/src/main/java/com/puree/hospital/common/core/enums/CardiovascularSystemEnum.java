package com.puree.hospital.common.core.enums;

/**
 * 心血管
 */
public enum CardiovascularSystemEnum {

    OPTION1("1", "<160"),
    OPTION2("2", "160-199"),
    OPTION3("3", "200-239"),
    OPTION4("4","240-279"),
    OPTION5("5","≥280");



    private final String  code;
    private final String info;

    CardiovascularSystemEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (CardiovascularSystemEnum enums : CardiovascularSystemEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }
}
