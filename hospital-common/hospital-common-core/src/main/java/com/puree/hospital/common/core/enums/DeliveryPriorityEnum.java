package com.puree.hospital.common.core.enums;

public enum DeliveryPriorityEnum {

    OFFICINA("0","药房发货"),
    ENTERPRISE("1","配送企业发货");

    private final String code;
    private final String info;

    DeliveryPriorityEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}
