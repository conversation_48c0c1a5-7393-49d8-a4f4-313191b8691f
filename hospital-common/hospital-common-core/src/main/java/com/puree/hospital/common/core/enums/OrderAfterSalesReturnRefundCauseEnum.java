package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * @ClassName: OrderAfterSalesReturnRefundCauseEnum
 * @Date 2023/3/6 19:16
 * <AUTHOR>
 * @Description: 退货退款售后
 * @Version 1.0
 */
public enum OrderAfterSalesReturnRefundCauseEnum {
    INCONSISTENCY("0", "退回商品与售后商品不一致"),
    INCOMPLETE("1", "退回商品不完整"),
    EMPTY_PARCEL("2", "空包裹"),
    NO_REFUND_RETURNED("3", "已协商完毕不退货退款"),
    OTHER("4", "其他");
    private final String code;
    private final String info;

    OrderAfterSalesReturnRefundCauseEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static OrderAfterSalesReturnRefundCauseEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (OrderAfterSalesReturnRefundCauseEnum enums : OrderAfterSalesReturnRefundCauseEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
