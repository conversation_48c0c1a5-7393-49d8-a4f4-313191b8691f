package com.puree.hospital.common.core.enums;

/**
 * <AUTHOR>
 * @date 2023/2/6 12:03
 */
public enum StockEnum {
    // 0扣减库存 1释放库存
    REDUCE("0", "reduce"),
    INCREASE("1", "increase");

    private final String value;
    private final String name;

    StockEnum(String value, String name)
    {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }
    public String getName() {
        return name;
    }
}
