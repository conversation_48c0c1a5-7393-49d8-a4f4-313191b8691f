package com.puree.hospital.common.core.enums;

import lombok.Getter;

/**
 * <p>
 * 药品溯源码状态
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/19 17:54
 */
@Getter
public enum DrugTraceCodeStatusEnum {

    /**
     * 待上传
     */
    PENDING_UPLOAD(0,"待上传"),

    /**
     * 已上传
     */
    UPLOADED(1,"已上传"),

    /**
     * 上传失败
     */
    UPLOAD_FAILED(2,"上传失败"),

    /**
     * 待退货
     */
    PENDING_RETURN(3,"待退货"),

    /**
     * 退货中
     */
    RETURNED(4,"退货中"),

    /**
     * 退货失败
     */
    RETURN_FAILED(5, "退货失败"),

    /**
     * 部分上传-返回前端部分，不与数据库做关联
     */
    PARTIALLY_UPLOADED(10000, "部分上传");

    /**
     * 状态
     */
    private final Integer status;

    /**
     * 描述
     */
    private final String desc;

    DrugTraceCodeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
