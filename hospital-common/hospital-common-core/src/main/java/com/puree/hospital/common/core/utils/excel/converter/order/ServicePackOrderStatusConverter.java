package com.puree.hospital.common.core.utils.excel.converter.order;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.puree.hospital.common.core.enums.ServicePackOrderStatusEnum;

public class ServicePackOrderStatusConverter implements Converter<String> {

    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration
            globalConfiguration) {
        if (value.equals(ServicePackOrderStatusEnum.TO_BE_PAID.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.TO_BE_PAID.getInfo());
        }
        if (value.equals(ServicePackOrderStatusEnum.CANCELLED.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.CANCELLED.getInfo());
        }
        if (value.equals(ServicePackOrderStatusEnum.TO_BE_ACTIVATED.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.TO_BE_ACTIVATED.getInfo());
        }
        if (value.equals(ServicePackOrderStatusEnum.ACTIVATED.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.ACTIVATED.getInfo());
        }
        if (value.equals(ServicePackOrderStatusEnum.ENDED.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.ENDED.getInfo());
        }
        if (value.equals(ServicePackOrderStatusEnum.AFTER_SALES.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.AFTER_SALES.getInfo());
        }
        if (value.equals(ServicePackOrderStatusEnum.REFUNDED.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.REFUNDED.getInfo());
        }
        if (value.equals(ServicePackOrderStatusEnum.REFUNDING.getCode())) {
            return new WriteCellData(ServicePackOrderStatusEnum.REFUNDING.getInfo());
        }
        return null;
    }
}
