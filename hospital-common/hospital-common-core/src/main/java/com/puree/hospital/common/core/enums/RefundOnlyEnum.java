package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/3/8 15:16
 */
public enum RefundOnlyEnum {
    // 仅退款
    zero("0", "拍多/拍错/不想要"),
    one("1", "未按期发货"),
    two("2", "快递一直未送达"),
    three("3", "协商一致退款"),
    four("4", "其他");

    private final String code;
    private final String info;

    RefundOnlyEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static RefundOnlyEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (RefundOnlyEnum enums : RefundOnlyEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
