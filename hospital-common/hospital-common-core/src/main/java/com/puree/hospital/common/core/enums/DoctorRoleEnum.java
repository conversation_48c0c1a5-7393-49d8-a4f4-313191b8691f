package com.puree.hospital.common.core.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/9/20 17:55
 * @desc 医生角色 0:医生 1:药师 2:护士 4:健康管理师  5:心理咨询师 9康复师 10营养师
 */
@Getter
public enum DoctorRoleEnum {

    /**
     * 医生
     */
    DOCTOR("0", "doctor", "医生"),
    /**
     *药师
     */
    PHARMACIST("1", "pharmacist", "药师"),
    /**
     *护士
     */
    NURSE("2", "nurse", "护士"),
    /**
     *健康管理师
     */
    HEALTH_MANAGER("4", "healthManager", "健康管理师"),
    /**
     *心理咨询师
     */
    PSYCHOLOGIST("5", "psychologist", "心理咨询师"),
    /**
     *康复师
     */
    THERAPIST("9", "therapist", "康复师"),
    /**
     *营养师
     */
    NUTRITIONIST("10", "nutritionist", "营养师");

    private final String code;

    private final String name;

    private final String desc;


    DoctorRoleEnum(String code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static DoctorRoleEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        DoctorRoleEnum[] metaArr = DoctorRoleEnum.values();
        for (DoctorRoleEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static DoctorRoleEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        DoctorRoleEnum[] metaArr = DoctorRoleEnum.values();
        for (DoctorRoleEnum type : metaArr) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 是否是医生
     *
     * @param code code
     * @return boolean
     */
    public static boolean isDoctor(String code) {
        return DOCTOR.getCode().equals(code);
    }

}
