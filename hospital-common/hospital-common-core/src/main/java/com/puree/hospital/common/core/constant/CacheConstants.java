package com.puree.hospital.common.core.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";
    /**
     * 药品分类key
     */
    public final static String DRUGS_CLASSIFY_KEY = "drugs_classify:";
    /**
     * 短信验证码前缀
     */
    public final static String SMS_VERIFY_KEY = "sms_verify:";
    /**
     * 短信发送限制
     */
    public final static String SMS_SEND_LIMIT_KEY = "sms_send_limit:";
    /**
     * 短信验证码错误限制
     */
    public final static String SMS_CODE_ERROR_LIMIT_KEY = "sms_code_error_limit:";
    /**
     * 唯一请求ID
     */
    public final static String UNIQUE_ID = "unique_id:";
    /**
     * 医院报告扫码授权
     */
    public final static String QRCPDE = "qrCode:";
    /**
     * 医院后台登录失败锁定账号
     */
    public final static String LOGIN_LOCK = "login_lock:";
    /**
     * ocr错误限制 redis key
     */
    public static final String OCR_LIMIT_KEY = "ocr_limit:";
    /**
     * 锁
     */
    public static final String LOCK_EHR = "lock:ehr:";
    public static final String VIDEO_ORDER_RESCHEDULE = "videoOrder:reschedule:";
    public static final String LOCK_FOLLOW_UP = "lock:follow_up:";


    /**
     * gateway 防止重复请求 key
     */
    public final static String GATEWAY_REQUESTID = "gateway:requestId:";

    public static final String REFUND_CALLBACK = "pay:refund_callback:";

    public static final String CHANNEL_PARTNER_KEY = "channel_partner:%s:%s:%s";

    /**
     * 过期时间30天
     */
    public static final long LOGIN_TOKEN_EXPIRE_TIME = 30 * 24 * 60 * 60;
}
