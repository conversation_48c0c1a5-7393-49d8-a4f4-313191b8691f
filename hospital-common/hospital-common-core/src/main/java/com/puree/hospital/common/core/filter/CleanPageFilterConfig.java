package com.puree.hospital.common.core.filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

/**
 * <AUTHOR>
 * @Description:
 * @date 2024/7/11 17:59
 * @Version 1.0
 */
@Configuration
public class CleanPageFilterConfig {
    @Bean
    public FilterRegistrationBean<Filter> cleanPageFilter() {
        FilterRegistrationBean<Filter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(new CleanPageFilter());
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setName("cleanPageFilter");
        return filterRegistrationBean;
    }
}
