package com.puree.hospital.common.core.calculator.price.drug;

import com.puree.hospital.common.core.calculator.price.IPrice;

/**
 * <p>
 * 药品价格
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/15 16:14
 */
public interface IDrugPrice extends IPrice {

    /**
     * 获取重量
     * @return 重量
     */
    String getWeight();

    /**
     * 中药剂数
     *
     * @return 中药剂数
     */
    Integer getDoses();

    /**
     * 设置中药剂数
     *
     * @param doses 设置计数
     */
    void setDoses(Integer doses);
}
