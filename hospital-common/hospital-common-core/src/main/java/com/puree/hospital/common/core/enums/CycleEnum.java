package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

public enum CycleEnum {
    ONE_DAY(1, "一天"),
    ONE_WEEK(1,"一周"),
    ONE_MONTH(1,"一月"),
    ONE_SEASON(3,"一季"),
    HALF_YEAR(6,"半年"),
    ONE_YEAR(12,"一年");
    private final Integer code;
    private final String info;

    CycleEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过code取枚举
     * @param code
     * @return
     */
    public static CycleEnum getTypeByCode(String code){
        if (StringUtils.isNull(code)){
            return null;
        }
        for (CycleEnum enums : CycleEnum.values()) {
            if (enums.getCode().equals(code) ) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code取枚举
     * @param value
     * @return
     */
    public static CycleEnum getTypeByInfo(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (CycleEnum enums : CycleEnum.values()) {
            if (enums.getInfo().equals(value) ) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 枚举转换为天数
     * @param cycleEnum
     * @return
     */
    public static int getTypeByInfoToDay(CycleEnum cycleEnum){
        if (cycleEnum == null){
            return 0;
        }
        switch (cycleEnum){
            case ONE_DAY:
                return 1;
            case ONE_WEEK:
                //一周七天
                return 7;
            case ONE_MONTH:
                //一个月30天
                return 30;
            case ONE_SEASON:
                //一个季度90天
                return 90;
            case HALF_YEAR:
                //半年180天
                return 180;
            case ONE_YEAR:
                //一年365天
                return 365;
            default:
                return 0;
        }
    }
}
