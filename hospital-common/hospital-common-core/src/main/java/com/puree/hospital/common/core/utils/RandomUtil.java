package com.puree.hospital.common.core.utils;

import java.security.SecureRandom;
import java.util.Random;

public class RandomUtil {

    /**
     * 生成6位随机编码
     *
     * @return
     */
    public static String Generate6Code() {
        byte[] nonce = new byte[6];
        new SecureRandom().nextBytes(nonce);
        return convertBytesToHex(nonce);
    }

    /**
     * 随机生成指定区间数
     *
     * @return
     */
    public static int GenerateNumber(int max, int min) {
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }

    // util to print bytes in hex
    private static String convertBytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte temp : bytes) {
            result.append(String.format("%02x", temp));
        }
        return result.toString();
    }
}
