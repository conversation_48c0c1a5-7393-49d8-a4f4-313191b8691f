package com.puree.hospital.common.core.enums;

/**
 * xiao
 *
 * <AUTHOR>
 */
public enum AuthenticationEnum {
    NOT_AUTHENTICATION(0, "未认证"),
    REAL_NAME_AUTHENTICATION(1, "实名认证"),
    PHYSICIAN_CERTIFICATION(2, "医师认证"),
    CERTIFICATION_SUCCESS(3, "认证成功"),
    CERTIFICATION_FAIL(4, "认证失败");

    private final Integer code;
    private final String info;

    AuthenticationEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
