package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.constant.PrescriptionConstant;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/11
 */
@Getter
public enum PrescriptionTypeEnum {
    /**
     * 中药饮片
     */
    TCM("0", PrescriptionConstant.CM_PIECES),
    /**
     * 西药或中成药
     */
    MM("1", PrescriptionConstant.MM),
    /**
     * 中药方剂
     */
    ZYXDF("2", PrescriptionConstant.CM_RECIPE);

    private final String code;

    private final String constName;

    PrescriptionTypeEnum(String code, String constName) {
        this.code = code;
        this.constName = constName;
    }

    /**
     * 根据处方类型获取枚举
     *
     * @param type 处方类型
     * @return 枚举
     */
    public static PrescriptionTypeEnum getByType(String type) {
        for (PrescriptionTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为中药饮片
     *
     * @param prescriptionType 处方类型
     * @return 是否为中药饮片
     */
    public static boolean isTcm(String prescriptionType) {
        return Objects.equals(TCM.getCode(), prescriptionType);
    }

    /**
     * 判断是否为中药协定方
     *
     * @param prescriptionType 处方类型
     * @return 是否为中药协定方
     */
    public static boolean isZyxdf(String prescriptionType) {
        return Objects.equals(ZYXDF.getCode(), prescriptionType);
    }

    /**
     * 判断是否为西药处方
     * @param prescriptionType 处方类型
     * @return 是否为西药处方
     */
    public static boolean isMm(String prescriptionType) {
        return Objects.equals(MM.getCode(), prescriptionType);
    }

    /**
     * 判断是否为中药饮片或中药协定方
     * @param prescriptionType 处方类型
     * @return 是否为中药饮片或中药协定方
     */
    public static boolean isTcmOrZyxdf(String prescriptionType) {
        return isTcm(prescriptionType) || isZyxdf(prescriptionType);
    }
}
