package com.puree.hospital.common.core.utils.http;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpUtil {

    /**
     * 处理get请求
     *
     * @param url 请求url
     * @return JSONObject
     */
    public static JSONObject doGet(String url) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        JSONObject jsonObject = null;
        try {
            CloseableHttpResponse response = httpclient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity);
                jsonObject = JSONObject.parseObject(result);
            }
        } catch (IOException e) {
            log.error("doGet异常：", e);
        }
        return jsonObject;

    }

    /**
     * 处理post请求
     *
     * @param url     请求url
     * @param outStr  数据
     * @return JSONObject
     */
    public static JSONObject doPost(String url, String outStr) {
        return doPost(url, outStr, null);
    }
    /**
     * 处理post请求
     *
     * @param url     请求url
     * @param outStr  数据
     * @param headers 请求头信息
     * @return JSONObject
     */
    public static JSONObject doPost(String url, String outStr, Header[] headers) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        JSONObject jsonObject = null;
        try {
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            if (headers != null && headers.length > 0) {
                httpPost.setHeaders(headers);
            }
            httpPost.setEntity(new StringEntity(outStr, StandardCharsets.UTF_8));
            CloseableHttpResponse response = httpclient.execute(httpPost);
            String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            jsonObject = JSONObject.parseObject(result);
        } catch (IOException e) {
            log.error("doPostStr异常：", e);
        }
        return jsonObject;
    }

    public static JSONObject sendPost(String url, String param) {
        StringBuilder result = new StringBuilder();
        try {
            URL httpurl = new URL(url);
            HttpURLConnection httpConn = (HttpURLConnection) httpurl.openConnection();
            httpConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            httpConn.setDoOutput(true);
            httpConn.setDoInput(true);
            PrintWriter out = new PrintWriter(new OutputStreamWriter(httpConn.getOutputStream(), StandardCharsets.UTF_8));
            out.print(param);
            out.flush();
            out.close();
            BufferedReader in = new BufferedReader(new InputStreamReader(httpConn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
        } catch (Exception e) {
            log.error("请求失败:{}", e.getMessage(), e);
        }
        return JSONObject.parseObject(result.toString());
    }

    public static byte[] getWxaCreateQrcode(String uri,Map<String, Object> paraMap){
        HttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(uri);
        byte[] result = null;
        try {
            // 设置请求的参数
            JSONObject postData = new JSONObject();
            postData.putAll(paraMap);
            StringEntity s = new StringEntity(postData.toString(),"UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            // 设置发送的数据(数据尽量为json格式)
            post.setEntity(s);
            post.addHeader("content-type", "application/json;charset=UTF-8");
            //  获取数据
            HttpResponse res = client.execute(post);
            //  判断网络请求的是否成功，成功的状态码为200
            if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                //  HttpEntity为消息实体，内容是http传送的报文,获取数据
                HttpEntity entity = res.getEntity();
                result = EntityUtils.toByteArray(entity);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            // 释放连接
            post.releaseConnection();
        }
        return result;
    }
}
