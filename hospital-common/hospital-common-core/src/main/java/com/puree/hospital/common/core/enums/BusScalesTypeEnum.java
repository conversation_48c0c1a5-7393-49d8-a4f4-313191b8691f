package com.puree.hospital.common.core.enums;

public enum BusScalesTypeEnum {

    AGE("age", "年龄"),
    CHOLESTEROL("cholesterol", "总胆固醇"),
    HDL_C("HDL-C", "hdlc"),
    SYSTOLIC("systolic", "收缩压"),
    SMOKING("smoking", "吸烟"),
    IS_TREATMENT("isTreatment", "是否治疗");


    private final String  code;
    private final String info;

    BusScalesTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (BusScalesTypeEnum enums : BusScalesTypeEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }
}
