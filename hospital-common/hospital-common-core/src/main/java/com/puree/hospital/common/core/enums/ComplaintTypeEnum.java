package com.puree.hospital.common.core.enums;

/**
 * <p>
 * 投诉举报类型
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/21 16:10
 */
public enum ComplaintTypeEnum {

    /**
     * 投诉举报医院
     */
    COMPLAINT_HOSPITAL(1, "投诉举报医院"),

    /**
     * 投诉举报个人
     */
    COMPLAINT_PERSON(2, "投诉举报个人"),;

    private final Integer type;
    private final String desc;

    ComplaintTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static ComplaintTypeEnum getByType(Integer type) {
        for (ComplaintTypeEnum value : ComplaintTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
