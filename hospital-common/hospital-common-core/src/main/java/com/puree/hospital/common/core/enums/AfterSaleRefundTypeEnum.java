package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * @ClassName: AfterSaleRefundTypeEnum
 * @Date 2023/3/7 16:02
 * <AUTHOR>
 * @Description: 售后方式
 * @Version 1.0
 */
public enum AfterSaleRefundTypeEnum {

    REFUND_ONLY("0", "仅退款"),
    REFUND_RETURN("1", "退货退款"),
    REFUNDS_EXCHANGES("2", "退换货");
    private final String code;
    private final String info;

    AfterSaleRefundTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static AfterSaleRefundTypeEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (AfterSaleRefundTypeEnum enums : AfterSaleRefundTypeEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
