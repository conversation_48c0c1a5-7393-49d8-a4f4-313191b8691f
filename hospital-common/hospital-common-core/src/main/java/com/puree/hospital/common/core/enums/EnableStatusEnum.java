package com.puree.hospital.common.core.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * 可用状态枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/16 16:02
 */
@Getter
public enum EnableStatusEnum {

    /**
     * 启用
     */
    ENABLED(1, "启用"),

    /**
     * 禁用
     */
    DISABLED(0, "禁用");

    private final Integer status;

    private final String desc;

    EnableStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static EnableStatusEnum getByStatus(Integer status) {
        for (EnableStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static boolean isEnabled(Integer status) {
        return Objects.equals(ENABLED.getStatus(), status);
    }

    public static boolean isDisabled(Integer status) {
        return Objects.equals(DISABLED.getStatus(), status);
    }
}
