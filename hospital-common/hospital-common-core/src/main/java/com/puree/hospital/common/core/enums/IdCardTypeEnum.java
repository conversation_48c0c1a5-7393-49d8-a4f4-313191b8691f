package com.puree.hospital.common.core.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: IdCardTypeEnum
 * @Date 2024/7/31 17:24
 * <AUTHOR>
 * @Description: 身份证件类型  id_card.居民身份证  residence.居民户口簿 passport.护照  officer.军官证  driver_license.驾驶证 hk_id.港澳居民来往内地通行证 tw_id.台湾居民来往内地通行证 other.其他
 * @Version 1.0
 */
public enum IdCardTypeEnum {

    ID_CARD("01", "居民身份证"),
    BIRTH_CERTIFICATE("08", "出生证"),;

    //编码
    private String code;
    //描述
    private String desc;


    IdCardTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static IdCardTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        IdCardTypeEnum[] metaArr = IdCardTypeEnum.values();
        for (IdCardTypeEnum type : metaArr) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static Boolean isIdCard(String code) {
        return ID_CARD.getCode().equals(code);
    }

    public static boolean isBirthCertificate(String code) {
        return BIRTH_CERTIFICATE.getCode().equals(code);
    }


}
