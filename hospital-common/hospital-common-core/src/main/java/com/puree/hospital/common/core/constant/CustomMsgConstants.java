package com.puree.hospital.common.core.constant;

/**
 * 自定义消息模板名称
 * <AUTHOR>
 */
public class CustomMsgConstants {

    /**
     * 群组创建成功
     */
    @SuppressWarnings("unused")
    public static final String CREATED_SUCCESS = "CREATED_SUCCESS";

    /**
     * 患者图文问诊信息消息
     */
    public static final String PATIENT_QUICKCONSULTATION = "PATIENT_QUICKCONSULTATION";

    /**
     * 处方消息
     */
    public static final String PRESCRIPTION = "PRESCRIPTION";

    /**
     * 视频聊天消息消息
     */
    public static final String VIDEOCHAT_FILES = "VIDEOCHAT_FILES";

    /**
     * 服务包人员加入
     */
    public static final String SERVIEC_PACK_PEOPLE_JOIN = "SERVIEC_PACK_PEOPLE_JOIN";

    /**
     * 服务包激活
     */
    public static final String SERVIEC_PACK_ACTIVATE = "SERVIEC_PACK_ACTIVATE";

    /**
     * 服务包过期
     */
    public static final String SERVIEC_PACK_EXPIRE = "SERVIEC_PACK_EXPIRE";

    /**
     * 服务包退款
     */
    public static final String SERVIEC_PACK_REFUND = "SERVIEC_PACK_REFUND";

    /**
     * 服务包系统消息
     */
    @SuppressWarnings("unused")
    public static final String SERVIEC_PACK_SYSTEM_INFO = "SERVIEC_PACK_SYSTEM_INFO";

    /**
     * 提醒完善诊疗档案消息
     */
    public static final String REMIND_DIAGNOSIS_ARCHIVES = "REMIND_DIAGNOSIS_ARCHIVES";

    /**
     * 诊疗档案信息消息
     */
    public static final String DIAGNOSIS_ARCHIVES = "DIAGNOSIS_ARCHIVES";

    /**
     * 转发患教资料消息
     */
    public static final String FORWARD_TUTORIAL = "FORWARD_TUTORIAL";

    /**
     * 医生公告
     */
    public static final String DOCTOR_NOTICE = "DOCTOR_NOTICE";

    /**
     *视频倒计时
     */
    public static final String VIDEOCHAT_COUNTDOWN = "VIDEOCHAT_COUNTDOWN";

    /**
     * 结束问诊
     */
    public static final String CONSULTATION_END = "CONSULTATION_END";
    /**
     *
     */
    public static final String JOIN_SCAN_ROOM = "JOIN_SCAN_ROOM";

    /**
     * 药品清单消息
     */
    @SuppressWarnings("unused")
    public static final String DRUG_LIST = "DRUG_LIST";

    /**
     * 设置vip
     */
    public static final String SETTINGVIP = "SETTING_VIP";

    /**
     * 打开会话
     */
    public static final String OPEN_SESSION = "OPEN_SESSION";

    /**
     * 关闭会话
     */
    public static final String CLOSE_SESSION = "CLOSE_SESSION";

    /**
     * 处方未审核
     */
    public static final String PRESCRIPTION_UNAPPROVED = "PRESCRIPTION_UNAPPROVED";

    /**
     * 赠送问诊包
     */
    public static final String GIVE_CONSULTATION_BAG = "GIVE_CONSULTATION_BAG";
    /**
     * 问诊包复诊
     */
    public static final String FURTHER_CONSULTATION = "FURTHER_CONSULTATION";

    /**
     * 推荐医生
     */
    public static final String RECOMMENDED_DOCTOR = "RECOMMENDED_DOCTOR";
    /**
     * 推荐商品
     */
    public static final String RECOMMENDED_GOODS = "RECOMMENDED_GOODS";
    /**
     * 通知患者完善个人信息
     */
    public static final String IMPROVE_PERSONAL_INFORMATION = "IMPROVE_PERSONAL_INFORMATION";
    /**
     * 通知医生患者已完善个人信息
     */
    public static final String IMPROVED_PERSONAL_INFORMATION = "IMPROVED_PERSONAL_INFORMATION";
    /**
     * im发送量表
     */
    public static final String SEND_SCALES = "SEND_SCALES";
    /**
     * 处方已支付
     */
    public static final String PRESCRIPTION_PAID = "PRESCRIPTION_PAID";
    /**
     * 商品已支付
     */
    public static final String GOODS_PAID = "GOODS_PAID";

    /**
     * 双通道授权码卡片
     */
    public static final String DUAL_CHANNEL_AUTH_CARD = "DUAL_CHANNEL_AUTH_CARD";

    /**
     * 双通道医保授权成功
     */
    public static final String DUAL_CHANNEL_AUTHORIZED = "DUAL_CHANNEL_AUTHORIZED";

    /**
     * 问诊继续
     */
    public static final String CONSULTATION_GO_ON = "CONSULTATION_GO_ON";
}
