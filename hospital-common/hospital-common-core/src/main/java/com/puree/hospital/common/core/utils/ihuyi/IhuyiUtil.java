package com.puree.hospital.common.core.utils.ihuyi;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
@Deprecated
public class IhuyiUtil {
    public final Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private IhuyiProperties ihuyiProperties;
    private static final String Url = "http://106.ihuyi.cn/webservice/sms.php?method=Submit";

    public boolean sendSMS(String mobile,String mobile_code){
        String content = new String("您的验证码是：" + mobile_code + "。请不要把验证码泄露给其他人。");
        Map<String, Object> params = this.paramsSplicing(mobile, content,null);
        try {
            String result = HttpUtil.httpPostRequest(Url, params);
            Document doc = DocumentHelper.parseText(result);
            Element root = doc.getRootElement();
            String code = root.elementText("code");
            String msg = root.elementText("msg");
            String smsid = root.elementText("smsid");
            logger.info("互亿无线短信mobile={},content={},code={},msg={},smsid={}",mobile, content, code, msg, smsid);
            if("2".equals(code)){
                return true;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean sendNotice(String mobile,String content,String sign){
        Map<String, Object> params = this.paramsSplicing(mobile, content,sign);
        try {
            String result = HttpUtil.httpPostRequest(Url, params);
            Document doc = DocumentHelper.parseText(result);
            Element root = doc.getRootElement();
            String code = root.elementText("code");
            String msg = root.elementText("msg");
            String smsid = root.elementText("smsid");
            logger.info("互亿无线短信mobile={},content={},code={},msg={},smsid={}",mobile, content, code, msg, smsid);
            if("2".equals(code)){
                return true;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private Map<String, Object> paramsSplicing(String mobile,String content,String sign){
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("account", ihuyiProperties.getAccount());
        params.put("password", ihuyiProperties.getPassword());
        params.put("sign", sign);
        params.put("mobile", mobile);
        params.put("content", content);
        return params;
    }

}
