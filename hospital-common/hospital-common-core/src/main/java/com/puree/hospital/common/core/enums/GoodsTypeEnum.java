package com.puree.hospital.common.core.enums;

/**
 * 商品类型枚举
 *
 * <AUTHOR>
 * @date 2023/8/3 16:09
 */
public enum GoodsTypeEnum {
    MATERIAL("0","实物"),
    NURSE("1","护理");
    private String code;
    private String info;

    GoodsTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
