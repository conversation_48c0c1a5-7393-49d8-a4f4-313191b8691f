package com.puree.hospital.common.core.enums;

/**
 * 商品订单枚举
 */
public enum GoodsOrderEnum {
    // 订单状态；0待支付 1待发货 2待自提 3待收货 4已取消 5售后中 6售后完成 7退款中 8已退款 9已完成 10部分发货
    PENDING_PAYMENT("0", "待支付"),
    TO_BE_SHIPPED("1", "待发货"),
    TOBEPICKEDUP("2","待自提"),
    GOODS_TO_BE_RECEIVED("3", "待收货"),
    CANCELLED("4", "已取消"),
    INVALID("5", "已失效"),
    AFTERSALES("6", "售后中"),
    REFUNDED("7", "已退款"),
    COMPLETED("8", "已完成"),
    RECEIVED("9","退货/换货完成"),
    AFTERSALES_COMPLETION("10","售后完成"),
    REFUNDED_ZHONG("11","退款中"),
    PART_DELIVERY("12","部分发货");
    private final String code;
    private final String info;

    GoodsOrderEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}
