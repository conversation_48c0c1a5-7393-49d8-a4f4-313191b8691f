package com.puree.hospital.common.core.enums;

public enum SexTypeEnum {
    WOMAN("0", "女"),
    MAN("1", "男");

    private final String code;
    private final String info;

    SexTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    public static SexTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        SexTypeEnum[] metaArr = SexTypeEnum.values();
        for (SexTypeEnum type : metaArr) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
