package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * 五师共管角色枚举
 */
public enum FiveGroupMemberIdentityEnum {
    MENBER("0","成员"),
    ADMIN("1","管理员");


    private final String code;
    private final String info;

    FiveGroupMemberIdentityEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static FiveGroupMemberIdentityEnum getTypeByValue(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (FiveGroupMemberIdentityEnum enums : FiveGroupMemberIdentityEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }



    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (FiveGroupMemberIdentityEnum enums : FiveGroupMemberIdentityEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }


}
