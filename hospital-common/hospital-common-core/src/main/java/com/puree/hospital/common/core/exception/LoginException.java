package com.puree.hospital.common.core.exception;

import java.util.Map;

/**
 * 登录异常
 */
public class LoginException extends RuntimeException{
    private static final long serialVersionUID = 1L;

    private String phoneNumber;
    private String openid;
    public LoginException(String message)
    {
        super(message);
    }
    public LoginException(String message,String openid)
    {
        super(message);
        this.openid = openid;
    }

    public LoginException(String message,String phoneNumber,String openid)
    {
        super(message);
        this.phoneNumber = phoneNumber;
        this.openid = openid;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public String getOpenid() {
        return openid;
    }

    public LoginException(Throwable cause)
    {
        super(cause);
    }

    public LoginException(String message, Throwable cause)
    {
        super(message, cause);
    }

    public LoginException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace)
    {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
