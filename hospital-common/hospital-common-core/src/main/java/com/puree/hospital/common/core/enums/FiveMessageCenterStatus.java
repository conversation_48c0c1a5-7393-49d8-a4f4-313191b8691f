package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * 五师共管角色枚举
 */
public enum FiveMessageCenterStatus {
    JOIN("0","加入通知"),
    REMOVE("1","移除通知"),
    SIGN_OUT("2","退出通知"),
    INVITE("3", "邀请通知"),
    DISSOLUTION("4", "解散通知"),
    IS_ADMIN("5", "设置为管理员"),
    NO_ADMIN("6", "取消管理员"),
    RETURN("7", "拒绝入组");

    private final String code;
    private final String info;

    FiveMessageCenterStatus(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static FiveMessageCenterStatus getTypeByValue(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (FiveMessageCenterStatus enums : FiveMessageCenterStatus.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }



    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (FiveMessageCenterStatus enums : FiveMessageCenterStatus.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }


}
