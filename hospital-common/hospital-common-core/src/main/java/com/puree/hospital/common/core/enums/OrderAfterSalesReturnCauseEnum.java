package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * @ClassName: OrderAfterSalesReturnCauseEnum
 * @Date 2023/3/6 19:11
 * <AUTHOR>
 * @Description: 退货
 * @Version 1.0
 */
public enum OrderAfterSalesReturnCauseEnum {
    INVALID_PROOF("0", "商品没问题，买家未举证/举证无效"),
    NO_REFUND_RETURNED("1", "已协商完毕不退货退款"),
    REFUND_RETURNED("2", "已协商完毕退货退款"),
    OTHER("3", "其他");
    private final String code;
    private final String info;

    OrderAfterSalesReturnCauseEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static OrderAfterSalesReturnCauseEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (OrderAfterSalesReturnCauseEnum enums : OrderAfterSalesReturnCauseEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
