package com.puree.hospital.common.core.enums;

/**
 * 小程序消息模板枚举
 *
 * <AUTHOR>
 * @date 2023/6/12 10:08
 */
public enum AppletMsgTemplateEnum {
    RECEPTION("reception","接诊消息模板","/pages/chatRoom/im?"),
    SCALES("scales","量表消息模板","/pages/chatRoom/im?"),
    IM("im","im消息模板","/pages/chatRoom/im?");

    private final String code;
    private final String info;
    private final String page;

    AppletMsgTemplateEnum(String code, String info, String page) {
        this.code = code;
        this.info = info;
        this.page = page;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public String getPage() {
        return page;
    }
}
