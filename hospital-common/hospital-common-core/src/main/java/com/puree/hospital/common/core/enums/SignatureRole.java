package com.puree.hospital.common.core.enums;

/**
 * 用于保存签名（bus_signature）
 */
public enum SignatureRole {
    DOCTOR("0", "开方医生"),
    PHARMACIST("1", "审方药师"),
    NURSE("2", "护师"),
    ASSISTANT_DOCTOR("3", "医助"),
    HEALTH_MANAGER("4","健康管理师"),
    PSYCHIATRIST("5","心理咨询师"),
    DELIVERY_PHARMACIST("6", "发货药师"),
    CHEMIST("7","调剂师");

    private final String value;
    private final String name;

    SignatureRole(String value, String name)
    {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }
    public String getName() {
        return name;
    }

}
