package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * 跟进状态枚举
 */
public enum FollowUpStatusEnum {
    TO_BE_FOLLOWED_UP("0","待跟进"),
    FOLLOW_UP("1","跟进中"),
    FINISHED("2","已结束");
    private final String code;
    private final String info;

    FollowUpStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static FiveRoleEnum getTypeByValue(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (FiveRoleEnum enums : FiveRoleEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }



    /**
     * 通过value取描述
     * @param value
     * @return
     */
    public static String getInfoByValue(String value) {
        for (FiveRoleEnum enums : FiveRoleEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums.getInfo();
            }
        }
        return "";
    }

}
