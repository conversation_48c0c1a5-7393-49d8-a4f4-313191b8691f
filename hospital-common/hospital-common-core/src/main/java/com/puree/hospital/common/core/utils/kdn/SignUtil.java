package com.puree.hospital.common.core.utils.kdn;

import com.puree.hospital.common.core.utils.sign.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class SignUtil {

	/** Logger实例 */
	private static Logger logger = LoggerFactory.getLogger(SignUtil.class);
	
	public static String sign(String str, String secret) {
		StringBuilder enValue = new StringBuilder();
		enValue.append(secret);
		enValue.append(str);
		enValue.append(secret);
		return encryptByMD5(enValue.toString());
	}

	private static String encryptByMD5(String data) {
		String re_md5 = new String();
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(data.getBytes());
			byte b[] = md.digest();
			int i;
			StringBuffer buf = new StringBuffer();
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			re_md5 = buf.toString();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return re_md5.toUpperCase();
	}
//  没有对应的使用，同时没有对一个gson的jar包
//	public static String compare(String jsonStr,String secret ){
//		logger.info(jsonStr);
//		JsonParser jsonParser = new JsonParser();
//		JsonObject jsonObject = jsonParser.parse(jsonStr).getAsJsonObject();
//		logger.info(jsonObject.toString());
//		logger.info("source compare: " + Objects.equals(jsonStr, jsonObject.toString()));
//		String sign1 = "null";
//		JsonElement signElement = jsonObject.remove("sign");
//		if( signElement != null ){
//			sign1 = signElement.getAsString();
//		}
//		logger.info("sign1: " + sign1);
//		StringBuilder enValue = new StringBuilder();
//		enValue.append(secret);
//		enValue.append(jsonObject.toString());
//		enValue.append(secret);
//		String sign2 = encryptByMD5(enValue.toString());
//		logger.info("sign2: " + sign2);
//		logger.info("sign compare: " + Objects.equals(sign1, sign2));
//		jsonObject.addProperty("sign", sign2);
//		logger.info(jsonObject.toString());
//		return jsonObject.toString();
//	}

	/**
	 * 电商Sign签名生成
	 * content 内容
	 * keyValue ApiKey
	 * charset 编码方式
	 * @throws UnsupportedEncodingException ,Exception
	 * @return DataSign签名
	 */
	@SuppressWarnings("unused")
	public static String encrypt(String content, String keyValue, String charset) throws UnsupportedEncodingException, Exception
	{
		if (keyValue != null)
		{
			return base64(MD5(content + keyValue, charset), charset);
		}
		return base64(MD5(content, charset), charset);
	}

	/**
	 * base64编码
	 * str 内容
	 * charset 编码方式
	 * @throws UnsupportedEncodingException
	 */
	private static String base64(String str, String charset) throws UnsupportedEncodingException {
		String encoded = Base64.encode(str.getBytes(charset));
		return encoded;
	}

	@SuppressWarnings("unused")
	public static String urlEncoder(String str, String charset) throws UnsupportedEncodingException{
		String result = URLEncoder.encode(str, charset);
		return result;
	}

	/**
	 * MD5加密
	 * str 内容
	 * charset 编码方式
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private static String MD5(String str,String charset) throws Exception {
		MessageDigest md = MessageDigest.getInstance("MD5");
		md.update(str.getBytes(charset));
		byte[] result = md.digest();
		StringBuffer sb = new StringBuffer(32);
		for (int i = 0; i < result.length; i++) {
			int val = result[i] & 0xff;
			if (val <= 0xf) {
				sb.append("0");
			}
			sb.append(Integer.toHexString(val));
		}
		return sb.toString().toLowerCase();
	}
}