package com.puree.hospital.common.core.enums;

public enum AfterSaleStatusEnum {
    UNDER_REVIEW("0", "审核中"),
    DISAPPROVAL("1", "退款申请不予通过"),
    APPLY_PASS("2", "申请通过"),
    SENT_BACK("3", "已寄回"),
    REFUND_SUCCESS("4", "退款/退换成功");

    private final String code;
    private final String info;

    AfterSaleStatusEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
