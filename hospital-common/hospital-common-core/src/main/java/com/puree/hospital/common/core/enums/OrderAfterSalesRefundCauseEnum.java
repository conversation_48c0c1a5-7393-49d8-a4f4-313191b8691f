package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * @ClassName: OrderAfterSalesRefundCauseEnum
 * @Date 2023/3/6 19:08
 * <AUTHOR>
 * @Description: 售后退款原因
 * @Version 1.0
 */
public enum OrderAfterSalesRefundCauseEnum {
    NO_REFUND("0", "已协商完毕不退款"),
    REFUND("1", "已协商完毕退款"),
    OTHER("2", "其他");
    private final String code;
    private final String info;

    OrderAfterSalesRefundCauseEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static OrderAfterSalesRefundCauseEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (OrderAfterSalesRefundCauseEnum enums : OrderAfterSalesRefundCauseEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
