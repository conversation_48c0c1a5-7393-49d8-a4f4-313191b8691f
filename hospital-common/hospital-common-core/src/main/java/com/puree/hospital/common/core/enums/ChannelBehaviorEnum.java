package com.puree.hospital.common.core.enums;
/**
 *
 *  患教-专栏用户行为类型;0为点赞，1为订阅，2为收藏， 3为阅读
 * <AUTHOR>
 * @Data 2024-2-28 11:54:53
 */

public enum ChannelBehaviorEnum {
    LIKE(0, "点赞"),
    SUBSCRIBE(1, "订阅"),
    COLLECT(2, "收藏"),
    READING(3, "阅读"),
    FORWARD(4, "转发");

    private final Integer code;
    private final String info;

    ChannelBehaviorEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
