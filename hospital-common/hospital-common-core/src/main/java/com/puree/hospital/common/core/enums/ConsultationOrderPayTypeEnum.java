package com.puree.hospital.common.core.enums;

/**
 * @ClassName: ConsultationOrderPayTypeEnum
 * @Date 2023/12/26 11:18
 * <AUTHOR>
 * @Description: f
 * @Version 1.0
 */
public enum ConsultationOrderPayTypeEnum {

    // 1-患者支付 ，2-医生赠送， 3-健康解读报告

    PATIENT_PAY(1, "患者支付") ,
    DOCTOR_GIVE(2, "医生赠送") ,
    HEALTH_REPORT(3, "健康解读报告")

    ;

    private Integer status;
    private String msg;

    ConsultationOrderPayTypeEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
