package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.constant.PrescriptionConstant;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/11
 */
@Getter
public enum DirectoryTypeEnum {
    /**
     * 西药&中成药-院内
     */
    MM_INNER("1", PrescriptionConstant.INNER),

    /**
     * 西药&中成药-双通道
     */
    MM_DUAL_CHANNEL("2", PrescriptionConstant.DUAL_CHANNEL),

    /**
     * 中药-院内
     */
    CM_INNER("3", PrescriptionConstant.INNER);

    /**
     * 类型
     */
    private final String type;

    /**
     * 标识常量
     */
    private final String constName;

    DirectoryTypeEnum(String type, String constName) {
        this.type = type;
        this.constName = constName;
    }

    public static DirectoryTypeEnum getByType(String type) {
        for (DirectoryTypeEnum directoryTypeEnum : DirectoryTypeEnum.values()) {
            if (directoryTypeEnum.getType().equals(type)) {
                return directoryTypeEnum;
            }
        }
        return null;
    }

    /**
     * 内外判断
     */
    public static boolean isInner(String type) {
        return Objects.equals(MM_INNER.getType(), type) || Objects.equals(CM_INNER.getType(), type);
    }

    /**
     * 外部判断
     */
    public static boolean isDualChannel(String type) {
        return Objects.equals(MM_DUAL_CHANNEL.getType(), type);
    }
}
