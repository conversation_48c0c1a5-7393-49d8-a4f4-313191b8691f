package com.puree.hospital.common.core.calculator.price.drug;

import com.puree.hospital.common.core.calculator.price.IPrice;
import com.puree.hospital.common.core.calculator.price.IPriceCalculator;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 西药价格计算
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/15 16:07
 */
public class MmDrugPriceCalculator<I extends IPrice> implements IPriceCalculator<I> {

    @Override
    public BigDecimal calculate(I input) {
        BigDecimal unitPrice = nullToZero(input.getUnitPrice()).setScale(4, RoundingMode.HALF_UP);
        BigDecimal quantity = nullToZero(input.getQuantity());
        return unitPrice.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
    }
}
