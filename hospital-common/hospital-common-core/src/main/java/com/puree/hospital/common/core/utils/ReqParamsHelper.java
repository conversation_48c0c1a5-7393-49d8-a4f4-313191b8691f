package com.puree.hospital.common.core.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/31 16:52
 */
public class ReqParamsHelper {
    public static String genReqStringByMap(Map<String,String> params) {
        //keySet获取键集
        List<String> keys = new ArrayList<String>(params.keySet());
        //对键集进行排序
        Collections.sort(keys);
        String preStr = "";
        for(int i = 0;i < keys.size();i++) {
            String curKey = keys.get(i);
            String curValue = params.get(curKey);
            // 如果是最后一个参数，则不加“&”
            if(i== keys.size() - 1) {
                preStr = preStr + curKey + "=" + curValue;
            } else {
                preStr = preStr + curKey + "=" + curValue + "&";
            }
        }
        return preStr;
    }
}