package com.puree.hospital.common.core.aspect;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.puree.hospital.common.api.annotation.RsaEncryptedField;
import com.puree.hospital.common.api.annotation.RsaEncryptedOperate;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.enums.RsaOperateType;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/7 14:28
 */
@Slf4j
@Aspect
@Component
public class RsaEncryptedOperateAspect {
    @Value("${rsa.encryptPublicKey}")
    private String encryptPublicKey;
    @Value("${rsa.encryptPrivateKey}")
    private String encryptPrivateKey;
    @Value("${rsa.decryptPublicKey}")
    private String decryptPublicKey;
    @Value("${rsa.decryptPrivateKey}")
    private String decryptPrivateKey;



    @Around("@annotation(com.puree.hospital.common.api.annotation.RsaEncryptedOperate)")
    public Object beforeMethodExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        RsaEncryptedOperate operateType = method.getAnnotation(RsaEncryptedOperate.class);
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        // 无参数或加密操作
        if (args.length < 1 || operateType.value().equals(RsaOperateType.ENCRYPT)) {
            return joinPoint.proceed(args);
        }
        RSA rsa = new RSA(decryptPrivateKey, null);
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            RsaEncryptedField annotation = parameter.getDeclaredAnnotation(RsaEncryptedField.class);
            // 参数直接使用注解情况
            if (null != annotation) {
                paramAnnotation(args, rsa, i, parameter, annotation);
            } else {
                // 实体内字段使用注解
                entityAnnotation(rsa, parameter, args[i]);
            }
        }
        return joinPoint.proceed(args);
    }

    @AfterReturning(pointcut = "@annotation(com.puree.hospital.common.api.annotation.RsaEncryptedOperate)", returning = "result")
    public void afterMethodExecution(JoinPoint joinPoint, Object result) throws IllegalAccessException {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        RsaEncryptedOperate operateType = method.getAnnotation(RsaEncryptedOperate.class);
        if (operateType.value().equals(RsaOperateType.DECRYPT)) {
            return;
        }
        if (result instanceof AjaxResult) {
            AjaxResult<?> ret = (AjaxResult<?>) result;
            Object data = ret.getData();
            // 请求失败或数据为空
            if (!ret.getCode().equals(HttpStatus.SUCCESS) || null == data) {
                return;
            }
            // 返回数据为List数组
            if (data instanceof List){
                List<?> list = (List<?>) data;
                for (Object item : list) {
                    respEncrypt(item);
                }
                return;
            }
            // 普通对象
            respEncrypt(data);
        }

        // 分页数据
        if (result instanceof TableDataInfo){
            TableDataInfo ret = (TableDataInfo) result;
            List<?> rows = ret.getRows();
            if (CollectionUtil.isEmpty(rows)) {
                return;
            }
            for (Object item : rows) {
                respEncrypt(item);
            }
        }
    }

    /**
     * 返参加密
     *
     * @param o 加密对象
     * @throws IllegalAccessException 异常
     */
    private void respEncrypt(Object o) throws IllegalAccessException {
        Field[] fields = o.getClass().getDeclaredFields();
        RSA rsa = new RSA(null, encryptPublicKey);
        for (Field field : fields) {
            field.setAccessible(true);
            RsaEncryptedField annotation = field.getAnnotation(RsaEncryptedField.class);
            // 无注解、非字符串、解密操作
            if (null == annotation || annotation.value().equals(RsaOperateType.DECRYPT)
                    || !String.class.equals(field.getType())) {
                continue;
            }
            // 读取字段值，为空不加密
            Object value = field.get(o);
            if (value == null || StringUtils.isBlank(value.toString())) {
                continue;
            }
            // 加密
            String encrypt = rsa.encryptBase64(value.toString(), KeyType.PublicKey);
            field.set(o, encrypt);
        }
    }

    /**
     * 参数实体内的字段使用注解解密
     */
    private void entityAnnotation(RSA rsa, Parameter parameter, Object args) throws IllegalAccessException {
        Class<?> type = parameter.getType();
        Field[] fields = type.getDeclaredFields();
        for (Field field : fields) {
            // 检查字段是否带有 @RsaEncryptedField 注解
            RsaEncryptedField fieldAnnotation = field.getAnnotation(RsaEncryptedField.class);
            if (null == fieldAnnotation || fieldAnnotation.value().equals(RsaOperateType.ENCRYPT)
                    || !String.class.equals(field.getType())) {
                continue;
            }

            // 获取字段的值
            field.setAccessible(true);
            Object value = field.get(args);
            if (value == null || StringUtils.isBlank((String) value)) {
                continue;
            }

            log.debug("解密前的值：{}", value);
            byte[] bytes = rsa.decrypt((String) value, KeyType.PrivateKey);
            field.set(args, new String(bytes));
        }
    }

    /**
     * 参数直接使用注解解密
     */
    private void paramAnnotation(Object[] args, RSA rsa, int i, Parameter parameter, RsaEncryptedField annotation) {
        Object arg = args[i];
        // 参数值为空、加密、非字符串
        if (annotation.value().equals(RsaOperateType.ENCRYPT)
                || !String.class.equals(parameter.getType())
                || null == arg || StringUtils.isBlank((String) arg)) {
            return;
        }
        byte[] bytes = rsa.decrypt(arg.toString(), KeyType.PrivateKey);
        args[i] = new String(bytes);
    }
}
