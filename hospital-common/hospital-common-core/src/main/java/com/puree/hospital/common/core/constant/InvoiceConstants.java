package com.puree.hospital.common.core.constant;

/**
 * @ClassName: InvoiceConstants
 * @Date 2023/10/27 12:27
 * <AUTHOR> jian
 * @Description: invoice constant
 * @Version 1.0
 */
public class InvoiceConstants {

    final public static String HOSPITAL_NO_INVOICE_RIGHT = "当前医院不支持发票功能！" ;
    final public static String EXISTED_PRODUCT_INVOICE_CONFIG = "已存在该商品类型的发票配置！" ;
    final public static String EXISTED_HOSPITAL_INVOICE_CONFIG = "已存在该医院的发票配置！" ;
    final public static String WRONG_INVOICE_TYPE = "发票类型不合法，既不是税务盘也不是全电！" ;
    final public static String WRONG_HOSPITAL_INVOICE_STATUS = "启用状态不合法!" ;
    final public static String WRONG_INVOICE_HEADER_TYPE = "发票抬头类型不合法!" ;
    final public static String WRONG_INVOICE_HEADER_DEFAULT = "设置发票抬头默认不合法!" ;
    final public static String WRONG_INVOICE_STATUS = "发票状态不合法!" ;
    final public static String WRONG_ORDER_STATUS = "订单状态不合法!" ;
    final public static String ORDER_COMPLETE_TIME_OVER = "订单完成时间超过1年，无法线上开具发票！" ;
    final public static String NO_INVOICE_HEADER = "没有发票抬头信息！" ;
    final public static String WRONG_INVOICE_HEADER_SHOW = "是否显示发票抬头参数不正确!" ;
    final public static String DUPLICATE_INVOICE_HEADER = "发票抬头重复!" ;
    public static final String NO_COMPLETE_TIME = "订单未记录完成时间，无法开具发票！";
}
