package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * 体检值范围
 *
 * <AUTHOR>
 * @date 2023-02-28
 */
public enum EhrScopeEnum {
    BMI("BMI", "身体质量指数"),
    DBP("BP_001", "舒张压"),
    SBP("BP_002", "收缩压"),
    HR("RHR", "心率"),
    FBG("BG_002", "空腹血糖"),
    PBG("BG_001", "餐后血糖"),
    SBG("BG_003", "随机血糖"),
    TEMP("TC", "体温"),
    CHOL("BF_004", "总胆固醇"),
    TG("BF_003", "甘油三酯"),
    LDL("BF_001", "低密度脂蛋白"),
    HDL("BF_002", "高密度脂蛋白"),
    MUA("MUA", "男尿酸"),
    WUA("WUA", "女尿酸"),
    BO("BO", "血氧"),
    NBZ("UAS_002", "尿比重"),
    UAS_003("UAS_003", "氢离子浓度"),
    MWHR("MWHR", "男性腰臀比"),
    WWHR("WWHR", "女性腰臀比"),
    AVI24("AVI24", "AVI"),
    AVI2529("AVI2529", "AVI"),
    AVI3034("AVI3034", "AVI"),
    AVI3539("AVI3539", "AVI"),
    AVI4044("AVI4044", "AVI"),
    AVI4549("AVI4549", "AVI"),
    AVI5054("AVI5054", "AVI"),
    AVI5559("AVI5559", "AVI"),
    AVI6064("AVI6064", "AVI"),
    AVI6569("AVI6569", "AVI"),
    AVI7074("AVI7074", "AVI"),
    AVI7579("AVI7579", "AVI"),
    AVI80("AVI80", "AVI"),
    API24("API24", "API"),
    API2529("API2529", "API"),
    API3034("API3034", "API"),
    API3539("API3539", "API"),
    API4044("API4044", "API"),
    API4554("API4554", "API"),
    API5559("API5559", "API"),
    API6064("API6064", "API"),
    API6569("API6569", "API"),
    API7074("API7074", "API"),
    API7579("API7579", "API"),
    API80("API80", "API"),
    ARCR_018("ARCR_018", "CSBP"),
    ARCR_019("ARCR_019", "CAPP"),
    BCP_005("BCP_005", "无机盐"),
    BCP_017("BCP_017", "总水分"),
    BCP_012("BCP_012", "内脏脂肪等级"),
    BCP_004("BCP_004", "蛋白质"),
    BCP_009("BCP_009", "细胞内液"),
    BCP_010("BCP_010", "细胞外液"),
    MGGJL("MGGJL", "男性骨骼肌率"),
    WGGJL("WGGJL", "女性骨骼肌率"),
    BCP_002("BCP_002", "体脂肪量"),
    MBM1049("MBM1049", "男性基础代谢"),
    MBM5064("MBM5064", "男性基础代谢"),
    MBM65200("MBM65200", "男性基础代谢"),
    WBM1049("WBM1049", "女性基础代谢"),
    WBM5064("WBM5064", "女性基础代谢"),
    WBM65200("WBM65200", "女性基础代谢"),
    BCP_007("BCP_007", "人体水分率"),
    MBYFR1055("MBYFR1055", "男性体脂肪率"),
    MBYFR5666("MBYFR5666", "男性体脂肪率"),
    MBYFR67200("MBYFR67200", "男性体脂肪率"),
    WBYFR1050("WBYFR1050", "女性体脂肪率"),
    WBYFR5165("WBYFR5165", "女性体脂肪率"),
    WBYFR66200("WBYFR66200", "女性体脂肪率"),
    UAS_008("UAS_008","微白蛋白"),
    UAS_013("UAS_013","微白蛋白"),
    UAS_014("UAS_014","钙离子");

    private final String code;
    private final String info;

    EhrScopeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    /**
     * 通过value取枚举
     *
     * @param value
     * @return
     */
    public static EhrScopeEnum getTypeByCode(String value) {
        if (StringUtils.isNull(value)) {
            return null;
        }
        for (EhrScopeEnum enums : EhrScopeEnum.values()) {
            if (enums.getCode().equals(value)) {
                return enums;
            }
        }
        return null;
    }
}
