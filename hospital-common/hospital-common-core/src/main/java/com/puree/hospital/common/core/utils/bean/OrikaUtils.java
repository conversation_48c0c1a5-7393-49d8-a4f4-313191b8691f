package com.puree.hospital.common.core.utils.bean;

import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;

import java.util.List;

public class OrikaUtils {
    private static final MapperFacade mapperFacade;
    private static final MapperFactory mapperFactory;

    static {
        mapperFactory = new DefaultMapperFactory.Builder().build();
        mapperFacade = mapperFactory.getMapperFacade();
    }

    /**
     * 将对象sourceObject的值拷贝到对象targetObject中
     */
    public static <S, D> D convert(S source, Class<D> toClass) {
        return mapperFacade.map(source, toClass);
    }

    /**
     * 映射集合（默认字段）
     * 映射为集合的形式
     * @param sources    数据（集合） DO对象
     * @param targetClass 映射类对象 DTO对象
     * @return 映射类对象
     */
    public static <S, D> List<D> converts(Iterable<S> sources, Class<D> targetClass) {
        return mapperFacade.mapAsList(sources, targetClass);
    }

}
