package com.puree.hospital.common.core.enums;
/**
 *
 *  数字
 * <AUTHOR>
 */
public enum NemberEnum {
    ZERO(0, "零"),
    ONE(1, "一"),
    TWO(2, "二");

    private final Integer code;
    private final String info;

    NemberEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
