package com.puree.hospital.common.core.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 五师共管角色枚举
 */
public enum FiveRoleEnum {
    DOCTOR("0","医生"),
    PSYCHOLOGY("1","审方药师"),
    NURSE("2", "护师"),
    ASSISTANT("3", "医助"),
    HEALTH_MANAGE("4", "健康管理师"),
    PSYCHOLOGY_CONSULT("5", "心理咨询师"),
    PATIENT("6","患者"),
    REHABILITATION_SPECIALIST("9","康复师"),
    DIETITIAN("10","营养师");
    private final String code;
    private final String info;

    private static final Map<String, FiveRoleEnum> FIVE_ROLE_ENUM_MAP = new HashMap<>();
    FiveRoleEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }
    static {
        for (FiveRoleEnum enums : FiveRoleEnum.values()) {
            FIVE_ROLE_ENUM_MAP.put(enums.getCode(), enums);
        }
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举---每次都要遍历-太慢了，所以摒弃
     * @param value
     * @return
     */
//    @Deprecated
//    public static FiveRoleEnum getTypeByValue(String value){
//        if (StringUtils.isNull(value)){
//            return null;
//        }
//        for (FiveRoleEnum enums : FiveRoleEnum.values()) {
//            if (enums.getCode().equals(value) ) {
//                return enums;
//            }
//        }
//        return null;
//    }
    /**
     * 通过value取枚举---map获取
     * @param value
     * @return
     */
    public static FiveRoleEnum getTypeByValue(String value){
        return FIVE_ROLE_ENUM_MAP.get(value);
    }

    /**
     * 通过value取描述
     * @param value--每次都要遍历-太慢了，所以摒弃
     * @return
     */
//    @Deprecated
//    public static String getInfoByValue(String value) {
//        for (FiveRoleEnum enums : FiveRoleEnum.values()) {
//            if (enums.getCode().equals(value)) {
//                return enums.getInfo();
//            }
//        }
//        return "";
//    }
    /**
     * 通过value取描述
     * @param value--每次都要遍历-太慢了，所以摒弃
     * @return
     */
    public static String getInfoByValue(String value) {
        FiveRoleEnum fiveRoleEnum = FIVE_ROLE_ENUM_MAP.get(value);
        if (fiveRoleEnum != null) {
            return fiveRoleEnum.getInfo();
        }
        return "";
    }

    public static boolean isDoctor(String role){
        return DOCTOR.getCode().equals(role);
    }

    public static boolean isPsychology(String role){
        return PSYCHOLOGY.getCode().equals(role);
    }
}
