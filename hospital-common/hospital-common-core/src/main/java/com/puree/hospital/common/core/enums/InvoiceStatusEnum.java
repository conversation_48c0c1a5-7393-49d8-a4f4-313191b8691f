package com.puree.hospital.common.core.enums;

/**
 * @ClassName: InvoiceStatusEnum
 * @Date 2023/10/23 18:41
 * <AUTHOR>  jian
 * @Description: 发票状态枚举
 * @Version 1.0
 */
public enum InvoiceStatusEnum {

    // 0-不可开具  1-可开具  2-待开具  3-开具中  4-已开具  5-开具失败

    CAN_NOT_ISSUE(0, "不可开具") ,
    CAN_ISSUE(1, "可开具") ,
    TO_ISSUE(2, "待开具") ,
    ISSUING(3, "开具中") ,
    ISSUE_SUCCESS(4, "已开具") ,
    ISSUE_FAIL(5, "开具失败")



    ;


    private Integer status;
    private String msg;

    InvoiceStatusEnum(Integer status, String msg){
        this.status=status;
        this.msg=msg;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
