package com.puree.hospital.common.core.enums;

/**
 * App角色枚举
 */
public enum AppRoleEnum {
    /**
     * 医生
     */
    DOCTOR("0", "医生"),
    /**
     * 药师
     */
    PHARMACIST("1", "审方药师"),
    /**
     * 护士
     */
    NURSE("2", "护师"),
    /**
     * 医助
     */
    ASSISTANT("3", "医助"),
    /**
     * 健康管理师
     */
    HEALTH_MANAGE("4", "健康管理师"),
    /**
     * 心理咨询师
     */
    PSYCHOLOGY_CONSULT("5", "心理咨询师"),
    /**
     * 患者
     */
    PATIENT("6","患者"),
    /**
     * 经纪人
     */
    AGENT("7","经纪人"),
    /**
     * 合作渠道
     */
    CHANNEL("8","合作渠道"),
    /**
     * 康复师
     */
    REHABILITATION_SPECIALIST("9","康复师"),
    /**
     * 营养师
     */
    DIETITIAN("10","营养师"),
    /**
     * 复核药师
     */
    DISPENSING_PHARMACIST("11","复核药师");

    private final String code;
    private final String info;

    AppRoleEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 是否是合作渠道
     *
     * @param identity 身份标识
     * @return 是否是合作渠道
     */
    public static boolean isChannelOrAgent(String identity) {
        return CHANNEL.getCode().equals(identity) || AGENT.getCode().equals(identity);
    }

    public static boolean isAgent(String identity) {
        return AGENT.getCode().equals(identity);
    }

    public static boolean isChannel(String identity) {
        return CHANNEL.getCode().equals(identity);
    }

    public static boolean isPatient(String identity) {
        //历史数据有将患者身份信息为2，需要做兼容
        return PATIENT.getCode().equals(identity) || "2".equals(identity);
    }
}
