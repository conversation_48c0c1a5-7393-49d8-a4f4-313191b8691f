package com.puree.hospital.common.core.enums;

import com.puree.hospital.common.core.utils.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/3/8 15:30
 */
public enum RefundsExchangesEnum {
    // 退换货
    zero("0", "货品破损/少件"),
    one("1", "货品与描述不符"),
    two("2", "发错货品"),
    three("3", "质量问题"),
    four("4", "其他");

    private final String code;
    private final String info;

    RefundsExchangesEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    /**
     * 通过value取枚举
     * @param value
     * @return
     */
    public static RefundsExchangesEnum getTypeByCode(String value){
        if (StringUtils.isNull(value)){
            return null;
        }
        for (RefundsExchangesEnum enums : RefundsExchangesEnum.values()) {
            if (enums.getCode().equals(value) ) {
                return enums;
            }
        }
        return null;
    }
}
