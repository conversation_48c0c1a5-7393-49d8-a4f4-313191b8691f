package com.puree.hospital.common.core.enums;

/**
 * 搜索类型枚举
 *
 * @date 2023/6/7 14:50
 * <AUTHOR>
 */
public enum SearchTypeEnum {
    PRODUCT_DRUGS(0,"商品药品"),
    DOCTOR(1,"商品药品"),
    BE_GOOD_AT(2,"擅长"),
    DEPT(3,"科室"),
    PRODUCT(4, "商品"),
    DRUGS(5, "药品");

    private Integer code;
    private String info;

    SearchTypeEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static boolean containsCode(int code) {
        for (SearchTypeEnum type : SearchTypeEnum.values()) {
            if (type.getCode() == code) {
                return true;
            }
        }
        return false;
    }
}
