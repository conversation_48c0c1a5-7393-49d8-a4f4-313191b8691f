package com.puree.hospital.common.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zjx
 * @Date: 2023/02/01/15:01
 * @Description:
 */
@Slf4j
public class AgeUtil {
    public static String getAgeDetail(String date) {
        StringBuffer tag = new StringBuffer();
        try {
            /*如果有空格*/
            int index = date.indexOf(" ");
            if (index != -1) {
                date = date.substring(0, index);
            }
            String[] data = date.split("-");
            Calendar birthday = new GregorianCalendar(Integer.valueOf(data[0]), Integer.valueOf(data[1]), Integer.valueOf(data[2]));
            Calendar now = Calendar.getInstance();
            int day = now.get(Calendar.DAY_OF_MONTH) - birthday.get(Calendar.DAY_OF_MONTH);
            /*月份从0开始计算，所以需要+1*/
            int month = now.get(Calendar.MONTH) + 1 - birthday.get(Calendar.MONTH);
            int year = now.get(Calendar.YEAR) - birthday.get(Calendar.YEAR);
            /*按照减法原理，先day相减，不够向month借；然后month相减，不够向year借；最后year相减。*/
            if (day < 0) {
                month -= 1;
                /*得到上一个月，用来得到上个月的天数。*/
                now.add(Calendar.MONTH, -1);
                day = day + now.getActualMaximum(Calendar.DAY_OF_MONTH);
            }
            if (month < 0) {
                month = (month + 12) % 12;
                year--;

            }
            //System.out.println("年龄：" + year +"岁" + month +"月" + day +"天");
            if (year > 0) {
                tag.append(year + "岁");
            } else {
                if (month > 0) {
                    tag.append(month + "个月");
                }
                if (day > 0) {
                    tag.append(day + "天");
                }
                if (year == 0 && month == 0 && day == 0) {
                    tag.append("今日出生");
                }
            }
        } catch (Exception ex) {
            log.error("获取年龄error = {}",ex);
        }
        return String.valueOf(tag);
    }

    /**
     * 通过身份证号码计算年龄
     * @param idCard 身份证号
     * @return 年龄
     */
    public static int calAgeByIdCard(String idCard) {
        if (idCard.length() != 18 && idCard.length() != 15) {
            throw new IllegalArgumentException("身份证号长度错误");
        }
        String year;
        String monthDay;
        if (idCard.length() == 18) {
            year = idCard.substring(6,10);
            monthDay = idCard.substring(10,14);
        } else {
            year = "19" + idCard.substring(6, 8);
            monthDay = idCard.substring(8, 12);
        }
        //获取当前时间字符串如：2022-1128
        String nowTimeStr = new SimpleDateFormat("yyyy-MMdd").format(new Date());
        // 当前年份
        String yearNow = nowTimeStr.substring(0, 4);
        // 当前月日
        String monthDayNow = nowTimeStr.substring(5, 9);
        int age = Integer.parseInt(yearNow) - Integer.parseInt(year);
        //age减一的情况 ：用户月日大于当前月日（开头可以为0的4位数int）
        if (Integer.parseInt(monthDay) > Integer.parseInt(monthDayNow)) {
            age = age - 1;
        }
        return age;
    }
}
