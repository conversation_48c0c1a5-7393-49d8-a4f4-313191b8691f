package com.puree.hospital.common.core.enums;

/**
 * <AUTHOR>
 * @date 2023/7/11 17:29
 */
public enum HrOrderStatusEnum {
    HR_20(20, "已付款"),
    HR_30(30, "已审方"),
    HR_40(40, "已配药"),
    HR_50(50, "已发药"),
    HR_60(60, "已完成"),
    HR_70(70, "已退款"),
    HR_254(254, "已关闭");

    private final Integer code;
    private final String info;

    HrOrderStatusEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
