package com.puree.hospital.common.core.enums;

/**
 * 审核状态
 */
public enum AuditStatus {
    NOTAPPROVED(0, "未审核"),
    FAIL(1, "未通过"),
    ADOPT(2, "通过"),
    DISCARD(3, "作废");

    private final Integer code;
    private final String info;

    AuditStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    /**
     * 是否审批通过
     *
     * @param code code
     * @return boolean
     */
    public static boolean isAdopt(Integer code) {
        return ADOPT.getCode().equals(code);
    }
}
