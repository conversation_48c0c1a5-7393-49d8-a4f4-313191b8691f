package com.puree.hospital.common.core.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.stream.Collectors;

public class SHA1Util {
    public static String getSHA1(String srcStr) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA1");
            md.update(srcStr.getBytes(StandardCharsets.UTF_8));
            byte[] digest = md.digest();

            StringBuilder hexstr = new StringBuilder();
            String shaHex = "";
            for (byte b : digest) {
                shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }
                hexstr.append(shaHex);
            }
            return hexstr.toString();
        } catch (Exception e) {
            return "签名验证错误";
        }
    }

    public static void main(String[] args){
        String timestampStr = String.valueOf(System.currentTimeMillis());
        System.out.println(timestampStr);
        String[] array = new String[]{"cffa15bf03a71b96", "04d490719e85912eed105d1bcdc758bb", timestampStr};
        Arrays.sort(array);
        String str = Arrays.asList(array).stream().collect(Collectors.joining(""));
        String signSHA1 = SHA1Util.getSHA1(str);
        System.out.println(signSHA1);
    }
}
