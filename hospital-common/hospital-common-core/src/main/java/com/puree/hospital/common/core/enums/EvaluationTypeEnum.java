package com.puree.hospital.common.core.enums;

/**
 * <p>
 * 评价类型枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/27 19:17
 */
public enum EvaluationTypeEnum {

    /**
     * 问诊订单评价
     */
    CONSULTATION_ORDER(1, "问诊订单评价");

    private final Integer type;

    private final String desc;

    EvaluationTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isConsultationOrder(Integer type) {
        return CONSULTATION_ORDER.getType().equals(type);
    }
}
