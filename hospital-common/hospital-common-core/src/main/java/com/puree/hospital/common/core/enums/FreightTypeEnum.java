package com.puree.hospital.common.core.enums;

import lombok.Getter;

/**
 * <p>
 * 运费类型
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/25 16:07
 */
@Getter
public enum FreightTypeEnum {

    /**
     * 下单支付
     */
    ORDER_PAY("ORDER_PAY", "下单支付"),

    /**
     * 到付
     */
    ARRIVE_PAY("ARRIVE_PAY", "到付");

    private final String code;
    private final String name;

    FreightTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static FreightTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        FreightTypeEnum[] metaArr = FreightTypeEnum.values();
        for (FreightTypeEnum type : metaArr) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    public static FreightTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FreightTypeEnum[] metaArr = FreightTypeEnum.values();
        for (FreightTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isOrderPay(String code) {
        return ORDER_PAY.getCode().equals(code);
    }
}
