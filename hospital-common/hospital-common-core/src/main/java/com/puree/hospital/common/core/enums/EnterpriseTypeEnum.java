package com.puree.hospital.common.core.enums;

import lombok.Data;

/**
 * @ClassName: EnterpriseTypeEnum
 * @Date 2023/1/16 17:14
 * <AUTHOR>
 * @Description: 配送企业类型枚举
 * @Version 1.0
 */
public enum EnterpriseTypeEnum {
    WM("0", "西药"),
    TCM("1", "中药"),
    GOODS("2", "商品");
    private final String code;
    private final String info;

    EnterpriseTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
