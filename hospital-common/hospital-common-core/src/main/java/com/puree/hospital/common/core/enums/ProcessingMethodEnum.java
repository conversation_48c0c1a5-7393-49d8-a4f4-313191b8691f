package com.puree.hospital.common.core.enums;

import java.util.HashMap;
import java.util.Map;

public enum ProcessingMethodEnum {
    ZJ("0","自煎"),
    <PERSON>("1","代煎"),
    <PERSON><PERSON>("2","颗粒代配"),
    <PERSON><PERSON>("3","膏方"),
    <PERSON><PERSON>("4","素丸"),
    <PERSON><PERSON><PERSON>("5","水丸"),
    SMW("6","水蜜丸"),
    XMW("7","小蜜丸"),
    NSW("8","浓缩丸");
    private final String code;
    private final String info;

    ProcessingMethodEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }


    public static String getInfo(String code){
        String info="";
        for (ProcessingMethodEnum processingMethodEnum : ProcessingMethodEnum.values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            if(processingMethodEnum.getCode().equals(code)){
                info= processingMethodEnum.getInfo();
                break;
            }
        }
        return info;
    }
}
