package com.puree.hospital.common.core.utils.kdn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class KDNUtil {

    @Autowired
    private KDNProperties kdnProperties;

    /**
     * 即时查询
     *
     * @param shipperCode
     * @param customerName
     * @param logisticCode
     * @return
     */
    public String realTimeQuery(String shipperCode,
                                String customerName,
                                String logisticCode) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("CustomerName", customerName);
            jsonObject.put("ShipperCode", shipperCode);
            jsonObject.put("LogisticCode", logisticCode);
            Map<String, String> params = new HashMap<String, String>();
            params.put("RequestData", SignUtil.urlEncoder(jsonObject.toJSONString(), "UTF-8"));
            params.put("EBusinessID", kdnProperties.getEBusinessID());
            params.put("RequestType", kdnProperties.getRequestType());//免费即时查询接口指令1002/在途监控即时查询接口指令8001/地图版即时查询接口指令8003
            String dataSign = SignUtil.encrypt(jsonObject.toJSONString(), kdnProperties.getApiKey(), "UTF-8");
            params.put("DataSign", SignUtil.urlEncoder(dataSign, "UTF-8"));
            params.put("DataType", "2");
            // 以form表单形式提交post请求，post请求体中包含了应用级参数和系统级参数
            String result = HttpUtils.sendPost(kdnProperties.getReqURL(), params);
            return result;
        } catch (Exception e) {
            throw new ServiceException("查询物流失败,请稍后重试");
        }
    }

    /**
     * 识别物流单号
     *
     * @param logisticCode
     * @return
     */
    public String numberIdentification(String logisticCode) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("LogisticCode", logisticCode);
            Map<String, String> params = new HashMap<String, String>();
            params.put("RequestData", SignUtil.urlEncoder(jsonObject.toJSONString(), "UTF-8"));
            params.put("EBusinessID", kdnProperties.getEBusinessID());
            params.put("RequestType", "2002");//免费即时查询接口指令1002/在途监控即时查询接口指令8001/地图版即时查询接口指令8003
            String dataSign = SignUtil.encrypt(jsonObject.toJSONString(), kdnProperties.getApiKey(), "UTF-8");
            params.put("DataSign", SignUtil.urlEncoder(dataSign, "UTF-8"));
            params.put("DataType", "2");
            // 以form表单形式提交post请求，post请求体中包含了应用级参数和系统级参数
            String result = HttpUtils.sendPost(kdnProperties.getReqURL(), params);
            JSONObject retObj = JSON.parseObject(result);
            if (!retObj.getBoolean("Success")) {
                throw new ServiceException(retObj.getString("Reason"));
            }
            return result;
        } catch (Exception e) {
            log.error("快递鸟快递识别：", e);
            throw new ServiceException("识别单号失败,请稍后重试");
        }
    }
}
