package com.puree.hospital.common.core.enums;

/**
 * @ClassName: YfOrderEnum
 * @Date 2024/3/14 11:36
 * <AUTHOR>
 * @Description: YfOrderEnum
 * @Version 1.0
 */
public enum YfOrderEnum {

    DRAFT("DRAFT", "草稿")
    ,
    ORDERED("ORDERED", "下单（待抄方）")
    ,
    COPIED("COPIED", "已抄方（待审方）")
    ,
    COPY_REJECT("COPY_REJECT", "抄发退回")
    ,
    AUDITED("AUDITED", "已审方（待调剂）")
    ,
    AUDIT_REJECT("AUDIT_REJECT", "审方拒绝")
    ,
    MAKEUP_ING("MAKEUP_ING", "调剂中")
    ,
    MAKEUP_FAILED("MAKEUP_FAILED", "调剂失败")
    ,
    MAKEUP_FINISH("MAKEUP_FINISH", "调剂完成（待复核）")
    ,
    CHECKED("CHECKED", "已复核（待发货）")
    ,
    DELIVERED("DELIVERED", "已发货")
    ,
    RECEIVED("RECEIVED", "已收货")
    ,
    CANCELLED("CANCELLED", "已取消")

    ;

    private String status ;
    private String msg ;

    YfOrderEnum(String status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
