package com.puree.hospital.common.oss;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.IdUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.storage.core.common.utils.IOUtils;
import com.puree.storage.core.model.StorageObjectModel;
import com.puree.storage.core.model.StoragePutResponse;
import com.puree.storage.core.service.PureeStorageClientService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@Slf4j
@Component
public class OSSUtil {
    private Logger logger = LoggerFactory.getLogger(OSSUtil.class);
    @Autowired
    private OSSProperties ossProperties;
    @Autowired
    private PureeStorageClientService pureeStorageClientService;


    /**
     * @Param objectPath
     * @Param charset
     * @Return String
     * @Description 获取文件内容
     * <AUTHOR>
     * @Date 2024/10/16 18:19
     **/
    public String getObjectAsString(String objectPath, String charset) {
        try {
            return pureeStorageClientService.getObjectAsString(objectPath, charset);
        } catch (IOException e) {
            log.error("获取文件失败:路径{}", objectPath, e);
            throw new RuntimeException(e);
        }

    }

    /**
     * @Param objectPath
     * @Return String
     * @Description 获取文件内容，UTF-8格式
     * <AUTHOR>
     * @Date 2024/10/16 18:19
     **/
    public String getObjectAsString(String objectPath) {
        try {
            return pureeStorageClientService.getObjectAsString(objectPath);
        } catch (IOException e) {
            log.error("获取文件失败:路径{}", objectPath, e);
            throw new RuntimeException(e);
        }

    }

    /**
     * @Param objectPath
     * @Return byte
     * @Description 获取文件内容
     * <AUTHOR>
     * @Date 2024/10/16 18:19
     **/
    public byte[] getObjectAsByteArray(String objectPath) {
        try {
            return pureeStorageClientService.getObjectAsByteArray(objectPath);
        } catch (IOException e) {
            log.error("获取文件失败:路径{}", objectPath, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * @Param objectPath
     * @Return String
     * @Description 获取全路径名
     * <AUTHOR>
     * @Date 2024/10/16 18:24
     **/
    public String getFullPath( String objectPath) {
        return pureeStorageClientService.getFullName(objectPath);
    }

    /**
     * @Param objectPath
     * @Return InputStream
     * @Description 获取文件内容
     * <AUTHOR>
     * @Date 2024/10/16 18:19
     **/
    public InputStream getObjectAsInputStream(String objectPath) {
        return pureeStorageClientService.getObjectAsInputStream(objectPath);
    }

    /**
     * 获取 存储对象模型
     * @param objectPath 相对路径
     * @return StorageObjectModel
     */
    public StorageObjectModel getObject(String objectPath) {
        return pureeStorageClientService.getObject(objectPath);
    }


    /**
     * 生成文件的唯一签名,加上文件大小尽可能避免可能的hash冲突
     * @param fileBytes
     * @param fileSize
     * @return
     */
    public String generateSignature(byte[] fileBytes, Long fileSize) {
        return DigestUtils.md5DigestAsHex(fileBytes) + fileSize;
    }


    /**
     * @Param file
     * @Param directory
     * @Return String
     * @Description 简单上传
     * <AUTHOR>
     * @Date 2024/10/12 16:45
     **/
    public String simpleUpload(MultipartFile file, String directory) {
        Assert.notEmpty(directory, "businessName 不能为空");
        Assert.notNull(file, "file 不能为空");
        try {
            StoragePutResponse respone = pureeStorageClientService.putObject(file, directory);
            return respone.getObjectPath();
        } catch (IOException e) {
            log.error("上传文件失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 校验文件后缀
     *
     * @param suffix
     */
    private void checkFileSuffix(String suffix) {
        //校验文件类型
        boolean isValidExtension = false;
        //Nacos上获取
        for (String extension : ossProperties.getAllowFileAddressSuffix()) {
            if (("." + extension).equalsIgnoreCase(suffix)) {
                isValidExtension = true;
                break;
            }
        }
        if (!isValidExtension) {
            logger.error("文件类型不允许上传：{}", suffix);
            throw new ServiceException("文件类型不允许上传");
        }
    }

    /**
     * @Param inputStream
     * @Param directory
     * @Param suffix
     * @Return String
     * @Description 简单上传
     * <AUTHOR>
     * @Date 2024/10/12 16:46
     **/
    public String simpleUpload(InputStream inputStream, String directory, String suffix) {
        Assert.notEmpty(directory, "businessName 不能为空");
        Assert.notNull(inputStream, "inputStream 不能为空");
        //如果 suffix 是 . 开头则去除 .
        if (StrUtil.isNotEmpty(suffix) && suffix.startsWith(".")) {
            suffix = suffix.substring(1);
        }

        try {
            //如果没有开启filter，则给默认值
            String uuid = IdUtils.simpleUUID();
            StoragePutResponse response = pureeStorageClientService.putObject(inputStream, directory, uuid, suffix);
            return response.getObjectPath();
        } catch (IOException e) {
            log.error("上传文件失败", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 简单上传
     *
     * @param outStream
     * @return
     */
    public String simpleUpload(ByteArrayOutputStream outStream, String directory, String suffix) {
        try {
            checkFileSuffix(suffix);
            InputStream in = new ByteArrayInputStream(outStream.toByteArray());
            return this.simpleUpload(in, directory, suffix);
        } catch (Exception e) {
            logger.error("OSS上传异常：" + e.getMessage());
            return null;
        }
    }


    /**
     * @Param fileUrl 文件的URL
     * @Param prefix 文件前缀
     * @Param suffix 文件后缀
     * @Return String 上传后的URL
     * @Description 获取网络文件
     * @throws IllegalArgumentException 如果参数无效
     * @throws ServiceException 如果文件下载失败
     * <AUTHOR>
     * @Date 2024/10/29 15:05
     **/
    public String uploadNetworkFileToOSS(String fileUrl, String prefix, String suffix) {
        if (StringUtils.isEmpty(fileUrl) || StringUtils.isEmpty(prefix) || StringUtils.isEmpty(suffix)) {
            throw new IllegalArgumentException("文件URL、前缀和后缀不能为空");
        }
        //修改为 事件通知

        HttpURLConnection httpUrl = null; // 声明HttpURLConnection

        try {
            URL url = new URL(fileUrl);
            httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.setConnectTimeout(5000); // 设置连接超时
            httpUrl.setReadTimeout(5000); // 设置读取超时
            httpUrl.connect();

            // 检查HTTP响应状态码
            int responseCode = httpUrl.getResponseCode();
            InputStream errorStream = httpUrl.getErrorStream();
            String s = IOUtils.readStreamAsString(errorStream, "UTF-8");
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("文件下载失败: URL={}, 前缀={}, 后缀={}, 响应码={}, 响应内容={}", fileUrl, prefix, suffix, responseCode, s);
                throw new ServiceException("文件下载失败，HTTP状态码: " + responseCode, null);
            }

            try (InputStream is = httpUrl.getInputStream()) {
                return simpleUpload(is, prefix, suffix);
            }
        } catch (IOException e) {
            log.error("文件下载失败: URL={}, 前缀={}, 后缀={}", fileUrl, prefix, suffix, e);
            throw new ServiceException("文件下载失败");
        }finally {
            if (httpUrl != null) {
                // 确保HttpURLConnection被关闭
                httpUrl.disconnect();
            }
        }
    }
}
